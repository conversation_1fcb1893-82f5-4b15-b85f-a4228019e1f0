﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="TB_TRAYINFO_UPLOAD" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="TB_TRAYINFO_UPLOAD" type="SiaSun.LMS.Model.TB_TRAYINFO_UPLOAD, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SELECTRESULT" class="TB_TRAYINFO_UPLOAD">
      <result property="DATA_INDEX" column="data_index" />
      <result property="MDL_NAME" column="mdl_name" />
      <result property="BATCH_ID" column="batch_id" />
      <result property="TRAY_NO" column="tray_no" />
      <result property="CELL_COUNT" column="cell_count" />
      <result property="LINE_ID" column="line_id" />
      <result property="TIME_UPDATE" column="time_update" />
      <result property="REMARK" column="remark" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="TB_TRAYINFO_UPLOAD_SELECT" parameterClass="int" resultMap="SELECTRESULT">
      Select
      data_index,
      mdl_name,
      batch_id,
      tray_no,
      cell_count,
      line_id,
      time_update,
      remark
      From TB_TRAYINFO_UPLOAD
    </select>

    <select id="TB_TRAYINFO_UPLOAD_SELECT_BY_ID" parameterClass="int" extends = "TB_TRAYINFO_UPLOAD_SELECT" resultMap="SELECTRESULT">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          data_index=#DATA_INDEX#
        </isParameterPresent>
      </dynamic>
    </select>

  </statements>
</sqlMap>