# Test script for InventoryReversalReceiptSync implementation
Write-Host "Testing InventoryReversalReceiptSync implementation..." -ForegroundColor Green

# Read test JSON
$testJson = Get-Content "test_inventory_reversal_receipt.json" -Raw
Write-Host "Test JSON loaded successfully" -ForegroundColor Yellow

# Test 1: Valid input
Write-Host "`nTest 1: Valid input test" -ForegroundColor Cyan
try {
    # Create instance and test
    Add-Type -Path "SiaSun.LMS.Implement\bin\Debug\SiaSun.LMS.Implement.dll" -ErrorAction SilentlyContinue
    $instance = New-Object SiaSun.LMS.Implement.Interface.WMS.InventoryReversalReceiptSync
    $result = $instance.IntefaceMethod($testJson)
    Write-Host "Result: $result" -ForegroundColor Green
} catch {
    Write-Host "Error in Test 1: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Invalid redStorageType
Write-Host "`nTest 2: Invalid redStorageType test" -ForegroundColor Cyan
try {
    $invalidJson = $testJson -replace '"redStorageType": 1', '"redStorageType": 3'
    $result = $instance.IntefaceMethod($invalidJson)
    Write-Host "Result: $result" -ForegroundColor Green
} catch {
    Write-Host "Error in Test 2: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Missing required fields
Write-Host "`nTest 3: Missing required fields test" -ForegroundColor Cyan
try {
    $invalidJson = $testJson -replace '"warehouseId": "WH_001"', '"warehouseId": ""'
    $result = $instance.IntefaceMethod($invalidJson)
    Write-Host "Result: $result" -ForegroundColor Green
} catch {
    Write-Host "Error in Test 3: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Invalid JSON
Write-Host "`nTest 4: Invalid JSON test" -ForegroundColor Cyan
try {
    $invalidJson = "{ invalid json }"
    $result = $instance.IntefaceMethod($invalidJson)
    Write-Host "Result: $result" -ForegroundColor Green
} catch {
    Write-Host "Error in Test 4: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTesting completed!" -ForegroundColor Green