﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_I_BaseService&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_I_BaseService" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:8001/Service/BaseService&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_I_BaseService&quot; contract=&quot;BaseService.I_BaseService&quot; name=&quot;BasicHttpBinding_I_BaseService&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:8001/Service/BaseService&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_I_BaseService&quot; contract=&quot;BaseService.I_BaseService&quot; name=&quot;BasicHttpBinding_I_BaseService&quot; /&gt;" contractName="BaseService.I_BaseService" name="BasicHttpBinding_I_BaseService" />
  </endpoints>
</configurationSnapshot>