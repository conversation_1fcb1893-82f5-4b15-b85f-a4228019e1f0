# Test script for GoodsIssueSync interface
Write-Host "Testing GoodsIssueSync interface..." -ForegroundColor Green

# Read test data
$testData = Get-Content -Path "test_goods_issue.json" -Raw

# Test 1: Valid request
Write-Host "`nTest 1: Valid GoodsIssue request" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/interface/GoodsIssueSync" -Method Post -Body $testData -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Invalid applyType
Write-Host "`nTest 2: Invalid applyType (should be 1 or 2)" -ForegroundColor Yellow
$invalidData = $testData | ConvertFrom-Json
$invalidData.applyType = 5
$invalidJson = $invalidData | ConvertTo-Json -Depth 10
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/interface/GoodsIssueSync" -Method Post -Body $invalidJson -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Missing required fields
Write-Host "`nTest 3: Missing required field (applyGoodsCode)" -ForegroundColor Yellow
$missingData = $testData | ConvertFrom-Json
$missingData.applyGoodsCode = $null
$missingJson = $missingData | ConvertTo-Json -Depth 10
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/interface/GoodsIssueSync" -Method Post -Body $missingJson -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Empty applyGoodsInfoList
Write-Host "`nTest 4: Empty applyGoodsInfoList" -ForegroundColor Yellow
$emptyListData = $testData | ConvertFrom-Json
$emptyListData.applyGoodsInfoList = @()
$emptyListJson = $emptyListData | ConvertTo-Json -Depth 10
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/interface/GoodsIssueSync" -Method Post -Body $emptyListJson -ContentType "application/json"
    Write-Host "Response: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nGoodsIssueSync interface testing completed!" -ForegroundColor Green