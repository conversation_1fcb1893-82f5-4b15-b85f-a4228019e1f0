﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_PROPERTY
	/// </summary>
	public class P_GOODS_PROPERTY : P_Base_House
	{
		public P_GOODS_PROPERTY ()
		{
			//
			// TODO: 此处添加GOODS_PROPERTY的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_PROPERTY> GetList()
		{
			return ExecuteQueryForList<GOODS_PROPERTY>("GOODS_PROPERTY_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<GOODS_PROPERTY> GetListGoodsTypeID(int GOODS_TYPE_ID)
        {
            return this.ExecuteQueryForList<GOODS_PROPERTY>("GOODS_PROPERTY_SELECT_GOODS_TYPE_ID", GOODS_TYPE_ID);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(GOODS_PROPERTY goods_property)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("GOODS_PROPERTY");
                goods_property.GOODS_PROPERTY_ID = id;
            }

            return ExecuteInsert("GOODS_PROPERTY_INSERT", goods_property);
        }
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_PROPERTY goods_property)
		{
			return ExecuteUpdate("GOODS_PROPERTY_UPDATE",goods_property);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_PROPERTY GetModel(System.Int32 GOODS_PROPERTY_ID)
		{
			return ExecuteQueryForObject<GOODS_PROPERTY>("GOODS_PROPERTY_SELECT_BY_ID",GOODS_PROPERTY_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_PROPERTY_ID)
		{
			return ExecuteDelete("GOODS_PROPERTY_DELETE",GOODS_PROPERTY_ID);
		}
		

	}
}
