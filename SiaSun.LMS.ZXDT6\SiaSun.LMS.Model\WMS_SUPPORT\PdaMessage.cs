﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace SiaSun.LMS.Model
{
    public class PdaMessage
    {
        public bool Flag { get; set; }
        public string Message { get; set; }

        public override string ToString()
        {
            return $"Flag[{Flag}]_Message[{Message}]";
        }
    }

    public class PdaMessage<T> where T : class
    {
        public bool Flag { get; set; }
        public string Message { get; set; }
        public List<T> DataList { get; set; } = new List<T>();

        public override string ToString()
        {
            return $"Flag[{Flag}]_Message[{Message}]_DataListCount[{DataList.Count}]";
        }
    }

    public class PdaMessage<T, U> where T : class where U : class
    {
        public bool Flag { get; set; }
        public string Message { get; set; }
        public Dictionary<T, U> DataDictionary { get; set; } = new Dictionary<T, U>();

        public override string ToString()
        {
            return $"Flag[{Flag}]_Message[{Message}]_DataListCount[{DataDictionary.Count}]";
        }
    }

}
