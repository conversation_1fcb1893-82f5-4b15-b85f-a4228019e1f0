﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// IO_CONTROL_APPLY 
	/// </summary>
    [Serializable]
    [DataContract]
	public class IO_CONTROL_APPLY
	{
		public IO_CONTROL_APPLY()
		{
			
		}

        private int _control_apply_id;
        private int _control_id;
        private string _control_apply_type;
        private string _warehouse_code;
        private string _device_code;
        private string _stock_barcode;
        private int _apply_task_status;
        private string _control_error_text;
        private string _manage_error_text;
        private string _create_time;
        private string _control_apply_parameter;
        private string _control_apply_para01;
        private string _control_apply_para02;
        private string _control_apply_remark;
		
		
		///<sumary>
		/// 申请编号
        ///</sumary>
        [DataMember]
		public int CONTROL_APPLY_ID
		{
			get{return _control_apply_id;}
			set{_control_apply_id = value;}
		}
		///<sumary>
		/// 控制编号
        ///</sumary>
        [DataMember]
		public int CONTROL_ID
		{
			get{return _control_id;}
			set{_control_id = value;}
		}
		///<sumary>
		/// 申请类型
        ///</sumary>
        [DataMember]
		public string CONTROL_APPLY_TYPE
		{
			get{return _control_apply_type;}
			set{_control_apply_type = value;}
		}
		///<sumary>
		/// 仓库编码
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_CODE
		{
			get{return _warehouse_code;}
			set{_warehouse_code = value;}
		}
		///<sumary>
		/// 设备类别
        ///</sumary>
        [DataMember]
		public string DEVICE_CODE
		{
			get{return _device_code;}
			set{_device_code = value;}
		}
		///<sumary>
		/// 托盘条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}
		///<sumary>
		/// 申请状态
        ///</sumary>
        [DataMember]
		public int APPLY_TASK_STATUS
		{
			get{return _apply_task_status;}
			set{_apply_task_status = value;}
		}
        ///<sumary>
        /// 监控错误信息（文本）
        ///</sumary>
        [DataMember]
        public string CONTROL_ERROR_TEXT
        {
            get { return this._control_error_text; }
            set { this._control_error_text = value; }
        }
        ///<sumary>
        /// 管理错误信息（文本）
        ///</sumary>
        [DataMember]
        public string MANAGE_ERROR_TEXT
        {
            get { return this._manage_error_text; }
            set { this._manage_error_text = value; }
        }
		///<sumary>
		/// 创建时间
        ///</sumary>
        [DataMember]
		public string CREATE_TIME
		{
			get{return _create_time;}
			set{_create_time = value;}
		}
        ///<sumary>
        /// 参数
        ///</sumary>
        [DataMember]
        public string CONTROL_APPLY_PARAMETER
        {
            get { return _control_apply_parameter; }
            set { _control_apply_parameter = value; }
        }
		///<sumary>
        /// 参数01
        ///</sumary>
        [DataMember]
		public string CONTROL_APPLY_PARA01
		{
			get{return _control_apply_para01;}
			set{_control_apply_para01 = value;}
		}
        ///<sumary>
        /// 参数02
        ///</sumary>
        [DataMember]
        public string CONTROL_APPLY_PARA02
        {
            get { return _control_apply_para02; }
            set { _control_apply_para02 = value; }
        }
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string CONTROL_APPLY_REMARK
		{
			get{return _control_apply_remark;}
			set{_control_apply_remark = value;}
		}
	}
}
