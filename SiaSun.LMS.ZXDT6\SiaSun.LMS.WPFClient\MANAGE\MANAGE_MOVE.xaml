﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_MOVE"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="MANAGE_DOWN"
    Width="1200"
    Height="561"
    Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <uc:ucSplitPropertyGridTab x:Name="ucStorageGroup" Grid.Row="2" />

        <WrapPanel
            Grid.Row="3"
            HorizontalAlignment="Left"
            VerticalAlignment="Center"
            ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition" />
            <CheckBox
                Name="cbxCheckCellStatus"
                Width="150"
                Checked="cbxCheckCellStatus_CheckChanged"
                Content="检查起止货位状态"
                IsChecked="True"
                Unchecked="cbxCheckCellStatus_CheckChanged"
                Visibility="Collapsed" />
            <CheckBox
                Name="cbxDownloadControl"
                Width="150"
                Content="下达控制任务"
                IsChecked="True"
                Visibility="Collapsed" />
            <Button Name="btnConfirm" Width="60">下达任务</Button>
            <Button
                Name="btnRefresh"
                Width="60"
                Margin="10,0,0,0">
                刷新
            </Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent>
