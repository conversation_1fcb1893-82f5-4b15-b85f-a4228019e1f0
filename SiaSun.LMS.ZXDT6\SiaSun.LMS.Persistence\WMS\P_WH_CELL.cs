﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_CELL
	/// </summary>
	public class P_WH_CELL : P_Base_House
	{
		public P_WH_CELL ()
		{
			//
			// TODO: 此处添加WH_CELL的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_CELL> GetList()
		{
			return ExecuteQueryForList<WH_CELL>("WH_CELL_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_CELL wh_cell)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("WH_CELL");
                wh_cell.CELL_ID = id;
            }

            return ExecuteInsert("WH_CELL_INSERT",wh_cell);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_CELL wh_cell)
		{
			return ExecuteUpdate("WH_CELL_UPDATE",wh_cell);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_CELL GetModel(System.Int32 CELL_ID)
		{
			return ExecuteQueryForObject<WH_CELL>("WH_CELL_SELECT_BY_ID",CELL_ID);
		}

        /// <summary>
        /// 多仓库使用相同的cellCode
        /// 2020-03-27 15:46
        /// </summary>
        /// <param name="warehouseCode">对于站台,可空</param>
        /// <param name="cellCode"></param>
        /// <returns></returns>
        public WH_CELL GetModel(string warehouseCode, string cellCode)
        {
            Hashtable ht = new Hashtable();
            if (string.IsNullOrEmpty(warehouseCode))
            {
                ht.Add("CELL_CODE", cellCode);
                return this.ExecuteQueryForObject<WH_CELL>("WH_CELL_SELECT_BY_CELL_CODE", ht);
            }
            else
            {
                ht.Add("WAREHOUSE_CODE", warehouseCode);
                ht.Add("CELL_CODE", cellCode);
                return this.ExecuteQueryForObject<WH_CELL>("WH_CELL_SELECT_BY_WAREHOUSE_CODE_CELL_CODE", ht);
            }
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public WH_CELL GetModel_LockDeviceCode(string LOCK_DEVICE_CODE)
        {
            return this.ExecuteQueryForObject<WH_CELL>("WH_CELL_SELECT_BY_LOCK_DEVICE_CODE", LOCK_DEVICE_CODE);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 CELL_ID)
		{
			return ExecuteDelete("WH_CELL_DELETE",CELL_ID);
		}

        public IList<WH_CELL> CELL_GetList_Z(int WAREHOUSE_ID,string CELL_Z)
        {
            Hashtable ht = new Hashtable();

            ht.Add("WAREHOUSE_ID", WAREHOUSE_ID);

            ht.Add("CELL_Z", CELL_Z);

            return ExecuteQueryForList<WH_CELL>("WH_CELL_SELECT_BY_WAREHOUSE_ID_CELL_Z", ht);
        }

        public IList<WH_CELL> CELL_GetList(string AREA_ID, string CELL_Z)
        {
            Hashtable ht = new Hashtable();

            ht.Add("AREA_ID", AREA_ID);

            ht.Add("CELL_Z", CELL_Z);

            return ExecuteQueryForList<WH_CELL>("WH_CELL_SELECT_BY_AREA_ID_CELL_Z", ht);
        }

        public IList<WH_CELL> CELL_GetListByCellModel(int WAREHOUSE_ID, string CELL_MODEL)
        {
            Hashtable ht = new Hashtable();

            ht.Add("warehouseCode", WAREHOUSE_ID);

            ht.Add("CELL_MODEL", CELL_MODEL);

            return ExecuteQueryForList<WH_CELL>("WH_CELL_SELECT_BY_CELL_MODEL_WAREHOUSE_CODE",ht);
        }

        public int CellInAllocate(string START_DEVICE_CODE, string CELL_MODEL, int MANAGE_ID, int FORK_COUNT, out string sResult)
        {
            try
            {
                sResult = string.Empty;

                Hashtable ht = new Hashtable();

                ht.Add("STARTDEVICECODE", START_DEVICE_CODE);

                ht.Add("CELLMODE", CELL_MODEL);

                ht.Add("MANAGEID", MANAGE_ID);

                ht.Add("FORKCOUNT", FORK_COUNT);

                ht.Add("ENDCELLID", 0);

                ht.Add("RESULTMESSAGE", string.Empty);

                _sqlMap.QueryForList("CellInAllocate2", ht);

                sResult = ht["RESULTMESSAGE"].ToString();


                return ht["ENDCELLID"] == null ? 0 : (int)ht["ENDCELLID"];

            }
            catch (Exception e)
            {
                throw (e);
               
            }
        }


	}
}
