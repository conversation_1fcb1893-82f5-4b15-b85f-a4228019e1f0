﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    //DisallowConcurrentExecution特性，程序会等任务执行完毕以后再去执行，否则会在任务的时间间隔 [Interval]时再启用新的线程执行
    [DisallowConcurrentExecution]
    public class AutoUpdateStatusJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                string autoUpdateStatusSwitch = string.Empty;

                if (MainApp.BaseService._S_SystemService.GetSysParameter("AutoUpdateBatteryStatusSwitch", out autoUpdateStatusSwitch)
                    && autoUpdateStatusSwitch == Enum.FLAG.Enable.ToString())
                {
                    bResult = MainApp.BaseService._S_ManageService.AutoUpdateBatteryStatus(out sResult);
                }
            }
            catch (Exception ex)
            {
                Program.sysLog.ErrorFormat("AutoUpdateStatusJob.Execute():自动更新静置电池状态时异常_异常信息[{0}]", ex.Message);
                throw;
            }
            finally
            {
                if(!bResult)
                {
                    Program.sysLog.WarnFormat("AutoUpdateStatusJob.Execute():自动更新静置电池状态失败_信息[{0}]", sResult);
                }
            }            
        }
    }
}
