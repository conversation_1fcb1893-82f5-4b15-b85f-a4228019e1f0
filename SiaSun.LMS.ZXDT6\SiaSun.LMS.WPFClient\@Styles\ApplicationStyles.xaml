﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock">

    <!--  默认Button样式  -->
    <Style x:Key="styleDefaultButton" TargetType="Button">
        <Setter Property="Height" Value="80" />
        <Setter Property="Padding" Value="2,0,2,0" />
        <Setter Property="Margin" Value="10,2,10,2" />
        <Setter Property="Background" Value="{StaticResource buttonBackgroundDefaultBrush}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource mouseOverDefaultBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  默认TextBox样式  -->
    <Style x:Key="styleDefaultTextBox" TargetType="TextBox">
        <Setter Property="ToolTip" Value="{Binding RelativeSource={RelativeSource self}, Path=(Validation.Errors)[0].ErrorContent}" />
        <Setter Property="MaxHeight" Value="23" />
        <Setter Property="MinHeight" Value="21" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Style.Triggers>
            <Trigger Property="IsReadOnly" Value="True">
                <Setter Property="Background" Value="LightGray" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsFocused" Value="True" />
                    <Condition Property="IsEnabled" Value="True" />
                    <Condition Property="IsReadOnly" Value="False" />
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="LightGreen" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <!--  默认Window样式  -->
    <Style x:Key="styleDefaultWindow" TargetType="Window">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Window">
                    <Grid Background="{StaticResource windowBackgroundDefaultBrush}">
                        <AdornerDecorator>
                            <ContentPresenter />
                        </AdornerDecorator>
                        <ResizeGrip
                            x:Name="WindowResizeGrip"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            IsTabStop="false"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="ResizeMode" Value="CanResizeWithGrip">
                            <Setter TargetName="WindowResizeGrip" Property="Visibility" Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认DockWindow样式  -->
    <Style x:Key="styleDefaultDockWindow" TargetType="ad:DocumentContent">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                    <GradientStop Offset="0" Color="White" />
                    <GradientStop Offset="0.5" Color="White" />
                    <GradientStop Offset="1" Color="White" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认Menu样式  -->
    <Style x:Key="styleDefaultMenu" TargetType="Menu">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="FontFamily" Value="微软雅黑" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Menu}">
                    <Border
                        Background="{StaticResource menuBackgroundDefaultBrush}"
                        BorderBrush="{StaticResource NormalBorderBrush}"
                        BorderThickness="1">
                        <StackPanel
                            ClipToBounds="True"
                            IsItemsHost="True"
                            Orientation="Horizontal" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认MenuItem样式  -->
    <Style x:Key="styleDefaultMenuItem" TargetType="MenuItem">
        <Setter Property="FontSize" Value="13" />
        <Style.Triggers>
            <Trigger Property="Role" Value="SubmenuHeader">
                <Setter Property="Background" Value="{StaticResource menuItemBackgroundDefaultBrush}" />
            </Trigger>
            <Trigger Property="Role" Value="SubmenuItem">
                <Setter Property="Background" Value="{StaticResource menuItemBackgroundDefaultBrush}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  带有CheckBox的MenuItem  -->
    <ControlTemplate x:Key="templateCheckBoxMenuItem" TargetType="MenuItem">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <CheckBox
                Grid.Column="0"
                Margin="5"
                IsChecked="{TemplateBinding IsChecked}" />
            <TextBlock
                Grid.Column="1"
                Margin="5"
                Text="{TemplateBinding Header}" />
        </Grid>
    </ControlTemplate>

    <!--  默认ContextMenu样式  -->
    <Style x:Key="styleDefaultContextMenu" TargetType="ContextMenu">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="Grid.IsSharedSizeScope" Value="true" />
        <Setter Property="HasDropShadow" Value="True" />
        <Setter Property="Padding" Value="2,1,5,1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">
                    <Border
                        Name="Border"
                        BorderBrush="DarkBlue"
                        BorderThickness="1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.Column="0" Width="15">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="1,0" EndPoint="0,0">
                                        <GradientStop Offset="0.1" Color="Black" />
                                        <GradientStop Offset="1" Color="White" />
                                        <GradientStop />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <StackPanel
                                Grid.Column="1"
                                Background="{StaticResource menuItemBackgroundDefaultBrush}"
                                IsItemsHost="True"
                                KeyboardNavigation.DirectionalNavigation="Cycle" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasDropShadow" Value="true">
                            <Setter TargetName="Border" Property="Padding" Value="0,3,0,3" />
                            <Setter TargetName="Border" Property="CornerRadius" Value="4" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认Separator样式  -->
    <Style x:Key="styleDefaultSeparator" TargetType="Separator">
        <Setter Property="Height" Value="1" />
        <Setter Property="Margin" Value="0,2,2,4" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Separator}">
                    <Border BorderBrush="#888" BorderThickness="1" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>

    <!--  默认StatusBar样式  -->
    <Style x:Key="styleDefaultStatusBar" TargetType="StatusBar">
        <Setter Property="FontFamily" Value="微软雅黑" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Background" Value="{StaticResource menuBackgroundDefaultBrush}" />
        <Setter Property="BitmapEffect">
            <Setter.Value>
                <DropShadowBitmapEffect Direction="0.2" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认ToolBar样式  -->
    <Style x:Key="styleDefaultToolBar" TargetType="ToolBar">
        <Setter Property="FontFamily" Value="微软雅黑" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Background" Value="{StaticResource statusBarBackgroundDefaultBrush}" />
        <Setter Property="BitmapEffect">
            <Setter.Value>
                <DropShadowBitmapEffect Direction="0.2" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认GroupBox样式  -->
    <Style x:Key="styleDefaultGroupBox" TargetType="GroupBox">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            BorderThickness="1"
                            CornerRadius="2,2,0,0">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Offset="0.0" Color="{DynamicResource BorderLightColor}" />
                                            <GradientStop Offset="1.0" Color="{DynamicResource BorderDarkColor}" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.BorderBrush>

                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Offset="0.0" Color="White" />
                                            <GradientStop Offset="1.0" Color="PaleGoldenrod" />
                                            <!--<GradientStop Color="{DynamicResource ControlLightColor}" Offset="0.0" />-->
                                            <!--<GradientStop Color="{DynamicResource ControlMediumColor}" Offset="1.0" />-->
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.Background>

                            <ContentPresenter
                                Margin="4"
                                ContentSource="Header"
                                RecognizesAccessKey="True" />
                        </Border>

                        <Border
                            Grid.Row="1"
                            Background="{StaticResource ControlBackBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="0,0,2,2">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{StaticResource BorderMediumColor}" />
                            </Border.BorderBrush>
                            <ContentPresenter Margin="1" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认GridSplitter样式  -->
    <Style x:Key="styleDefaultGridSplitter" TargetType="GridSplitter">
        <Setter Property="ShowsPreview" Value="False" />
        <Style.Triggers>
            <Trigger Property="HorizontalAlignment" Value="Stretch">
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="Margin" Value="20,2,20,2" />
                <Setter Property="Height" Value="3" />
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect />
                    </Setter.Value>
                </Setter>
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0.5">
                            <GradientStop Offset="0.3" Color="Gray" />
                            <GradientStop Offset="0.5" Color="Black" />
                            <GradientStop Offset="0.8" Color="Gray" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="VerticalAlignment" Value="Stretch">
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Margin" Value="2,20,2,20" />
                <Setter Property="Width" Value="3" />
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect />
                    </Setter.Value>
                </Setter>
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0.5,1">
                            <GradientStop Offset="0.3" Color="Gray" />
                            <GradientStop Offset="0.5" Color="Black" />
                            <GradientStop Offset="0.8" Color="Gray" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  默认TabControl样式  -->
    <Style x:Key="styleDefaultTabControl" TargetType="TabControl">
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid KeyboardNavigation.TabNavigation="Local">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <TabPanel
                            x:Name="HeaderPanel"
                            Grid.Row="0"
                            Margin="0,0,4,-1"
                            Panel.ZIndex="1"
                            Background="Transparent"
                            IsItemsHost="True"
                            KeyboardNavigation.TabIndex="1" />
                        <Border
                            x:Name="Border"
                            Grid.Row="1"
                            Background="{StaticResource ControlBackBrush}"
                            BorderThickness="1"
                            CornerRadius="2"
                            KeyboardNavigation.DirectionalNavigation="Contained"
                            KeyboardNavigation.TabIndex="2"
                            KeyboardNavigation.TabNavigation="Local">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{DynamicResource BorderMediumColor}" />
                            </Border.BorderBrush>
                            <ContentPresenter
                                x:Name="PART_SelectedContentHost"
                                Margin="4"
                                ContentSource="SelectedContent" />
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#FFAAAAAA" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认TabItem样式  -->
    <Style x:Key="styleDefaultTabItem" TargetType="TabItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Grid x:Name="Root">
                        <Border
                            x:Name="Border"
                            Margin="0,0,-4,0"
                            BorderThickness="1,1,1,1"
                            CornerRadius="2,12,0,0">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{DynamicResource BorderMediumColor}" />
                            </Border.BorderBrush>
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Offset="0.0" Color="{DynamicResource ControlLightColor}" />
                                            <GradientStop Offset="1.0" Color="{DynamicResource ControlMediumColor}" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.Background>
                            <ContentPresenter
                                x:Name="ContentSite"
                                Margin="12,2,12,2"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                ContentSource="Header"
                                RecognizesAccessKey="True" />
                        </Border>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <!--<ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Panel.Background).(GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource ControlPressedColor}" />
                                        </ColorAnimationUsingKeyFrames>-->
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Border.BorderThickness)">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1,1,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <!--<ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Panel.Background).(GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource DisabledControlDarkColor}" />
                                        </ColorAnimationUsingKeyFrames>-->
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource DisabledBorderLightColor}" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Panel.ZIndex" Value="100" />
                            <Setter TargetName="Border" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <LinearGradientBrush.GradientStops>
                                            <GradientStopCollection>
                                                <GradientStop Offset="0.2" Color="White" />
                                                <GradientStop Offset="1.0" Color="PaleGoldenrod" />
                                            </GradientStopCollection>
                                        </LinearGradientBrush.GradientStops>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  默认Border样式  -->
    <Style x:Key="styleDefaultBorder" TargetType="Border">
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value=" DarkGray" />
        <Setter Property="Background" Value="{StaticResource ControlBackBrush}" />
        <Setter Property="CornerRadius" Value="4" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect ShadowDepth="2" Color="DarkBlue" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Expander  -->
    <Style x:Key="styleDefaultExpander" TargetType="Expander">
        <Setter Property="Padding" Value="5,0,5,0" />
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStopCollection>
                            <GradientStop Offset="0.0" Color="White" />
                            <GradientStop Offset="1.0" Color="PaleGoldenrod" />
                        </GradientStopCollection>
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>