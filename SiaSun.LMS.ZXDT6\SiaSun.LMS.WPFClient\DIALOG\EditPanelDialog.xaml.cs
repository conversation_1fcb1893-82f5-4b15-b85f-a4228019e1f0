﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// EditPanelDialog.xaml 的交互逻辑
    /// </summary>
    public partial class EditPanelDialog : Window
    {
        string strTableName = null;
        string windowName = null;

        /// <summary>
        /// 关联XML表名
        /// </summary>
        public string U_XmlTableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 关联窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return windowName; }
            set { windowName = value; }
        }

        public EditPanelDialog()
        {
            InitializeComponent();
            this.ucWinHeader.DataContext = this;
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        public void U_InitWindow(DataGrid gridApp,DataRow rowSource)
        {
            this.editPanel.U_InitControl(false,gridApp,rowSource);
            //this.Height = SystemParameters.PrimaryScreenHeight > this.editPanel.Height + 50 ? this.editPanel.Height + 50 : SystemParameters.PrimaryScreenHeight;
        }

        /// <summary>
        /// 操作按钮
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            switch (btn.Name)
            { 
                case "btnOK":
                    this.DialogResult = true;
                    break;
                case "btnClose":
                    this.DialogResult = false;
                    break;
            }
            this.Close();
            return;
        }
    }
}
