﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_MAIN
	/// </summary>
	public class P_GOODS_MAIN : P_Base_House
	{
		public P_GOODS_MAIN ()
		{
			//
			// TODO: 此处添加GOODS_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_MAIN> GetList()
		{
			return ExecuteQueryForList<GOODS_MAIN>("GOODS_MAIN_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<GOODS_MAIN> GetListGoodsClassID(int GOODS_CLASS_ID)
        {
            return ExecuteQueryForList<GOODS_MAIN>("GOODS_MAIN_SELECT_BY_GOODS_CLASS_ID", GOODS_CLASS_ID);
        }
		/// <summary>
		/// 新建
		/// </summary>
		public int Add(GOODS_MAIN goods_main)
		{
            if (_isOracleProvider)
            { 
                int id = this.GetPrimaryID("GOODS_MAIN");
                goods_main.GOODS_ID = id;
            }

			if (goods_main.GOODS_ORDER == 0)
			{
				goods_main.GOODS_ORDER = goods_main.GOODS_ID;
			}

            return ExecuteInsert("GOODS_MAIN_INSERT",goods_main);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_MAIN goods_main)
		{
			return ExecuteUpdate("GOODS_MAIN_UPDATE",goods_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_MAIN GetModel(System.Int32 GOODS_ID)
		{
			return ExecuteQueryForObject<GOODS_MAIN>("GOODS_MAIN_SELECT_BY_ID",GOODS_ID);
		}


        public GOODS_MAIN GetModel(string GOODS_CODE)
        {
			if(string.IsNullOrEmpty(GOODS_CODE))
            {
				return null;
            }

            return ExecuteQueryForObject<GOODS_MAIN>("GOODS_MAIN_SELECT_BY_GOODS_CODE", GOODS_CODE);
        }

        public GOODS_MAIN GetModel(string GOODS_CODE,string CONTRACT)
        {
            Hashtable ht = new Hashtable();

            ht.Add("GOODS_CODE", GOODS_CODE);

            ht.Add("CONTRACT", CONTRACT);
            return ExecuteQueryForObject<GOODS_MAIN>("GOODS_MAIN_SELECT_BY_GOODS_CODE_CONTRACT", ht);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_ID)
		{
			return ExecuteDelete("GOODS_MAIN_DELETE",GOODS_ID);
		}
		

	}
}
