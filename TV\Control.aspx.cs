﻿using System;
using System.Collections.Generic;
//using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Timers;
using System.Data.SqlClient;
using System.Data;
using System.Net.Sockets;
using System.Net;
using System.Threading;
using System.Net.Sockets;
using System.Net;

public partial class Control : System.Web.UI.Page
{

    public string DATATIME;

    string connctionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["myConn"].ToString();

    string imes_connctionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["IMESConn"].ToString();

    string IP_localhost;

    private string GetIpAddress()
    {
        IP_localhost = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
        if (string.IsNullOrEmpty(IP_localhost))
            IP_localhost = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];

        if (string.IsNullOrEmpty(IP_localhost))
            IP_localhost = HttpContext.Current.Request.UserHostAddress;
        return IP_localhost;
    }
    
    public void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            try
            {
                this.GetIpAddress();
                this.get_data_manage(IP_localhost);
            }
            catch (Exception ex)
            {

            }
        }
        
    }

    protected void Timer_Tick(object sender, EventArgs e)
    {
        try
        {
            this.GetIpAddress();
            this.get_data_manage(IP_localhost);
        }
        catch (Exception)
        {
        }

        System.GC.Collect();
    }


    public void get_data_manage(string IP_localhost)
    {
        try
        {

            string sql_ip = string.Format(@"select * from SHOW_MAIN where SHOW_IP='{0}'", IP_localhost);
            string sql_Station = string.Format(@"select distinct show_code from SHOW_MAIN where SHOW_IP='{0}'", IP_localhost);

            SqlConnection mycon = new SqlConnection(connctionString);
            mycon.Open();
            SqlDataAdapter my_ip = new SqlDataAdapter(sql_ip, mycon);
            SqlDataAdapter my_Station = new SqlDataAdapter(sql_Station, mycon);
            DataSet da_ip = new DataSet();
            my_ip.Fill(da_ip);

            string station = da_ip.Tables[0].Rows[0]["SHOW_STATION"].ToString();
            DataSet da_Station = new DataSet();
            my_Station.Fill(da_Station);
            ratTable.DataSource = da_Station.Tables[0];
            ratTable.DataBind();

            string sql_manage_main1 = string.Format(@"select * from IO_CONTROL where START_DEVICE_CODE='{0}' AND ERROR_TEXT IS NULL", station);

            SqlDataAdapter myda_manage1 = new SqlDataAdapter(sql_manage_main1, mycon);
            DataSet da_manage1 = new DataSet();
            myda_manage1.Fill(da_manage1);

            managelist.DataSource = da_manage1.Tables[0];
            managelist.DataBind();

            string Now_Date = System.DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd HH:mm:ss");
            string sql_apply = string.Format(@"select TOP 1 * from IO_CONTROL_APPLY_HIS where DEVICE_CODE='{0}' AND CREATE_TIME>'{1}' ORDER BY CREATE_TIME desc", station, Now_Date);

            SqlDataAdapter myda_apply = new SqlDataAdapter(sql_apply, mycon);
            DataSet da_apply = new DataSet();
            myda_apply.Fill(da_apply);

            control.DataSource = da_apply.Tables[0];
            control.DataBind();

            mycon.Close();
            mycon.Dispose();

            #region 

            if (da_manage1.Tables[0].Rows.Count > 6)
            {
                managelist1.DataSource = da_manage1;
                managelist1.DataBind();
            }
            else
            {
                managelist1.DataSource = null;
                managelist1.DataBind();
            }

            if (da_apply.Tables[0].Rows.Count > 3)
            {
                control2.DataSource = da_apply;
                control2.DataBind();
            }
            else
            {
                control2.DataSource = null;
                control2.DataBind();
            }

            #endregion
        }
        catch(Exception ex)
        {

        }

    }

    protected void GridView_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType==DataControlRowType.DataRow)
        {
            int ID = e.Row.RowIndex + 1;
            e.Row.Cells[0].Text = (e.Row.RowIndex + 1).ToString();
        }
    }
}