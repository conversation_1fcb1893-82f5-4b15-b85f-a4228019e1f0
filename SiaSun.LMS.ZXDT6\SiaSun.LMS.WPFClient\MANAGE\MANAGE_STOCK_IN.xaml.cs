﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_STOCK_IN : AvalonDock.DocumentContent
    {
        string strGoodsClassCode = string.Empty;         
        
        SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = null;

        public MANAGE_STOCK_IN()
        {
            InitializeComponent();
        }

        public MANAGE_STOCK_IN(string MANAGE_TYPE_CODE, string GOODS_CLASS_CODE)
        {
            InitializeComponent();

            this.strGoodsClassCode = GOODS_CLASS_CODE;

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE",
                                                                                     MANAGE_TYPE_CODE).RequestObject;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.grpbManage.Header = string.Format(this.grpbManage.Tag.ToString(), this.Title);

            try
            {
                if (mMANAGE_TYPE == null)
                {
                    MainApp._MessageDialog.ShowException("未找到任务类型");
                    return;
                }
                else
                {
                    ManagePositin_Init();

                    ucStockIn_Init();

                    this.ucStockIn.U_StockTypeChanged += new UC.ucStockIn.U_StockTypeValueChangedHandler(ucStockIn_U_StockTypeChanged);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void ucStockIn_U_StockTypeChanged()
        {
            if( this.ucStockIn.U_GoodsID != 0)
                this.StorageListBind();
        }

        private void ManagePositin_Init()
        {
            try
            {
                this.ucManagePosition.U_InitControl(this.mMANAGE_TYPE.MANAGE_TYPE_ID);
                this.ucManagePosition.U_CELL_MODEL = "Height%";
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void ucStockIn_Init()
        {
            try
            {

                Model.GOODS_CLASS mGOODS_CLASS = (Model.GOODS_CLASS)MainApp.I_BaseService.GetModel("GOODS_CLASS_SELECT_BY_GOODS_CLASS_CODE", strGoodsClassCode).RequestObject;

                this.ucStockIn.U_GoodsClassID = mGOODS_CLASS.GOODS_CLASS_ID;

                this.ucStockIn.U_GoodsTypeID = mGOODS_CLASS.GOODS_TYPE_ID;

                this.ucStockIn.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void Refresh()
        {
            try
            {
                this.ucManagePosition.U_Refresh();

                this.ucStockIn.U_Refresh();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void StorageListBind()
        {
            this.ucStockStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStockStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStockStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            this.ucStockStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStockStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStockStorageGroup.U_Where = string.Format(" goods_id = {0} ", this.ucStockIn.U_GoodsID);
            this.ucStockStorageGroup.U_AllowOperatData = false;
            this.ucStockStorageGroup.U_AllowChecked = false;
            this.ucStockStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStockStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStockStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStockStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStockStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStockStorageGroup.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnCreateTask":
                        this.CreateStockTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        private void CreateStockTask()
        {
            bool boolResult = true;
            bool boolBatchResult = true;
            string strResult = string.Empty;
            string sBatchResult = string.Empty;

            boolResult = this.ucStockIn.U_CheckValidate(out strResult);
            if (!boolResult)
            {
                MainApp._MessageDialog.ShowResult(false, strResult);
                return;
            }

            //判断输送位置是否合法
            boolResult = this.ucManagePosition.U_CHECK_WAREHOUSE();
            if (!boolResult)
            {
                return;
            }

            ////tzyg add 2017-02-27  验证托盘使用范围
            //Model.WH_CELL mWH_CELL_START = (Model.WH_CELL)MainApp.I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", this.ucManagePosition.U_START_POSITION_ID).RequestObject;

            if (MainApp._MessageDialog.ShowDialog("ConfirmCreateTask", null) == Sid.Windows.Controls.TaskDialogResult.Cancel)
                return;

            try
            {
                int intStartPositionID = this.ucManagePosition.U_START_POSITION_ID;

                List<Model.MANAGE_LIST> listMANAGE_LIST = new List<Model.MANAGE_LIST>();

                Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                mMANAGE_LIST.GOODS_ID = this.ucStockIn.U_StockType;
                mMANAGE_LIST.GOODS_PROPERTY1 = "0";//箱型
                mMANAGE_LIST.GOODS_PROPERTY2 = "gray";//颜色
                mMANAGE_LIST.MANAGE_LIST_QUANTITY = this.ucStockIn.U_StockQuantity;

                listMANAGE_LIST.Add(mMANAGE_LIST);

                foreach (string sStockBarcode in this.ucStockIn.U_lsStockBarcodes)
                {
                    ////tzyg add 2017-02-27  验证托盘使用范围
                    //boolResult = SiaSun.LMS.Common.RegexValid.CheckStockWH(sStockBarcode, mWH_CELL_START.WAREHOUSE_ID);
                    //if(!boolResult)
                    //{
                    //    strResult = string.Format("托盘条码{0}使用区域有误",sStockBarcode);
                    //    MainApp._MessageDialog.ShowResult(false, strResult);
                    //}

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = "0";
                    mMANAGE_MAIN.END_CELL_ID = this.ucStockIn.rbSingleContinue.IsChecked.Value ? 0 : this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME= SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = MainApp.I_BaseService.GetSysParameter("ManualOutLevel", "0");
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS= Enum.MANAGE_STATUS.Waitting.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE= mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID= this.ucManagePosition.U_START_POSITION_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = sStockBarcode;                    
                    
                    if (mMANAGE_MAIN.END_CELL_ID != 0)
                    {
                        //验证高低货位
                        Model.WH_CELL mWH_CELL_END = (Model.WH_CELL)MainApp.I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.END_CELL_ID).RequestObject;
                        if (mWH_CELL_END == null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "用户指定终点位置不合法");
                            return;
                        }
                        //验证库区
                        Model.WH_CELL mWH_CELL_START = (Model.WH_CELL)MainApp.I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.START_CELL_ID).RequestObject;
                        if (mWH_CELL_START == null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "起始位置位置不合法");
                            return;
                        }
                        if (mWH_CELL_START.WAREHOUSE_ID != mWH_CELL_END.WAREHOUSE_ID)
                        {
                            MainApp._MessageDialog.ShowResult(false, "指定终点位置的库区不正确");
                            return;
                        }
                    }

                    boolResult = MainApp.I_ManageService.ManageCreate(
                        mMANAGE_MAIN,
                        listMANAGE_LIST,
                        raiseTrans: true,
                        checkStorage: this.ucManagePosition.U_CheckStockExistStorage,
                        checkManage: true,
                        checkCellStatus: true,
                        autoComplete: this.ucManagePosition.U_AutoCompleteTask,
                        autoControl: this.ucManagePosition.U_AutoDownloadControlTask,
                        out strResult);

                    if (!boolResult)
                    {
                        MainApp._MessageDialog.ShowResult(boolResult, strResult);
                        boolBatchResult = false;
                        break;
                    }
                    else
                    {
                        sBatchResult += mMANAGE_MAIN.STOCK_BARCODE + "\n";
                    }                    
                }
            }
            catch (Exception ex)
            {
                boolResult = false;
                strResult = ex.Message;
            }

            if (sBatchResult != string.Empty)
            {
                MainApp._MessageDialog.ShowResult(true, sBatchResult + "\n" + "任务下达成功");
            }

            if (boolBatchResult)
            {
                this.Refresh();
            }
        }
    }
}
