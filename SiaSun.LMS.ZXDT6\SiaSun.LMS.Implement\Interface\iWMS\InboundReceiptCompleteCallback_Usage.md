# 入库单完成上报回调接口使用说明

## 概述
`InboundReceiptCompleteCallback` 类实现了向中央库物资系统上报入库单完成状态的功能。该接口遵循中央库接口文档中"入库单完成上报"的规范。

## 接口流程
1. **注册(regist)** - 获取系统密钥(secrit)
2. **获取凭证(applyToken)** - 使用密钥获取访问凭证(certificate)
3. **访问接口(accessInterface)** - 携带凭证调用具体业务接口

## 使用方法

### 方法1：批量上报多个入库明细
```csharp
using SiaSun.LMS.Implement.Interface.iWMS;

// 创建入库单完成上报实例
var callback = new InboundReceiptCompleteCallback();

// 准备入库明细数据
var items = new List<InboundReceiptCompleteCallback.InboundReceiptCompleteItem>
{
    new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
    {
        storageNum = 100,                    // 本次入库数量
        warehouseCode = "WH001",             // 仓库编码
        shelfCode = "SH001",                 // 货架编码
        oId = "ORIGINAL_ID_001",             // 入库明细原ID
        lId = "STEREO_WAREHOUSE_ID_001",     // 立体仓系统入库明细ID
        storageType = 28                     // 入库类型：28=入库单
    },
    new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
    {
        storageNum = 50,
        warehouseCode = "WH002",
        shelfCode = "SH002",
        oId = "ORIGINAL_ID_002",
        lId = "STEREO_WAREHOUSE_ID_002",
        storageType = 73                     // 入库类型：73=出库红冲单
    }
};

// 调用接口
bool success = callback.IntefaceMethod(items, out string message);
if (success)
{
    Console.WriteLine($"入库单完成上报成功：{message}");
}
else
{
    Console.WriteLine($"入库单完成上报失败：{message}");
}
```

### 方法2：单个入库明细上报
```csharp
using SiaSun.LMS.Implement.Interface.iWMS;

// 创建入库单完成上报实例
var callback = new InboundReceiptCompleteCallback();

// 调用接口（单个明细）
bool success = callback.IntefaceMethod(
    storageNum: 100,                         // 本次入库数量
    warehouseCode: "WH001",                  // 仓库编码
    shelfCode: "SH001",                      // 货架编码
    oId: "ORIGINAL_ID_001",                  // 入库明细原ID
    lId: "STEREO_WAREHOUSE_ID_001",          // 立体仓系统入库明细ID
    storageType: 28,                         // 入库类型
    out string message
);

if (success)
{
    Console.WriteLine($"入库单完成上报成功：{message}");
}
else
{
    Console.WriteLine($"入库单完成上报失败：{message}");
}
```

## 参数说明

### InboundReceiptCompleteItem 属性
| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storageNum | int | 是 | 本次入库数量 |
| warehouseCode | string | 是 | 仓库编码 |
| shelfCode | string | 是 | 货架编码 |
| oId | string | 是 | 入库明细原ID |
| lId | string | 是 | 立体仓系统入库明细ID |
| storageType | int | 是 | 入库类型：28=入库单，73=出库红冲单，75=归还单 |

## 配置要求
确保在配置文件中设置了以下配置项：
```xml
<add key="ExternalServiceUrl" value="http://************:9008/api/ws/AuthService?wsdl" />
```

## 错误处理
接口会返回详细的错误信息，包括：
- 参数验证错误
- 网络连接错误
- 认证失败错误
- 业务逻辑错误

所有错误都会记录到系统日志中，便于问题排查。

## 注意事项
1. 确保网络连接正常，能够访问中央库物资系统
2. 入库类型必须为有效值：28、73或75
3. 所有必填字段都不能为空
4. 建议在调用前进行充分的参数验证
5. 接口调用会记录详细日志，包括请求参数和响应结果

## 日志记录
成功调用时会记录：
```
InboundReceiptCompleteCallback成功_明细数[2]_traceId[xxx-xxx-xxx]_信息[入库单完成上报成功]
```

失败时会记录：
```
InboundReceiptCompleteCallback异常：具体错误信息
```
