﻿using System;
using System.Collections.Generic;
using System.Data;
using System.EnterpriseServices;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class _1 : System.Web.UI.Page
{
    protected void Page_Load()
    {
        if (!Page.IsPostBack)
        {
            bRefresh_Click(null, null); 
        }
    }

    protected void bSearch_Click(object sender, EventArgs e)
    {
        try
        {
            string searchTxt = this.tbSearch.Text.Trim();
            DataTable dtGoods = Common._I_DatabaseClient.GetList(string.Format(@"
                select GOODS_ID, GOODS_CODE, GOODS_NAME from GOODS_MAIN where GOODS_CLASS_ID = 1 and GOODS_FLAG = '1' 
                and (GOODS_CODE like '%{0}%' or GOODS_NAME like '%{0}%') order by GOODS_CODE", searchTxt), "HouseMap");
            if(dtGoods.Rows.Count == 0)
            {
                Common.LayerFailed(this, string.Format("未能找到物资 {0}", searchTxt));
                return;
            }
            DataTable dt = Common.NewDataTable(3);
            foreach(DataRow dr in dtGoods.Rows)
            {
                Common.AddDataTable(dt, dr["GOODS_ID"].ToString(), dr["GOODS_CODE"].ToString(), dr["GOODS_NAME"].ToString());
                
            }
            this.gvGoods.DataSource = dt.DefaultView;
            this.gvGoods.DataBind();
        }
        catch (WebException ex)
        {
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch(Exception ex)
        {
            bRefresh_Click(null, null);
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bSave_Click(object sender, EventArgs e)
    {
        try
        {

        }catch(Exception ex)
        {

        }
    }

    protected void bRefresh_Click(object sender, EventArgs e)
    {
        try
        {
            this.tbStockBarcode.Text = string.Empty;
            this.tbSearch.Text = string.Empty;
            DataTable dt = Common.NewDataTable(3);
            this.gvGoods.DataSource = dt.DefaultView;
            this.gvGoods.DataBind();
            dt =  Common.NewDataTable(3);
            this.gvInStorage.DataSource = dt.DefaultView;
            this.gvInStorage.DataBind();
            
        }catch(Exception ex)
        {
            Common.LayerFailed(this, ex.Message);

        }
    }

    protected void gvGoods_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dt2 = (DataTable)this.gvInStorage.DataSource;
        if (this.gvGoods.SelectedRow != null)
        {
            Label label1 = this.gvGoods.SelectedRow.FindControl("value1") as Label;
            Label label2 = this.gvGoods.SelectedRow.FindControl("value2") as Label;
            Label label3 = this.gvGoods.SelectedRow.FindControl("value3") as Label;
            if (label1 != null && label2 != null && label3 != null)
            {
                DataTable dt = Common.NewDataTable(4);
                int iCount = this.gvInStorage.Rows.Count;
                if (iCount > 0)
                {
                    foreach (GridViewRow gvr in this.gvInStorage.Rows)
                    {
                        Common.AddDataTable(dt, (gvr.FindControl("value1") as Label).Text, (gvr.FindControl("value2") as Label).Text,
                            (gvr.FindControl("value3") as Label).Text, (gvr.FindControl("value4") as TextBox).Text);
                    }
                }
                Common.AddDataTable(dt, label1.Text, label2.Text, label3.Text, "0");
                this.gvInStorage.DataSource = dt.DefaultView;
                this.gvInStorage.DataBind();
            }
        }
    }

    protected void GoodsDataBound(object sender, GridViewRowEventArgs e)
    {
        e.Row.Attributes.Add("ondblclick", Page.ClientScript.GetPostBackClientHyperlink(sender as GridView, "Select$" + e.Row.RowIndex.ToString()));
        if (e.Row.RowIndex >= 0)
        {
            e.Row.Attributes.Add("onmouseover", "currentcolor=this.style.backgroundColor;this.style.backgroundColor='#1E9FFF';");
            e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor=currentcolor;");
        }
    }

    protected void gvGoods_SelectedIndexDelete(object sender, EventArgs e)
    {
        if (this.gvGoods.SelectedRow != null)
        {
            Label label1 = this.gvGoods.SelectedRow.FindControl("value1") as Label;
            Label label2 = this.gvGoods.SelectedRow.FindControl("value2") as Label;
            Label label3 = this.gvGoods.SelectedRow.FindControl("value3") as Label;
            if (label1 != null && label2 != null && label3 != null)
            {
                DataTable dt = Common.NewDataTable(4);
                int iCount = this.gvInStorage.Rows.Count;
                if (iCount > 0)
                {
                    foreach (GridViewRow gvr in this.gvInStorage.Rows)
                    {
                        Common.AddDataTable(dt, (gvr.FindControl("value1") as Label).Text, (gvr.FindControl("value2") as Label).Text,
                            (gvr.FindControl("value3") as Label).Text, (gvr.FindControl("value4") as TextBox).Text);
                    }
                }
                dt.Rows.RemoveAt(this.gvInStorage.SelectedIndex);
                this.gvInStorage.DataSource = dt.DefaultView;
                this.gvInStorage.DataBind();
            }
        }
    }

    protected void GoodsDataUnbind(object sender, GridViewRowEventArgs e)
    {
        e.Row.Attributes.Add("ondblclick", Page.ClientScript.GetPostBackClientHyperlink(sender as GridView, "Select$" + e.Row.RowIndex.ToString()));
        e.Row.Attributes.Add("onmouseover", "currentcolor=this.style.backgroundColor;this.style.backgroundColor='#1E9FFF';");
        e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor=currentcolor;");
    }

    protected void GoodsRowDelete(object sender, GridViewDeleteEventArgs e)
    {

    }
}