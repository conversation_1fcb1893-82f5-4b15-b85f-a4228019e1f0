﻿<configuredServices>
  <service type="SiaSun.LMS.Implement.S_Database, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_Database" binding="basicHttpBinding" url ="{0}/Database" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_System, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_System" binding="basicHttpBinding" url ="{0}/System" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_Plan, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_Plan" binding="basicHttpBinding" url ="{0}/Plan" render ="House"/>
	<service type="SiaSun.LMS.Implement.S_Manage, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_Manage" binding="basicHttpBinding" url ="{0}/Manage" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_Device, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_Device" binding="basichttpbinding" url ="{0}/Device" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_Interface, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_Interface" binding="basichttpbinding" url ="{0}/Interface" render ="House"/>
</configuredServices>
