﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.WH.SET_CELL_GOODS"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock" 
        Height="500" Width="1000"  Title="SET_CELL_GOODS"
        Loaded="DocumentContent_Loaded">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="1*"></RowDefinition>
        </Grid.RowDefinitions>
        
        <uc:ucQuickQuery x:Name="ucQueryCell" Grid.Row="0" BorderBrush="Black"></uc:ucQuickQuery>
        <uc:ucSplitPropertyGridTab x:Name="gridCellConfig" Grid.Row="1"></uc:ucSplitPropertyGridTab>
        
    </Grid>
</ad:DocumentContent>
