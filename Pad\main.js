$(function () {
  $('input[wms-confirm]').off('click').on('click', function (e) {
    e.preventDefault();
    var $this = $(this);
    layer.confirm($this.attr('wms-confirm'), { title: $this.val(), icon: 3, shadeClose: true }, function (index) {
      layer.close(index);
      $this.next().click();
    });
  });
  $('input[wms-enter]').off('keypress').on('keypress', function (e) {
    if (e.keyCode == 13) {
      e.preventDefault();
      $(this).next().click();
    }
  });
  $('.menu-block').off('click').on('click', function (e) {
    e.preventDefault();
    $(this).next().click();
  });
  $('.android-menu').off('click').on('click', function (e) {
    e.preventDefault();
    if (typeof (androidJS) != 'undefined') {
      androidJS.JSInterface($(this).attr('menu'));
    }
  });
  $('input[wms-focus]').each(function () {
    $this = $(this);
    if ($this.val() == "") {
      $this.focus();
      return false;
    }
  });
});

function layerAlert(success, title, content) {
  setTimeout(function () {
    layer.alert(content, {
      icon: success ? 1 : 2, title: title, shadeClose: true, end: function () {
        $('input[wms-focus]').each(function () {
          $this = $(this);
          if ($this.val() == "") {
            $this.focus();
            return false;
          }
        });
      }
    });
  }, 100);
  if (typeof(androidJS) != 'undefined' && success == false) {
    androidJS.JSInterface("Error");
  }
}

function layerConfirm(title, content, id) {
  setTimeout(function () {
    layer.confirm(content, { title: title, icon: 3, shadeClose: true }, function (index) {
      layer.close(index);
      $(id).click();
    });
  }, 100);
}