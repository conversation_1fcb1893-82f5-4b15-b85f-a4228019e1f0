﻿<?xml version="1.0" encoding="utf-8"?>
<!--PLAN|MANAGE|RECORD|STORAGE|IO|FLOW|GOODS|WH|LED|SYS-->
<Tables>
  <Table Name="PLAN_TYPE">
    <Field Column="PLAN_TYPE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_GROUP" DbType="String" Header="分组" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_INOUT" DbType="String" Header="类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="32" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_CLASS" DbType="String" Header="运行时调用类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="关联任务类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="22" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--PLAN_EDIT-->
  <Table Name="PLAN_MAIN">
    <Field Column="PLAN_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_RELATIVE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_ROOT_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_CODE" DbType="Int32" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="SELECT PLAN_TYPE_CODE AS value, PLAN_TYPE_NAME AS name FROM PLAN_TYPE WHERE PLAN_TYPE_FLAG='1'" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_INOUT_STATION" DbType="String" Header="出入站台" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select CELL_CODE as value , CELL_NAME as name from WH_CELL where CELL_TYPE='Station' and CELL_FLAG='1'" Remark="" Order="145" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_FROM_DEPT" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="-1" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="select Convert(varchar(50),project_id) AS value,project_qkcode+project_name as name from project_main where PROJECT_FLAG='1'" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CREATE_TIME" DbType="String" Header="创建时间" ControlType="DateTimePicker" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_BEGIN_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_END_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="311" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_STATUS" DbType="String" Header="计划状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="PLAN_STATUS" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CREATER" DbType="String" Header="操作员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_FROM_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TO_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="900" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_FLAG" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--PLAN_EDIT PLAN_ACTION-->
  <Table Name="V_PLAN">
    <Field Column="PLAN_ID" DbType="String" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_GROUP" DbType="String" Header="拣配单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="115" AllowQuery="1" QueryOperation="" />
    <Field Column="PLAN_CODE" DbType="string" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="" />
    <Field Column="PLAN_TYPE_CODE" DbType="String" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="SELECT PLAN_TYPE_CODE as value,PLAN_TYPE_NAME as name FROM PLAN_TYPE where PLAN_TYPE_FLAG='1' order by PLAN_TYPE_ORDER" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="PLAN_CREATE_TIME" DbType="string" Header="制单时间" ControlType="DateTimePicker" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_BEGIN_TIME" DbType="string" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_END_TIME" DbType="string" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="PLAN_STATUS" Remark="" Order="170" AllowQuery="1" QueryOperation="" />
    <Field Column="BACKUP_FIELD5" DbType="String" Header="包含立库" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <!--<Field Column="PLAN_CREATER" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="like" />-->
    <Field Column="PLAN_FLAG" DbType="String" Header="源系统" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="like" />
    <Field Column="PLAN_LEVEL" DbType="String" Header="优先级" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="PLAN_LEVEL" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_INOUT_STATION" DbType="String" Header="整托出口" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="1" QueryOperation="like" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="箱拣出口" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD2" DbType="String" Header="物流公司" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_FROM_USER" DbType="String" Header="送达方" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="245" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CONFIRM_USER" DbType="String" Header="送达城市" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CONFIRM_TIME" DbType="String" Header="送货日期" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TO_USER" DbType="String" Header="收货人" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TO_DEPT" DbType="String" Header="送达地址" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_FROM_DEPT" DbType="Int32" Header="当前拣选人" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="暂停前状态" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD4" DbType="String" Header="出库队列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--PLAN_ACTION-->
  <Table Name="V_PLAN_LIST">
    <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="PLAN_LIST_CODE" DbType="String" Header="需求行号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="115" AllowQuery="0" QueryOperation="" />-->
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="PLAN_LIST_PICKED_QUANTITY" DbType="String" Header="拣配绑定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="任务下达数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_QUANTITY_APPEND" DbType="String" Header="输送完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="177" AllowQuery="0" QueryOperation="" />-->
    <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="OUT_STATION" DbType="string" Header="出库站台" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="182" AllowQuery="0" QueryOperation="" />-->
    <!--<Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="185" AllowQuery="0" QueryOperation="" />-->
    <!--<Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />-->
  </Table>
  <!--<Table Name="PLAN_DETAIL">
    <Field Column="PLAN_DETAIL_ID" DbType="String" Header="计划明细ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_ID" DbType="string" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_BARCODE" DbType="string" Header="SN码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_DETAIL_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="MANAGE_TYPE">
    <Field Column="MANAGE_TYPE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_INOUT" DbType="String" Header="出入库" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_GROUP" DbType="String" Header="分组" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CLASS" DbType="String" Header="任务运行类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_TYPE_CLASS" DbType="String" Header="库存运行类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_FLAG" DbType="String" Header="标记" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_PROPERTY" DbType="String" Header="属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--MANAGE_ACTION-->
  <Table Name="V_MANAGE">
    <Field Column="MANAGE_ID" DbType="String" Header="任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="任务类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select MANAGE_TYPE_CODE as value,MANAGE_TYPE_NAME as name from MANAGE_TYPE where MANAGE_TYPE_FLAG = 1 order by MANAGE_TYPE_ORDER" Remark="" Order="140" AllowQuery="1" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="like" />
    <Field Column="START_AREA_NAME" DbType="String" Header="起始库区" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="0" QueryOperation="" />
    <Field Column="START_DEVICE_CODE" DbType="String" Header="起始设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="167" AllowQuery="0" QueryOperation="" />
    <Field Column="START_CELL_NAME" DbType="String" Header="起始位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="1" QueryOperation="like" />
    <Field Column="END_AREA_NAME" DbType="String" Header="目标区域" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
    <Field Column="END_DEVICE_CODE" DbType="String" Header="目标设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="177" AllowQuery="0" QueryOperation="" />
    <Field Column="END_CELL_NAME" DbType="String" Header="目标位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="like" />
    <Field Column="MANAGE_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="MANAGE_STATUS" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
    <Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="下达时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_OPERATOR" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="V_MANAGE_LIST">
    <Field Column="MANAGE_LIST_ID" DbType="String" Header="任务单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="122" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--<Table Name="V_MANAGE_PICK">
    <Field Column="MANAGE_LIST_ID" DbType="String" Header="任务列表ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_ID" DbType="String" Header="任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_ID" DbType="String" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="121" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="122" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_LIST_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="123" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="124" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY6" DbType="String" Header="拣选任务单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="131" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY7" DbType="String" Header="拣选项目号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="132" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY8" DbType="String" Header="拣选WBS号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="133" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="135" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="DETAIL_FLAG" DbType="String" Header="是否扫码" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="任务开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="START_CELL_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="END_CELL_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="Decimal" Header="箱号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_STATUS" DbType="String" Header="任务状态" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CODE" DbType="String" Header="WBS编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="END_POSITION" DbType="String" Header="拣选目标位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_SOURCE" DbType="String" Header="任务来源" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="MANAGE_DETAIL">
    <Field Column="MANAGE_DETAIL_ID" DbType="String" Header="任务明细ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_LIST_ID" DbType="string" Header="任务单ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_BARCODE" DbType="string" Header="防伪码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_DETAIL_QUANTITY" DbType="string" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_DETAIL_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="RECORD_DETAIL">
    <Field Column="RECORD_DETAIL_ID" DbType="String" Header="记录明细ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_LIST_ID" DbType="string" Header="记录单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_BARCODE" DbType="string" Header="防伪码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_DETAIL_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--<Table Name="V_RECORD_MAIN">
    <Field Column="RECORD_ID" DbType="String" Header="记录ID" ControlType="" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_TYPE_CODE" DbType="String" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select PLAN_TYPE_CODE as value ,PLAN_TYPE_NAME as name from PLAN_TYPE where PLAN_TYPE_FLAG =1 " Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_RELATE_CODE" DbType="String" Header="任务单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="135" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="任务类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select MANAGE_TYPE_CODE as value ,MANAGE_TYPE_NAME as name from MANAGE_TYPE where MANAGE_TYPE_FLAG =1 " Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select GOODS_CLASS_ID as value,GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="START_POSITION" DbType="String" Header="开始位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="END_POSITION" DbType="String" Header="终止位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_OPERATOR" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_SOURCE" DbType="String" Header="原系统" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="下达时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_END_TIME" DbType="String" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="V_RECORD_LIST">
    <Field Column="RECORD_ID" DbType="String" Header="记录ID" ControlType="" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_ID" DbType="String" Header="任务ID" ControlType="" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="115" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="任务类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select MANAGE_TYPE_CODE as value ,MANAGE_TYPE_NAME as name from MANAGE_TYPE where MANAGE_TYPE_FLAG =1 " Remark="" Order="140" AllowQuery="1" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="like" />
    <Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="157" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select GOODS_CLASS_ID as value ,GOODS_CLASS_NAME as name from GOODS_CLASS where GOODS_CLASS_FLAG =1 " Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="1" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_LIST_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
    <Field Column="START_AREA_NAME" DbType="String" Header="起始库区" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="START_DEVICE_CODE" DbType="String" Header="起始设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
    <Field Column="START_CELL_CODE" DbType="String" Header="起始位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="195" AllowQuery="0" QueryOperation="" />
    <Field Column="END_AREA_NAME" DbType="String" Header="目标库区" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="END_DEVICE_CODE" DbType="String" Header="目标设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="1" QueryOperation="" />
    <Field Column="END_CELL_CODE" DbType="String" Header="目标位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="215" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="下达时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_END_TIME" DbType="String" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_CONFIRM_TIME" DbType="String" Header="过账时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_OPERATOR" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
    <Field Column="RECORD_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="V_STORAGE_LIST">
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="like" />
    <Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select GOODS_CLASS_ID as value ,GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="127" AllowQuery="1" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_QUANTITY_UNLOCK" DbType="Decimal" Header="可用数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="155" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="1" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="1" QueryOperation="=" />
    <Field Column="AREA_ID" DbType="String" Header="区域" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value ,AREA_NAME as name from WH_AREA" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--<Table Name="V_STORAGE_MAIN">
    <Field Column="STORAGE_ID" DbType="String" Header="库存ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="高度" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select GOODS_CLASS_ID as value,GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="160" AllowQuery="0" QueryOperation="=" />
    <Field Column="ENTRY_TIME" DbType="String" Header="箱中最后入库物料的入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="179" AllowQuery="0" QueryOperation="" />
    <Field Column="IS_EXCEPTION" DbType="Decimal" Header="是否异常" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="KITBOX_UP_COMPLETE" DbType="Decimal" Header="齐套箱首次上架完成" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <!--<Table Name="V_STORAGE_LIST_UPDATE">
    <Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="105" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="垛条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="库存类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="190" AllowQuery="0" QueryOperation="=" />
    <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="UPDATE_TIME" DbType="String" Header="流程开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="V_STORAGE_LIST_BIND">
    <Field Column="STORAGE_LOCK_ID" DbType="String" Header="锁定ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="1" QueryOperation="" />
    <Field Column="LOCK_CODE" DbType="String" Header="假锁单行" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="145" AllowQuery="1" QueryOperation="like" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="" />
    <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value , AREA_NAME as name from WH_AREA" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="库区编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="167" AllowQuery="1" QueryOperation="" />
    <Field Column="STORAGE_LOCK_QUANTITY" DbType="String" Header="锁定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="DETAIL_FLAG" DbType="String" Header="扫防伪码发货标记" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LOCK_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="占用托盘自动下达信息" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
  </Table>
  <!--<Table Name="STORAGE_LOCK">
    <Field Column="STORAGE_LOCK_ID" DbType="String" Header="锁定ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LOCK_QUANTITY" DbType="String" Header="锁定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value , AREA_NAME as name from WH_AREA" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LOCK_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="STORAGE_DETAIL">
    <Field Column="STORAGE_DETAIL_ID" DbType="String" Header="库存明细ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_LIST_ID" DbType="string" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="BOX_BARCODE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_BARCODE" DbType="string" Header="防伪码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="STORAGE_DETAIL_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="IO_CONTROL_APPLY">
    <Field Column="CONTROL_APPLY_ID" DbType="Int32" Header="申请ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_APPLY_TYPE" DbType="String" Header="申请类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CONTROL_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="申请设备" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_CODE" DbType="String" Header="仓库编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TASK_STATUS" DbType="Int32" Header="申请状态" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="CREATE_TIME" DbType="String" Header="创建时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_APPLY_PARAMETER" DbType="String" Header="异常申请码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_ERROR_TEXT" DbType="String" Header="调度错误信息" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_ERROR_TEXT" DbType="String" Header="管理错误信息" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_APPLY_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="IO_CONTROL_APPLY_HIS">
    <Field Column="CONTROL_APPLY_ID" DbType="Int32" Header="申请ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="CONTROL_ID" DbType="Int32" Header="控制任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />-->
    <Field Column="CONTROL_APPLY_TYPE" DbType="String" Header="申请类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="申请设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="1" QueryOperation="" />
    <Field Column="WAREHOUSE_CODE" DbType="String" Header="仓库编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="1" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="1" QueryOperation="like" />
    <Field Column="APPLY_TASK_STATUS" DbType="Int32" Header="申请状态" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" AllowQuery="1" QueryOperation="" />
    <Field Column="CREATE_TIME" DbType="String" Header="创建时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="HANDLE_TIME" DbType="String" Header="处理时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_APPLY_PARAMETER" DbType="String" Header="重量(KG)" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_APPLY_PARA01" DbType="String" Header="高度编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_APPLY_PARA02" DbType="String" Header="子托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="12" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_ERROR_TEXT" DbType="String" Header="调度错误信息" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_ERROR_TEXT" DbType="String" Header="管理错误信息" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_APPLY_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="IO_CONTROL">
    <Field Column="CONTROL_ID" DbType="Int32" Header="控制任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATIVE_CONTROL_ID" DbType="String" Header="关联ID" ControlType="TextBox" DefaultValue="0" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="MANAGE_ID" DbType="Int32" Header="管理任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="任务条码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="PRE_CONTROL_STATUS" DbType="String" Header="前序状态" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_TASK_TYPE" DbType="String" Header="任务类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_TASK_LEVEL" DbType="String" Header="优先级" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="START_WAREHOUSE_CODE" DbType="String" Header="起始仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select WAREHOUSE_CODE as value,WAREHOUSE_NAME as name from WH_WAREHOUSE" Remark="" Order="200" AllowQuery="1" QueryOperation="" />
    <Field Column="START_DEVICE_CODE" DbType="String" Header="起始位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="210" AllowQuery="1" QueryOperation="" />
    <Field Column="END_WAREHOUSE_CODE" DbType="String" Header="终止仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select WAREHOUSE_CODE as value,WAREHOUSE_NAME as name from WH_WAREHOUSE" Remark="" Order="220" AllowQuery="1" QueryOperation="" />
    <Field Column="END_DEVICE_CODE" DbType="String" Header="终止位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="230" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CONTROL_STATUS" Remark="" Order="240" AllowQuery="1" QueryOperation="" />
    <Field Column="ERROR_TEXT" DbType="String" Header="故障描述" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_BEGIN_TIME" DbType="String" Header="下达时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="IO_CONTROL_ROUTE">
    <Field Column="CONTROL_ROUTE_ID" DbType="Int32" Header="路径ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_ROUTE_TYPE" DbType="Int32" Header="类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_ROUTE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_ROUTE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="START_DEVICE" DbType="String" Header="开始设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="1" QueryOperation="" />
    <Field Column="END_DEVICE" DbType="String" Header="结束设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <!--<Field Column="FINALL_DEVICE" DbType="String" Header="最终巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />-->
    <Field Column="CONTROL_ROUTE_STATUS" DbType="Int32" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_ROUTE_MANAGE" DbType="Int32" Header="是否启用巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
    <Field Column="CONTROL_ROUTE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="200" AllowQuery="1" QueryOperation="like" />
  </Table>
  <Table Name="APPLY_TYPE">
    <Field Column="APPLY_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_CODE" DbType="String" Header="类型编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_DEVICE" DbType="String" Header="设备" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_CLASS" DbType="String" Header="运行类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="APPLY_TYPE_PROPERTY" DbType="String" Header="属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="FLOW_TYPE">
    <Field Column="FLOW_TYPE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="ComboBox" Remark="11" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="FLOW_NODE">
    <Field Column="FLOW_NODE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_TYPE_ID" DbType="Int32" Header="流程类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select FLOW_TYPE_ID as value , FLOW_TYPE_NAME as name from FLOW_TYPE" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_NODE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_NODE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_NODE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_NODE_ORDER" DbType="String" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="FLOW_NODE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="FLOW_PARA">
    <Field Column="FLOW_PARA_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" />
    <Field Column="FLOW_TYPE_ID" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select FLOW_TYPE_ID as value , FLOW_TYPE_NAME as name from FLOW_TYPE" Remark="" Order="2" />
    <Field Column="FLOW_PARA_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" />
    <Field Column="FLOW_PARA_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" />
    <Field Column="FLOW_PARA_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" />
    <Field Column="FLOW_PARA_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" />
    <Field Column="FLOW_PARA_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="8" />
  </Table>
  <Table Name="FLOW_ACTION">
    <Field Column="FLOW_ACTION_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" />
    <Field Column="FLOW_NODE_ID" DbType="Int32" Header="节点" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select FLOW_NODE_ID as value , FLOW_NODE_NAME as name from FLOW_NODE" Remark="" Order="2" />
    <Field Column="FLOW_ACTION_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" />
    <Field Column="FLOW_ACTION_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" />
    <Field Column="FLOW_ACTION_EVENT" DbType="String" Header="事件" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" />
    <Field Column="FLOW_ACTION_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" />
    <Field Column="FLOW_ACTION_ORDER" DbType="Decimal" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" />
    <Field Column="FLOW_ACTION_IMAGE" DbType="String" Header="显示图标" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" />
    <Field Column="FLOW_ACTION_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="9" />
  </Table>
  <Table Name="GOODS_MAIN">
    <Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="Unique|NoneNull" DataBind="select GOODS_CLASS_ID as value ,GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="115" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="Unique|NoneNull" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="" />
    <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="Unique|NoneNull" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="like" />
    <Field Column="GOODS_UNITS" DbType="String" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="防伪码拣选" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="165" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CONST_PROPERTY3" DbType="String" Header="最后更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="168" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_FLAG" DbType="String" Header="启用标识" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="GOODS_TYPE">
    <Field Column="GOODS_TYPE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="GOODS_CLASS">
    <Field Column="GOODS_CLASS_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_PARENT_ID" DbType="Int32" Header="父类别" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select GOODS_CLASS_ID as value , GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_ID" DbType="Int32" Header="物料类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select GOODS_TYPE_ID as value , GOODS_TYPE_NAME as name from GOODS_TYPE where GOODS_TYPE_REMARK='GOODS_TYPE'" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CLASS_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="GOODS_PROPERTY">
    <Field Column="GOODS_PROPERTY_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select GOODS_CLASS_ID as value , GOODS_CLASS_NAME as name from GOODS_CLASS" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_FLAG" DbType="String" Header="启用标识" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_DATASOURCE" DbType="String" Header="combox数据源" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_FIELD" DbType="string" Header="对应字段名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_FIELDTYPE" DbType="String" Header="控件类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_VALID" DbType="String" Header="校验标识" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="61" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_PROPERTY_KEYFLAG" DbType="String" Header="出库关键字段" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="62" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="双伸分配货位关键字段" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD2" DbType="String" Header="素电入库两幢属性关键字段" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="素电自动检验出库两幢属性关键字段" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD4" DbType="String" Header="平库拣选确认时假锁漂移寻找替代库存属性关键字段" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="WH_AREA">
    <Field Column="AREA_ID" DbType="Int32" Header="库区编号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select WAREHOUSE_ID as value , WAREHOUSE_NAME as name from WH_WAREHOUSE" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_TYPE" DbType="String" Header="类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="AREA_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_GROUP" DbType="String" Header="分组" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="WH_WAREHOUSE">
    <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库编号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_FLAG" DbType="String" Header="标记" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_TYPE" DbType="String" Header="类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="WH_CELL">
    <Field Column="CELL_ID" DbType="Int32" Header="货位ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select WAREHOUSE_ID as value , WAREHOUSE_NAME as name from WH_WAREHOUSE where WAREHOUSE_FLAG='1'" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_ID" DbType="Int32" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as value , AREA_NAME as name from WH_AREA" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="LOGIC_ID" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select LOGIC_ID as value , LOGIC_NAME as name from WH_LOGIC" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="GET_IN_STATION" DbType="String" Header="定位关键字" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="165" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_TYPE" DbType="String" Header="类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_TYPE" Remark="" Order="170" AllowQuery="1" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="设备" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_Z" DbType="Int32" Header="排" ControlType="TextBox" DefaultValue="0" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_X" DbType="Int32" Header="列" ControlType="TextBox" DefaultValue="0" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_Y" DbType="Int32" Header="层" ControlType="TextBox" DefaultValue="0" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_INOUT" DbType="String" Header="出入库" ControlType="ComboBox" DefaultValue="InOut" ReadOnly="1" Validation="" DataBind="CELL_INOUT" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="货位高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="1" QueryOperation="like" />
    <Field Column="CELL_STATUS" DbType="String" Header="货位状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_STATUS" Remark="" Order="240" AllowQuery="1" QueryOperation="" />
    <Field Column="RUN_STATUS" DbType="String" Header="运行状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="RUN_STATUS" Remark="" Order="250" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_FORK_TYPE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_LOGICAL_NAME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="0" ReadOnly="0" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_GROUP" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
    <Field Column="SHELF_TYPE" DbType="String" Header="货架类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
    <Field Column="SHELF_NEIGHBOUR" DbType="String" Header="同组货位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_STORAGE_TYPE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
    <Field Column="LOCK_DEVICE_CODE" DbType="String" Header="锁定标记" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_WIDTH" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_HEIGTH" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="350" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_PROPERTY" DbType="String" Header="站台属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="360" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_FLAG" DbType="String" Header="是否启用" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="370" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="GET_IN_STATION" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="380" AllowQuery="0" QueryOperation="" />-->
    <Field Column="UNIT_STATUS" DbType="String" Header="指示灯状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="390" AllowQuery="0" QueryOperation="" />
    <Field Column="UPDATETIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="400" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_Y_ORDER" DbType="String" Header="拣选货位" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="410" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="V_CELL_THRESHOLD_AREA">
    <Field Column="AREA_ID" DbType="String" Header="区域ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="AREA_CODE" DbType="String" Header="区域编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />-->
    <Field Column="AREA_NAME" DbType="String" Header="区域名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="ENABLE_QTY" DbType="String" Header="可用货位数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="TOTAL_QTY" DbType="String" Header="货位总数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="ENABLE_RATE" DbType="String" Header="可用百分百" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="V_CELL_THRESHOLD_LANEWAY">
    <!--<Field Column="LANE_WAY" DbType="String" Header="巷道ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />-->
    <Field Column="LANE_WAY" DbType="String" Header="巷道名称" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="ENABLE_QTY" DbType="String" Header="可用货位数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="TOTAL_QTY" DbType="String" Header="货位总数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="ENABLE_RATE" DbType="String" Header="可用百分百" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="WH_DESCRIPTION">
    <Field Column="DESCRIPTION_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select WAREHOUSE_ID as value , WAREHOUSE_NAME as name from WH_WAREHOUSE" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_ID" DbType="Int32" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select AREA_ID as value , AREA_NAME as name from WH_AREA" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LOGIC_ID" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select LOGIC_ID as value , LOGIC_NAME as name from WH_LOGIC" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_TYPE" DbType="String" Header="货位类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_TYPE" Remark="" Order="23" AllowQuery="0" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="设备号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_INOUT" DbType="String" Header="入出库类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_INOUT" Remark="" Order="25" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="货位高度" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_MODEL" Remark="" Order="24" AllowQuery="0" QueryOperation="" />
    <Field Column="START_Z" DbType="Int32" Header="起始排" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
    <Field Column="END_Z" DbType="Int32" Header="终止排" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="14" AllowQuery="0" QueryOperation="" />
    <Field Column="START_Y" DbType="Int32" Header="起始层" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="17" AllowQuery="0" QueryOperation="" />
    <Field Column="END_Y" DbType="Int32" Header="终止层" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="18" AllowQuery="0" QueryOperation="" />
    <Field Column="START_X" DbType="Int32" Header="起始列" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="15" AllowQuery="0" QueryOperation="" />
    <Field Column="END_X" DbType="Int32" Header="终止列" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="16" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_LOGICAL_NAME" DbType="String" Header="逻辑排" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="23" AllowQuery="0" QueryOperation="" />
    <Field Column="DEVICE_NAME" DbType="String" Header="设备名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="22" AllowQuery="0" QueryOperation="" />
    <Field Column="SHELF_TYPE" DbType="String" Header="货架类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="SHELF_TYPE" Remark="" Order="26" AllowQuery="0" QueryOperation="" />
    <Field Column="SHELF_NEIGHBOUR" DbType="String" Header="双深相临排" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="27" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_STORAGE_TYPE" DbType="String" Header="货位存储类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_STORAGE_TYPE" Remark="" Order="28" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_FORK_TYPE" DbType="String" Header="货叉类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_FORK_TYPE" Remark="" Order="29" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_FORK_COUNT" DbType="String" Header="货叉数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="29" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_WIDTH" DbType="String" Header="显示宽度" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="29" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_HEIGHT" DbType="String" Header="显示高度" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="29" AllowQuery="0" QueryOperation="" />
    <Field Column="DESCRIPTION_FLAG" DbType="String" Header="启用标识" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="LED_MAIN">
    <Field Column="LED_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_IP" DbType="String" Header="设备IP" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="设备编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="CONTROL_TYPE" DbType="String" Header="控制卡类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="CONTROL_TYPE" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="SCREEN_WIDTH" DbType="String" Header="屏幕宽度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="SCREEN_HEIGHT" DbType="String" Header="屏幕高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="LINE_NUM" DbType="String" Header="显示行数" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="AUTO_FLAG" DbType="String" Header="开启服务" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="FLAG" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_STATUS" DbType="String" Header="发送标识" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="FLAG" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_PARA1" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_PARA2" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_PARA3" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_PARA4" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_MAIN_PARA5" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="LED_LIST">
    <Field Column="LED_LIST_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="LINE_NO" DbType="String" Header="行号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_X" DbType="String" Header="区域横坐标" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_Y" DbType="String" Header="区域纵坐标" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_WIDTH" DbType="String" Header="区域宽度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="AREA_HEIGHT" DbType="String" Header="区域高度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="FILE_NAME" DbType="String" Header="文件名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="LINE_TEXT" DbType="String" Header="发送内容" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="FONT_SIZE" DbType="String" Header="字体" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="SHOW_STUNT" DbType="String" Header="显示特技" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="SHOW_STUNT" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="RUN_SPEED" DbType="String" Header="运行速度" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="SHOW_TIME" DbType="String" Header="停留时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="NoneNull" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_PARA1" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_PARA2" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_PARA3" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_PARA4" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="LED_LIST_PARA5" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="FIELD_DESCRIPTION">
    <Field Column="Column" DbType="String" Header="列名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="DbType" DbType="String" Header="数据类型" ControlType="ElementComBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="String|Int32|Int64|Decimal|Boolean" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="Header" DbType="String" Header="列描述" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="ControlType" DbType="String" Header="控件类型" ControlType="ElementCombox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="TextBox|ComboBox|CheckBox|ElementCombox|DateTimePicker" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="DefaultValue" DbType="String" Header="默认值" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="ReadOnly" DbType="Int32" Header="是否只读" ControlType="CheckBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="Validation" DbType="String" Header="校验规则" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="DataBind" DbType="String" Header="关键字" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
    <Field Column="Remark" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    <Field Column="Order" DbType="Int32" Header="序号" ControlType="TextBox" DefaultValue="1" ReadOnly="0" Validation="0" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="AllowQuery" DbType="Int32" Header="可查询" ControlType="CheckBox" DefaultValue="0" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="QueryOperation" DbType="数据类型" Header="查询符号" ControlType="控件类型" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_ITEM">
    <Field Column="ITEM_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_PARENT_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="ITEM_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_ITEM_LIST">
    <Field Column="ITEM_LIST_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" />
    <Field Column="ITEM_ID" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select ITEM_ID as value , ITEM_NAME as name from SYS_ITEM" Remark="" Order="2" />
    <Field Column="ITEM_LIST_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" />
    <Field Column="ITEM_LIST_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" />
    <Field Column="ITEM_LIST_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" />
    <Field Column="ITEM_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" />
    <Field Column="ITEM_LIST_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" />
  </Table>
  <Table Name="SYS_RELATION_LIST">
    <Field Column="RELATION_LIST_ID" DbType="Int32" Header="" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_ID" DbType="Int32" Header="关系编号" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_LIST_FLAG" DbType="Int32" Header="标识" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_ID1" DbType="Int32" Header="关联编号1值" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_ID2" DbType="Int32" Header="关联编号2值" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_LIST_REMARK" DbType="String" Header="备注" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_RELATION">
    <Field Column="RELATION_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_ID1" DbType="Int32" Header="关联列1" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_ID2" DbType="Int32" Header="关联列2" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATION_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATON_NAME1" DbType="String" Header="关联列1名称列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="DisplayMember" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="RELATON_NAME2" DbType="String" Header="关联列2名称列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="DisplayMember" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_USER">
    <Field Column="USER_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="USER_CODE" DbType="String" Header="登录名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="Unique|NoneNull||MaxLength=10" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="USER_NAME" DbType="String" Header="用户名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="NoneNull||MaxLength=10" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <!--默认密码为【123456】MD5加密后取前16位-->
    <Field Column="USER_PASSWORD" DbType="String" Header="" ControlType="TextBox" DefaultValue="e10adc3949ba59ab" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="USER_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="USER_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="USER_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_TABLE_CONVERTER">
    <Field Column="TABLE_CONVERTER_ID" DbType="Decimal" Header="" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="TABLE_CONVERTER_CODE" DbType="String" Header="转换编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="TABLE_CONVERTER_NAME" DbType="String" Header="转换名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="PARENT_TABLE" DbType="String" Header="父级表名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="PARENT_KEY" DbType="String" Header="父级表主键名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="CHILD_TABLE" DbType="String" Header="子级表名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="CHILD_FOREIGN_KEY" DbType="String" Header="子级表外键名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_TYPE_ID" DbType="String" Header="物料类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_TABLE_CONVERTER_LIST">
    <Field Column="TABLE_CONVERTER_LIST_ID" DbType="String" Header="" ControlType="" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="TABLE_CONVERTER_ID" DbType="Int32" Header="" ControlType="text" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="TABLE_NAME" DbType="String" Header="表名" ControlType="" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="COLUMN_NAME" DbType="String" Header="字段名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="CONVERT_COLUMN_NAME" DbType="String" Header="导入字段名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="UNIQUE_FLAG" DbType="String" Header="唯一标识" ControlType="ComboBox" DefaultValue="0" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="ISNULL_FLAG" DbType="String" Header="允许空标识" ControlType="ComboBox" DefaultValue="1" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_ROLE">
    <Field Column="ROLE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_START_MENU_ID" DbType="Int32" Header="起始页面" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select MENU_ID as value,MENU_NAME as name from SYS_MENU where MENU_PARAMETER IS NOT NULL and MENU_SELECTEDFLAG =1 order by MENU_NAME " Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="ROLE_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_MENU">
    <Field Column="MENU_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_PARENT_ID" DbType="Int32" Header="父菜单" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select MENU_ID as value , MENU_NAME as name from SYS_MENU where menu_parameter is null order by menu_name" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_CLASS" DbType="String" Header="窗体类" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_PARAMETER" DbType="String" Header="参数" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_PARAMETER_SL" DbType="String" Header="SilverLight参数" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_SYSFLAG" DbType="String" Header="系统菜单" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_CHILDNODEFLAG" DbType="String" Header="可运行标识" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_IMAGE" DbType="String" Header="图片" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_DEVELOPFLAG" DbType="String" Header="开发标识" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="9" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_GROUP" DbType="String" Header="菜单分组" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="MENU_SELECTEDFLAG" DbType="String" Header="启用标识" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_LOG">
    <Field Column="LOG_ID" DbType="Int32" Header="日志ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_THREAD" DbType="String" Header="日志线程" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="SELECT LOG_THREAD as value,LOG_THREAD as name from SYS_LOG group by LOG_THREAD" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_LEVEL" DbType="String" Header="日志等级" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LOG_LEVEL" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_LOGGER" DbType="String" Header="生产者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_DATE" DbType="String" Header="日志日期" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_MESSAGE" DbType="String" Header="日志信息" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_LOG1">
    <Field Column="LOG_ID" DbType="Int32" Header="日志ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_DATE" DbType="String" Header="日期" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_THREAD" DbType="String" Header="线程" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_LEVEL" DbType="String" Header="等级" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LOG_LEVEL" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_LOGGER" DbType="String" Header="记录者" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LOG_LOGGER" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_TYPE" DbType="String" Header="类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LOG_TYPE" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_OPERATOR" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_CLASS" DbType="String" Header="类名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="192" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_METHOD" DbType="String" Header="方法名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="196" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_MESSAGE" DbType="String" Header="信息" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
    <Field Column="LOG_EXCEPTION" DbType="String" Header="异常" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="SYS_PARAMETER">
    <Field Column="PARAMETER_ID" DbType="Int32" Header="ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="PARAMETER_KEY" DbType="Int32" Header="KEY" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="PARAMETER_VALUE" DbType="String" Header="VALUE" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />-->
    <Field Column="PARAMETER_TYPE" DbType="String" Header="类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="SYSTEM_PARAM_TYPE" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="PARAMETER_DESCRIBE" DbType="String" Header="描述" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="145" AllowQuery="0" QueryOperation="" />
    <Field Column="PARAMETER_ORDER" DbType="String" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="PARAMETER_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="V_CELL_GOODS_CONFIG">
    <Field Column="AREA_ID" DbType="String" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value,AREA_NAME as name from WH_AREA where AREA_CODE='Carton' or AREA_CODE='Bulk'" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="DEVICE_CODE" DbType="String" Header="设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_Z" DbType="Int32" Header="列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_MODEL" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_STATUS" DbType="String" Header="货位状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_STATUS" Remark="" Order="240" AllowQuery="1" QueryOperation="" />
    <Field Column="RUN_STATUS" DbType="String" Header="运行状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="RUN_STATUS" Remark="" Order="250" AllowQuery="1" QueryOperation="" />
    <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
    <Field Column="CONFIG_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
    <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD2" DbType="String" Header="补货阈值(只)" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="最大库存量(只)" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="错误信息" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="CONFIG_USER" DbType="String" Header="配置人" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="350" AllowQuery="0" QueryOperation="" />-->
    <!--<Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="360" AllowQuery="0" QueryOperation="" />-->
    <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="370" AllowQuery="0" QueryOperation="" />
  </Table>
  <!--<Table Name="PROJECT_MAIN">
    <Field Column="PROJECT_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    <Field Column="PROJECT_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
    <Field Column="PROJECT_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
    <Field Column="PROJECT_QKCODE" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
    <Field Column="PROJECT_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    <Field Column="PROJECT_AREA" DbType="String" Header="域" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
  </Table>-->
  <Table Name="CELL_PLAN_CONFIG">
    <Field Column="CONFIG_ID" DbType="String" Header="配置ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="CELL_ID" DbType="String" Header="货位ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="1" QueryOperation="" />
    <Field Column="CONFIG_TYPE" DbType="String" Header="类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
    <Field Column="CONFIG_GROUP" DbType="Int32" Header="编组" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    <Field Column="CONFIG_VALUE" DbType="String" Header="拣配单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="CONFIG_USER" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="1" QueryOperation="" />
    <Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="1" QueryOperation="" />
    <Field Column="CONFIG_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
    <Field Column="CONFIG_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD2" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="INTERFACE_QUEUE">
    <Field Column="QUEUE_ID" DbType="Int32" Header="队列ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="TARGET_SYSTEM" DbType="String" Header="目标系统D" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
    <Field Column="INTERFACE_NAME" DbType="String" Header="接口名" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <!--<Field Column="INTERFACE_TYPE" DbType="String" Header="接口类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="INVOKE_TYPE" DbType="String" Header="调用类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />-->
    <Field Column="PLAN_ID" DbType="Int32" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="PLAN_CODE" DbType="String" Header="计划编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="1" QueryOperation="" />
    <Field Column="MANAGE_ID" DbType="Int32" Header="任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="1" QueryOperation="" />
    <Field Column="PARAM_IN" DbType="String" Header="入参" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="200" AllowQuery="1" QueryOperation="like" />
    <Field Column="PARAM_OUT" DbType="String" Header="出参" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="1" QueryOperation="like" />
    <Field Column="WRITE_DATETIME" DbType="String" Header="记录写入时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
    <Field Column="HANDLE_DATETIME" DbType="String" Header="处理时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
    <Field Column="HANDLE_FLAG" DbType="Int32" Header="处理标记" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="240" AllowQuery="1" QueryOperation="" />
    <Field Column="ERROR_DESCRIBE" DbType="String" Header="错误描述" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="1" QueryOperation="like" />
    <Field Column="QUEUE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD1" DbType="String" Header="备用字段1" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD2" DbType="String" Header="备用字段2" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD3" DbType="String" Header="备用字段3" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD4" DbType="String" Header="备用字段4" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
    <Field Column="BACKUP_FIELD5" DbType="String" Header="备用字段5" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="PALLET_STACK">
    <Field Column="STACK_ID" DbType="Int32" Header="ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="CELL_CODE" DbType="String" Header="站台编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="" />
    <Field Column="PALLET_NO" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="PALLET_ORDER" DbType="Int32" Header="排序" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="INSERT_DATE" DbType="String" Header="写入时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <!--<Field Column="BACKUP_FIELD1" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />-->
    <!--<Field Column="BACKUP_FIELD2" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="1" QueryOperation="" />-->
    <!--<Field Column="BACKUP_FIELD3" DbType="String" Header="ID" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />-->
    <Field Column="STACK_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
  </Table>
  <Table Name="LCD_MAIN">
    <Field Column="LCD_ID" DbType="Int32" Header="ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
    <Field Column="LCD_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="" />
    <Field Column="LCD_IP" DbType="String" Header="IP地址" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
    <Field Column="LCD_MESSAGE" DbType="Int32" Header="消息内容" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
    <Field Column="LCD_REMARK" DbType="String" Header="位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
    <Field Column="LCD_UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    <Field Column="LCD_READ_FLAGE" DbType="String" Header="读取标记" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="1" QueryOperation="" />
    <Field Column="LCD_STATION" DbType="String" Header="站台描述" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
  </Table>
</Tables>