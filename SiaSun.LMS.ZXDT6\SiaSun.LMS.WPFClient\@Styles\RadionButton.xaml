<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="RadioButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle
                            Margin="15,0,0,0"
                            Stroke="#60000000"
                            StrokeDashArray="1 2"
                            StrokeThickness="1" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type RadioButton}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource RadioButtonFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />

        <Setter Property="Template" Value="{DynamicResource RadioButtonTemplate}" />
    </Style>

    <ControlTemplate x:Key="RadioButtonTemplate" TargetType="{x:Type RadioButton}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation
                    Storyboard.TargetName="CircleOver"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation
                    Storyboard.TargetName="CircleOver"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <DoubleAnimation
                    Storyboard.TargetName="CirclePress"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <DoubleAnimation
                    Storyboard.TargetName="CirclePress"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="DisabledOn" />
            <Storyboard x:Key="CheckedOn">
                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="CheckIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="CheckedOff">
                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="CheckIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.4000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusedVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusedVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <BulletDecorator Background="Transparent">
            <BulletDecorator.Bullet>
                <Grid>
                    <Ellipse
                        x:Name="Background"
                        Width="14"
                        Height="14"
                        Margin="1"
                        Fill="{DynamicResource RadioButtonBackgroundBrush}"
                        Stroke="{DynamicResource RadioButtonBorderBrush}"
                        StrokeThickness="1" />
                    <Ellipse
                        x:Name="CircleFill"
                        Margin="3.045,3.157,2.955,2.843"
                        Fill="{DynamicResource RadioButtonInnerCircleBrush}"
                        Stroke="{DynamicResource RadioButtonInnerCircleBorderBrush}"
                        StrokeThickness="1" />
                    <Ellipse
                        x:Name="CircleOver"
                        Margin="2.847,2.847,3.153,3.153"
                        Fill="{DynamicResource RadioButtonMouseOverBrush}"
                        Opacity="0"
                        Stroke="{DynamicResource RadioButtonMouseOverBorderBrush}"
                        StrokeThickness="1" />
                    <Ellipse
                        x:Name="CirclePress"
                        Margin="2.73,2.73,3.27,3.27"
                        Fill="{DynamicResource RadioButtonPressBrush}"
                        Opacity="0"
                        Stroke="{DynamicResource RadioButtonPressBorderBrush}"
                        StrokeThickness="1" />
                    <Ellipse
                        x:Name="CheckIcon"
                        Margin="4.47,4.498,3.53,3.502"
                        Fill="{DynamicResource RadioButtonCheckIconBrush}"
                        Stroke="{DynamicResource RadioButtonCheckIconBorderBrush}"
                        StrokeThickness="1"
                        Visibility="Collapsed" />
                    <Ellipse
                        x:Name="DisabledVisualElement"
                        Width="14"
                        Height="14"
                        Opacity="0.35"
                        Visibility="Visible">
                        <Ellipse.Stroke>
                            <SolidColorBrush Color="{DynamicResource WhiteColor}" />
                        </Ellipse.Stroke>
                        <Ellipse.Fill>
                            <SolidColorBrush Color="{DynamicResource WhiteColor}" />
                        </Ellipse.Fill>
                    </Ellipse>
                    <Ellipse
                        x:Name="FocusedVisualElement"
                        Width="16"
                        Height="16"
                        IsHitTestVisible="false"
                        Opacity="0"
                        Stroke="{DynamicResource FocusBrush}"
                        StrokeThickness="1" />
                </Grid>
            </BulletDecorator.Bullet>
            <ContentPresenter
                x:Name="contentPresenter"
                Grid.Column="1"
                Margin="{TemplateBinding Padding}"
                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                Content="{TemplateBinding Content}"
                ContentTemplate="{TemplateBinding ContentTemplate}" />
        </BulletDecorator>

        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="FocusedOn_BeginStoryboard" Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsChecked" Value="false">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource CheckedOn}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>

                    <BeginStoryboard Storyboard="{StaticResource CheckedOff}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOn}" />
                </Trigger.EnterActions>
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
            </Trigger>

        </ControlTemplate.Triggers>
    </ControlTemplate>

</ResourceDictionary>