﻿/***************************************************************************
 * 
 *       功能：     任务列表持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// MANAGE_LIST
	/// </summary>
	public class P_MANAGE_LIST : P_Base_House
	{
		public P_MANAGE_LIST ()
		{
			//
			// TODO: 此处添加MANAGE_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<MANAGE_LIST> GetList()
		{
			return ExecuteQueryForList<MANAGE_LIST>("MANAGE_LIST_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_LIST> GetListManageID(int MANAGE_ID)
        {
            return ExecuteQueryForList<MANAGE_LIST>("MANAGE_LIST_SELECT_BY_MANAGE_ID", MANAGE_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_LIST> GetListBySTORAGE_LOCK_ID(int STORAGE_LOCK_ID)
        {
            return ExecuteQueryForList<MANAGE_LIST>("MANAGE_LIST_SELECT_BY_STORAGE_LOCK_ID", STORAGE_LOCK_ID);
        }

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<MANAGE_LIST> GetListByPlanListId(int PLAN_LIST_ID)
		{
			return ExecuteQueryForList<MANAGE_LIST>("MANAGE_LIST_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(MANAGE_LIST manage_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("MANAGE_LIST");
                manage_list.MANAGE_LIST_ID = id;
            }
            return ExecuteInsert("MANAGE_LIST_INSERT",manage_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(MANAGE_LIST manage_list)
		{
			return ExecuteUpdate("MANAGE_LIST_UPDATE",manage_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public MANAGE_LIST GetModel(System.Int32 MANAGE_LIST_ID)
		{
			return ExecuteQueryForObject<MANAGE_LIST>("MANAGE_LIST_SELECT_BY_ID",MANAGE_LIST_ID);
		}


		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public MANAGE_LIST GetModel(string BOX_BARCODE)
		{
			return ExecuteQueryForObject<MANAGE_LIST>("MANAGE_LIST_SELECT_BY_BOX_BARCODE", BOX_BARCODE);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 MANAGE_LIST_ID)
		{
			return ExecuteDelete("MANAGE_LIST_DELETE",MANAGE_LIST_ID);
		}

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int DeleteManageID(System.Int32 MANAGE_ID)
        {
            return ExecuteDelete("MANAGE_LIST_DELETE_MANAGE_ID", MANAGE_ID);
        }
		

	}
}
