﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="INTERFACE_QUEUE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="INTERFACE_QUEUE" type="SiaSun.LMS.Model.INTERFACE_QUEUE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SELECTRESULT" class="INTERFACE_QUEUE">
			<result property="QUEUE_ID" column="queue_id" />
			<result property="TARGET_SYSTEM" column="target_system" />
			<result property="INTERFACE_NAME" column="interface_name" />
			<result property="INTERFACE_TYPE" column="interface_type" />
			<result property="INVOKE_TYPE" column="invoke_type" />
			<result property="PLAN_ID" column="plan_id" />
			<result property="PLAN_CODE" column="plan_code" />
			<result property="MANAGE_ID" column="manage_id" />
			<result property="STOCK_BARCODE" column="stock_barcode" />
			<result property="PARAM_IN" column="param_in" />
			<result property="PARAM_OUT" column="param_out" />
			<result property="WRITE_DATETIME" column="write_datetime" />
			<result property="HANDLE_DATETIME" column="handle_datetime" />
			<result property="HANDLE_FLAG" column="handle_flag" />
			<result property="ERROR_DESCRIBE" column="error_describe" />
			<result property="QUEUE_REMARK" column="queue_remark" />
			<result property="BACKUP_FILED1" column="backup_filed1" />
			<result property="BACKUP_FILED2" column="backup_filed2" />
			<result property="BACKUP_FILED3" column="backup_filed3" />
			<result property="BACKUP_FILED4" column="backup_filed4" />
			<result property="BACKUP_FILED5" column="backup_filed5" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="INTERFACE_QUEUE_SELECT" parameterClass="int" resultMap="SELECTRESULT">
			Select
			queue_id,
			target_system,
			interface_name,
			interface_type,
			invoke_type,
			plan_id,
			plan_code,
			manage_id,
			stock_barcode,
			param_in,
			param_out,
			write_datetime,
			handle_datetime,
			handle_flag,
			error_describe,
			queue_remark,
			backup_filed1,
			backup_filed2,
			backup_filed3,
			backup_filed4,
			backup_filed5
			From INTERFACE_QUEUE
		</select>

		<select id="INTERFACE_QUEUE_SELECT_BY_ID" parameterClass="int" extends = "INTERFACE_QUEUE_SELECT" resultMap="SELECTRESULT">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					queue_id=#QUEUE_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="INTERFACE_QUEUE_SELECT_BY_STATUS" parameterClass="int" extends = "INTERFACE_QUEUE_SELECT" resultMap="SELECTRESULT">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					handle_flag=#invokeStatus#
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="INTERFACE_QUEUE_INSERT" parameterClass="INTERFACE_QUEUE">
			Insert Into INTERFACE_QUEUE (
			<!--queue_id,-->
			target_system,
			interface_name,
			interface_type,
			invoke_type,
			plan_id,
			plan_code,
			manage_id,
			stock_barcode,
			param_in,
			param_out,
			write_datetime,
			handle_datetime,
			handle_flag,
			error_describe,
			queue_remark,
			backup_filed1,
			backup_filed2,
			backup_filed3,
			backup_filed4,
			backup_filed5
			)Values(
			<!--#QUEUE_ID#,-->
			#TARGET_SYSTEM#,
			#INTERFACE_NAME#,
			#INTERFACE_TYPE#,
			#INVOKE_TYPE#,
			#PLAN_ID#,
			#PLAN_CODE#,
			#MANAGE_ID#,
			#STOCK_BARCODE#,
			#PARAM_IN#,
			#PARAM_OUT#,
			#WRITE_DATETIME#,
			#HANDLE_DATETIME#,
			#HANDLE_FLAG#,
			#ERROR_DESCRIBE#,
			#QUEUE_REMARK#,
			#BACKUP_FILED1#,
			#BACKUP_FILED2#,
			#BACKUP_FILED3#,
			#BACKUP_FILED4#,
			#BACKUP_FILED5#
			)
			<selectKey  resultClass="int" type="post" property="QUEUE_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="INTERFACE_QUEUE_UPDATE" parameterClass="INTERFACE_QUEUE">
			Update INTERFACE_QUEUE Set
			target_system=#TARGET_SYSTEM#,
			interface_name=#INTERFACE_NAME#,
			interface_type=#INTERFACE_TYPE#,
			invoke_type=#INVOKE_TYPE#,
			plan_id=#PLAN_ID#,
			plan_code=#PLAN_CODE#,
			manage_id=#MANAGE_ID#,
			stock_barcode=#STOCK_BARCODE#,
			param_in=#PARAM_IN#,
			param_out=#PARAM_OUT#,
			write_datetime=#WRITE_DATETIME#,
			handle_datetime=#HANDLE_DATETIME#,
			handle_flag=#HANDLE_FLAG#,
			error_describe=#ERROR_DESCRIBE#,
			queue_remark=#QUEUE_REMARK#,
			backup_filed1=#BACKUP_FILED1#,
			backup_filed2=#BACKUP_FILED2#,
			backup_filed3=#BACKUP_FILED3#,
			backup_filed4=#BACKUP_FILED4#,
			backup_filed5=#BACKUP_FILED5#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					queue_id=#QUEUE_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="INTERFACE_QUEUE_DELETE" parameterClass="int">
			Delete From INTERFACE_QUEUE
			<dynamic prepend="WHERE">
				<isParameterPresent>
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>