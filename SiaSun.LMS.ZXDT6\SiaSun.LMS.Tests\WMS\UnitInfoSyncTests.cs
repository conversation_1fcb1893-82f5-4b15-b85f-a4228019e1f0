using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement.Interface.WMS;
using System;
using System.Text.Json;

namespace SiaSun.LMS.Tests.WMS
{
    [TestClass]
    public class UnitInfoSyncTests
    {
        private UnitInfoSync _unitInfoSync;

        [TestInitialize]
        public void Setup()
        {
            _unitInfoSync = new UnitInfoSync();
        }

        [TestMethod]
        public void IntefaceMethod_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var validInput = new
            {
                measuringUnitStatus = "ACTIVE",
                measuringUnitCode = "MU001",
                code = "UNIT001",
                parentId = "PARENT001",
                name = "测试单位",
                ignoreParent = false,
                createDate = "2025-01-08 10:00:00",
                createUser = "USER001",
                createName = "测试用户",
                updateDate = "2025-01-08 10:00:00",
                updateUser = "USER001",
                updateName = "测试用户",
                id = "ID001",
                status = 1,
                billCode = "BILL001"
            };

            string inputJson = JsonSerializer.Serialize(validInput);

            // Act
            string result = _unitInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Length > 0);
            Assert.IsTrue(response.GetProperty("traceId").GetString().Length > 0);
        }

        [TestMethod]
        public void IntefaceMethod_InvalidJson_ReturnsError()
        {
            // Arrange
            string invalidJson = "{ invalid json }";

            // Act
            string result = _unitInfoSync.IntefaceMethod(invalidJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("解析错误"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Code_ReturnsError()
        {
            // Arrange
            var inputWithMissingCode = new
            {
                measuringUnitStatus = "ACTIVE",
                measuringUnitCode = "MU001",
                // code is missing
                name = "测试单位"
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingCode);

            // Act
            string result = _unitInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("code"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Name_ReturnsError()
        {
            // Arrange
            var inputWithMissingName = new
            {
                measuringUnitStatus = "ACTIVE",
                measuringUnitCode = "MU001",
                code = "UNIT001"
                // name is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingName);

            // Act
            string result = _unitInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("name"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_MeasuringUnitCode_ReturnsError()
        {
            // Arrange
            var inputWithMissingMeasuringUnitCode = new
            {
                measuringUnitStatus = "ACTIVE",
                // measuringUnitCode is missing
                code = "UNIT001",
                name = "测试单位"
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingMeasuringUnitCode);

            // Act
            string result = _unitInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("measuringUnitCode"));
        }

        [TestMethod]
        public void IntefaceMethod_EmptyStringFields_ReturnsError()
        {
            // Arrange
            var inputWithEmptyFields = new
            {
                measuringUnitStatus = "ACTIVE",
                measuringUnitCode = "",
                code = "UNIT001",
                name = "测试单位"
            };

            string inputJson = JsonSerializer.Serialize(inputWithEmptyFields);

            // Act
            string result = _unitInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("measuringUnitCode"));
        }

        [TestMethod]
        public void IntefaceMethod_NullInput_ReturnsError()
        {
            // Act
            string result = _unitInfoSync.IntefaceMethod(null);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("解析错误"));
        }

        [TestMethod]
        public void IntefaceMethod_EmptyInput_ReturnsError()
        {
            // Act
            string result = _unitInfoSync.IntefaceMethod("");

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("解析错误"));
        }
    }
}