﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using System.Reflection;
using System.Xml;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// FIELD_DESCRIPTION.xaml 的交互逻辑
    /// </summary>
    public partial class FIELD_DESCRIPTION : AvalonDock.DocumentContent
    {
        DataTable tableDescription = null;
        public FIELD_DESCRIPTION()
        {
            InitializeComponent();
            this.Loaded += new RoutedEventHandler(FIELD_DESCRIPTION_Loaded);
            this.listBoxModel.SelectionChanged += new SelectionChangedEventHandler(listBoxModel_SelectionChanged);
        }

        /// <summary>
        /// 窗体加载
        /// </summary>
        void FIELD_DESCRIPTION_Loaded(object sender, RoutedEventArgs e)
        {
            //加载所有的Models
            LoadModels();

            //初始化DataTable
            InitDataTable();
        }

        //加载Model
        private void LoadModels()
        {
            try
            {
                //加载所有实体类中的节点
                Assembly assemblyModel = Assembly.LoadFrom(MainApp._APP_PATH + "SiaSun.LMS.Model.dll");
                if (assemblyModel == null)
                    return;
                Module moduleModel = assemblyModel.ManifestModule;
                Type[] arTypeModel = moduleModel.GetTypes();
                var query = from typeModel in arTypeModel where typeModel.IsClass == true orderby typeModel.Name select typeModel;
                //添加节点
                listBoxModel.Items.Clear();
                List<string> listModelName = new List<string>();
                foreach (Type type in query)
                {
                    ListBoxItem item = new ListBoxItem();
                    item.Content = type.Name;
                    item.Tag = item.Content;
                    this.listBoxModel.Items.Add(item);

                    //记录节点列表
                    listModelName.Add(type.Name);
                }

                //加载所有已经存在的节点
                SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);
                XmlNodeList nodeListModel = xmlFile.SelectNodes("Tables/Table");
                foreach (XmlNode xmlNode in nodeListModel)
                {
                    //判断是否空值
                    if (xmlNode.Attributes.Count == 0)
                    {
                        xmlNode.RemoveAll();
                        continue;
                    }
                    else
                    {
                        //判断是否已经存在该节点
                        if (!listModelName.Contains(xmlNode.Attributes["Name"].Value))
                        {
                            ListBoxItem item = new ListBoxItem();
                            item.Content = xmlNode.Attributes["Name"].Value;
                            item.Tag = item.Content;
                            this.listBoxModel.Items.Add(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 选项更改
        /// </summary>
        void listBoxModel_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.listBoxModel.SelectedItem == null)
                return;

            ListBoxItem item = this.listBoxModel.SelectedItem as ListBoxItem;

            //显示节点
            this.grpboxConfig.Header = string.Format("{0}-配置项", item.Content);

            //加载数据
            LoadData(item.Content.ToString());
        }

        //初始化DataTable
        private DataTable InitDataTable()
        {
            try
            {
                //从实体类中加载属性信息
                Assembly assemblyModel = Assembly.LoadFrom(MainApp._APP_PATH + "SiaSun.LMS.Model.dll");
                if (assemblyModel == null)
                    return null;
                Module moduleModel = assemblyModel.ManifestModule;
                Type typeModel = moduleModel.GetType(assemblyModel.GetName().Name + ".FIELD_DESCRIPTION");
                if (typeModel == null)
                    return null;

                //声明datatable并添加列
                tableDescription = new DataTable("FIELD_DESCRIPTION");
                SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION = new SiaSun.LMS.Model.FIELD_DESCRIPTION();
                foreach (PropertyInfo propertyInfo in typeModel.GetProperties())
                {
                    DataColumn column = new DataColumn(propertyInfo.Name, propertyInfo.PropertyType);
                    column.DefaultValue = propertyInfo.GetValue(mFIELD_DESCRIPTION, null).ToString();
                    tableDescription.Columns.Add(column);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            return tableDescription;
        }

        //点击节点，加载并显示数据
        private void LoadData(string TableName)
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);
                XmlNode nodeModel = xmlFile.SelectSingleNode("Tables/Table[@Name='" + TableName + "']");
                //判断XML中是否有该节点，如果没有则根据实体类加载
                if (nodeModel == null)
                {
                    //如果XML中没有定义该节点，则从类中加载该描述
                    LoadDataTableFromModel(TableName);
                }
                else
                {
                    LoadDataTableFromXml(nodeModel, TableName);
                }
                //数据绑定
                DataGrid_Bind(TableName);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        //重新加载数据
        private void LoadDataAgain(string TableName)
        {
            //记载XML信息
            SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);
            XmlNode nodeModel = xmlFile.SelectSingleNode("Tables/Table[@Name='" + TableName + "']");

            //从XML中加载描述定义
            tableDescription.Rows.Clear();
            try
            {
                Assembly assemblyModel = Assembly.LoadFrom(MainApp._APP_PATH + "SiaSun.LMS.Model.dll");
                if (assemblyModel == null)
                    return;
                Module moduleModel = assemblyModel.ManifestModule;
                Type typeModel = moduleModel.GetType(assemblyModel.GetName().Name + "." + TableName);
                if (typeModel == null)
                    return;
                //获得所有实体类的属性
                PropertyInfo[] arProperty = typeModel.GetProperties();
                //检查属性和XML中的列是否匹配，检查是否存在新增列
                foreach (PropertyInfo propertyInfo in arProperty)
                {
                    //添加新行
                    DataRow rowField = tableDescription.NewRow();
                    //判断XMl中是否有该列信息
                    XmlNode nodeFile = nodeModel.SelectSingleNode(string.Format("Field[@Column='{0}']", propertyInfo.Name));
                    if (nodeFile == null)
                    {
                        rowField["Column"] = propertyInfo.Name;
                        rowField["DbType"] = propertyInfo.PropertyType.Name;
                        rowField["Header"] = propertyInfo.Name;
                        rowField["ControlType"] = "text";
                        rowField["DefaultValue"] = "";
                        rowField["ReadOnly"] = false;
                        rowField["Validation"] = string.Empty;
                        rowField["DataBind"] = string.Empty;
                        rowField["Remark"] = string.Empty;
                        rowField["Order"] = 1;
                        rowField["AllowQuery"] = false;
                        rowField["QueryOperation"] = string.Empty;
                    }
                    else
                    {
                        rowField["Column"] = nodeFile.Attributes["Column"] == null ? string.Empty : nodeFile.Attributes["Column"].Value;
                        rowField["DbType"] = nodeFile.Attributes["DbType"] == null ? string.Empty : nodeFile.Attributes["DbType"].Value;
                        rowField["Header"] = nodeFile.Attributes["Header"] == null ? string.Empty : nodeFile.Attributes["Header"].Value;
                        rowField["ControlType"] = nodeFile.Attributes["ControlType"] == null ? string.Empty : nodeFile.Attributes["ControlType"].Value;
                        rowField["DefaultValue"] = nodeFile.Attributes["DefaultValue"] == null ? string.Empty : nodeFile.Attributes["DefaultValue"].Value;
                        rowField["ReadOnly"] = nodeFile.Attributes["ReadOnly"] == null ? false : Convert.ToBoolean(int.Parse(nodeFile.Attributes["ReadOnly"].Value));
                        rowField["Validation"] = nodeFile.Attributes["Validation"] == null ? string.Empty : nodeFile.Attributes["Validation"].Value;
                        rowField["DataBind"] = nodeFile.Attributes["DataBind"] == null ? string.Empty : nodeFile.Attributes["DataBind"].Value;
                        rowField["Remark"] = nodeFile.Attributes["Remark"] == null ? string.Empty : nodeFile.Attributes["Remark"].Value;
                        rowField["Order"] = nodeFile.Attributes["Order"] == null ? "1" : nodeFile.Attributes["Order"].Value;
                        rowField["AllowQuery"] = nodeFile.Attributes["AllowQuery"] == null ? false : Convert.ToBoolean(int.Parse(nodeFile.Attributes["AllowQuery"].Value));
                        rowField["QueryOperation"] = nodeFile.Attributes["QueryOperation"] == null ? string.Empty : nodeFile.Attributes["QueryOperation"].Value;

                    }
                    tableDescription.Rows.Add(rowField);
                }
            }
            catch (Exception ex)
            {
               MainApp._MessageDialog.ShowException(ex);
            }
        }

        //绑定数据并设置显示列信息
        private void DataGrid_Bind(string TableName)
        {
            //设置数据源
            this.gridModel.ItemsSource = tableDescription.DefaultView;
            //设置样式
            this.gridModel.U_TranslateDataGridViewStyle(null, "FIELD_DESCRIPTION", null, true);
        }

        //从XML中加载数据
        private void LoadDataTableFromXml(XmlNode nodeModel, string TableName)
        {
            //从XML中加载描述定义
            tableDescription.Rows.Clear();

            foreach (XmlNode nodeFile in nodeModel.SelectNodes("Field"))
            {
                try
                {
                    DataRow rowField = tableDescription.NewRow();
                    rowField["Column"] = nodeFile.Attributes["Column"] == null ? string.Empty : nodeFile.Attributes["Column"].Value;
                    rowField["DbType"] = nodeFile.Attributes["DbType"] == null ? string.Empty : nodeFile.Attributes["DbType"].Value;
                    rowField["Header"] = nodeFile.Attributes["Header"] == null ? string.Empty : nodeFile.Attributes["Header"].Value;
                    rowField["ControlType"] = nodeFile.Attributes["ControlType"] == null ? string.Empty : nodeFile.Attributes["ControlType"].Value;
                    rowField["DefaultValue"] = nodeFile.Attributes["DefaultValue"] == null ? string.Empty : nodeFile.Attributes["DefaultValue"].Value;
                    rowField["ReadOnly"] = nodeFile.Attributes["ReadOnly"] == null ? false : Convert.ToBoolean(int.Parse(nodeFile.Attributes["ReadOnly"].Value));
                    rowField["Validation"] = nodeFile.Attributes["Validation"] == null ? string.Empty : nodeFile.Attributes["Validation"].Value;
                    rowField["DataBind"] = nodeFile.Attributes["DataBind"] == null ? string.Empty : nodeFile.Attributes["DataBind"].Value;
                    rowField["Remark"] = nodeFile.Attributes["Remark"] == null ? string.Empty : nodeFile.Attributes["Remark"].Value;
                    rowField["Order"] = nodeFile.Attributes["Order"] == null ? "1" : nodeFile.Attributes["Order"].Value;
                    rowField["AllowQuery"] = nodeFile.Attributes["AllowQuery"] == null ? false : Convert.ToBoolean(int.Parse(nodeFile.Attributes["AllowQuery"].Value));
                    rowField["QueryOperation"] = nodeFile.Attributes["QueryOperation"] == null ? string.Empty : nodeFile.Attributes["QueryOperation"].Value;

                    tableDescription.Rows.Add(rowField);
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(string.Format("节点：{0}.", nodeFile.Attributes["Column"].Value) + ex.Message);
                }
            }
        }

        //根据Model类生成描述信息
        private void LoadDataTableFromModel(string TableName)
        {
            Assembly assemblyModel = Assembly.LoadFrom(MainApp._APP_PATH + "SiaSun.LMS.Model.dll");
            if (assemblyModel == null)
                return;
            Module moduleModel = assemblyModel.ManifestModule;
            Type typeModel = moduleModel.GetType(assemblyModel.GetName().Name + "." + TableName);
            if (typeModel == null)
                return;
            tableDescription.Rows.Clear();
            try
            {
                //通过反射将该实体类的所有属性加载过来
                int i = 1;
                foreach (PropertyInfo propertyInfo in typeModel.GetProperties())
                {
                    //获得属性描述
                    DataRow rowField = tableDescription.NewRow();
                    rowField["Column"] = propertyInfo.Name;
                    rowField["DbType"] = propertyInfo.PropertyType.Name;
                    rowField["Header"] = propertyInfo.Name;
                    rowField["ControlType"] = "text";
                    rowField["DefaultValue"] = "";
                    rowField["ReadOnly"] = false;
                    rowField["Validation"] = string.Empty;
                    rowField["DataBind"] = string.Empty;
                    rowField["Remark"] = string.Empty;
                    rowField["Order"] = i;
                    rowField["AllowQuery"] = false;
                    rowField["QueryOperation"] = string.Empty;
                    tableDescription.Rows.Add(rowField);
                    i++;
                }
                //save xml
                UpdateXml(TableName);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //从数据库中获得该信息
        private void LoadDataTableFromDataBase(XmlNode nodeModel, string TableName)
        {
            try
            {
                //从XML中加载描述定义
                tableDescription.Rows.Clear();

                //获得XML文件中所有数据
                XmlNodeList xmlNodeList = nodeModel.SelectNodes("Field");
                List<XmlNode> listNodeModel = new List<XmlNode>();
                Dictionary<string, XmlNode> dicXmlNode = new Dictionary<string, XmlNode>();
                foreach (XmlNode xmlNode in xmlNodeList)
                {
                    if (xmlNode.Attributes.Count > 0)
                    {
                        dicXmlNode.Add(xmlNode.Attributes["Column"].Value, xmlNode);
                    }
                }

                string strSql = string.Format("SELECT * FROM {0} WHERE 0=1", TableName);
                DataTable tableModel = MainApp.I_DatabaseService.GetList(strSql);
                foreach (DataColumn col in tableModel.Columns)
                {
                    //判断是否已经存在
                    bool blIsExist = dicXmlNode.ContainsKey(col.ColumnName);

                    DataRow rowField = tableDescription.NewRow();
                    rowField["Column"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["Column"] == null) ? col.ColumnName : dicXmlNode[col.ColumnName].Attributes["Column"].Value;
                    rowField["DbType"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["DbType"] == null) ? "String" : dicXmlNode[col.ColumnName].Attributes["DbType"].Value;
                    rowField["Header"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["Header"] == null) ? col.ColumnName : dicXmlNode[col.ColumnName].Attributes["Header"].Value;
                    rowField["ControlType"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["ControlType"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["ControlType"].Value;
                    rowField["DefaultValue"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["DefaultValue"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["DefaultValue"].Value;
                    rowField["ReadOnly"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["ReadOnly"] == null) ? false : Convert.ToBoolean(int.Parse(dicXmlNode[col.ColumnName].Attributes["ReadOnly"].Value));
                    rowField["Validation"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["Validation"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["Validation"].Value;
                    rowField["DataBind"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["DataBind"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["DataBind"].Value;
                    rowField["Remark"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["Remark"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["Remark"].Value;
                    rowField["Order"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["Order"] == null) ? "1" : dicXmlNode[col.ColumnName].Attributes["Order"].Value;
                    rowField["AllowQuery"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["AllowQuery"] == null) ? false : Convert.ToBoolean(int.Parse(dicXmlNode[col.ColumnName].Attributes["AllowQuery"].Value));
                    rowField["QueryOperation"] = (!blIsExist || dicXmlNode[col.ColumnName].Attributes["QueryOperation"] == null) ? string.Empty : dicXmlNode[col.ColumnName].Attributes["QueryOperation"].Value;
                    tableDescription.Rows.Add(rowField);
                }
            }
            catch (Exception ex)
            {
               MainApp._MessageDialog.ShowException(ex);
            }
        }

        //Update XML
        private void UpdateXml(string TableName)
        {
            if (TableName.Length == 0)
                return;

            SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);
            XmlNode nodeRoot = xmlFile.SelectSingleNode("Tables");
            XmlNode nodeModel = nodeRoot.SelectSingleNode("Table[@Name='" + TableName + "']");

            //移除所有节点
            if (nodeModel != null)
            {
                foreach (XmlNode node in nodeModel.ChildNodes)
                {
                    nodeModel.RemoveChild(node);
                }
                nodeModel.InnerXml = string.Empty;
            }

            if (nodeModel != null)
            {
                foreach (DataRow rowField in tableDescription.Rows)
                {
                    XmlNode nodeField = xmlFile.CreateNode(XmlNodeType.Element, "Field", xmlFile.NamespaceURI);
                    nodeModel.AppendChild(nodeField);
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("Column")).Value = rowField["Column"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("DbType")).Value = rowField["DbType"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("Header")).Value = rowField["Header"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("ControlType")).Value = rowField["ControlType"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("DefaultValue")).Value = rowField["DefaultValue"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("ReadOnly")).Value = Convert.ToInt32(rowField["ReadOnly"]).ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("Validation")).Value = rowField["Validation"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("DataBind")).Value = rowField["DataBind"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("Remark")).Value = rowField["Remark"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("Order")).Value = rowField["Order"].ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("AllowQuery")).Value = Convert.ToInt32(rowField["AllowQuery"]).ToString();
                    nodeField.Attributes.Append(xmlFile.CreateAttribute("QueryOperation")).Value = rowField["QueryOperation"].ToString();
                }

                //移除空值节点
                RemoveEmptyNode(xmlFile, nodeRoot);

                //提交保存
                xmlFile.Save(MainApp.File_FieldDescription_Path);
            }
        }

        /// <summary>
        /// 移除空值的节点
        /// </summary>
        private void RemoveEmptyNode(XmlDocument xmlDoc, XmlNode nodeRoot)
        {
            foreach (XmlNode node in nodeRoot.ChildNodes)
            {
                if (node.Attributes != null && node.Attributes.Count == 0)
                {
                    nodeRoot.RemoveChild(node);
                }
            }
        }

        #region     -------工具栏按钮操作

        /// <summary>
        /// 按钮操作
        /// </summary>
        private void ToolBar_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    if (btn == btnLoad)
                    {
                        this.LoadModelDescription();
                    }
                    else if (btn == btnAdd)
                    {
                        this.AddModelDescription();
                    }
                    else if (btn == btnDelete)
                    {
                        this.DeleteModelDescription();
                    }
                    else if (btn == btnSave)
                    {
                        this.SaveModelDescription();
                    }
                    else if (btn == btnUpdate)
                    {
                        this.UpdateModelDescription();
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }

        /// <summary>
        /// 加载描述
        /// </summary>
        private void LoadModelDescription()
        {
            ListBoxItem itemSelected = this.listBoxModel.SelectedItem as ListBoxItem;
            if (itemSelected == null)
                return;

            SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);
            XmlNode nodeModel = xmlFile.SelectSingleNode("Tables/Table[@Name='" + itemSelected.Content + "']");
            //从数据库中更新
            LoadDataTableFromDataBase(nodeModel, itemSelected.Content.ToString());

            //数据绑定
            DataGrid_Bind(itemSelected.Content.ToString());
        }

        /// <summary>
        /// 添加描述
        /// </summary>
        private void AddModelDescription()
        {
            ListBoxItem itemSelected = this.listBoxModel.SelectedItem as ListBoxItem;
            if (itemSelected == null)
                return;

            DataRow rowNew = tableDescription.NewRow();
            tableDescription.Rows.Add(rowNew);
        }

        /// <summary>
        /// 删除描述
        /// </summary>
        private void DeleteModelDescription()
        {
            if (MainApp._MessageDialog.ShowDialog("Delete",null) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                //遍历所有选定的行
                for (int i = gridModel.SelectedItems.Count - 1;i>=0 ; i--)
                {
                    DataRowView viewRow = gridModel.SelectedItems[i] as DataRowView;
                    if (viewRow != null)
                    {
                        viewRow.Delete();
                    }
                }
            }
        }

        /// <summary>
        /// 保存描述
        /// </summary>
        private void SaveModelDescription()
        {
            ListBoxItem itemSelected = this.listBoxModel.SelectedItem as ListBoxItem;
            if (itemSelected == null)
                return;

            //结束编辑
            this.gridModel.CommitEdit(DataGridEditingUnit.Row, true);
            UpdateXml(itemSelected.Content.ToString());
            MainApp._MessageDialog.Show(true);
        }

        /// <summary>
        /// 刷新描述
        /// </summary>
        private void UpdateModelDescription()
        {
            //获得当前选中节点
            ListBoxItem itemSelected = this.listBoxModel.SelectedItem as ListBoxItem;
            if (itemSelected == null)
                return;

            int intIndex = this.listBoxModel.SelectedIndex;
            
            //重新加载
            FIELD_DESCRIPTION_Loaded(null, null);

            //设置选定项
            this.listBoxModel.SelectedIndex = intIndex;

            //加载数据
            LoadData(itemSelected.Content.ToString());
            //焦点
            this.listBoxModel.Focus();
        }

        #endregion
    }
}
