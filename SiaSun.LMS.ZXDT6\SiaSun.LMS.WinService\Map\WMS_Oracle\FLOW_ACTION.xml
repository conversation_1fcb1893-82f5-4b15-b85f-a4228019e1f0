﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="FLOW_ACTION" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="FLOW_ACTION" type="SiaSun.LMS.Model.FLOW_ACTION, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="FLOW_ACTION">
			<result property="FLOW_ACTION_ID" column="flow_action_id" />
			<result property="FLOW_NODE_ID" column="flow_node_id" />
			<result property="FLOW_ACTION_CODE" column="flow_action_code" />
			<result property="FLOW_ACTION_NAME" column="flow_action_name" />
			<result property="FLOW_ACTION_EVENT" column="flow_action_event" />
			<result property="FLOW_ACTION_REMARK" column="flow_action_remark" />
			<result property="FLOW_ACTION_ORDER" column="flow_action_order" />
			<result property="FLOW_ACTION_IMAGE" column="flow_action_image" />
			<result property="FLOW_ACTION_FLAG" column="flow_action_flag" />
			<result property="FLOW_ACTION_DEFAULT" column="flow_action_default" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="FLOW_ACTION_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  flow_action_id,
				  flow_node_id,
				  flow_action_code,
				  flow_action_name,
				  flow_action_event,
				  flow_action_remark,
				  flow_action_order,
				  flow_action_image,
				  flow_action_flag,
				  flow_action_default
			From FLOW_ACTION
		</select>
		
		<select id="FLOW_ACTION_SELECT_BY_ID" parameterClass="int" extends = "FLOW_ACTION_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_action_id=#FLOW_ACTION_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="FLOW_ACTION_INSERT" parameterClass="FLOW_ACTION">
      Insert Into FLOW_ACTION (
      flow_action_id,
      flow_node_id,
      flow_action_code,
      flow_action_name,
      flow_action_event,
      flow_action_remark,
      flow_action_order,
      flow_action_image,
      flow_action_flag,
      flow_action_default
      )Values(
      #FLOW_ACTION_ID#,
      #FLOW_NODE_ID#,
      #FLOW_ACTION_CODE#,
      #FLOW_ACTION_NAME#,
      #FLOW_ACTION_EVENT#,
      #FLOW_ACTION_REMARK#,
      #FLOW_ACTION_ORDER#,
      #FLOW_ACTION_IMAGE#,
      #FLOW_ACTION_FLAG#,
      #FLOW_ACTION_DEFAULT#
      )
      <!--<selectKey  resultClass="int" type="post" property="FLOW_ACTION_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>
		
		<update id="FLOW_ACTION_UPDATE" parameterClass="FLOW_ACTION">
      Update FLOW_ACTION Set
        <!--flow_action_id=#FLOW_ACTION_ID#,-->
        flow_node_id=#FLOW_NODE_ID#,
        flow_action_code=#FLOW_ACTION_CODE#,
        flow_action_name=#FLOW_ACTION_NAME#,
        flow_action_event=#FLOW_ACTION_EVENT#,
        flow_action_remark=#FLOW_ACTION_REMARK#,
        flow_action_order=#FLOW_ACTION_ORDER#,
        flow_action_image=#FLOW_ACTION_IMAGE#,
        flow_action_flag=#FLOW_ACTION_FLAG#,
        flow_action_default=#FLOW_ACTION_DEFAULT#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					flow_action_id=#FLOW_ACTION_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="FLOW_ACTION_DELETE" parameterClass="int">
			Delete From FLOW_ACTION
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_action_id=#FLOW_ACTION_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>