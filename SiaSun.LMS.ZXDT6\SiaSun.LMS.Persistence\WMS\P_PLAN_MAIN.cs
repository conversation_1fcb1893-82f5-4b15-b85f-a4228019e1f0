﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// PLAN_MAIN
	/// </summary>
	public class P_PLAN_MAIN : P_Base_House
	{
		public P_PLAN_MAIN ()
		{
			//
			// TODO: 此处添加PLAN_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<PLAN_MAIN> GetList()
		{
			return ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(PLAN_MAIN plan_main)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("PLAN_MAIN");
                plan_main.PLAN_ID = id;
            }

			//if(string.IsNullOrEmpty(plan_main.PLAN_GROUP))
            //{
			//	plan_main.PLAN_GROUP = plan_main.PLAN_CODE;
            //}

            return ExecuteInsert("PLAN_MAIN_INSERT",plan_main);            
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(PLAN_MAIN plan_main)
		{
			return ExecuteUpdate("PLAN_MAIN_UPDATE",plan_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PLAN_MAIN GetModel(System.Int32 PLAN_ID)
		{
			return ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_ID",PLAN_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        public PLAN_MAIN GetModelPlanListId(System.Int32 planListId)
        {
            return ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_LIST_ID", planListId);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public PLAN_MAIN GetModelPlanCode(string PLAN_CODE)
        {
            return this.ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_CODE", PLAN_CODE);
        }

		/// <summary>
		/// 得到明细
		/// </summary>
		public IList<PLAN_MAIN> GetListPlanGroup(string PLAN_GROUP)
		{
			return this.ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_GROUP", PLAN_GROUP);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 PLAN_ID)
		{
			return ExecuteDelete("PLAN_MAIN_DELETE",PLAN_ID);
		}

        /// <summary>
        /// 根据状态拼接获取列表
        /// fst add 2018.07.27
        /// </summary>
        public IList<PLAN_MAIN> GetList_PLAN_STATUS(string PLAN_STATUS)
        {
            return this.ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_STATUS", PLAN_STATUS);
        }

    }
}
