﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using System.Xml;
using System.Reflection;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// ImportExcel.xaml 的交互逻辑
    /// </summary>
    public partial class TABLE_CONVERTER_EDIT : AvalonDock.DocumentContent
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public TABLE_CONVERTER_EDIT()
        {
            InitializeComponent();

            this.ucQkQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQkQuery_U_Query);
            this.gridTableConverter.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        /// <summary>
        /// 窗体加载
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //查询控件
            this.QueryBind();

            //加载转换信息
            this.TableConverterBind(string.Empty);
        }

        #region     ------查询

        /// <summary>
        /// 绑定查询
        /// </summary>
        private void QueryBind()
        {
            this.ucQkQuery.U_XmlTableName = "SYS_TABLE_CONVERTER";
            this.ucQkQuery.U_WindowName = this.GetType().Name;
            this.ucQkQuery.U_InitControl();
        }

        void ucQkQuery_U_Query(string QueryWhere)
        {
            this.TableConverterBind(QueryWhere);
        }

        #endregion

        /// <summary>
        /// 加载转换信息
        /// </summary>
        private void TableConverterBind(string QueryWhere)
        {
            this.gridTableConverter.U_TableName = "SYS_TABLE_CONVERTER";
            this.gridTableConverter.U_XmlTableName = "SYS_TABLE_CONVERTER";
            this.gridTableConverter.U_WindowName = this.GetType().Name;
            this.gridTableConverter.U_Where = QueryWhere;

            this.gridTableConverter.U_AllowChecked = false;
            this.gridTableConverter.U_AllowOperatData = true;
            this.gridTableConverter.U_AllowPage = false;

            this.gridTableConverter.U_InitControl();
        }

        #region     ------加载转换信息列表

        /// <summary>
        /// 更改选择项
        /// </summary>
        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (gridTableConverter.U_SelectedItem == null)
            {
                this.gridTableConverterList.U_DataSource = null;
            }
            else
            {
                this.TableConverterListBind(gridTableConverter.U_SelectedItem as DataRowView);
            }
        }

        /// <summary>
        /// 加载转换信息列表
        /// </summary>
        private void TableConverterListBind(DataRowView viewRowTableConverter)
        {
            if (viewRowTableConverter == null || viewRowTableConverter.Row.IsNull("TABLE_CONVERTER_ID"))
            {
                this.gridTableConverterList.U_DataSource = null;
                return;
            }

            this.gridTableConverterList.U_TableName = "SYS_TABLE_CONVERTER_LIST";
            this.gridTableConverterList.U_XmlTableName = "SYS_TABLE_CONVERTER_LIST";
            this.gridTableConverterList.U_WindowName = this.GetType().Name;
            this.gridTableConverterList.U_Where = string.Format("TABLE_CONVERTER_ID={0}", Convert.ToInt32(viewRowTableConverter["TABLE_CONVERTER_ID"]));

            this.gridTableConverterList.U_AllowChecked = true;
            this.gridTableConverterList.U_AllowOperatData = true;
            this.gridTableConverterList.U_AllowPage = false;
            this.gridTableConverterList.U_InitControl();

            //判断是否存在数据
            DataView viewSource = this.gridTableConverterList.U_DataSource;
            if (viewSource.Count == 0)
            {
                this.SetDefaultConverterListTable(viewRowTableConverter, viewSource);
            }
        }

        /// <summary>
        /// 获得默认的转换列表数据源
        /// </summary>
        private void SetDefaultConverterListTable(DataRowView viewRowTableConverter,DataView viewTableConverterList)
        {
            try
            {
                int intConverterID = Convert.ToInt32(viewRowTableConverter["TABLE_CONVERTER_ID"]);
                //获得父表的列
                if (!viewRowTableConverter.Row.IsNull("PARENT_TABLE") && !string.IsNullOrEmpty(viewRowTableConverter["PARENT_TABLE"].ToString()))
                {
                    this.SetConverterListSource(viewRowTableConverter["PARENT_TABLE"].ToString(), intConverterID);
                }

                //获得子表的列
                if (!viewRowTableConverter.Row.IsNull("CHILD_TABLE") && !string.IsNullOrEmpty(viewRowTableConverter["CHILD_TABLE"].ToString()))
                {
                    this.SetConverterListSource(viewRowTableConverter["CHILD_TABLE"].ToString(), intConverterID);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 设置数据源
        /// </summary>
        private void SetConverterListSource(string TABLE_NAME,int TABLE_CONVERTER_ID)
        {
            using (DataTable tableFields = new CustomerDescriptions().GetStyleDataTable(TABLE_NAME))
            {
                foreach (DataRow rowField in tableFields.Rows)
                {
                    if (this.gridTableConverterList.U_DataSource.Table.Select(string.Format("COLUMN_NAME='{0}'", rowField["Column"].ToString())).Length == 0)
                    {
                        DataRow rowNew = this.gridTableConverterList.U_DataSource.Table.NewRow();
                        rowNew["TABLE_CONVERTER_ID"] = TABLE_CONVERTER_ID;
                        rowNew["TABLE_NAME"] = TABLE_NAME;
                        rowNew["COLUMN_NAME"] = rowField["Column"];
                        rowNew["UNIQUE_FLAG"] = 0;
                        rowNew["ISNULL_FLAG"] = 1;
                        this.gridTableConverterList.U_DataSource.Table.Rows.Add(rowNew);
                    }
                }
            }
        }

        #endregion
    }
}
