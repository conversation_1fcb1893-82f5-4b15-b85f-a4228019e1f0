﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.SYS.LED_CONFIG"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="LED_CONFIG" Height="353" Width="620" Loaded="DocumentContent_Loaded">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*" ></RowDefinition>
            </Grid.RowDefinitions>
        <GroupBox Grid.Row="0" Header="LED_MAIN配置" Margin="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>
                <uc:ucQuickQuery x:Name="ucQueryLED" Grid.Row="0" ></uc:ucQuickQuery>
                <uc:ucCommonDataGrid x:Name="gridLED_MAIN" Grid.Row="1"></uc:ucCommonDataGrid>
                <WrapPanel Grid.Row="2" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="Button_Click">
                    <Button Name="btnInit"  Width="80" Margin="5" Visibility="Hidden">生成LED_LIST</Button>
                    <Button Name="btnClose"  Width="70" Margin="5">关闭服务</Button>
                    <Button Name="btnOpen"  Width="70" Margin="5">开启服务</Button>
                    <Button Name="btnSend"  Width="70" Margin="5">重新发送</Button>
                </WrapPanel>
                <WrapPanel Grid.Row="3" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="Button_Click">
                    <StackPanel  Name="panelLED_TEXT" Orientation="Horizontal" Margin="5,5,5,5" >
                        <TextBlock Text="默认发送文本:" Margin="2" VerticalAlignment="Center"  />
                        <TextBox Name="txtSEND_TEXT" Margin="2,0,2,0" MinWidth="320" MinHeight="23"/>
                    </StackPanel>
                    <Button Name="btnSave"  Width="70" Margin="5">发送</Button>
                    <Button Name="btnServerSend"  Width="100" Margin="5">模拟服务端发送</Button>
                </WrapPanel>
            </Grid>
        </GroupBox>
        <GridSplitter Height="2" HorizontalAlignment="Stretch" Grid.Row="1"></GridSplitter>
            <GroupBox Grid.Row="2" Header="LED_LIST" Margin="1">
            <uc:ucCommonDataGrid x:Name="gridLED_LIST" ></uc:ucCommonDataGrid>
        </GroupBox>
       </Grid>

</ad:DocumentContent >
