﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="IO_CONTROL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="IO_CONTROL" type="SiaSun.LMS.Model.IO_CONTROL, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="IO_CONTROL">
			<result property="CONTROL_ID" column="control_id" />
			<result property="RELATIVE_CONTROL_ID" column="relative_control_id" />
			<result property="MANAGE_ID" column="manage_id" />
			<result property="CELL_GROUP" column="cell_group" />
			<result property="STOCK_BARCODE" column="stock_barcode" />
			<result property="PRE_CONTROL_STATUS" column="pre_control_status" />
			<result property="CONTROL_TASK_TYPE" column="control_task_type" />
			<result property="CONTROL_TASK_LEVEL" column="control_task_level" />
			<result property="START_WAREHOUSE_CODE" column="start_warehouse_code" />
			<result property="END_WAREHOUSE_CODE" column="end_warehouse_code" />
			<result property="START_DEVICE_CODE" column="start_device_code" />
			<result property="END_DEVICE_CODE" column="end_device_code" />
			<result property="CONTROL_STATUS" column="control_status" />
			<result property="ERROR_TEXT" column="error_text" />
			<result property="CONTROL_BEGIN_TIME" column="control_begin_time" />
			<result property="CONTROL_END_TIME" column="control_end_time" />
			<result property="CONTROL_REMARK" column="control_remark" />

			<result property="PALLET_SIZE" column="pallet_size" />
			<result property="CONTROL_BATCH" column="control_batch" />
			<result property="DEVICE_CODE" column="device_code" />
			<result property="MANAGE_TASK_TYPE" column="manage_task_type" />
			<result property="MULTIPLEX_FLAG" column="multiplex_flag" />
			<result property="STACK_TYPE" column="stack_type" />
			<result property="STACK_QTY" column="stack_qty" />

			<result property="START_DEVICE_CODE2" column="start_device_code2" />
			<result property="END_DEVICE_CODE2" column="end_device_code2" />
			<result property="AGV_NO" column="agv_no" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="IO_CONTROL_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			control_id,
			relative_control_id,
			manage_id,
			cell_group,
			stock_barcode,
			pre_control_status,
			control_task_type,
			control_task_level,
			start_warehouse_code,
			end_warehouse_code,
			start_device_code,
			end_device_code,
			control_status,
			error_text,
			control_begin_time,
			control_end_time,
			control_remark,

			pallet_size,
			control_batch,
			device_code,
			manage_task_type,
			multiplex_flag,
			stack_type,
			stack_qty,
			start_device_code2,
			end_device_code2,
			agv_no

			From IO_CONTROL
		</select>

		<select id="IO_CONTROL_SELECT_BY_ID" parameterClass="int" extends = "IO_CONTROL_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					control_id=#CONTROL_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="IO_CONTROL_SELECT_BY_MANAGE_ID" parameterClass="int" extends = "IO_CONTROL_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_id=#MANAGE_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="IO_CONTROL_SELECT_BY_STOCK_BARCODE" parameterClass="string" extends = "IO_CONTROL_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					stock_barcode=#STOCK_BARCODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="IO_CONTROL_SELECT_CHANGED" parameterClass="int" extends = "IO_CONTROL_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					CONTROL_STATUS !=0 and CONTROL_STATUS !=7 and PRE_CONTROL_STATUS != CONTROL_STATUS
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="IO_CONTROL_INSERT" parameterClass="IO_CONTROL">
			Insert Into IO_CONTROL (
			<!--control_id,-->
			relative_control_id,
			manage_id,
			cell_group,
			stock_barcode,
			pre_control_status,
			control_task_type,
			control_task_level,
			start_warehouse_code,
			end_warehouse_code,
			start_device_code,
			end_device_code,
			control_status,
			error_text,
			control_begin_time,
			control_end_time,
			control_remark,

			pallet_size,
			control_batch,
			device_code,
			manage_task_type,
			multiplex_flag,
			stack_type,
			stack_qty,
			start_device_code2,
			end_device_code2,
			agv_no
			)Values(
			<!--#CONTROL_ID#,-->
			#RELATIVE_CONTROL_ID#,
			#MANAGE_ID#,
			#CELL_GROUP#,
			#STOCK_BARCODE#,
			#PRE_CONTROL_STATUS#,
			#CONTROL_TASK_TYPE#,
			#CONTROL_TASK_LEVEL#,
			#START_WAREHOUSE_CODE#,
			#END_WAREHOUSE_CODE#,
			#START_DEVICE_CODE#,
			#END_DEVICE_CODE#,
			#CONTROL_STATUS#,
			#ERROR_TEXT#,
			#CONTROL_BEGIN_TIME#,
			#CONTROL_END_TIME#,
			#CONTROL_REMARK#,
			#PALLET_SIZE#,
			#CONTROL_BATCH#,
			#DEVICE_CODE#,
			#MANAGE_TASK_TYPE#,
			#MULTIPLEX_FLAG#,
			#STACK_TYPE#,
			#STACK_QTY#,
			#START_DEVICE_CODE2#,
			#END_DEVICE_CODE2#,
			#AGV_NO#
			)
			<selectKey  resultClass="int" type="post" property="CONTROL_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="IO_CONTROL_UPDATE" parameterClass="IO_CONTROL">
			Update IO_CONTROL Set
			<!--control_id=#CONTROL_ID#,-->
			relative_control_id=#RELATIVE_CONTROL_ID#,
			manage_id=#MANAGE_ID#,
			cell_group=#CELL_GROUP#,
			stock_barcode=#STOCK_BARCODE#,
			pre_control_status=#PRE_CONTROL_STATUS#,
			control_task_type=#CONTROL_TASK_TYPE#,
			control_task_level=#CONTROL_TASK_LEVEL#,
			start_warehouse_code=#START_WAREHOUSE_CODE#,
			end_warehouse_code=#END_WAREHOUSE_CODE#,
			start_device_code=#START_DEVICE_CODE#,
			end_device_code=#END_DEVICE_CODE#,
			control_status=#CONTROL_STATUS#,
			error_text=#ERROR_TEXT#,
			control_begin_time=#CONTROL_BEGIN_TIME#,
			control_end_time=#CONTROL_END_TIME#,
			control_remark=#CONTROL_REMARK#,

			pallet_size=#PALLET_SIZE#,
			control_batch=#CONTROL_BATCH#,
			device_code=#DEVICE_CODE#,
			manage_task_type=#MANAGE_TASK_TYPE#,
			multiplex_flag=#MULTIPLEX_FLAG#,
			stack_type=#STACK_TYPE#,
			stack_qty=#STACK_QTY#,
			start_device_code2=#START_DEVICE_CODE2#,
			end_device_code2=#END_DEVICE_CODE2#,
			agv_no=#AGV_NO#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					control_id=#CONTROL_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="IO_CONTROL_DELETE_BY_MANAGE_ID" parameterClass="int">
			Delete From IO_CONTROL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_id=#MANAGE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

		<delete id="IO_CONTROL_DELETE" parameterClass="int">
			Delete From IO_CONTROL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					control_id=#CONTROL_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>