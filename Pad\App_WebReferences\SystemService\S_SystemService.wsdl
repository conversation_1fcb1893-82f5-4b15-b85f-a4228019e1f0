<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="S_SystemService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xs:element name="USER_LOGIN">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="USER_LOGINResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="USER_LOGINResult" type="xs:boolean" />
            <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="USER" nillable="true" type="q1:SYS_USER" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="USER_PASSWORD">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="USER_PASSWORD_OLD" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="USER_PASSWORD_NEW" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="USER_PASSWORDResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="USER_PASSWORDResult" type="xs:boolean" />
            <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_GetList">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_GetListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_GetListResult" nillable="true" type="q2:ArrayOfSYS_ROLE" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_GetList_ROLE_MENU">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
            <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_GetList_ROLE_MENUResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_WINDOW_GetList_ROLE_MENUResult" nillable="true" type="q3:ArrayOfSYS_ROLE_WINDOW" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_GetModel_MENU_CONTROL">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
            <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
            <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_GetModel_MENU_CONTROLResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_WINDOW_GetModel_MENU_CONTROLResult" nillable="true" type="q4:SYS_ROLE_WINDOW" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_Save">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
            <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
            <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="listROLE_WINDOW" nillable="true" type="q5:ArrayOfSYS_ROLE_WINDOW" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ROLE_WINDOW_SaveResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ROLE_WINDOW_SaveResult" type="xs:boolean" />
            <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ITEM_LIST_GetDictionary">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ITEM_LIST_GetDictionaryResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ITEM_LIST_GetDictionaryResult" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ITEM_LIST_GetList_ITEM_CODE">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ITEM_LIST_GetList_ITEM_CODEResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ITEM_LIST_GetList_ITEM_CODEResult" nillable="true" type="q6:ArrayOfSYS_ITEM_LIST" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetList">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetListResult" nillable="true" type="q7:ArrayOfSYS_MENU" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetList_ROLE_Select">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
            <xs:element minOccurs="0" name="bSelect" type="xs:boolean" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetList_ROLE_SelectResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetList_ROLE_SelectResult" nillable="true" type="q8:ArrayOfSYS_MENU" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetModel">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MENU_GetModelResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetModelResult" nillable="true" type="q9:SYS_MENU" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_GetModel">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_GetModelResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="RELATION_GetModelResult" nillable="true" type="q10:SYS_RELATION" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_LIST_GetList_ID1">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
            <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_LIST_GetList_ID1Response">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="RELATION_LIST_GetList_ID1Result" nillable="true" type="q11:ArrayOfSYS_RELATION_LIST" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_LIST_Add">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
            <xs:element xmlns:q12="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="List_RELATION_ID2" nillable="true" type="q12:ArrayOfint" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RELATION_LIST_AddResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="RELATION_LIST_AddResult" type="xs:boolean" />
            <xs:element minOccurs="0" name="Result" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="TABLE_CONVERTER_GetList">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="TABLE_CONVERTER_GetListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q13="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="TABLE_CONVERTER_GetListResult" nillable="true" type="q13:ArrayOfSYS_TABLE_CONVERTER" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SYS_TABLE_CONVERTER_Import">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="tableImport" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SYS_TABLE_CONVERTER_ImportResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="SYS_TABLE_CONVERTER_ImportResult" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:element ref="xs:schema" />
                  <xs:any />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="TABLE_CONVERTER_Save">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q14="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mTABLE_CONVERTER" nillable="true" type="q14:SYS_TABLE_CONVERTER" />
            <xs:element minOccurs="0" name="dsImport" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:element ref="xs:schema" />
                  <xs:any />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="TABLE_CONVERTER_SaveResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="TABLE_CONVERTER_SaveResult" type="xs:int" />
            <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Import_GOODS_MAIN">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="tableImport" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Import_GOODS_MAINResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="Import_GOODS_MAINResult" type="xs:boolean" />
            <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="anyType" nillable="true" type="xs:anyType" />
      <xs:element name="anyURI" nillable="true" type="xs:anyURI" />
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary" />
      <xs:element name="boolean" nillable="true" type="xs:boolean" />
      <xs:element name="byte" nillable="true" type="xs:byte" />
      <xs:element name="dateTime" nillable="true" type="xs:dateTime" />
      <xs:element name="decimal" nillable="true" type="xs:decimal" />
      <xs:element name="double" nillable="true" type="xs:double" />
      <xs:element name="float" nillable="true" type="xs:float" />
      <xs:element name="int" nillable="true" type="xs:int" />
      <xs:element name="long" nillable="true" type="xs:long" />
      <xs:element name="QName" nillable="true" type="xs:QName" />
      <xs:element name="short" nillable="true" type="xs:short" />
      <xs:element name="string" nillable="true" type="xs:string" />
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte" />
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt" />
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong" />
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort" />
      <xs:element name="char" nillable="true" type="tns:char" />
      <xs:simpleType name="char">
        <xs:restriction base="xs:int" />
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration" />
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?" />
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S" />
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid" />
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}" />
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName" />
      <xs:attribute name="Id" type="xs:ID" />
      <xs:attribute name="Ref" type="xs:IDREF" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:complexType name="SYS_USER">
        <xs:sequence>
          <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
          <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER" />
      <xs:complexType name="ArrayOfSYS_ROLE">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_ROLE" nillable="true" type="tns:ArrayOfSYS_ROLE" />
      <xs:complexType name="SYS_ROLE">
        <xs:sequence>
          <xs:element minOccurs="0" name="ROLE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_START_MENU_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
      <xs:complexType name="ArrayOfSYS_ROLE_WINDOW">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_ROLE_WINDOW" nillable="true" type="tns:ArrayOfSYS_ROLE_WINDOW" />
      <xs:complexType name="SYS_ROLE_WINDOW">
        <xs:sequence>
          <xs:element minOccurs="0" name="CONTROL_HEADER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_WINDOW_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
      <xs:complexType name="ArrayOfSYS_ITEM_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_ITEM_LIST" nillable="true" type="tns:ArrayOfSYS_ITEM_LIST" />
      <xs:complexType name="SYS_ITEM_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
      <xs:complexType name="ArrayOfSYS_MENU">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_MENU" nillable="true" type="tns:ArrayOfSYS_MENU" />
      <xs:complexType name="SYS_MENU">
        <xs:sequence>
          <xs:element minOccurs="0" name="MENU_CHILDNODEFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_CLASS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_DEVELOPFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_GROUP" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_IMAGE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_IMAGE_SL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_PARAMETER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_PARAMETER_SL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_PARENT_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_SELECTEDFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_SYSFLAG" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
      <xs:complexType name="SYS_RELATION">
        <xs:sequence>
          <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_ID2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATON_NAME1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATON_NAME2" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_RELATION" nillable="true" type="tns:SYS_RELATION" />
      <xs:complexType name="ArrayOfSYS_RELATION_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_RELATION_LIST" nillable="true" type="tns:ArrayOfSYS_RELATION_LIST" />
      <xs:complexType name="SYS_RELATION_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID2" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
      <xs:complexType name="ArrayOfSYS_TABLE_CONVERTER">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfSYS_TABLE_CONVERTER" nillable="true" type="tns:ArrayOfSYS_TABLE_CONVERTER" />
      <xs:complexType name="SYS_TABLE_CONVERTER">
        <xs:sequence>
          <xs:element minOccurs="0" name="CHILD_FOREIGN_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CHILD_TABLE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARENT_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARENT_TABLE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_COLUMN" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_NAME" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System.Data" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="DataTable" nillable="true">
        <xs:complexType>
          <xs:annotation>
            <xs:appinfo>
              <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
            </xs:appinfo>
          </xs:annotation>
          <xs:sequence>
            <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
            <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:complexType name="ArrayOfint">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="int" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfint" nillable="true" type="tns:ArrayOfint" />
    </xs:schema>
    <xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="DataSet" nillable="true">
        <xs:complexType>
          <xs:annotation>
            <xs:appinfo>
              <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
            </xs:appinfo>
          </xs:annotation>
          <xs:sequence>
            <xs:element ref="xs:schema" />
            <xs:any />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="I_SystemService_USER_LOGIN_InputMessage">
    <wsdl:part name="parameters" element="tns:USER_LOGIN" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_USER_LOGIN_OutputMessage">
    <wsdl:part name="parameters" element="tns:USER_LOGINResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_USER_PASSWORD_InputMessage">
    <wsdl:part name="parameters" element="tns:USER_PASSWORD" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_USER_PASSWORD_OutputMessage">
    <wsdl:part name="parameters" element="tns:USER_PASSWORDResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_GetList" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_GetList_ROLE_MENU_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetList_ROLE_MENU" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_GetList_ROLE_MENU_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetList_ROLE_MENUResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_GetModel_MENU_CONTROL_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetModel_MENU_CONTROL" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_GetModel_MENU_CONTROL_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetModel_MENU_CONTROLResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_Save" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ROLE_WINDOW_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ITEM_LIST_GetDictionary_InputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetDictionary" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ITEM_LIST_GetDictionary_OutputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetDictionaryResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ITEM_LIST_GetList_ITEM_CODE_InputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetList_ITEM_CODE" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_ITEM_LIST_GetList_ITEM_CODE_OutputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetList_ITEM_CODEResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetList_ROLE_Select_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList_ROLE_Select" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetList_ROLE_Select_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList_ROLE_SelectResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetModel" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_MENU_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_GetModel" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_LIST_GetList_ID1_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_GetList_ID1" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_LIST_GetList_ID1_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_GetList_ID1Response" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_LIST_Add_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_Add" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_RELATION_LIST_Add_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_AddResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_TABLE_CONVERTER_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_GetList" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_TABLE_CONVERTER_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_SYS_TABLE_CONVERTER_Import_InputMessage">
    <wsdl:part name="parameters" element="tns:SYS_TABLE_CONVERTER_Import" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_SYS_TABLE_CONVERTER_Import_OutputMessage">
    <wsdl:part name="parameters" element="tns:SYS_TABLE_CONVERTER_ImportResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_TABLE_CONVERTER_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_Save" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_TABLE_CONVERTER_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_Import_GOODS_MAIN_InputMessage">
    <wsdl:part name="parameters" element="tns:Import_GOODS_MAIN" />
  </wsdl:message>
  <wsdl:message name="I_SystemService_Import_GOODS_MAIN_OutputMessage">
    <wsdl:part name="parameters" element="tns:Import_GOODS_MAINResponse" />
  </wsdl:message>
  <wsdl:portType name="I_SystemService">
    <wsdl:operation name="USER_LOGIN">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/USER_LOGIN" message="tns:I_SystemService_USER_LOGIN_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/USER_LOGINResponse" message="tns:I_SystemService_USER_LOGIN_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="USER_PASSWORD">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/USER_PASSWORD" message="tns:I_SystemService_USER_PASSWORD_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/USER_PASSWORDResponse" message="tns:I_SystemService_USER_PASSWORD_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ROLE_GetList" message="tns:I_SystemService_ROLE_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ROLE_GetListResponse" message="tns:I_SystemService_ROLE_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetList_ROLE_MENU">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetList_ROLE_MENU" message="tns:I_SystemService_ROLE_WINDOW_GetList_ROLE_MENU_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetList_ROLE_MENUResponse" message="tns:I_SystemService_ROLE_WINDOW_GetList_ROLE_MENU_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetModel_MENU_CONTROL">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetModel_MENU_CONTROL" message="tns:I_SystemService_ROLE_WINDOW_GetModel_MENU_CONTROL_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetModel_MENU_CONTROLResponse" message="tns:I_SystemService_ROLE_WINDOW_GetModel_MENU_CONTROL_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_Save" message="tns:I_SystemService_ROLE_WINDOW_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ROLE_WINDOW_SaveResponse" message="tns:I_SystemService_ROLE_WINDOW_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetDictionary">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ITEM_LIST_GetDictionary" message="tns:I_SystemService_ITEM_LIST_GetDictionary_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ITEM_LIST_GetDictionaryResponse" message="tns:I_SystemService_ITEM_LIST_GetDictionary_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetList_ITEM_CODE">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/ITEM_LIST_GetList_ITEM_CODE" message="tns:I_SystemService_ITEM_LIST_GetList_ITEM_CODE_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/ITEM_LIST_GetList_ITEM_CODEResponse" message="tns:I_SystemService_ITEM_LIST_GetList_ITEM_CODE_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetList" message="tns:I_SystemService_MENU_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetListResponse" message="tns:I_SystemService_MENU_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList_ROLE_Select">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetList_ROLE_Select" message="tns:I_SystemService_MENU_GetList_ROLE_Select_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetList_ROLE_SelectResponse" message="tns:I_SystemService_MENU_GetList_ROLE_Select_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetModel" message="tns:I_SystemService_MENU_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/MENU_GetModelResponse" message="tns:I_SystemService_MENU_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/RELATION_GetModel" message="tns:I_SystemService_RELATION_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/RELATION_GetModelResponse" message="tns:I_SystemService_RELATION_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_GetList_ID1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/RELATION_LIST_GetList_ID1" message="tns:I_SystemService_RELATION_LIST_GetList_ID1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/RELATION_LIST_GetList_ID1Response" message="tns:I_SystemService_RELATION_LIST_GetList_ID1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_Add">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/RELATION_LIST_Add" message="tns:I_SystemService_RELATION_LIST_Add_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/RELATION_LIST_AddResponse" message="tns:I_SystemService_RELATION_LIST_Add_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/TABLE_CONVERTER_GetList" message="tns:I_SystemService_TABLE_CONVERTER_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/TABLE_CONVERTER_GetListResponse" message="tns:I_SystemService_TABLE_CONVERTER_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SYS_TABLE_CONVERTER_Import">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/SYS_TABLE_CONVERTER_Import" message="tns:I_SystemService_SYS_TABLE_CONVERTER_Import_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/SYS_TABLE_CONVERTER_ImportResponse" message="tns:I_SystemService_SYS_TABLE_CONVERTER_Import_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/TABLE_CONVERTER_Save" message="tns:I_SystemService_TABLE_CONVERTER_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/TABLE_CONVERTER_SaveResponse" message="tns:I_SystemService_TABLE_CONVERTER_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Import_GOODS_MAIN">
      <wsdl:input wsaw:Action="http://tempuri.org/I_SystemService/Import_GOODS_MAIN" message="tns:I_SystemService_Import_GOODS_MAIN_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_SystemService/Import_GOODS_MAINResponse" message="tns:I_SystemService_Import_GOODS_MAIN_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_SystemService" type="tns:I_SystemService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="USER_LOGIN">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/USER_LOGIN" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="USER_PASSWORD">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/USER_PASSWORD" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_GetList">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ROLE_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetList_ROLE_MENU">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetList_ROLE_MENU" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetModel_MENU_CONTROL">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ROLE_WINDOW_GetModel_MENU_CONTROL" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_Save">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ROLE_WINDOW_Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetDictionary">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ITEM_LIST_GetDictionary" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetList_ITEM_CODE">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/ITEM_LIST_GetList_ITEM_CODE" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/MENU_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList_ROLE_Select">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/MENU_GetList_ROLE_Select" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetModel">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/MENU_GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_GetModel">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/RELATION_GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_GetList_ID1">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/RELATION_LIST_GetList_ID1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_Add">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/RELATION_LIST_Add" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_GetList">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/TABLE_CONVERTER_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SYS_TABLE_CONVERTER_Import">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/SYS_TABLE_CONVERTER_Import" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_Save">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/TABLE_CONVERTER_Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Import_GOODS_MAIN">
      <soap:operation soapAction="http://tempuri.org/I_SystemService/Import_GOODS_MAIN" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_SystemService">
    <wsdl:port name="BasicHttpBinding_I_SystemService" binding="tns:BasicHttpBinding_I_SystemService">
      <soap:address location="http://localhost:8001/Service/SystemService" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>