﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_TABLE_CONVERTER_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_TABLE_CONVERTER_LIST
	{
		public SYS_TABLE_CONVERTER_LIST()
		{
			
		}
		
		private int _table_converter_list_id;
		private int _table_converter_id;
		private string _table_name;
		private string _column_name;
		private string _convert_column_name;
		private string _unique_flag;
		private string _isnull_flag;
		private string _remark;
		
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int TABLE_CONVERTER_LIST_ID
		{
			get{return _table_converter_list_id;}
			set{_table_converter_list_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int TABLE_CONVERTER_ID
		{
			get{return _table_converter_id;}
			set{_table_converter_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TABLE_NAME
		{
			get{return _table_name;}
			set{_table_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string COLUMN_NAME
		{
			get{return _column_name;}
			set{_column_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CONVERT_COLUMN_NAME
		{
			get{return _convert_column_name;}
			set{_convert_column_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string UNIQUE_FLAG
		{
			get{return _unique_flag;}
			set{_unique_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string ISNULL_FLAG
		{
			get{return _isnull_flag;}
			set{_isnull_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string REMARK
		{
			get{return _remark;}
			set{_remark = value;}
		}
	}
}
