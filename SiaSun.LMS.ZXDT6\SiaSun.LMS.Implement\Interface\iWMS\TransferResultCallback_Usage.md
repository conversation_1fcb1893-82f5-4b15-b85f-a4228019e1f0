# 移库结果上报回调接口使用说明

## 概述

`TransferResultCallback` 类实现了移库结果上报回调功能，用于向外部物资系统上报移库操作的结果。该接口遵循项目现有的架构模式，继承自 `InterfaceBase` 基类。

## 接口规范

### 认证流程
1. **注册(regist)**: 获取系统密钥
2. **获取凭证(applyToken)**: 使用密钥获取访问凭证  
3. **访问接口(accessInterface)**: 携带凭证调用移库结果上报接口

### 接口地址
- **测试地址**: `http://10.92.234.42:9008/api/ws/AuthService?wsdl`
- **正式地址**: `http://10.92.233.34:9008/api/ws/AuthService?wsdl`

## 数据结构

### TransferResultItem 移库结果明细项

| 属性名 | 类型 | 说明 | 是否必填 |
|--------|------|------|----------|
| goodsCode | string | 物资编码 | 是 |
| removeNum | int | 移库数量 | 是(>0) |
| warehouseCode | string | 原库房编号 | 是 |
| warehouseName | string | 原库房名称 | 否 |
| warehouseId | string | 原库房id | 否 |
| shelfCode | string | 原货架位编号 | 是 |
| shelfName | string | 原货架位名称 | 否 |
| shelfId | string | 原货位id | 否 |
| targetWarehouseCode | string | 目标库房编号 | 是 |
| targetWarehouseName | string | 目标库房名称 | 否 |
| targetWarehouseId | string | 目标库房id | 否 |
| targetShelfCode | string | 目标货架位编号 | 是 |
| targetShelfName | string | 目标货架位名称 | 否 |
| targetShelfId | string | 目标货位id | 否 |

## 使用方法

### 方法1：批量上报移库结果

```csharp
var callback = new TransferResultCallback();
var transferItems = new List<TransferResultCallback.TransferResultItem>
{
    new TransferResultCallback.TransferResultItem
    {
        goodsCode = "GOODS001",
        removeNum = 10,
        warehouseCode = "WH001",
        warehouseName = "仓库1",
        warehouseId = "1",
        shelfCode = "SHELF001",
        shelfName = "货架1",
        shelfId = "1",
        targetWarehouseCode = "WH002",
        targetWarehouseName = "仓库2", 
        targetWarehouseId = "2",
        targetShelfCode = "SHELF002",
        targetShelfName = "货架2",
        targetShelfId = "2"
    }
};

bool success = callback.IntefaceMethod(transferItems, out string message);
if (success)
{
    Console.WriteLine($"移库结果上报成功: {message}");
}
else
{
    Console.WriteLine($"移库结果上报失败: {message}");
}
```

### 方法2：单个移库明细上报

```csharp
var callback = new TransferResultCallback();

bool success = callback.IntefaceMethod(
    goodsCode: "GOODS001",
    removeNum: 10,
    warehouseCode: "WH001",
    warehouseName: "仓库1",
    warehouseId: "1",
    shelfCode: "SHELF001", 
    shelfName: "货架1",
    shelfId: "1",
    targetWarehouseCode: "WH002",
    targetWarehouseName: "仓库2",
    targetWarehouseId: "2",
    targetShelfCode: "SHELF002",
    targetShelfName: "货架2",
    targetShelfId: "2",
    out string message
);

if (success)
{
    Console.WriteLine($"移库结果上报成功: {message}");
}
else
{
    Console.WriteLine($"移库结果上报失败: {message}");
}
```

## 参数校验

接口会自动进行以下参数校验：

1. **基础校验**：
   - transferResultItems 不能为空或空列表

2. **必填字段校验**：
   - goodsCode（物资编码）不能为空
   - warehouseCode（原库房编号）不能为空
   - shelfCode（原货架位编号）不能为空
   - targetWarehouseCode（目标库房编号）不能为空
   - targetShelfCode（目标货架位编号）不能为空

3. **数值校验**：
   - removeNum（移库数量）必须大于0

## 响应处理

### 成功响应
- code: 0
- msg: 成功消息
- traceId: 跟踪ID

### 失败响应
- code: 1 或 2
- msg: 错误消息
- traceId: 跟踪ID

## 错误处理

接口包含完整的错误处理机制：

1. **参数验证错误**：返回具体的参数错误信息
2. **网络通信错误**：返回网络异常信息
3. **认证失败**：返回认证相关错误信息
4. **业务逻辑错误**：返回业务处理错误信息

## 日志记录

接口会自动记录以下日志：

1. **成功日志**：
   ```
   TransferResultCallback成功_明细数[{count}]_traceId[{traceId}]_信息[{message}]
   ```

2. **异常日志**：
   ```
   TransferResultCallback异常：{exception.Message}
   ```

## 配置要求

确保在配置文件中设置了以下配置项：

- `ExternalServiceUrl`: 外部服务地址
- `ExternalServiceToken`: 外部服务Token（如需要）

## 注意事项

1. 该接口需要网络连接到外部物资系统
2. 认证凭证有时效性，接口会自动处理凭证获取
3. 建议在调用前确保网络连通性
4. 移库数量必须为正整数
5. 物资编码、原货位信息、目标货位信息为必填项
6. 接口支持批量和单个明细两种调用方式
7. 所有字符串参数建议进行非空校验后再调用

## 技术架构

- **继承关系**: TransferResultCallback : InterfaceBase
- **认证方式**: WebService SOAP认证
- **数据格式**: JSON
- **通信协议**: HTTP/HTTPS
- **编码格式**: UTF-8
