using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 出库红冲单接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class GoodsIssueReversalReceiptSync : InterfaceBase
    {
        class InputParam
        {
            public string outGoodsDate { get; set; }
            public string reason { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            public string operatorName { get; set; }
            public string operatorId { get; set; }
            public string outOperatorName { get; set; }
            public string outOperatorId { get; set; }
            public string outGoodsCode { get; set; }
            public string outGoodsName { get; set; }
            public decimal money { get; set; }
            public decimal taxMoney { get; set; }
            public string redOutName { get; set; }
            public List<RedOutDetailItem> redOutDetailList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class RedOutDetailItem
        {
            public string remark { get; set; }
            public string goodsId { get; set; }
            public string forWay { get; set; }
            public string forWayId { get; set; }
            public string reason { get; set; }
            public string orderIndex { get; set; }
            public int goodsNum { get; set; }
            public decimal tax { get; set; }
            public string redOutId { get; set; }
            public string outGoodsInfoId { get; set; }
            public string localSend { get; set; }
            public string gkDeptName { get; set; }
            public string gkDeptId { get; set; }
            public string manageDeptName { get; set; }
            public string manageDeptId { get; set; }
            public string batch { get; set; }
            public string gbName { get; set; }
            public string gbId { get; set; }
            public string bzName { get; set; }
            public string bzId { get; set; }
            public string deptName { get; set; }
            public string deptId { get; set; }
            public string orgName { get; set; }
            public string orgId { get; set; }
            public string zbCycle { get; set; }
            public string produceDate { get; set; }
            public string shelfName { get; set; }
            public string shelfId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            public decimal taxAllPrice { get; set; }
            public decimal taxPrice { get; set; }
            public string brand { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public int outStorageNum { get; set; }
            public string goodsVersion { get; set; }
            public string goodsType { get; set; }
            public string goodsName { get; set; }
            public string goodsCode { get; set; }
            public string outStorageType { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatGoodsIssueReversalErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.outGoodsDate) || string.IsNullOrEmpty(inputParam.reason) ||
                    string.IsNullOrEmpty(inputParam.warehouseId) || string.IsNullOrEmpty(inputParam.operatorId))
                {
                    result = false;
                    message = "接口入参必填项存在空值：outGoodsDate, reason, warehouseId, operatorId";
                    return FormatGoodsIssueReversalErrorMessage(message, traceId);
                }

                // 验证日期格式
                DateTime outGoodsDate;
                if (!DateTime.TryParse(inputParam.outGoodsDate, out outGoodsDate))
                {
                    result = false;
                    message = $"出库红冲时间格式错误：{inputParam.outGoodsDate}";
                    return FormatGoodsIssueReversalErrorMessage(message, traceId);
                }

                // 验证红冲明细列表
                if (inputParam.redOutDetailList == null || inputParam.redOutDetailList.Count == 0)
                {
                    result = false;
                    message = "红冲出库明细列表不能为空";
                    return FormatGoodsIssueReversalErrorMessage(message, traceId);
                }

                // 验证红冲明细项必填字段
                foreach (var item in inputParam.redOutDetailList)
                {
                    if (string.IsNullOrEmpty(item.goodsCode) || string.IsNullOrEmpty(item.goodsName) ||
                        item.outStorageNum <= 0 || string.IsNullOrEmpty(item.unitId))
                    {
                        result = false;
                        message = "红冲明细项必填字段存在空值或无效值：goodsCode, goodsName, outStorageNum, unitId";
                        return FormatGoodsIssueReversalErrorMessage(message, traceId);
                    }

                    // 验证生产日期格式（如果提供）
                    if (!string.IsNullOrEmpty(item.produceDate))
                    {
                        DateTime produceDate;
                        if (!DateTime.TryParse(item.produceDate, out produceDate))
                        {
                            result = false;
                            message = $"明细项生产日期格式错误：{item.produceDate}";
                            return FormatGoodsIssueReversalErrorMessage(message, traceId);
                        }
                    }
                }

                // 处理出库红冲单主表数据
                // 这里应该根据实际的数据库模型进行数据处理
                // 由于没有具体的数据库模型定义，这里只做基本的逻辑处理

                // 检查出库红冲单是否已存在
                // var existingReversal = S_Base.sBase.pGOODS_ISSUE_REVERSAL_MAIN.GetModel(inputParam.outGoodsCode);

                // 处理出库红冲单数据
                // 这里应该包含：
                // 1. 创建或更新出库红冲单主表记录
                // 2. 处理红冲明细数据
                // 3. 更新库存信息（红冲操作会增加库存）
                // 4. 更新原出库单的红冲状态
                // 5. 记录操作日志

                // 检查入库单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.outGoodsCode);

                if (existingReceipt != null)
                {
                    result = false;
                    message = $"已存在出库红冲单-{inputParam.outGoodsCode}";
                    return FormatGoodsIssueReversalErrorMessage(message, traceId);
                }
                else
                {
                    // 处理入库单数据
                    // 这里应该包含：
                    // 1. 创建或更新入库单主表记录
                    // 2. 处理入库明细数据
                    // 3. 更新库存信息
                    // 4. 记录操作日志
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.outGoodsCode,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanReversalIn.ToString(),
                        PLAN_CREATE_TIME = inputParam.createDate,
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                        PLAN_REMARK = inputParam.reason,
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (RedOutDetailItem item in inputParam.redOutDetailList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST()
                        {
                            PLAN_LIST_QUANTITY = item.goodsNum,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(item.goodsCode).GOODS_ID,
                            PLAN_LIST_REMARK = item.reason,
                        };
                        lsPLAN_LIST.Add(mPLAN_LIST);
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }

                // 模拟数据处理成功
                S_Base.sBase.Log.Info($"出库红冲单同步成功 - 红冲编码: {inputParam.outGoodsCode}, 红冲原因: {inputParam.reason}, 明细数量: {inputParam.redOutDetailList.Count}");

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"出库红冲单同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化出库红冲单错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatGoodsIssueReversalErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}