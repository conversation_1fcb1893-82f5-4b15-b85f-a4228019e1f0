﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B820FA47-3514-49DB-8D33-96367A80234E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Persistence</RootNamespace>
    <AssemblyName>SiaSun.LMS.Persistence</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <PublishUrl>http://localhost/SiaSun.LMS.Persistence/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="IBatisNet.Common, Version=3.0.0.0, Culture=neutral, PublicKeyToken=ed781d9fc396c6ca, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlBatis.Common.3.0.0\lib\netstandard2.0\IBatisNet.Common.dll</HintPath>
    </Reference>
    <Reference Include="IBatisNet.DataMapper, Version=1.6.2.0, Culture=neutral, PublicKeyToken=ed781d9fc396c6ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\IBatisNet.DataMapper.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.5.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.5.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.5.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BPMS\P_TB_TRAYINFO_UPLOAD.cs" />
    <Compile Include="P_Base_Bpms.cs" />
    <Compile Include="P_Base_House .cs" />
    <Compile Include="WMS\P_LCD_LIST.cs" />
    <Compile Include="WMS\P_STORAGE_LOCK.cs" />
    <Compile Include="WMS\P_INTERFACE_QUEUE.cs" />
    <Compile Include="WMS\P_LCD_MAIN.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WMS\P_APPLY_TYPE.cs" />
    <Compile Include="P_Base.cs" />
    <Compile Include="WMS\P_FIELD_DESCRIPTION.cs" />
    <Compile Include="WMS\P_FLOW_ACTION.cs" />
    <Compile Include="WMS\P_FLOW_NODE.cs" />
    <Compile Include="WMS\P_FLOW_PARA.cs" />
    <Compile Include="WMS\P_FLOW_TYPE.cs" />
    <Compile Include="WMS\P_GOODS_CLASS.cs" />
    <Compile Include="WMS\P_GOODS_MAIN.cs" />
    <Compile Include="BPMS\P_TB_TRAYINFO_CHECK.cs" />
    <Compile Include="WMS\P_GOODS_PROPERTY.cs" />
    <Compile Include="WMS\P_GOODS_TEMPLATE.cs" />
    <Compile Include="WMS\P_GOODS_TEMPLATE_LIST.cs" />
    <Compile Include="WMS\P_GOODS_TYPE.cs" />
    <Compile Include="WMS\P_IO_CONTROL.cs" />
    <Compile Include="WMS\P_IO_CONTROL_APPLY.cs" />
    <Compile Include="WMS\P_IO_CONTROL_APPLY_HIS.cs" />
    <Compile Include="WMS\P_IO_CONTROL_ROUTE.cs" />
    <Compile Include="WMS\P_LED_LIST.cs" />
    <Compile Include="WMS\P_LED_MAIN.cs" />
    <Compile Include="WMS\P_MANAGE_DETAIL.cs" />
    <Compile Include="WMS\P_MANAGE_LIST.cs" />
    <Compile Include="WMS\P_MANAGE_MAIN.cs" />
    <Compile Include="WMS\P_MANAGE_TYPE.cs" />
    <Compile Include="WMS\P_MANAGE_TYPE_PARAM.cs" />
    <Compile Include="WMS\P_PLAN_DETAIL.cs" />
    <Compile Include="WMS\P_PLAN_LIST.cs" />
    <Compile Include="WMS\P_PLAN_MAIN.cs" />
    <Compile Include="WMS\P_PLAN_TYPE.cs" />
    <Compile Include="WMS\P_RECORD_DETAIL.cs" />
    <Compile Include="WMS\P_RECORD_LIST.cs" />
    <Compile Include="WMS\P_RECORD_MAIN.cs" />
    <Compile Include="WMS\P_STORAGE_DETAIL.cs" />
    <Compile Include="WMS\P_STORAGE_LIST.cs" />
    <Compile Include="WMS\P_STORAGE_MAIN.cs" />
    <Compile Include="WMS\P_SYS_ITEM.cs" />
    <Compile Include="WMS\P_SYS_ITEM_LIST.cs" />
    <Compile Include="WMS\P_SYS_LOG.cs" />
    <Compile Include="WMS\P_SYS_MENU.cs" />
    <Compile Include="WMS\P_SYS_RELATION.cs" />
    <Compile Include="WMS\P_SYS_RELATION_LIST.cs" />
    <Compile Include="WMS\P_SYS_ROLE.cs" />
    <Compile Include="WMS\P_SYS_ROLE_WINDOW.cs" />
    <Compile Include="WMS\P_SYS_TABLE_CONVERTER_LIST.cs" />
    <Compile Include="WMS\P_SYS_USER.cs" />
    <Compile Include="WMS\P_WH_AREA.cs" />
    <Compile Include="WMS\P_WH_CELL.cs" />
    <Compile Include="WMS\P_WH_DESCRIPTION.cs" />
    <Compile Include="WMS\P_WH_LOGIC.cs" />
    <Compile Include="WMS\P_WH_WAREHOUSE.cs" />
    <Compile Include="WMS\P_SYS_TABLE_CONVERTER.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{fd10f3e5-a233-4f52-b4ee-33189d84dbef}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>