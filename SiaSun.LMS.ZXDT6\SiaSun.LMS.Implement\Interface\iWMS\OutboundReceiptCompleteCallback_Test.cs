using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 出库单完成上报回调接口测试类
    /// </summary>
    public static class OutboundReceiptCompleteCallback_Test
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 出库单完成上报回调接口测试 ===");
            Console.WriteLine();

            TestParameterValidation();
            Console.WriteLine();

            TestSingleItemReport();
            Console.WriteLine();

            TestBatchItemsReport();
            Console.WriteLine();

            TestDifferentOutboundTypes();
            Console.WriteLine();

            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 测试参数校验
        /// </summary>
        private static void TestParameterValidation()
        {
            Console.WriteLine("1. 测试参数校验");

            var callback = new OutboundReceiptCompleteCallback();

            // 测试空列表
            bool result1 = callback.IntefaceMethod(null, out string message1);
            Console.WriteLine($"   空列表测试: {(result1 ? "失败" : "通过")} - {message1}");

            // 测试空明细列表
            var emptyList = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>();
            bool result2 = callback.IntefaceMethod(emptyList, out string message2);
            Console.WriteLine($"   空明细列表测试: {(result2 ? "失败" : "通过")} - {message2}");

            // 测试必填字段为空
            var invalidItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 10,
                    warehouseCode = "", // 空值
                    shelfCode = "SH001",
                    oId = "OID001",
                    lId = "LID001",
                    outboundType = 63
                }
            };
            bool result3 = callback.IntefaceMethod(invalidItems, out string message3);
            Console.WriteLine($"   必填字段为空测试: {(result3 ? "失败" : "通过")} - {message3}");

            // 测试无效出库类型
            var invalidTypeItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 10,
                    warehouseCode = "WH001",
                    shelfCode = "SH001",
                    oId = "OID001",
                    lId = "LID001",
                    outboundType = 99 // 无效类型
                }
            };
            bool result4 = callback.IntefaceMethod(invalidTypeItems, out string message4);
            Console.WriteLine($"   无效出库类型测试: {(result4 ? "失败" : "通过")} - {message4}");
        }

        /// <summary>
        /// 测试单个明细上报
        /// </summary>
        private static void TestSingleItemReport()
        {
            Console.WriteLine("2. 测试单个明细上报");

            var callback = new OutboundReceiptCompleteCallback();

            // 测试有效的单个明细
            bool result = callback.IntefaceMethod(
                goodsNum: 100,
                warehouseCode: "WH001",
                shelfCode: "SH001-A01",
                oId: "OUTBOUND_DETAIL_001",
                lId: "STEREO_WH_OUTBOUND_001",
                outboundType: 63,
                out string message);

            Console.WriteLine($"   单个明细上报测试: {(result ? "成功" : "失败")} - {message}");

            // 测试无效参数的单个明细
            bool result2 = callback.IntefaceMethod(
                goodsNum: 50,
                warehouseCode: "", // 空值
                shelfCode: "SH001",
                oId: "OID001",
                lId: "LID001",
                outboundType: 63,
                out string message2);

            Console.WriteLine($"   无效参数单个明细测试: {(result2 ? "失败" : "通过")} - {message2}");
        }

        /// <summary>
        /// 测试批量明细上报
        /// </summary>
        private static void TestBatchItemsReport()
        {
            Console.WriteLine("3. 测试批量明细上报");

            var callback = new OutboundReceiptCompleteCallback();

            var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 100,
                    warehouseCode = "WH001",
                    shelfCode = "SH001-A01",
                    oId = "OUTBOUND_DETAIL_001",
                    lId = "STEREO_WH_OUTBOUND_001",
                    outboundType = 63
                },
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 50,
                    warehouseCode = "WH002",
                    shelfCode = "SH002-B01",
                    oId = "OUTBOUND_DETAIL_002",
                    lId = "STEREO_WH_OUTBOUND_002",
                    outboundType = 63
                }
            };

            bool result = callback.IntefaceMethod(items, out string message);
            Console.WriteLine($"   批量明细上报测试: {(result ? "成功" : "失败")} - {message}");
            Console.WriteLine($"   上报明细数量: {items.Count}");
        }

        /// <summary>
        /// 测试不同出库类型
        /// </summary>
        private static void TestDifferentOutboundTypes()
        {
            Console.WriteLine("4. 测试不同出库类型");

            var callback = new OutboundReceiptCompleteCallback();

            // 测试所有有效的出库类型
            var outboundTypes = new Dictionary<int, string>
            {
                { 63, "出库单" },
                { 72, "入库红冲单" },
                { 74, "借用" }
            };

            foreach (var type in outboundTypes)
            {
                var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
                {
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 25,
                        warehouseCode = "WH001",
                        shelfCode = "SH001-A01",
                        oId = $"DETAIL_{type.Key}_001",
                        lId = $"STEREO_{type.Key}_001",
                        outboundType = type.Key
                    }
                };

                bool result = callback.IntefaceMethod(items, out string message);
                Console.WriteLine($"   {type.Value}({type.Key})测试: {(result ? "成功" : "失败")} - {message}");
            }
        }

        /// <summary>
        /// 性能测试（可选）
        /// </summary>
        public static void PerformanceTest()
        {
            Console.WriteLine("=== 性能测试 ===");

            var callback = new OutboundReceiptCompleteCallback();
            var startTime = DateTime.Now;

            // 创建大量测试数据
            var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>();
            for (int i = 1; i <= 100; i++)
            {
                items.Add(new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = i,
                    warehouseCode = $"WH{i:D3}",
                    shelfCode = $"SH{i:D3}-A01",
                    oId = $"OUTBOUND_DETAIL_{i:D3}",
                    lId = $"STEREO_WH_OUTBOUND_{i:D3}",
                    outboundType = 63
                });
            }

            bool result = callback.IntefaceMethod(items, out string message);
            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            Console.WriteLine($"批量上报100条明细:");
            Console.WriteLine($"  结果: {(result ? "成功" : "失败")}");
            Console.WriteLine($"  消息: {message}");
            Console.WriteLine($"  耗时: {duration.TotalMilliseconds:F2} 毫秒");
            Console.WriteLine($"  平均每条: {duration.TotalMilliseconds / items.Count:F2} 毫秒");
        }

        /// <summary>
        /// 主测试入口
        /// </summary>
        public static void Main()
        {
            try
            {
                RunAllTests();
                
                Console.WriteLine();
                Console.WriteLine("是否运行性能测试? (y/n)");
                var input = Console.ReadLine();
                if (input?.ToLower() == "y")
                {
                    PerformanceTest();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
