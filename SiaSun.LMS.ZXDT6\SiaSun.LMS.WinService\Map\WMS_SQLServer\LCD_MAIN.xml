﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="LCD_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="LCD_MAIN" type="SiaSun.LMS.Model.LCD_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SELECTRESULT" class="LCD_MAIN">
			<result property="LCD_ID" column="lcd_id" />
			<result property="LCD_CODE" column="lcd_code" />
			<result property="LCD_IP" column="lcd_ip" />
			<result property="LCD_MESSAGE" column="lcd_message" />
			<result property="LCD_REMARK" column="lcd_remark" />
			<result property="LCD_UPDATE_TIME" column="lcd_update_time" />
			<result property="LCD_READ_FLAG" column="lcd_read_flag" />
			<result property="LCD_STATION" column="lcd_station" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="LCD_MAIN_SELECT" parameterClass="int" resultMap="SELECTRESULT">
			Select
			lcd_id,
			lcd_code,
			lcd_ip,
			lcd_message,
			lcd_remark,
			lcd_update_time,
			lcd_read_flag,
			lcd_station
			From LCD_MAIN
		</select>

		<select id="LCD_MAIN_SELECT_BY_ID" parameterClass="int" extends = "LCD_MAIN_SELECT" resultMap="SELECTRESULT">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					lcd_id=#LCD_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="LCD_MAIN_SELECT_BY_LCD_CODE" parameterClass="string" extends = "LCD_MAIN_SELECT" resultMap="SELECTRESULT">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					lcd_code=#LCD_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="LCD_MAIN_INSERT" parameterClass="LCD_MAIN">
			Insert Into LCD_MAIN (
			<!--lcd_id,-->
			lcd_code,
			lcd_ip,
			lcd_message,
			lcd_remark,
			lcd_update_time,
			lcd_read_flag,
			lcd_station
			)Values(
			<!--#LCD_ID#,-->
			#LCD_CODE#,
			#LCD_IP#,
			#LCD_MESSAGE#,
			#LCD_REMARK#,
			#LCD_UPDATE_TIME#,
			#LCD_READ_FLAG#,
			#LCD_STATION#
			)
			<selectKey  resultClass="int" type="post" property="LCD_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="LCD_MAIN_UPDATE" parameterClass="LCD_MAIN">
			Update LCD_MAIN Set
			<!--lcd_id=#LCD_ID#,-->
			lcd_code=#LCD_CODE#,
			lcd_ip=#LCD_IP#,
			lcd_message=#LCD_MESSAGE#,
			lcd_remark=#LCD_REMARK#,
			lcd_update_time=#LCD_UPDATE_TIME#,
			lcd_read_flag=#LCD_READ_FLAG#,
			lcd_station=#LCD_STATION#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					lcd_id=#LCD_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="LCD_MAIN_DELETE" parameterClass="int">
			Delete From LCD_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					lcd_id=#LCD_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>