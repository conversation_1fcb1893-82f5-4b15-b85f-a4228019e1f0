﻿<?xml version="1.0" encoding="utf-8"?>
  <log4net>
    <!--定义输出到文件中-->
    <appender name="LogFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--日志的路径-->
      <file value="log\" />
      <appendToFile value="true" />

      <rollingStyle value="Date" />

      <!--<datePattern value="yyyy-MM-dd&quot;.log&quot;" />-->
      <datePattern value="yyyy-MM-dd&quot;.html&quot;" />

      <staticLogFileName value="false" />

      <layout type="log4net.Layout.PatternLayout">
        <!--<layout type="log4net.Layout.XmlLayout">-->
        <!--每条日志末尾的文字说明-->
        <footer value="************************************" />
        <!--输出格式-->
        <!--样例：2008-03-26 13:42:32,111 [10] INFO  Log4NetDemo.MainClass [(null)] - info-->
        <!--<conversionPattern value="记录时间：%date 线程ID:[%thread] 日志级别：%-5level 出错类：%logger property:[%property{NDC}] - 错误描述：%message%newline" />-->
        <!--<conversionPattern value="记录时间：%date 线程ID:[%thread] 数据采集描述：%message%newline" />-->
        <header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='100%' border='1' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='1' cellspacing='0' &gt;
              &lt;tr&gt;
            &lt;td&gt;记录时间&lt;/td&gt;
            &lt;td&gt;线程&lt;/td&gt;
            &lt;td&gt;数据采集描述信息&lt;/td&gt;
            &lt;/tr&gt;" />

        <conversionPattern value="&lt;tr&gt;
             &lt;td&gt;%date&lt;/td&gt;
            &lt;td&gt;%thread&lt;/td&gt;
            &lt;td&gt;%message&lt;/td&gt;
            &lt;/tr&gt;" />
        <footer value="&lt;/table&gt;" />
      </layout>
    </appender>
    
    <!--定义输出到控制台命令行中-->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>
    
    <!--定义输出到windows事件中-->
    <appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>

    <!--定义输出到数据库中,这里举例输出到SQL SERVER数据库中,数据库为SSLMS_QHEP-->
    <appender xmlns="" name="ADONetAppender_SqlServer"  type="log4net.Appender.AdoNetAppender">

      <bufferSize value="100" />

      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data,System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>

      <connectionString value="server=alex;database=ORCL;user id=*****;password=*****"/>

      <commandText value="INSERT INTO SYS_Log ([log_Date],[log_Thread],[log_Level],[log_Logger],[log_Message]) VALUES (@log_date, @thread, @log_level, @logger, @message)" />

      <parameter>

        <parameterName value="@log_date" />

        <dbType value="DateTime" />

        <layout type="log4net.Layout.RawTimeStampLayout" />

      </parameter>

      <parameter>

        <parameterName value="@thread" />

        <dbType value="String" />

        <size value="255" />

        <layout type="log4net.Layout.PatternLayout">

          <conversionPattern value="%thread" />

        </layout>

      </parameter>

      <parameter>

        <parameterName value="@log_level" />

        <dbType value="String" />

        <size value="50" />

        <layout type="log4net.Layout.PatternLayout">

          <conversionPattern value="%level" />

        </layout>

      </parameter>

      <parameter>

        <parameterName value="@logger" />

        <dbType value="String" />

        <size value="255" />

        <layout type="log4net.Layout.PatternLayout">

          <conversionPattern value="%logger" />

        </layout>

      </parameter>

      <parameter>

        <parameterName value="@message" />

        <dbType value="String" />

        <size value="4000" />

        <layout type="log4net.Layout.PatternLayout">

          <conversionPattern value="%message" />

        </layout>

      </parameter>

      <parameter>

        <parameterName value="@exception" />

        <dbType value="String" />

        <size value="2000" />

        <layout type="log4net.Layout.ExceptionLayout" />

      </parameter>



    </appender>

    <!--定义输出到数据库中,这里举例输出到Access数据库中,数据库为C盘的log4net.mdb-->
    <appender name="AdoNetAppender_Access" type="log4net.Appender.AdoNetAppender">
      <connectionString value="Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:log4net.mdb" />
      <commandText value="INSERT INTO LogDetails ([LogDate],[Thread],[Level],[Logger],[Message]) VALUES (@logDate, @thread, @logLevel, @logger,@message)" />
      <!--定义各个参数-->
      <parameter>
        <parameterName value="@logDate" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%date" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@thread" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%thread" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logLevel" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="240" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
    </appender>
    
    <!--定义日志的输出媒介,下面定义日志以四种方式输出。也可以下面的按照一种类型或其他类型输出。-->
    
    <root>
      <!--文件形式记录日志-->
      <appender-ref ref="LogFileAppender" />
      <!--控制台控制显示日志-->
      <!--<appender-ref ref="ConsoleAppender" />-->
      <!--Windows事件日志-->
      <!--<appender-ref ref="EventLogAppender" />-->
      <!-- 数据库 -->
      <!--<appender-ref ref="ADONetAppender_SqlServer" />-->
      <!--<appender-ref ref="AdoNetAppender_Access" />-->     
    </root>

  </log4net>