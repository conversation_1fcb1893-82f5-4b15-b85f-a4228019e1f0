﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// STORAGE_MAIN
	/// </summary>
	public class P_STORAGE_MAIN : P_Base_House
	{
		public P_STORAGE_MAIN ()
		{
			//
			// TODO: 此处添加STORAGE_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_MAIN> GetList()
		{
			return ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT",null);
		}

        public IList<STORAGE_MAIN> GetListStockBarcode(string STOCK_BARCODE)
        {
            return this.ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_STOCK_BARCODE", STOCK_BARCODE);
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID和锁定库存标志获得可以下达下架任务的库存主表序列
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="STORAGE_LOCK_FLAG"></param>
        /// <returns></returns>
        public IList<STORAGE_MAIN> GetListByPickStationIdAndFlagForManageDown(int PICK_STATION_ID, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_PICK_STATION_ID_STORAGE_LOCK_FLAG", ht);
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID和锁定库存标志获得可以下达下架任务的库存主表序列
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="STORAGE_LOCK_FLAG"></param>
        /// <returns></returns>
        public IList<STORAGE_MAIN> GetListByPickPositionIdAndFlagForManageDown(int PICK_POSITION_ID, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_PICK_POSITION_ID_STORAGE_LOCK_FLAG", ht);
        }

        public IList<STORAGE_MAIN> GetListCellID(int CELL_ID)
        {
            return this.ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_CELL_ID", CELL_ID);
        }

        public IList<STORAGE_MAIN> GetListTemplateID(int TEMPLATE_ID)
        {
            return this.ExecuteQueryForList<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_TEMPLATE_ID", TEMPLATE_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(STORAGE_MAIN storage_main)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("STORAGE_MAIN");
                storage_main.STORAGE_ID = id;
            }

            return ExecuteInsert("STORAGE_MAIN_INSERT",storage_main);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(STORAGE_MAIN storage_main)
		{
			return ExecuteUpdate("STORAGE_MAIN_UPDATE",storage_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public STORAGE_MAIN GetModel(System.Int32 STORAGE_ID)
		{
			return ExecuteQueryForObject<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_ID",STORAGE_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public STORAGE_MAIN GetModelCellIDStockBarcode(System.Int32 CELL_ID, string STOCK_BARCODE)
        {
            Hashtable ht = new Hashtable();

            ht.Add("CELL_ID", CELL_ID);

            ht.Add("STOCK_BARCODE", STOCK_BARCODE);

            return this.ExecuteQueryForObject<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_CELL_ID_STOCK_BARCODE", ht);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public STORAGE_MAIN GetModelStockBarcode(string STOCK_BARCODE)
        {
            return this.ExecuteQueryForObject<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_STOCK_BARCODE", STOCK_BARCODE);
        }

        public STORAGE_MAIN GetModelCellID(int CELL_ID)
        {
            return this.ExecuteQueryForObject<STORAGE_MAIN>("STORAGE_MAIN_SELECT_BY_CELL_ID", CELL_ID);
        }


		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 STORAGE_ID)
		{
			return ExecuteDelete("STORAGE_MAIN_DELETE",STORAGE_ID);
		}
		

	}
}
