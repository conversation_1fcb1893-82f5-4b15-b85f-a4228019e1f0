﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_ITEM
	/// </summary>
	public class P_SYS_ITEM : P_Base_House
	{
		public P_SYS_ITEM ()
		{
			//
			// TODO: 此处添加SYS_ITEM的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_ITEM> GetList()
		{
			return ExecuteQueryForList<SYS_ITEM>("SYS_ITEM_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_ITEM sys_item)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_ITEM");
                sys_item.ITEM_ID = id;
            }

            return ExecuteInsert("SYS_ITEM_INSERT",sys_item);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_ITEM sys_item)
		{
			return ExecuteUpdate("SYS_ITEM_UPDATE",sys_item);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_ITEM GetModel(System.Int32 ITEM_ID)
		{
			return ExecuteQueryForObject<SYS_ITEM>("SYS_ITEM_SELECT_BY_ID",ITEM_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 ITEM_ID)
		{
			return ExecuteDelete("SYS_ITEM_DELETE",ITEM_ID);
		}
		

	}
}
