﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucTreeView.xaml 的交互逻辑
    /// </summary>
    public partial class ucTreeView : UserControl
    {
        public delegate void U_ItemSelectedChangedHandler(TreeViewItem itemSelected);
        public event U_ItemSelectedChangedHandler U_ItemSelectedChanged;

        public delegate void U_ItemCheckedChangedHandler(string Header);
        public event U_ItemCheckedChangedHandler U_ItemCheckedChanged;

        #region     ------自定义属性

        bool boolAllowChecked = false;
        string _tableName = string.Empty;
        string _ItemHeaderColumnName = string.Empty;
        string _ItemTagColumnName = string.Empty;
        string _ItemParentColumn = string.Empty;
        string _ItemOrderColumn = string.Empty;
        string _QueryFilter = string.Empty;

        /// <summary>
        /// 控件描述
        /// </summary>
        public string U_Header
        {
            set { this.grpBoxHeader.Header = string.Format(this.grpBoxHeader.Tag.ToString(),value); }
        }

        /// <summary>
        /// 是否允许显示复选框
        /// </summary>
        public bool U_AllowCheck
        {
            get { return boolAllowChecked; }
            set { boolAllowChecked = value;  }
        }
        
        /// <summary>
        /// 数据库表名
        /// </summary>
        public string U_TableName
        {
            get { return _tableName; }
        }

        /// <summary>
        /// 树控件Header属性的关联字段
        /// </summary>
        public string U_ItemHeaderColumnName
        {
            get { return _ItemHeaderColumnName; }
        }

        /// <summary>
        /// 树控件Tag属性的关联字段
        /// </summary>
        public string U_ItemTagColumnName
        {
            get { return _ItemTagColumnName; }
        }

        /// <summary>
        /// 关联上级节点属性的字段
        /// </summary>
        public string U_ItemParentColumn
        {
            get { return _ItemParentColumn; }
        }

        /// <summary>
        /// 树控件节点排序字段
        /// </summary>
        public string U_ItemOrderColumn
        {
            get { return _ItemOrderColumn; }
        }

        /// <summary>
        /// 树控件筛选条件
        /// </summary>
        public string U_QueryFilter
        {
            get { return _QueryFilter; }
        }

        /// <summary>
        /// 选中节点
        /// </summary>
        public TreeViewItem U_SelectedItem
        {
            get { return tvwList.SelectedItem as TreeViewItem; }
        }

        #endregion

        public ucTreeView()
        {
            InitializeComponent();
        }

        #region     ------查询节点

        /// <summary>
        /// 查询项目
        /// </summary>
        private void btnQuery_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(txtQuickQuery.Text)) return;

            //获得节点列表
            IEnumerable<TreeViewItem> list = this.tvwList.Items.Cast<TreeViewItem>();
            TreeViewItem itemFinded = FindItem(null,list, this.txtQuickQuery.Text);
            if (itemFinded != null)
            {
                itemFinded.IsSelected = true;

                //展开节点
                if (itemFinded.Parent != null && (itemFinded.Parent as TreeViewItem) != null)
                {
                    (itemFinded.Parent as TreeViewItem).IsExpanded = true;
                }
            }
        }

        /// <summary>
        /// 回车键，查询节点
        /// </summary>
        private void txtQuickQuery_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnQuery_Click(null, null);
            }
        }

        /// <summary>
        /// 根据查询条件获得节点
        /// </summary>
        private TreeViewItem FindItem(TreeViewItem itemParent,IEnumerable<TreeViewItem> list, string ItemHeader)
        {
            TreeViewItem itemFinded = null;
            if (itemParent == null)
            {
                if (list.Count(r => r.Header.ToString().Contains(ItemHeader)) > 0)
                {
                    itemFinded = list.First(r => r.Header.ToString().Contains(ItemHeader));
                    return itemFinded;
                }               
            }
            
            foreach (TreeViewItem item in list)
            {
                list = item.Items.Cast<TreeViewItem>();
                if (list.Count(r => r.Header.ToString().Contains(ItemHeader)) > 0)
                {
                    itemFinded = list.First(r => r.Header.ToString().Contains(ItemHeader));
                    return itemFinded;
                }
                else
                {
                    list = item.Items.Cast<TreeViewItem>();
                    itemFinded = FindItem(item, list,ItemHeader);
                }
            }
            return itemFinded;
        }

        #endregion

        /// <summary>
        /// 加载树控件列表
        /// </summary>
        public void U_LoadTreeViewItems(TreeViewItem ParentItem,string TableName, string ItemHeaderColumn, string ItemTagColumn, string QueryFilter, string OrderColumn)
        {
            this._tableName = TableName;
            this._ItemHeaderColumnName = ItemHeaderColumn;
            this._ItemTagColumnName = ItemTagColumn;
            this._QueryFilter = QueryFilter;
            this._ItemOrderColumn = OrderColumn;

            //查询语句
            string strSql = string.Format("SELECT {0}, {1} FROM {2} WHERE {3}{4}",
                                          ItemHeaderColumn,
                                          ItemTagColumn,
                                          TableName,
                                          string.IsNullOrEmpty(_QueryFilter) ? "1=1" : QueryFilter,
                                          string.IsNullOrEmpty(OrderColumn) ? string.Empty : string.Format(" ORDER BY {0}", OrderColumn));

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //清空节点
                if (ParentItem == null)
                    this.tvwList.Items.Clear();
                else
                    ParentItem.Items.Clear();

                using (DataTable tableSource = MainApp.I_DatabaseService.GetList(strSql))
                {
                    foreach (DataRow rowSource in tableSource.Rows)
                    {
                        TreeViewItem item = new TreeViewItem();
                        item.Style = (Style)this.Resources["styleTreeViewItem"];
                        item.Header = rowSource[ItemHeaderColumn].ToString();
                        item.Tag = rowSource[ItemTagColumn];

                        if (ParentItem == null)
                            this.tvwList.Items.Add(item);
                        else
                            ParentItem.Items.Add(item);
                    }

                    ////默认选中第一个节点
                    //if (this.tvwList.HasItems)
                    //{
                    //    (this.tvwList.Items[0] as TreeViewItem).IsSelected = true;
                    //}
                }

                this.txtQuickQuery.Focus();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 加载树控件列表
        /// </summary>
        public void U_LoadTreeViewItems(string RootNodeHeader,object RootNodeTag,string TableName,string ItemHeaderColumn,string ItemTagColumn,string ItemParentColumn,string ParentItemTagValue,string QueryFilter,string OrderColumn)
        {
            this._tableName = TableName;
            this._ItemHeaderColumnName = ItemHeaderColumn;
            this._ItemTagColumnName = ItemTagColumn;
            this._ItemParentColumn = ItemParentColumn;
            this._QueryFilter = QueryFilter;
            this._ItemOrderColumn = OrderColumn;
            
            //查询语句
            string strSql = string.Format("SELECT {0},{1},{2}{5} FROM {3} WHERE {4}",
                                          ItemHeaderColumn,
                                          ItemTagColumn,
                                          ItemParentColumn,
                                          TableName,
                                          string.IsNullOrEmpty(_QueryFilter) ? "1=1" : QueryFilter,
                                          string.IsNullOrEmpty(OrderColumn)?string.Empty:string.Format(",{0}",OrderColumn));

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //清空节点
                this.tvwList.Items.Clear();

                //添加根级节点
                TreeViewItem rootItem = this.U_AddTreeViewItem(null, RootNodeHeader, RootNodeTag);
                using (DataTable tableSource = MainApp.I_DatabaseService.GetList(strSql))
                {
                    this.AddTreeViewItem(tableSource, rootItem, ParentItemTagValue);

                    ////默认选中第一个节点
                    //if (this.tvwList.HasItems)
                    //{
                    //    (this.tvwList.Items[0] as TreeViewItem).IsSelected = true;
                    //}
                }
                //展开节点
                rootItem.IsExpanded = true;
                this.txtQuickQuery.Focus();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 添加树节点
        /// </summary>
        private void AddTreeViewItem(DataTable tabeSource, TreeViewItem ParentItem, string ParentItemTagValue)
        {
            //查询视图
            DataView dataView = new DataView(tabeSource);
            dataView.RowFilter = string.Format("{0}='{1}'", this._ItemParentColumn, ParentItemTagValue);
            dataView.Sort = this._ItemOrderColumn;

            //添加节点
            TreeViewItem itemNew = null;
            foreach (DataRowView view in dataView)
            {
                itemNew = new TreeViewItem();
                //itemNew.Header = view[this._ItemHeaderColumnName].ToString();
                itemNew.Header = view[0].ToString();
                itemNew.Tag = view[this._ItemTagColumnName];
                if (ParentItem == null)
                    this.tvwList.Items.Add(itemNew);
                else
                    ParentItem.Items.Add(itemNew);

                //设置样式和模板
                itemNew.Style = (Style)this.Resources["styleTreeViewItem"];
                if (this.boolAllowChecked)
                {
                    itemNew.Template = (ControlTemplate)this.Resources["templateCheckBoxTreeViewItem"];
                }

                //添加子节点
                AddTreeViewItem(tabeSource, itemNew,itemNew.Tag.ToString());
            }     
        }

        /// <summary>
        /// 添加节点
        /// </summary>
        public TreeViewItem U_AddTreeViewItem(TreeViewItem ParentItem,string Header,object Tag)
        {
            TreeViewItem itemNew = null;
            try
            {
                itemNew = new TreeViewItem();
                itemNew.Header = Header;
                itemNew.Tag = Tag;
                if (ParentItem == null)
                    this.tvwList.Items.Add(itemNew);
                else
                    ParentItem.Items.Add(itemNew);

                //设置样式和模板
                itemNew.Style = (Style)this.Resources["styleTreeViewItem"];
                if (this.boolAllowChecked)
                {
                    itemNew.Template = (ControlTemplate)this.Resources["templateCheckBoxTreeViewItem"];
                }

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            return itemNew;
        }

        /// <summary>
        /// 节点更改
        /// </summary>
        private void tvwList_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (!this.boolAllowChecked)
            {
                if (this.U_ItemSelectedChanged != null)
                {
                    TreeViewItem item = this.tvwList.SelectedItem as TreeViewItem;
                    this.U_ItemSelectedChanged(item);
                }
            }
        }

        //点击复选框，确认选择
        private void chkChecked_Click(object sender, RoutedEventArgs e)
        {
            //判断是否允许选中复选框
            if (this.boolAllowChecked)
            {
                CheckBox chkBox = e.OriginalSource as CheckBox;
                if (chkBox != null)
                {
                    StackPanel paneItem = chkBox.Parent as StackPanel;
                    if (paneItem != null)
                    {
                        TextBlock txtHeader = paneItem.Children[1] as TextBlock;
                        if (txtHeader != null)
                        {
                            if (this.U_ItemCheckedChanged != null)
                            {
                                this.U_ItemCheckedChanged(txtHeader.Text);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获得选中的节点查询条件
        /// </summary>
        public string U_GetItemsQueryWhere()
        {
            string strWhere = null;

            //判断是否复选框
            if (this.boolAllowChecked)
            {
                foreach (TreeViewItem item in tvwList.Items)
                {
                    StackPanel panelItem = item.TemplatedParent as StackPanel;
                    if (panelItem != null)
                    {
                        CheckBox chkBox = panelItem.Children.Cast<FrameworkElement>().First(r => r is CheckBox) as CheckBox;
                        if (chkBox.IsChecked == true)
                        {
                            strWhere = (string.IsNullOrEmpty(strWhere) ? string.Format("{0}='{1}'", _ItemTagColumnName, chkBox.Tag.ToString()) : string.Format(" OR {0}='{1}'", _ItemTagColumnName, chkBox.Tag.ToString()));
                        }
                    }
                }
            }
            else
            { 
                //选择当前的选定的项目
                if (this.tvwList.SelectedItem != null)
                {
                    strWhere = string.Format("{0}='{1}'", _ItemTagColumnName,(this.tvwList.SelectedItem as TreeViewItem).Tag.ToString());
                }
            }
            return strWhere;
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void U_Clear()
        {
            this.tvwList.Items.Clear();
        }
    }
}
