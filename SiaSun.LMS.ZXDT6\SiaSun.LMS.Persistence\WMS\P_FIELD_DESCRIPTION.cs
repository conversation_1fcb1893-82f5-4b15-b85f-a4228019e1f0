﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// FIELD_DESCRIPTION
    /// </summary>
    public class P_FIELD_DESCRIPTION : P_Base
    {
        public P_FIELD_DESCRIPTION()
        {
            //
            // TODO: 此处添加FLOW_ACTION的构造函数
            //
        }

        /// <summary>
        /// 获得实例
        /// </summary>
        /// <param name="Column">列名</param>
        /// <param name="DbType">数据类型</param>
        /// <param name="Header">显示标题</param>
        /// <param name="FieldType">控件类型</param>
        /// <param name="DefaultValue">默认值</param>
        /// <param name="Editable">是否只读</param>
        /// <param name="Valid">校验提示消息</param>
        /// <param name="Key">关键字</param>
        /// <param name="Remark">备注</param>
        /// <param name="Index">序号</param>
        public FIELD_DESCRIPTION GetModel(string Column, string DbType, string Header, string ControlType, string DefaultValue, int ReadOnly, string Validation, string DataBind, string Remark, int Order)
        {
            SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION = new FIELD_DESCRIPTION();
            mFIELD_DESCRIPTION.Column = Column;
            mFIELD_DESCRIPTION.DbType = DbType;
            mFIELD_DESCRIPTION.Header = Header;
            mFIELD_DESCRIPTION.ControlType = ControlType;
            mFIELD_DESCRIPTION.DefaultValue = DefaultValue;
            mFIELD_DESCRIPTION.ReadOnly = ReadOnly;
            mFIELD_DESCRIPTION.Validation = Validation;
            mFIELD_DESCRIPTION.DataBind = DataBind;
            mFIELD_DESCRIPTION.Remark = Remark;
            mFIELD_DESCRIPTION.Order = Order;
            return mFIELD_DESCRIPTION;
        }
    }
}
