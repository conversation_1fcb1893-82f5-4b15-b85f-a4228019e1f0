# 测试修复后的盘点结果回调接口
# PowerShell 测试脚本

Write-Host "=== 测试修复后的盘点结果回调接口 ===" -ForegroundColor Green

# 测试数据
$testData = @{
    stockTakeId = "84748e90507646b98cf3ea46017e2f7a"
    stockTakeName = "20250619盘点计划"
    stockTakeCode = "PDRW20250619055"
    stockTakeResultGoodsList = @(
        @{
            inventoryId = "a88dd2765e46497bb014d81e448f5879"
            stockTakeTaskId = "749b237528a5497083b1071e5ebe12ba"
            stockTakeTaskName = "20250619盘点计划-盘点任务"
            stockTakeTaskCode = "PDRW20250619055"
            stockTakeResultId = "6b91fbfe4e224d64a7db8bf59ef08b76"
            goodsId = "977f5b63ba01445cbb83a137015bc10c"
            goodsCode = "0102090100114001"
            goodsName = "牵引电机加油嘴"
            goodsVersion = "加油嘴（6号线）"
            storageNum = 150
            lockNum = $null
            stockTakeNum = 150
            stockTakeOverNum = $null
            stockTakeFloorNum = $null
            unitId = "55927cb0062a491db40d9eb4a6edac9f"
            unitName = "个"
            brand = "博士"
            warehouseId = "bbd71e62c4294eb1a8374a82d4f53c23"
            warehouseName = "徐铁总库"
            warehouseCode = "XZDT00001"
            shelfId = "52ce757dff3a4c849ec1a725d9719eea"
            shelfName = "机电部货架0001"
            shelfCode = "YY-JDB0001"
            stockTakeUserId = "03d08490a23e46d2b20f669126d72dc6"
            stockTakeUserName = "刘为"
            stockTakePlanStartDate = "2025-06-19"
            stockTakePlanEndDate = "2025-06-27"
            createDate = "2025-06-19 16:31:28"
            createUser = ""
            createName = ""
            updateDate = "2025-06-19 16:31:28"
            updateUser = ""
            updateName = ""
            id = "0d0c0476d1104331838ceb989f95ba87"
            status = $null
            billCode = $null
        }
    )
}

# 转换为JSON
$jsonData = $testData | ConvertTo-Json -Depth 10

Write-Host "测试数据:" -ForegroundColor Yellow
Write-Host $jsonData

# 调用接口
try {
    Write-Host "`n开始调用盘点结果回调接口..." -ForegroundColor Yellow
    
    # 这里需要根据实际的测试环境调用方式进行调整
    # 例如通过HTTP API或直接调用DLL
    
    Write-Host "接口调用完成，请查看日志文件获取详细信息" -ForegroundColor Green
    
    # 显示可能的日志文件位置
    $logPaths = @(
        ".\Logs\",
        ".\bin\Debug\Logs\",
        ".\bin\Release\Logs\"
    )
    
    Write-Host "`n请检查以下位置的日志文件:" -ForegroundColor Cyan
    foreach ($path in $logPaths) {
        if (Test-Path $path) {
            Write-Host "  - $path" -ForegroundColor White
            $logFiles = Get-ChildItem $path -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
            foreach ($file in $logFiles) {
                Write-Host "    $($file.Name) (修改时间: $($file.LastWriteTime))" -ForegroundColor Gray
            }
        }
    }
    
} catch {
    Write-Host "调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
