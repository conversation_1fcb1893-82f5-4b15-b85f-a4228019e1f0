﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="MANAGE_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="MANAGE_LIST" type="SiaSun.LMS.Model.MANAGE_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="MANAGE_LIST">
			<result property="MANAGE_LIST_ID" column="manage_list_id" />
			<result property="STORAGE_LIST_ID" column="storage_list_id" />
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="MANAGE_ID" column="manage_id" />
			<result property="GOODS_ID" column="goods_id" />
			<result property="MANAGE_LIST_QUANTITY" column="manage_list_quantity" />
			<result property="MANAGE_LIST_REMARK" column="manage_list_remark" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_PROPERTY1" column="goods_property1" />
			<result property="GOODS_PROPERTY2" column="goods_property2" />
			<result property="GOODS_PROPERTY3" column="goods_property3" />
			<result property="GOODS_PROPERTY4" column="goods_property4" />
			<result property="GOODS_PROPERTY5" column="goods_property5" />
			<result property="GOODS_PROPERTY6" column="goods_property6" />
			<result property="GOODS_PROPERTY7" column="goods_property7" />
			<result property="GOODS_PROPERTY8" column="goods_property8" />
      <result property="STORAGE_LOCK_ID" column="storage_lock_id" />
      <result property="DETAIL_FLAG" column="detail_flag" />
      <result property="GOODS_PROPERTY9" column="goods_property9" />
      <result property="GOODS_PROPERTY10" column="goods_property10" />
      <result property="GOODS_PROPERTY11" column="goods_property11" />
      <result property="GOODS_PROPERTY12" column="goods_property12" />
      <result property="GOODS_PROPERTY13" column="goods_property13" />
      <result property="GOODS_PROPERTY14" column="goods_property14" />
      <result property="GOODS_PROPERTY15" column="goods_property15" />
      <result property="GOODS_PROPERTY16" column="goods_property16" />
      <result property="GOODS_PROPERTY17" column="goods_property17" />
      <result property="GOODS_PROPERTY18" column="goods_property18" />
      <result property="GOODS_PROPERTY19" column="goods_property19" />
      <result property="GOODS_PROPERTY20" column="goods_property20" />
      <result property="GOODS_PROPERTY21" column="goods_property21" />
      <result property="GOODS_PROPERTY22" column="goods_property22" />
      <result property="GOODS_PROPERTY23" column="goods_property23" />
      <result property="GOODS_PROPERTY24" column="goods_property24" />
      <result property="GOODS_PROPERTY25" column="goods_property25" />
      <result property="GOODS_PROPERTY26" column="goods_property26" />
      <result property="GOODS_PROPERTY27" column="goods_property27" />
      <result property="GOODS_PROPERTY28" column="goods_property28" />
      <result property="GOODS_PROPERTY29" column="goods_property29" />
      <result property="GOODS_PROPERTY30" column="goods_property30" />
      <result property="STORAGE_LIST_ID_SPLIT_SOURCE" column="storage_list_id_split_source" />
      <result property="BACKUP_FIELD1" column="backup_field1" />
      <result property="BACKUP_FIELD2" column="backup_field2" />
      <result property="BACKUP_FIELD3" column="backup_field3" />
      <result property="BACKUP_FIELD4" column="backup_field4" />
      <result property="BACKUP_FIELD5" column="backup_field5" />

    </resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="MANAGE_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        manage_list_id,
        storage_list_id,
        plan_list_id,
        manage_id,
        goods_id,
        manage_list_quantity,
        manage_list_remark,
        box_barcode,
        goods_property1,
        goods_property2,
        goods_property3,
        goods_property4,
        goods_property5,
        goods_property6,
        goods_property7,
        goods_property8,
        storage_lock_id,
        detail_flag,
        goods_property9,
        goods_property10,
        goods_property11,
        goods_property12,
        goods_property13,
        goods_property14,
        goods_property15,
        goods_property16,
        goods_property17,
        goods_property18,
        goods_property19,
        goods_property20,
        goods_property21,
        goods_property22,
        goods_property23,
        goods_property24,
        goods_property25,
        goods_property26,
        goods_property27,
        goods_property28,
        goods_property29,
        goods_property30,
        storage_list_id_split_source,
        backup_field1,
        backup_field2,
        backup_field3,
        backup_field4,
        backup_field5
        From MANAGE_LIST
      </select>
		
		<select id="MANAGE_LIST_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="MANAGE_LIST_SELECT_BY_BOX_BARCODE" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          box_barcode=#BOX_BARCODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_LIST_SELECT_BY_MANAGE_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_LIST_SELECT_BY_STORAGE_LOCK_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_LIST_SELECT_BY_PLAN_LIST_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id=#PLAN_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="MANAGE_LIST_INSERT" parameterClass="MANAGE_LIST">
      Insert Into MANAGE_LIST (
      manage_list_id,
      storage_list_id,
      plan_list_id,
      manage_id,
      goods_id,
      manage_list_quantity,
      manage_list_remark,
      box_barcode,
      goods_property1,
      goods_property2,
      goods_property3,
      goods_property4,
      goods_property5,
      goods_property6,
      goods_property7,
      goods_property8,
      storage_lock_id,
      detail_flag,
      goods_property9,
      goods_property10,
      goods_property11,
      goods_property12,
      goods_property13,
      goods_property14,
      goods_property15,
      goods_property16,
      goods_property17,
      goods_property18,
      goods_property19,
      goods_property20,
      goods_property21,
      goods_property22,
      goods_property23,
      goods_property24,
      goods_property25,
      goods_property26,
      goods_property27,
      goods_property28,
      goods_property29,
      goods_property30,
      storage_list_id_split_source,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      )Values(
      #MANAGE_LIST_ID#,
      #STORAGE_LIST_ID#,
      #PLAN_LIST_ID#,
      #MANAGE_ID#,
      #GOODS_ID#,
      #MANAGE_LIST_QUANTITY#,
      #MANAGE_LIST_REMARK#,
      #BOX_BARCODE#,
      #GOODS_PROPERTY1#,
      #GOODS_PROPERTY2#,
      #GOODS_PROPERTY3#,
      #GOODS_PROPERTY4#,
      #GOODS_PROPERTY5#,
      #GOODS_PROPERTY6#,
      #GOODS_PROPERTY7#,
      #GOODS_PROPERTY8#,
      #STORAGE_LOCK_ID#,
      #DETAIL_FLAG#,
      #GOODS_PROPERTY9#,
      #GOODS_PROPERTY10#,
      #GOODS_PROPERTY11#,
      #GOODS_PROPERTY12#,
      #GOODS_PROPERTY13#,
      #GOODS_PROPERTY14#,
      #GOODS_PROPERTY15#,
      #GOODS_PROPERTY16#,
      #GOODS_PROPERTY17#,
      #GOODS_PROPERTY18#,
      #GOODS_PROPERTY19#,
      #GOODS_PROPERTY20#,
      #GOODS_PROPERTY21#,
      #GOODS_PROPERTY22#,
      #GOODS_PROPERTY23#,
      #GOODS_PROPERTY24#,
      #GOODS_PROPERTY25#,
      #GOODS_PROPERTY26#,
      #GOODS_PROPERTY27#,
      #GOODS_PROPERTY28#,
      #GOODS_PROPERTY29#,
      #GOODS_PROPERTY30#,
      #STORAGE_LIST_ID_SPLIT_SOURCE#,
      #BACKUP_FIELD1#,
      #BACKUP_FIELD2#,
      #BACKUP_FIELD3#,
      #BACKUP_FIELD4#,
      #BACKUP_FIELD5#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="MANAGE_LIST_UPDATE" parameterClass="MANAGE_LIST">
      Update MANAGE_LIST Set
      <!--manage_list_id=#MANAGE_LIST_ID#,-->
      storage_list_id=#STORAGE_LIST_ID#,
      plan_list_id=#PLAN_LIST_ID#,
      manage_id=#MANAGE_ID#,
      goods_id=#GOODS_ID#,
      manage_list_quantity=#MANAGE_LIST_QUANTITY#,
      manage_list_remark=#MANAGE_LIST_REMARK#,
      box_barcode=#BOX_BARCODE#,
      goods_property1=#GOODS_PROPERTY1#,
      goods_property2=#GOODS_PROPERTY2#,
      goods_property3=#GOODS_PROPERTY3#,
      goods_property4=#GOODS_PROPERTY4#,
      goods_property5=#GOODS_PROPERTY5#,
      goods_property6=#GOODS_PROPERTY6#,
      goods_property7=#GOODS_PROPERTY7#,
      goods_property8=#GOODS_PROPERTY8#,
      storage_lock_id=#STORAGE_LOCK_ID#,
      detail_flag=#DETAIL_FLAG#,
      goods_property9=#GOODS_PROPERTY9#,
      goods_property10=#GOODS_PROPERTY10#,
      goods_property11=#GOODS_PROPERTY11#,
      goods_property12=#GOODS_PROPERTY12#,
      goods_property13=#GOODS_PROPERTY13#,
      goods_property14=#GOODS_PROPERTY14#,
      goods_property15=#GOODS_PROPERTY15#,
      goods_property16=#GOODS_PROPERTY16#,
      goods_property17=#GOODS_PROPERTY17#,
      goods_property18=#GOODS_PROPERTY18#,
      goods_property19=#GOODS_PROPERTY19#,
      goods_property20=#GOODS_PROPERTY20#,
      goods_property21=#GOODS_PROPERTY21#,
      goods_property22=#GOODS_PROPERTY22#,
      goods_property23=#GOODS_PROPERTY23#,
      goods_property24=#GOODS_PROPERTY24#,
      goods_property25=#GOODS_PROPERTY25#,
      goods_property26=#GOODS_PROPERTY26#,
      goods_property27=#GOODS_PROPERTY27#,
      goods_property28=#GOODS_PROPERTY28#,
      goods_property29=#GOODS_PROPERTY29#,
      goods_property30=#GOODS_PROPERTY30#,
      storage_list_id_split_source=#STORAGE_LIST_ID_SPLIT_SOURCE#,
      backup_field1=#BACKUP_FIELD1#,
      backup_field2=#BACKUP_FIELD2#,
      backup_field3=#BACKUP_FIELD3#,
      backup_field4=#BACKUP_FIELD4#,
      backup_field5=#BACKUP_FIELD5#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="MANAGE_LIST_DELETE" parameterClass="int">
			Delete From MANAGE_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="MANAGE_LIST_DELETE_MANAGE_ID" parameterClass="int">
      Delete From MANAGE_LIST
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>