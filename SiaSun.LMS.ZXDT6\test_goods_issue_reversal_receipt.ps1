# Test script for GoodsIssueReversalReceiptSync interface
# This script tests the 出库红冲单 (Goods Issue Reversal Receipt) interface

Write-Host "Testing GoodsIssueReversalReceiptSync interface..." -ForegroundColor Green

# Read the test JSON data
$testData = Get-Content "test_goods_issue_reversal_receipt.json" -Raw

# Display the test data
Write-Host "Test JSON data:" -ForegroundColor Yellow
Write-Host $testData

# Here you would typically make an HTTP POST request to test the interface
# For now, we'll just validate the JSON structure
try {
    $jsonObject = $testData | ConvertFrom-Json
    Write-Host "JSON validation: PASSED" -ForegroundColor Green
    
    # Validate required fields
    $requiredFields = @("outGoodsDate", "reason", "warehouseId", "operatorId", "redOutDetailList")
    $missingFields = @()
    
    foreach ($field in $requiredFields) {
        if (-not $jsonObject.$field) {
            $missingFields += $field
        }
    }
    
    if ($missingFields.Count -eq 0) {
        Write-Host "Required fields validation: PASSED" -ForegroundColor Green
    } else {
        Write-Host "Required fields validation: FAILED" -ForegroundColor Red
        Write-Host "Missing fields: $($missingFields -join ', ')" -ForegroundColor Red
    }
    
    # Validate redOutDetailList structure
    if ($jsonObject.redOutDetailList -and $jsonObject.redOutDetailList.Count -gt 0) {
        Write-Host "redOutDetailList validation: PASSED" -ForegroundColor Green
        Write-Host "Detail items count: $($jsonObject.redOutDetailList.Count)" -ForegroundColor Cyan
        
        # Check first detail item for required fields
        $detailRequiredFields = @("goodsCode", "goodsName", "outStorageNum", "unitId", "reason")
        $detailMissingFields = @()
        
        $firstDetail = $jsonObject.redOutDetailList[0]
        foreach ($field in $detailRequiredFields) {
            if (-not $firstDetail.$field) {
                $detailMissingFields += $field
            }
        }
        
        if ($detailMissingFields.Count -eq 0) {
            Write-Host "Detail item required fields validation: PASSED" -ForegroundColor Green
        } else {
            Write-Host "Detail item required fields validation: FAILED" -ForegroundColor Red
            Write-Host "Missing detail fields: $($detailMissingFields -join ', ')" -ForegroundColor Red
        }
    } else {
        Write-Host "redOutDetailList validation: FAILED - Empty or missing" -ForegroundColor Red
    }
    
} catch {
    Write-Host "JSON validation: FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed." -ForegroundColor Green