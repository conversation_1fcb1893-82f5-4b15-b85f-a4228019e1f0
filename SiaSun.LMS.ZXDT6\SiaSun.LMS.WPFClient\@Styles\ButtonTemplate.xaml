﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  鼠标进入时动画显示  -->
    <Storyboard x:Key="flashMouseEnter" AutoReverse="True">
        <ThicknessAnimation
            Storyboard.TargetProperty="Margin"
            From="0"
            To="2" />
    </Storyboard>

    <!--  确认按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonConfirm" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/ok.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="确认" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  加载按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonLoad" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/feed_add.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="加载" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  添加按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonAdd" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/add.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="添加" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  编辑按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonEdit" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/edit.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="编辑" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  保存按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonSave" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/save.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="保存" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  删除按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonDelete" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/delete.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="删除" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  取消按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonCancel" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/cancel.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="取消" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  刷新按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonUpdate" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/refresh.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="刷新" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  查询按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonSearch" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Margin="1"
                Source="/@Images/search.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="查询" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  打印按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonPrint" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/printer.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="打印" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  导出Excel按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonExportExcel" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/export_excel.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="导出Excel" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  明细按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonDetail" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/list.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="显示明细" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  首页按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonFirst" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/first.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="首页" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  前一页按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonBack" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/back.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="前一页" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  下一页按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonNext" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/next.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="下一页" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  末页按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonLast" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/last.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="末页" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  实际尺寸按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonZoom" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/trimsize.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="实际尺寸" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  放大按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonZoomOut" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/zoom_out.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="缩小" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--  缩小按钮样式  -->
    <ControlTemplate x:Key="templateImageButtonZoomIn" TargetType="Button">
        <StackPanel Margin="1,0,5,0" Orientation="Horizontal">
            <Image
                Width="16"
                Height="16"
                Source="/@Images/zoom_in.png" />
            <TextBlock
                VerticalAlignment="Center"
                Foreground="{TemplateBinding Button.Foreground}"
                Text="{TemplateBinding Button.Content}"
                ToolTip="{TemplateBinding Button.Content}" />
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="缩小" />
            </Trigger>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard Name="storyEnterBtn" Storyboard="{StaticResource flashMouseEnter}" />
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <StopStoryboard BeginStoryboardName="storyEnterBtn" />
            </EventTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

</ResourceDictionary>