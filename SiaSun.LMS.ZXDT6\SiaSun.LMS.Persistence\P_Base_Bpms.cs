﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using IBatisNet.DataMapper;
using System.Collections;
using System.Data;
using IBatisNet.DataMapper.Configuration;

namespace SiaSun.LMS.Persistence
{
    public class P_Base_Bpms : P_Base
    {
        public new static ISqlMapper _sqlMap;

        public P_Base_Bpms()
        {
            if (_sqlMap == null)
            {
                if (_sqlMap == null)
                {
                    DomSqlMapBuilder builder = new DomSqlMapBuilder();
                    _sqlMap = builder.Configure("BpmsMap.config");
                }
            }

            base._sqlMap = _sqlMap;
        }



    
    }
}