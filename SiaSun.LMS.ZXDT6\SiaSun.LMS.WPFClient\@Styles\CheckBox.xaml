<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="CheckBoxFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle
                            Margin="15,0,0,0"
                            Stroke="#60000000"
                            StrokeDashArray="1 2"
                            StrokeThickness="1" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type CheckBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource CheckBoxFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource CheckBoxTemplate}" />
    </Style>

    <ControlTemplate x:Key="CheckBoxTemplate" TargetType="{x:Type CheckBox}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <ColorAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[1].(GradientStop.Color)"
                    To="#FFF28A27"
                    Duration="00:00:00.1000000" />
                <ColorAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[0].(GradientStop.Color)"
                    To="#FFF4D9BE"
                    Duration="00:00:00.1000000" />
                <DoubleAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
                <DoubleAnimation
                    Storyboard.TargetName="BackgroundFill"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <ColorAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[1].(GradientStop.Color)"
                    To="#FFFDDA81"
                    Duration="00:00:00.4000000" />
                <ColorAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[0].(GradientStop.Color)"
                    To="#FFFCE7AF"
                    Duration="00:00:00.4000000" />
                <DoubleAnimation
                    Storyboard.TargetName="BoxOver"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
                <DoubleAnimation
                    Storyboard.TargetName="BackgroundFill"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="DisabledOn">
                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="DisabledVisualElement"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>

            </Storyboard>
            <Storyboard x:Key="CheckedOn">
                <DoubleAnimation
                    Storyboard.TargetName="BoxPress"
                    Storyboard.TargetProperty="Opacity"
                    To="1"
                    Duration="00:00:00.1000000" />
                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="CheckIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="CheckedOff">
                <DoubleAnimation
                    Storyboard.TargetName="BoxPress"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="CheckIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.4000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="IndeterminateOn">

                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="IndeterminateIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="IndeterminateOff">

                <ObjectAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="IndeterminateIcon"
                    Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusedVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusedVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <BulletDecorator Background="Transparent">
            <BulletDecorator.Bullet>
                <Grid>
                    <Rectangle
                        x:Name="Background"
                        Width="13"
                        Height="13"
                        Margin="1"
                        Fill="{DynamicResource CheckBoxBackgroundBrush}"
                        RadiusX="0"
                        RadiusY="0"
                        Stroke="{DynamicResource CheckBoxBorderBrush}"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="BoxFill"
                        Width="9"
                        Height="9"
                        Fill="{DynamicResource CheckBoxInnerBoxBackgroundBrush}"
                        RadiusX="0"
                        RadiusY="0"
                        Stroke="{DynamicResource CheckBoxInnerBoxBorderBrush}"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="BackgroundFill"
                        Width="13"
                        Height="13"
                        Margin="1"
                        Fill="{DynamicResource CheckBoxBackgroundFillBrush}"
                        Opacity="0"
                        RadiusX="0"
                        RadiusY="0"
                        Stroke="#FF5577A3"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="BoxOver"
                        Width="9"
                        Height="9"
                        Margin="3"
                        Fill="{DynamicResource CheckBoxMouseOverBrush}"
                        Opacity="0"
                        RadiusX="0"
                        RadiusY="0"
                        StrokeThickness="1">
                        <Rectangle.Stroke>
                            <LinearGradientBrush StartPoint="0.886,0.808" EndPoint="0.055,0.119">
                                <GradientStop Color="#FFFCE7AF" />
                                <GradientStop Offset="1" Color="#FFFDDA81" />
                            </LinearGradientBrush>
                        </Rectangle.Stroke>
                    </Rectangle>
                    <Rectangle
                        x:Name="BoxPress"
                        Width="9"
                        Height="9"
                        Margin="3"
                        Opacity="0"
                        RadiusX="0"
                        RadiusY="0"
                        Stroke="{DynamicResource CheckBoxPressBorderBrush}"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="BoxGradient"
                        Width="7"
                        Height="7"
                        Fill="{DynamicResource CheckBoxInnerBoxGradientBrush}"
                        RadiusX="0"
                        RadiusY="0"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="IndeterminateIcon"
                        Width="5"
                        Height="2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Fill="{DynamicResource GlyphBrush}"
                        Visibility="Collapsed" />
                    <Path
                        x:Name="CheckIcon"
                        Width="7"
                        Height="9"
                        Margin="0,3.333,3.833,0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Data="M103.78572,598.96112 L105.09846,597.5661 L107.00806,600.16229 C107.00806,600.16229 109.91004,592.74463 109.91004,592.74463 C109.91004,592.74463 111.74678,593.79761 111.74678,593.79761 C111.74678,593.79761 107.88566,602.75848 107.88566,602.75848 L106.60118,602.75848 z"
                        Fill="{DynamicResource GlyphBrush}"
                        Stretch="Fill"
                        Visibility="Collapsed" />
                    <Rectangle
                        x:Name="FocusedVisualElement"
                        Opacity="0"
                        RadiusX="0"
                        RadiusY="0"
                        Stroke="{DynamicResource FocusBrush}"
                        StrokeThickness="1" />
                    <Rectangle
                        x:Name="DisabledVisualElement"
                        Margin="1"
                        Fill="{DynamicResource DisabledBackgroundBrush}"
                        RadiusX="0"
                        RadiusY="0"
                        Visibility="Collapsed" />
                </Grid>
            </BulletDecorator.Bullet>
            <ContentPresenter
                x:Name="contentPresenter"
                Grid.Column="1"
                Margin="{TemplateBinding Padding}"
                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                Content="{TemplateBinding Content}"
                ContentTemplate="{TemplateBinding ContentTemplate}" />
        </BulletDecorator>

        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="FocusedOn_BeginStoryboard" Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsChecked" Value="True" />
                    <Condition Property="IsThreeState" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard x:Name="CheckedOff_BeginStoryboard" Storyboard="{StaticResource CheckedOff}" />
                </MultiTrigger.EnterActions>
                <MultiTrigger.ExitActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard" Storyboard="{StaticResource CheckedOn}" />
                </MultiTrigger.ExitActions>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsChecked" Value="{x:Null}" />
                    <Condition Property="IsThreeState" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource IndeterminateOn}" />
                </MultiTrigger.EnterActions>
                <MultiTrigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource IndeterminateOff}" />
                </MultiTrigger.ExitActions>
                <Setter TargetName="CheckIcon" Property="Opacity" Value="0" />
            </MultiTrigger>
            <Trigger Property="IsChecked" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard2" Storyboard="{StaticResource CheckedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard1" Storyboard="{StaticResource CheckedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOn}" />
                </Trigger.EnterActions>
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
</ResourceDictionary>