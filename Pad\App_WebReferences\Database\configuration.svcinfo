﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_I_Database&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_I_Database" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://127.0.0.1:8002/Service/Database&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_I_Database&quot; contract=&quot;Database.I_Database&quot; name=&quot;BasicHttpBinding_I_Database&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://127.0.0.1:8002/Service/Database&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_I_Database&quot; contract=&quot;Database.I_Database&quot; name=&quot;BasicHttpBinding_I_Database&quot; /&gt;" contractName="Database.I_Database" name="BasicHttpBinding_I_Database" />
  </endpoints>
</configurationSnapshot>