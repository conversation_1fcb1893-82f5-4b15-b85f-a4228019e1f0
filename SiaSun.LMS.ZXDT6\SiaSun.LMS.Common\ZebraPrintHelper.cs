﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Drawing.Printing;
using System.Drawing;
using System.Windows.Forms;

namespace SiaSun.LMS.Common
{
    //使用方法
    //第一步 构建打印信息，每一个PrintInfo代表一个目标文档的一个位置，把所有打印信息添加到List中
    //List<PrintInfo> lsPrintInfo = new List<PrintInfo>(); 
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 2), Size = 8, TextContent = string.Format("货位：{0}", dr["LOCK_DEVICE_CODE"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(30, 2), Size = 8, TextContent = string.Format("油漆：{0}", dr["GOODS_CONST_PROPERTY8"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 6), Size = 8, TextContent = string.Format("数量：{0}", dr["QUANTITY"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 10), Size = 8, TextContent = string.Format("编码：{0}", dr["GOODS_CODE"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 14), Size = 8, TextContent = string.Format("名称：{0}", dr["GOODS_NAME"].ToString().Substring(0, dr["GOODS_NAME"].ToString().IndexOf('\\'))) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 26), Size = 8, TextContent = string.Format("规格：{0}", dr["GOODS_NAME"].ToString().Substring(dr["GOODS_NAME"].ToString().IndexOf('\\'), dr["GOODS_NAME"].ToString().Length - dr["GOODS_NAME"].ToString().IndexOf('\\'))) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 22), Size = 8, TextContent = string.Format("单号：{0}", dr["PLAN_CODE"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(45, 22), Size = 8, TextContent = string.Format("配送工位：{0}", dr["GOODS_PROPERTY21"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 18), Size = 8, TextContent = string.Format("工序编码：{0}", dr["GOODS_PROPERTY18"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(45, 18), Size = 8, TextContent = string.Format("库存地点：{0}", dr["LOCATION_CODE"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 30), Size = 8, TextContent = string.Format("配盘方案：{0}", dr["GOODS_PROPERTY24"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.Text, Start = new System.Drawing.Point(2, 34), Size = 8, TextContent = string.Format("备注：{0}", dr["PLAN_REMARK"]) });
    //lsPrintInfo.Add(new PrintInfo() { PageNum = i, PrtType = PrintType.BarcodeImage, Start = new System.Drawing.Point(60, 2), Size = 10, QRImage = new Bitmap(new QRCodeEncoder().Encode(string.Format("{0}||{1}||{2}||{3}||{4}", dr["SUPPLIER"], dr["GOODS_CODE"], dr["GOODS_PROPERTY4"], dr["ODO_SN"], dr["MANAGE_LIST_ID"])), 65, 65) });

    //第二步 显示打印选型对话框（可选）
    //System.Windows.Forms.PrintDialog printDialog = new System.Windows.Forms.PrintDialog();
    //if (printDialog.ShowDialog() == System.Windows.Forms.DialogResult.Cancel)
    //{
    //    return;
    //}

    //第三步 实例化ZebraPrintHelper类，传入标签大小、边距和第一步构建的PrintInfo列表
    //ZebraPrintHelper printHelper = new ZebraPrintHelper(80, 40, 2, 2, lsPrintInfo);

    //第四步 调用Print方法打印
    //printHelper.Print();

    public class ZebraPrintHelper
    {
        private PrintDocument printDocument;
        private List<PrintInfo> printInfos;
        private int handleCount = 0;


        public ZebraPrintHelper(int width_p, int height_p, int margin_lr, int margin_tb, List<PrintInfo> printInfos)
        {
            printDocument = new PrintDocument();
            this.printInfos = printInfos;

            PrintDialog printDialog = new PrintDialog();
            printDialog.ShowDialog();

            PrinterSettings printSetting = new PrinterSettings();
            printSetting.PrintRange = PrintRange.AllPages;

            if (!string.IsNullOrEmpty(printDialog.PrinterSettings.PrinterName))
            {
                printSetting.PrinterName = printDialog.PrinterSettings.PrinterName;
            }

            int width_in = MM2Inch(width_p);
            int height_in = MM2Inch(height_p);
            PageSettings pageSetting = new PageSettings(printSetting);
            pageSetting.PaperSize = new PaperSize("customer", width_in, height_in);
            
            int margin_lr_in = MM2Inch(margin_lr);
            int margin_tb_in = MM2Inch(margin_tb);
            pageSetting.Margins = new Margins(margin_lr_in, margin_lr_in, margin_tb_in, margin_tb_in);
            printDocument.DefaultPageSettings = pageSetting;

            printDocument.PrintPage += PrintDocument_PrintPage;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (this.printInfos != null && this.printInfos.Count > 0)
            {
                Graphics g = e.Graphics;
                g.PageScale = 1;
                g.PageUnit = GraphicsUnit.Millimeter;//单位

                //foreach (PrintInfo p in this.printInfos)
                //{
                //    switch (p.PrtType)
                //    {
                //        case PrintType.Text:
                //            Font tFont = new Font("Arial", p.Size, FontStyle.Bold);
                //            Brush b = new SolidBrush(Color.Black);
                //            g.DrawString(p.TextContent, tFont, b, p.Start);
                //            break;
                //        case PrintType.BarcodeImage:
                //            g.DrawImage(p.QRImage, p.Start);
                //            break;
                //        default:
                //            break;
                //    }
                //}

                int currentPageNum = this.printInfos.Skip(handleCount).Min(r => r.PageNum);

                foreach (var printInfo in this.printInfos.Where(r => r.PageNum == currentPageNum))
                {
                    switch (printInfo.PrtType)
                    {
                        case PrintType.Text:
                            Font tFont = new Font("Arial", printInfo.Size, FontStyle.Bold);
                            Brush b = new SolidBrush(Color.Black);
                            g.DrawString(printInfo.TextContent, tFont, b, printInfo.Start);
                            break;
                        case PrintType.BarcodeImage:
                            g.DrawImage(printInfo.QRImage, printInfo.Start);
                            break;
                        default:
                            break;
                    }

                    handleCount++;
                }

                e.HasMorePages = printInfos.Count != handleCount;
            }
        }

        public void Print()
        {
            this.printDocument.Print();
        }

        private int MM2Inch(int mm)
        {
            return (int)(mm * 100.0f / 25.4f);
        }
    }

    public struct PrintInfo
    {
        public int PageNum { get; set; }
        public PrintType PrtType { get; set; }
        public Point Start { get; set; }
        public int Size { get; set; }
        public string TextContent { get; set; }
        public Image QRImage { get; set; }
    }

    public enum PrintType
    {
        Text = 0,
        BarcodeImage = 2,
    }

}
