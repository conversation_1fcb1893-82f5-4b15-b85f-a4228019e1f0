<?xml version="1.0"?>
<sqlMapConfig  xmlns="http://ibatis.apache.org/dataMapper" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <settings>
		<setting useStatementNamespaces="false"/>
	</settings>
  
  <!--<database>	  
    <provider name="oracleClient1.0"/>
    <dataSource name="SqlMap" connectionString="Data Source=SSWMS;User ID=**************;Password=**************"/>
  </database>-->

  <database>
    <provider name="sqlServer2.0"/>
    <!--<dataSource name="SqlMap" connectionString="server=127.0.0.1;database=JXFST_BPMS;uid=sa;pwd=***"/>-->
    <dataSource name="SqlMap" connectionString="server=172.28.1.8;database=HK_XS_DB;uid=sa;pwd=abc+***"/>
  </database>

  <sqlMaps>
    <sqlMap resource="Map\MapBase.xml" />

    <sqlMap resource="Map\BPMS\TB_TRAYINFO_CHECK.xml" />
    <sqlMap resource="Map\BPMS\TB_TRAYINFO_UPLOAD.xml" />
  </sqlMaps>
	
</sqlMapConfig>
