﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// BarCodePlanarDialog.xaml 的交互逻辑
    /// </summary>
    public partial class BarCodePlanarDialog : Window
    {
        PrintDialog printDiag = new PrintDialog();
        List<string> listPlanarBarCode = new List<string>();
        int intManageID = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public BarCodePlanarDialog(string Header,string Data)
        {
            InitializeComponent();

            this.txtBarCode.Text = Data;
            this.ucBarCode.U_Header = Header;
            this.ucBarCode.U_Data = Data;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public BarCodePlanarDialog(int MANAGE_ID, string Header, string Data)
            : this(<PERSON><PERSON>,Data)
        {
            this.intManageID = MANAGE_ID;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            if (this.intManageID > 0)
            {
                //加载打印条码值
                this.LoadPlanarBarCode();
            }
            else
            {
                if (!string.IsNullOrEmpty(this.txtBarCode.Text))
                {
                    this.listPlanarBarCode.Add(this.txtBarCode.Text);
                }
            }
            this.lblCount.Text = this.listPlanarBarCode.Count.ToString();
        }
        
        /// <summary>
        /// 获得组合的二维码值
        /// </summary>
        private void LoadPlanarBarCode()
        {
            //try
            //{
            //    using (DataTable tableManageList = MainApp.I_ManageService.V_MANAGE_LIST_GetTable(this.intManageID))
            //    {
            //        //获得配置文件的内容
            //        string strPlanarBarCode = SiaSun.LMS.Common.AppSettings.GetValue("PlanarBarCode");
            //        foreach (DataRow rowManageList in tableManageList.Rows)
            //        {
            //            //获得物料信息
            //            Model.GOODS_MAIN mGOODS_MAIN = MainApp.I_StorageService.GOODS_MAIN_GetModel(Convert.ToInt32(rowManageList["GOODS_ID"]));
            //            if (mGOODS_MAIN != null)
            //            {
            //                Dictionary<string, string> dicList = new Dictionary<string, string>();

            //                //获得物料属性
            //                string strGoodsProperty = rowManageList["GOODS_PROPERTY"].ToString();
            //                //获得属性
            //                IDictionary<string, string> dic = MainApp.I_SystemService.SYS_SPLIT_PROPERTY_GetPropertyList("GOODS_TYPE", mGOODS_MAIN.GOODS_TYPE_ID.ToString(), strGoodsProperty);
            //                foreach (string strKey in strPlanarBarCode.Split('|'))
            //                {
            //                    if (!string.IsNullOrEmpty(strKey))
            //                    {
            //                        if (dic.ContainsKey(strKey))
            //                        {
            //                            dicList.Add(strKey, dic[strKey]);
            //                        }
            //                        else
            //                        {
            //                            if (strKey.ToUpper() == "DATE")
            //                            {
            //                                dicList.Add(strKey, SiaSun.LMS.Common.StringUtil.GetDate());
            //                            }
            //                        }
            //                    }
            //                }

            //                //获得批号
            //                string strGoodsNo = rowManageList["GOODS_NO"].ToString();
            //                //添加到列表
            //                dicList.Add("PH", strGoodsNo);

            //                //解析格式
            //                string strPlanarBarCodeValue = null;
            //                foreach (string strKey in strPlanarBarCode.Split('|'))
            //                {
            //                    if (string.IsNullOrEmpty(strPlanarBarCodeValue))
            //                        strPlanarBarCodeValue = (dicList.ContainsKey(strKey)? dicList[strKey] : string.Empty) ;
            //                    else
            //                        strPlanarBarCodeValue += "&" + (dicList.ContainsKey(strKey) ? dicList[strKey] : string.Empty);
            //                }

            //                //添加到条码列表中
            //                if (!string.IsNullOrEmpty(strPlanarBarCodeValue))
            //                {
            //                    this.listPlanarBarCode.Add(strPlanarBarCodeValue);
            //                    //显示数量
            //                    this.lblCount.Text = listPlanarBarCode.Count.ToString();
            //                }
            //            }
            //        }

            //        //显示第一个
            //        if (this.listPlanarBarCode.Count > 0)
            //        {
            //            this.txtBarCode.Text = listPlanarBarCode[0];
            //        }
            //    }
            //}
            //catch (Exception ex)
            //{
            //    MainApp._MessageDialog.ShowException(ex);
            //}
        }

        //打印
        private void btnPrintAll_Click(object sender, RoutedEventArgs e)
        {
            if (this.printDiag.ShowDialog() == true)
            {
                if (this.intManageID <= 0)
                {
                    listPlanarBarCode = new List<string>();
                    listPlanarBarCode.Add(this.txtBarCode.Text);
                    this.ucBarCode.U_PrintBarCode(this.printDiag,listPlanarBarCode);
                }

                //打印
                if (this.listPlanarBarCode.Count > 0)
                {
                    this.ucBarCode.U_PrintBarCode(this.printDiag,listPlanarBarCode);
                }
            }
        }
    }
}
