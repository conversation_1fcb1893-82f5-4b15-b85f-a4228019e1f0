using System;
using SiaSun.LMS.Persistence;
using log4net;
using System.Collections.Generic;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "Log4Net.config", Watch = true)]
namespace SiaSun.LMS.Implement
{
    public sealed class S_Base
    {
        private S_Base()
        {
            WMSInterface.Add("GoodsInfoSync", new Interface.WMS.GoodsInfoSync());
            WMSInterface.Add("PalletTransportRequest", new Interface.WMS.PalletTransportRequest());
            WMSInterface.Add("UnitInfoSync", new Interface.WMS.UnitInfoSync());
            WMSInterface.Add("WarehouseInfoSync", new Interface.WMS.WarehouseInfoSync());
            WMSInterface.Add("OrganizationalStructureInfoSync", new Interface.WMS.OrganizationalStructureInfoSync());
            WMSInterface.Add("ShelfSpaceSync", new Interface.WMS.ShelfSpaceSync());
            WMSInterface.Add("InboundReceiptSync", new Interface.WMS.InboundReceiptSync());
            WMSInterface.Add("InventoryReversalReceiptSync", new Interface.WMS.InventoryReversalReceiptSync());
            WMSInterface.Add("OutboundReceiptSync", new Interface.WMS.OutboundReceiptSync());
            WMSInterface.Add("GoodsIssueSync", new Interface.WMS.GoodsIssueSync());
            WMSInterface.Add("GoodsIssueReversalReceiptSync", new Interface.WMS.GoodsIssueReversalReceiptSync());
            WMSInterface.Add("InventoryPlanSync", new Interface.WMS.InventoryPlanSync());
        }

        private static readonly Lazy<S_Base> lazy = new Lazy<S_Base>(() => new S_Base());
        public static S_Base sBase { get => lazy.Value; }

        #region [Log]

        public ILog Log { get; } = LogManager.GetLogger("ImplementLog");

        public ILog ClientLog = log4net.LogManager.GetLogger("ClientLog");     //WPF�ͻ�����־

        //public ILog PdaLog { get; } = LogManager.GetLogger("PdaLogFileAppender");

        #endregion

        #region [Implement]

        public S_Interface sInterface { get; } = new S_Interface();
        public S_Manage sManage { get; } = new S_Manage();
        public S_Device sDevice { get; } = new S_Device();
        public S_Plan sPlan { get; } = new S_Plan();
        public S_System sSystem { get; } = new S_System();
        public S_Database sDatabase { get; } = new S_Database();

        public Dictionary<string, Interface.InterfaceBase> WMSInterface { get; } = new Dictionary<string, Interface.InterfaceBase>();

        #endregion

        #region [Persistence]

        public P_FIELD_DESCRIPTION pFIELD_DESCRIPTION { get; } = new P_FIELD_DESCRIPTION();
        public P_FLOW_ACTION pFLOW_ACTION { get; } = new P_FLOW_ACTION();
        public P_FLOW_NODE pFLOW_NODE { get; } = new P_FLOW_NODE();
        public P_FLOW_PARA pFLOW_PARA { get; } = new P_FLOW_PARA();
        public P_FLOW_TYPE pFLOW_TYPE { get; } = new P_FLOW_TYPE();
        public P_GOODS_CLASS pGOODS_CLASS { get; } = new P_GOODS_CLASS();
        public P_GOODS_MAIN pGOODS_MAIN { get; } = new P_GOODS_MAIN();
        public P_GOODS_TEMPLATE pGOODS_TEMPLATE { get; } = new P_GOODS_TEMPLATE();
        public P_GOODS_TEMPLATE_LIST pGOODS_TEMPLATE_LIST { get; } = new P_GOODS_TEMPLATE_LIST();
        public P_GOODS_TYPE pGOODS_TYPE { get; } = new P_GOODS_TYPE();
        public P_GOODS_PROPERTY pGOODS_PROPERTY { get; } = new P_GOODS_PROPERTY();
        public P_IO_CONTROL pIO_CONTROL { get; } = new P_IO_CONTROL();
        public P_IO_CONTROL_APPLY pIO_CONTROL_APPLY { get; } = new P_IO_CONTROL_APPLY();
        public P_IO_CONTROL_APPLY_HIS pIO_CONTROL_APPLY_HIS { get; } = new P_IO_CONTROL_APPLY_HIS();
        public P_IO_CONTROL_ROUTE pIO_CONTROL_ROUTE { get; } = new P_IO_CONTROL_ROUTE();
        public P_RECORD_DETAIL pRECORD_DETAIL { get; } = new P_RECORD_DETAIL();
        public P_RECORD_LIST pRECORD_LIST { get; } = new P_RECORD_LIST();
        public P_RECORD_MAIN pRECORD_MAIN { get; } = new P_RECORD_MAIN();
        public P_MANAGE_DETAIL pMANAGE_DETAIL { get; } = new P_MANAGE_DETAIL();
        public P_MANAGE_LIST pMANAGE_LIST { get; } = new P_MANAGE_LIST();
        public P_MANAGE_MAIN pMANAGE_MAIN { get; } = new P_MANAGE_MAIN();
        public P_MANAGE_TYPE pMANAGE_TYPE { get; } = new P_MANAGE_TYPE();
        public P_MANAGE_TYPE_PARAM pMANAGE_TYPE_PARAM { get; } = new P_MANAGE_TYPE_PARAM();
        public P_PLAN_DETAIL pPLAN_DETAIL { get; } = new P_PLAN_DETAIL();
        public P_PLAN_LIST pPLAN_LIST { get; } = new P_PLAN_LIST();
        public P_PLAN_MAIN pPLAN_MAIN { get; } = new P_PLAN_MAIN();
        public P_PLAN_TYPE pPLAN_TYPE { get; } = new P_PLAN_TYPE();
        public P_STORAGE_DETAIL pSTORAGE_DETAIL { get; } = new P_STORAGE_DETAIL();
        public P_STORAGE_LIST pSTORAGE_LIST { get; } = new P_STORAGE_LIST();
        public P_STORAGE_MAIN pSTORAGE_MAIN { get; } = new P_STORAGE_MAIN();
        public P_STORAGE_LOCK pSTORAGE_LOCK { get; } = new P_STORAGE_LOCK();
        public P_SYS_ITEM pSYS_ITEM { get; } = new P_SYS_ITEM();
        public P_SYS_ITEM_LIST pSYS_ITEM_LIST { get; } = new P_SYS_ITEM_LIST();
        public P_SYS_MENU pSYS_MENU { get; } = new P_SYS_MENU();
        public P_SYS_ROLE pSYS_ROLE { get; } = new P_SYS_ROLE();
        public P_SYS_ROLE_WINDOW pSYS_ROLE_WINDOW { get; } = new P_SYS_ROLE_WINDOW();
        public P_SYS_USER pSYS_USER { get; } = new P_SYS_USER();
        public P_SYS_LOG pSYS_LOG { get; } = new P_SYS_LOG();
        public P_SYS_TABLE_CONVERTER pSYS_TABLE_CONVERTER { get; } = new P_SYS_TABLE_CONVERTER();
        public P_SYS_TABLE_CONVERTER_LIST pSYS_TABLE_CONVERTER_LIST { get; } = new P_SYS_TABLE_CONVERTER_LIST();
        public P_SYS_RELATION pSYS_RELATION { get; } = new P_SYS_RELATION();
        public P_SYS_RELATION_LIST pSYS_RELATION_LIST { get; } = new P_SYS_RELATION_LIST();
        public P_WH_AREA pWH_AREA { get; } = new P_WH_AREA();
        public P_WH_CELL pWH_CELL { get; } = new P_WH_CELL();
        public P_WH_DESCRIPTION pWH_DESCRIPTION { get; } = new P_WH_DESCRIPTION();
        public P_WH_LOGIC pWH_LOGIC { get; } = new P_WH_LOGIC();
        public P_WH_WAREHOUSE pWH_WAREHOUSE { get; } = new P_WH_WAREHOUSE();
        public P_LED_MAIN pLED_MAIN { get; } = new P_LED_MAIN();
        public P_LED_LIST pLED_LIST { get; } = new P_LED_LIST();
        public P_LCD_MAIN pLCD_MAIN { get; } = new P_LCD_MAIN();
        public P_LCD_LIST pLCD_LIST { get; } = new P_LCD_LIST();
        public P_APPLY_TYPE pAPPLY_TYPE { get; } = new P_APPLY_TYPE();
        public P_TB_TRAYINFO_UPLOAD pTB_TRAYINFO_UPLOAD { get; } = new P_TB_TRAYINFO_UPLOAD();
        public P_TB_TRAYINFO_CHECK pTB_TRAYINFO_CHECK { get; } = new P_TB_TRAYINFO_CHECK();
        public P_INTERFACE_QUEUE pINTERFACE_QUEUE { get; } = new P_INTERFACE_QUEUE();


        #endregion
    }
}
