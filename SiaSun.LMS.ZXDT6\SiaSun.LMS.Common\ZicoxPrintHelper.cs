﻿using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace SiaSun.LMS.Common
{
    public class ZicoxPrintHelper
    {
        Socket socket = null;
        string ip = string.Empty;

        public ZicoxPrintHelper(string ip)
        {
            this.ip = ip;
        }

        private void Connect()
        {
            IPEndPoint serverIp = new IPEndPoint(IPAddress.Parse(ip), 9100);
            socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            socket.Connect(serverIp);
        }

        private void DisConnect()
        {
            try
            {
                socket.Disconnect(true);
            }
            catch(Exception ex)
            {
                //阻止异常抛出
            }
        }

        private void Send(byte[] data)
        {
            socket.Send(data);
        }

        private void SendCommand(string command)
        {
            byte[] data = (from r in command.Split('|') select Convert.ToByte(r, 16)).ToArray();
            socket.Send(data);
        }

        private void SendContent(string message)
        {
            byte[] data = Encoding.Default.GetBytes(message);
            socket.Send(data);
        }

        private void SendBarcode(string barcode)
        {
            string barcodeCommand = string.Format(ESC.打印128条码, string.Join("|", Encoding.Default.GetBytes("{A" + barcode).Select(r => r.ToString("X2")).ToArray()));
            SendCommand(barcodeCommand);
        }

        private bool ReadStatus(out string message)
        {
            bool result = false;
            message = string.Empty;

            this.SendCommand(ESC.读取状态);

            var buff = new byte[4];
            if (socket.Receive(buff) > 0)
            {
                var status = buff[2];

                if(status == 0x00)
                {
                    result = true;
                }
                else
                {
                    result = false;
                    message = (0x01 & status) == 0x00 ? "有纸" : "缺纸";
                    message += (0x02 & status) == 0x00 ? "|合盖" : "|开盖";
                    message += (0x04 & status) == 0x00 ? "|温度正常" : "|机芯过热";
                    message += (0x08 & status) == 0x00 ? "|电量未报警" : "|电量过低";
                    message += (0x10 & status) == 0x00 ? "|未打印状态" : "|打印状态";
                }
            }
            else
            {
                result = false;
                message = "读取失败";
            }

            return result;
        }


        public bool PrintCartonPickBarcode(string countInfo ,string stockBarcode,string goodsName, string sendCity ,string receiver, string expressCorp, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                Connect();

                result = ReadStatus(out message);
                if (!result)
                {
                    message = $"打印机状态有误_信息[{message}]";
                    return result;
                }

                SendCommand(ESC.复位);
                SendCommand(ESC.左边距2F);
                SendContent(countInfo);
                SendCommand(ESC.走纸1行);
                //SendCommand(ESC.走纸1行);
                SendCommand(ESC.左边距2F);
                SendCommand(ESC.居左);
                SendCommand(ESC.条码文字_下方);
                SendCommand(ESC.条码文字_大);
                SendCommand(ESC.条码宽度_小);
                SendCommand(ESC.条码高度_中);
                SendBarcode(stockBarcode);
                SendCommand(ESC.居左);
                SendContent($"物料:{goodsName}");
                SendCommand(ESC.走纸1行);
                SendContent($"城市:{(string.IsNullOrEmpty(sendCity) ? "-":sendCity)}-物流公司:{expressCorp}");
                SendCommand(ESC.走纸1行);
                SendContent($"收货人:{receiver}");
                SendCommand(ESC.走纸1页);

                //result = ReadStatus(out message);
                //if (!result)
                //{
                //    message = $"打印机状态有误_信息[{message}]";
                //    return result;
                //}
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);                
            }
            finally
            {
                DisConnect();
            }

            return result;
        }
    }

    public struct ESC
    {
        public const string 复位 = "1B|40";
        public const string 读取状态 = "1D|99";
        public const string 走纸1行 = "0A";
        public const string 走纸1页 = "0C";

        public const string 居中 = "1B|61|01";
        public const string 居左 = "1B|61|00";
        public const string 左边距2F = "1D|4C|2F|00";
        public const string 字符间距30 = "1B|20|30";
        public const string 制表符 = "09";

        public const string 字号_3倍 = "1D|21|22";
        public const string 字号_默认 = "1D|21|00";
        public const string 粗体 = "1B|45|01";

        public const string 条码高度_小 = "1D|68|2F";
        public const string 条码高度_中 = "1D|68|6F";
        public const string 条码高度_大 = "1D|68|8F";
        public const string 条码宽度_小 = "1D|77|02";
        public const string 条码宽度_中 = "1D|77|03";
        public const string 条码宽度_大 = "1D|77|04";
        public const string 条码文字_下方 = "1D|48|02";
        public const string 条码文字_上方 = "1D|48|01";
        public const string 条码文字_大 = "1D|66|00";
        public const string 条码文字_小 = "1D|66|01";
        public const string 打印128条码 = "1D|6B|08|{0}|00";

    }
}
