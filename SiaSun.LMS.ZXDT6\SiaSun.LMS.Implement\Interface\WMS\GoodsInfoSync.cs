﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 4.16 同步物料信息接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class GoodsInfoSync : InterfaceBase
    {
        class InputParam
        {
            public string daLeiId { get; set; }
            public string daLeiName { get; set; }
            public string xiaoLeiId { get; set; }
            public string xiao<PERSON>eiName { get; set; }
            public string daZuId { get; set; }
            public string daZuName { get; set; }
            public string xiaoZuId { get; set; }
            public string xiaoZuName { get; set; }
            public int goodsStatus { get; set; }
            public string code { get; set; }
            public string parentId { get; set; }
            public string parentName { get; set; }
            /// <summary>
            /// 物资名称（GOODS_NAME）
            /// </summary>
            public string name { get; set; }
            public string goodsType { get; set; }
            public string goodsAttribute { get; set; }
            /// <summary>
            /// 计量单位ID（GOODS_CONST_PROPERTY3）
            /// </summary>
            public string unitId { get; set; }
            /// <summary>
            /// 计量单位名称（GOODS_UNIT）
            /// </summary>
            public string unitName { get; set; }
            /// <summary>
            /// 规格型号（GOODS_CONST_PROPERTY2）
            /// </summary>
            public string goodsVersion { get; set; }
            public string goodsClass { get; set; }
            /// <summary>
            /// 物资编码（GOODS_CODE）
            /// </summary>
            public string goodsCode { get; set; }
            public decimal suggestedAmount { get; set; }
            public int isLaborInsuranceMaterials { get; set; }
            public bool ignoreParent { get; set; }
            public string fixedAssetFlag { get; set; }
            public string professionalAssetFlag { get; set; }
            public UnitInformationEntityDTO unitInformationEntityDTO { get; set; }
            public List<BrandVO> brandVOs { get; set; }
            public string isControlledByProductDate { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class UnitInformationEntityDTO
        {
            /// <summary>
            /// 物资ID（GOODS_CONST_PROPERTY1)
            /// </summary>
            public string goodsId { get; set; }
            public List<ConversionDetailVO> conversionDetailVOs { get; set; }
            public int grossWeight { get; set; }
            public int netWeight { get; set; }
            public int unitHeight { get; set; }
            public int unitWide { get; set; }
            public int unitLength { get; set; }
            public string unit { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class ConversionDetailVO
        {
            public string conversionRatio { get; set; }
            public string conversionUnit { get; set; }
        }

        class BrandVO
        {
            /// <summary>
            /// 品牌（GOODS_CONST_PROPERTY4）
            /// </summary>
            public string brandName { get; set; }
            public string purchasesNum { get; set; }
            public string initialPutawayTime { get; set; }
            public int inboundQuantity { get; set; }
            public string remark { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                // JSON parsing
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    resultCode = 2;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Required field validation
                if (string.IsNullOrEmpty(inputParam.goodsCode))
                {
                    resultCode = 2;
                    message = "接口入参必填项[goodsCode]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    resultCode = 2;
                    message = "接口入参必填项[name]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.goodsType))
                {
                    resultCode = 2;
                    message = "接口入参必填项[goodsType]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.code))
                {
                    resultCode = 2;
                    message = "接口入参必填项[code]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate goods status (should be 0 or 1)
                if (inputParam.goodsStatus != 0 && inputParam.goodsStatus != 1)
                {
                    resultCode = 2;
                    message = $"接口入参物资状态字段[goodsStatus]值{inputParam.goodsStatus}有误，应为0或1";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate fixed asset flag
                if (!string.IsNullOrEmpty(inputParam.fixedAssetFlag) && !ZeroOne.Contains(inputParam.fixedAssetFlag))
                {
                    resultCode = 2;
                    message = $"接口入参固定资产标识字段[fixedAssetFlag]值{inputParam.fixedAssetFlag}有误，应为0或1";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate professional asset flag
                if (!string.IsNullOrEmpty(inputParam.professionalAssetFlag) && !ZeroOne.Contains(inputParam.professionalAssetFlag))
                {
                    resultCode = 2;
                    message = $"接口入参专业资产标识字段[professionalAssetFlag]值{inputParam.professionalAssetFlag}有误，应为0或1";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate labor insurance materials flag
                if (inputParam.isLaborInsuranceMaterials != 0 && inputParam.isLaborInsuranceMaterials != 1)
                {
                    resultCode = 2;
                    message = $"接口入参劳保物资标识字段[isLaborInsuranceMaterials]值{inputParam.isLaborInsuranceMaterials}有误，应为0或1";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Check if goods already exists by goodsCode
                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(inputParam.goodsCode);
                if (goodsMain == null)
                {
                    // Create new goods
                    goodsMain = new Model.GOODS_MAIN()
                    {
                        GOODS_CLASS_ID = 1,
                        GOODS_CODE = inputParam.goodsCode,
                        GOODS_NAME = inputParam.name,
                        GOODS_FLAG = inputParam.goodsStatus.ToString(),
                        GOODS_UNITS = inputParam.unitName,
                        //GOODS_REMARK = BuildGoodsRemark(inputParam),
                        GOODS_CONST_PROPERTY1 = inputParam.unitInformationEntityDTO.goodsId, // 
                        GOODS_CONST_PROPERTY2 = inputParam.goodsVersion, // 规格型号
                        GOODS_CONST_PROPERTY3 = inputParam.unitId, // 计量单位ID
                        GOODS_CONST_PROPERTY4 = inputParam.brandVOs[0].brandName,//品牌
                        //GOODS_CONST_PROPERTY5 = inputParam.unitName, // 计量单位名称
                        ////GOODS_CONST_PROPERTY6 = inputParam.fixedAssetFlag, // 固定资产标识
                        //GOODS_CONST_PROPERTY8 = inputParam.isLaborInsuranceMaterials.ToString(), // 劳保物资标识
                    };

                    S_Base.sBase.pGOODS_MAIN.Add(goodsMain);
                    message = "物资信息创建成功";
                }
                else
                {
                    // Update existing goods
                    goodsMain.GOODS_CLASS_ID = 1;
                    goodsMain.GOODS_NAME = inputParam.name;
                    goodsMain.GOODS_FLAG = inputParam.goodsStatus.ToString();
                    goodsMain.GOODS_UNITS = inputParam.unitName;
                    //GOODS_REMARK = BuildGoodsRemark(inputParam),
                    goodsMain.GOODS_CONST_PROPERTY1 = inputParam.unitInformationEntityDTO.goodsId;
                    goodsMain.GOODS_CONST_PROPERTY2 = inputParam.goodsVersion; // 规格型号
                    goodsMain.GOODS_CONST_PROPERTY3 = inputParam.unitId; // 计量单位ID
                    goodsMain.GOODS_CONST_PROPERTY4 = inputParam.brandVOs[0].brandName;//品牌
                    S_Base.sBase.pGOODS_MAIN.Update(goodsMain);
                    message = "物资信息更新成功";
                }

                // Process nested unitInformationEntityDTO if provided
                if (inputParam.unitInformationEntityDTO != null)
                {
                    ProcessUnitInformation(inputParam.unitInformationEntityDTO, inputParam.goodsCode);
                }

                // Process nested brandVOs if provided
                if (inputParam.brandVOs != null && inputParam.brandVOs.Count > 0)
                {
                    ProcessBrandInformation(inputParam.brandVOs, inputParam.goodsCode);
                }

                // Log the operation
                S_Base.sBase.Log.Info($"GoodsInfoSync处理成功_入参[{inputJson}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"GoodsInfoSync处理异常_入参[{inputJson}]_异常[{ex.Message}]_traceId[{traceId}]");
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = resultCode,
                    msg = message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = code,
                msg = msg,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }

        private string BuildGoodsRemark(InputParam inputParam)
        {
            var remarkParts = new List<string>();
            
            if (!string.IsNullOrEmpty(inputParam.daLeiName))
                remarkParts.Add($"大类:{inputParam.daLeiName}");
            if (!string.IsNullOrEmpty(inputParam.xiaoLeiName))
                remarkParts.Add($"小类:{inputParam.xiaoLeiName}");
            if (!string.IsNullOrEmpty(inputParam.daZuName))
                remarkParts.Add($"大组:{inputParam.daZuName}");
            if (!string.IsNullOrEmpty(inputParam.xiaoZuName))
                remarkParts.Add($"小组:{inputParam.xiaoZuName}");
            if (!string.IsNullOrEmpty(inputParam.goodsClass))
                remarkParts.Add($"物资分类:{inputParam.goodsClass}");
            if (!string.IsNullOrEmpty(inputParam.parentName))
                remarkParts.Add($"父级:{inputParam.parentName}");

            return string.Join("|", remarkParts);
        }

        private void ProcessUnitInformation(UnitInformationEntityDTO unitInfo, string goodsCode)
        {
            try
            {
                // Store unit information in GOODS_MAIN extended properties or create separate unit records
                // This is a placeholder for unit information processing
                // In a real implementation, you might store this in a separate GOODS_UNIT table
                
                if (unitInfo.conversionDetailVOs != null && unitInfo.conversionDetailVOs.Count > 0)
                {
                    // Process conversion details
                    foreach (var conversion in unitInfo.conversionDetailVOs)
                    {
                        // Store conversion ratios - this would typically go to a separate table
                        S_Base.sBase.Log.Info($"处理单位换算信息_物资编码[{goodsCode}]_换算比率[{conversion.conversionRatio}]_换算单位[{conversion.conversionUnit}]");
                    }
                }

                S_Base.sBase.Log.Info($"处理单位信息成功_物资编码[{goodsCode}]_毛重[{unitInfo.grossWeight}]_净重[{unitInfo.netWeight}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理单位信息异常_物资编码[{goodsCode}]_异常[{ex.Message}]");
            }
        }

        private void ProcessBrandInformation(List<BrandVO> brands, string goodsCode)
        {
            try
            {
                // Process brand information
                // This would typically be stored in a separate GOODS_BRAND table
                foreach (var brand in brands)
                {
                    S_Base.sBase.Log.Info($"处理品牌信息_物资编码[{goodsCode}]_品牌[{brand.brandName}]_采购次数[{brand.purchasesNum}]_入库数量[{brand.inboundQuantity}]");
                }

                S_Base.sBase.Log.Info($"处理品牌信息成功_物资编码[{goodsCode}]_品牌数量[{brands.Count}]");
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"处理品牌信息异常_物资编码[{goodsCode}]_异常[{ex.Message}]");
            }
        }
    }
}
