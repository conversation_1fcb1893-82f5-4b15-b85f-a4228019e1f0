﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// PLAN_TYPE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class PLAN_TYPE
	{
		public PLAN_TYPE()
		{
			
		}
		
		private int _plan_type_id;
		private string _plan_type_code;
		private string _plan_type_name;
		private string _plan_type_group;
		private string _plan_type_inout;
		private string _plan_type_remark;
		private int _plan_type_order;
		private string _plan_type_flag;
		private string _plan_type_class;
		private string _manage_type_code;
		
		///<sumary>
		/// 计划类型编号
        ///</sumary>
        [DataMember]
		public int PLAN_TYPE_ID
		{
			get{return _plan_type_id;}
			set{_plan_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_CODE
		{
			get{return _plan_type_code;}
			set{_plan_type_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_NAME
		{
			get{return _plan_type_name;}
			set{_plan_type_name = value;}
		}
		///<sumary>
		/// 分组
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_GROUP
		{
			get{return _plan_type_group;}
			set{_plan_type_group = value;}
		}
		///<sumary>
		/// 出入库
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_INOUT
		{
			get{return _plan_type_inout;}
			set{_plan_type_inout = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_REMARK
		{
			get{return _plan_type_remark;}
			set{_plan_type_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int PLAN_TYPE_ORDER
		{
			get{return _plan_type_order;}
			set{_plan_type_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_FLAG
		{
			get{return _plan_type_flag;}
			set{_plan_type_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_CLASS
		{
			get{return _plan_type_class;}
            set { _plan_type_class = value; }
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CODE
		{
			get{return _manage_type_code;}
			set{_manage_type_code = value;}
		}
	}
}
