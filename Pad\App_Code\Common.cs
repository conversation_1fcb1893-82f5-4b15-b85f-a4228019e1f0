﻿using System;
using System.Data;
using System.Web.UI;

public class Common
{
    public static SystemService.I_SystemServiceClient _I_SystemService = new SystemService.I_SystemServiceClient();
    public static BaseService.I_BaseServiceClient _I_BaseService = new BaseService.I_BaseServiceClient();
    public static ManageService.I_ManageServiceClient _I_ManageService = new ManageService.I_ManageServiceClient();

    public static System.I_SystemClient _I_SystemClient = new System.I_SystemClient();
    public static Database.I_DatabaseClient _I_DatabaseClient = new Database.I_DatabaseClient();
    public static Manage.I_ManageClient _I_ManageClient = new Manage.I_ManageClient();

    public static DataTable NewDataTable(int iColumn)
    {
        DataTable dt = new DataTable();
        for (int i = 0; i < iColumn; ++i)
        {
            dt.Columns.Add("value" + (i + 1).ToString(), Type.GetType("System.String"));
        } 
        return dt;
    }

    public static void AddDataTable(DataTable dt, params string[] sValue)
    {
        DataRow dr = dt.NewRow();
        for (int i = 0; i < sValue.Length; ++i)
        {
            dr["value" + (i + 1).ToString()] = sValue[i];
        }
        dt.Rows.Add(dr);
    }

    public static void LayerSuccess(Page page, string content)
    {
        LayerAlert(page, true, "成功", content);
    }

    public static void LayerFailed(Page page, string content)
    {
        LayerAlert(page, false, "失败", content);
    }

    private static void LayerAlert(Page page, bool success, string title, string content)
    {
        page.ClientScript.RegisterStartupScript(page.GetType(), "LayerAlert",
            string.Format("layerAlert({0}, '{1}', '{2}');", success.ToString().ToLower(),
            title, content.Replace('\'', '\"').Replace("\n", "<br/>")), true);
    }
}