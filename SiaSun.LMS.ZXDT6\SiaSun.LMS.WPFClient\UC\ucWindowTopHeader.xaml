﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucWindowTopHeader"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="32" d:DesignWidth="300">
    <UserControl.Resources>
        
        <LinearGradientBrush x:Key="brushMiniMouseEnter" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="White" Offset="0"></GradientStop>
            <GradientStop Color="YellowGreen" Offset="1"></GradientStop>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="brushCloseMouseEnter" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="White" Offset="0"></GradientStop>
            <GradientStop Color="Red" Offset="1"></GradientStop>
        </LinearGradientBrush>
    </UserControl.Resources>
    <Border CornerRadius="2" BorderThickness="1" BorderBrush="Blue">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="DarkBlue" Offset="1"></GradientStop>
                <GradientStop Color="LightBlue" Offset="0.5"></GradientStop>
                <GradientStop Color="CornflowerBlue" Offset="0.5"></GradientStop>
                <GradientStop Color="DarkBlue" Offset="0"></GradientStop>
            </LinearGradientBrush>
        </Border.Background>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Image Grid.Column="0" VerticalAlignment="Center" Source="{Binding Path=Icon}"></Image>
            <TextBlock Text="{Binding Path=Title}" Grid.Column="1" Margin="3,0,0,0" VerticalAlignment="Center" FontSize="{Binding Path=FontSize}" FontWeight="Bold" Foreground="White"></TextBlock>
            
            <WrapPanel Grid.Column="2" VerticalAlignment="Top" Mouse.MouseDown="Path_MouseDown" Margin="1,1,2,0">
                <Path Name="pathMiniSize" Fill="YellowGreen" Stroke="White" StrokeThickness="2" Margin="1" VerticalAlignment="Center" ToolTip="最小化">
                    <Path.Effect>
                        <DropShadowEffect ShadowDepth="1"></DropShadowEffect>
                    </Path.Effect>
                    <Path.Data>
                        <GeometryGroup>
                            <RectangleGeometry Rect="0,0,20,20" RadiusX="2" RadiusY="2"> </RectangleGeometry>
                            <LineGeometry StartPoint="5,15" EndPoint="15,15"></LineGeometry>
                        </GeometryGroup>
                    </Path.Data>
                    <Path.Style>
                        <Style TargetType="Path">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Margin" Value="2"></Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Path.Style>
                </Path>
                
                <Path Name="pathClose" Fill="Red" Stroke="White" StrokeThickness="2" VerticalAlignment="Center" Margin="1" ToolTip="关闭">
                    <Path.Effect>
                        <DropShadowEffect ShadowDepth="1"></DropShadowEffect>
                    </Path.Effect>
                    <Path.Data>
                        <GeometryGroup>
                            <RectangleGeometry Rect="0,0,20,20" RadiusX="2" RadiusY="2" ></RectangleGeometry>
                            
                            <LineGeometry StartPoint="5,5" EndPoint="15,15"></LineGeometry>
                            <LineGeometry StartPoint="5,15" EndPoint="15,5"></LineGeometry>
                        </GeometryGroup>
                    </Path.Data>
                    <Path.Style>
                        <Style TargetType="Path">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Margin" Value="2"></Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Path.Style>
                </Path>
            </WrapPanel>
        </Grid>
    </Border>
</UserControl>
