﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_USER
	/// </summary>
	public class P_SYS_USER : P_Base_House
	{
		public P_SYS_USER ()
		{
			//
			// TODO: 此处添加SYS_USER的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_USER> GetList()
		{
			return ExecuteQueryForList<SYS_USER>("SYS_USER_SELECT",null);
		}

        public SiaSun.LMS.Model.SYS_USER Login(string USER_CODE, string USER_PASSWORD)
        {
            Hashtable ht = new Hashtable();

            ht.Add("USER_CODE", USER_CODE);

            ht.Add("USER_PASSWORD", USER_PASSWORD);

            SiaSun.LMS.Model.SYS_USER SYS_USER = new SiaSun.LMS.Model.SYS_USER();

            SYS_USER = this.ExecuteQueryForObject<SiaSun.LMS.Model.SYS_USER>("SYS_USER_LOGIN", ht);

            return SYS_USER;
        }
		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_USER sys_user)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_USER");
                sys_user.USER_ID = id;
            }

            return ExecuteInsert("SYS_USER_INSERT",sys_user);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_USER sys_user)
		{
			return ExecuteUpdate("SYS_USER_UPDATE",sys_user);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_USER GetModel(System.Int32 USER_ID)
		{
			return ExecuteQueryForObject<SYS_USER>("SYS_USER_SELECT_BY_ID",USER_ID);
		}

        /// <summary>
        /// 得到用户明细
        /// </summary>
        /// <param name="USER_CODE"></param>
        /// <returns></returns>
        public SYS_USER GetModel(string USER_CODE)
        {
            return ExecuteQueryForObject<SYS_USER>("SYS_USER_SELECT_BY_USER_CODE", USER_CODE);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_USER> GetList_ROLE_ID(int ROLE_ID)
        {
            return this.ExecuteQueryForList<SiaSun.LMS.Model.SYS_USER>("SYS_USER_SELECT_BY_ROLE_ID", ROLE_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_USER> GetList_ROLE_ID_NO(int ROLE_ID)
        {
            return this.ExecuteQueryForList<SiaSun.LMS.Model.SYS_USER>("SYS_USER_SELECT_NOTBY_ROLE_ID", ROLE_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 USER_ID)
		{
			return ExecuteDelete("SYS_USER_DELETE",USER_ID);
		}
		

	}
}
