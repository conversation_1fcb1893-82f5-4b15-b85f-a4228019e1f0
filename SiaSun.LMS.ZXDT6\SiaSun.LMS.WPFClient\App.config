<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<appSettings>
		<!--WCF服务地址_内网-->
		<add key="SiaSunSrvUrl" value="http://127.0.0.1:8002/Service"/>
		<!--<add key="SiaSunSrvUrl" value="http://192.168.0.3:8002/Service" />-->
		<!--语言-->
		<add key="Language" value="Default"/>
		<!--数据库类型 Oracle|SQLServer-->
		<add key="DatabaseType" value="SQLServer"/>

		<!--显示配置-->
		<add key="Title" value="新松智能物流管理系统"/>
		<add key="Status" value="用户名:{0}       角色:{1}       徐州市城市轨道交通"/>
	</appSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/></startup></configuration>
