﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucStoreCell.xaml 的交互逻辑
    /// </summary>
    public partial class ucStoreCell : UserControl
    {
        #region     ------变量和属性

        #region     ------绘图常量

        private const int tickFontSpac = 10;                //绘制坐标时边界
        private const int tickTopBounds = 10;                //顶端边界
        private const int tickLeftBounds = 60;              //左边界
        private const int tickClearanceX = 2;				//货格间隙
        private const int tickClearanceY = 2;

        private int intXMin = 1, intYMin = 1;                   //极限值
        double dPercent = 0;                                //缩放比例

        #endregion

        IList<SiaSun.LMS.Model.SYS_ITEM_LIST> listCELL_STATUS = null;
        //DataTable tableCellSize = null;
        IList<SiaSun.LMS.Model.WH_CELL> listWH_CELL = null;

        //选定货位事件代理
        public delegate void U_SelectCELLConfirmHandler(SiaSun.LMS.Model.WH_CELL ST_CELL);
        public event U_SelectCELLConfirmHandler U_SelectCellConfirm;

        //单击事件代理
        public delegate void U_CELLClickHandler(SiaSun.LMS.Model.WH_CELL ST_CELL);
        public event U_CELLClickHandler U_CellClick;

        int intWareHouseID = 0;
        string strZoonScale = string.Empty;
        SiaSun.LMS.Model.WH_CELL mWH_CELL_Selected = null;

        private string SelectCellStatus = Enum.CELL_STATUS.Nohave.ToString();

        public string U_SelectCellStatus
        {
            get
            {
                return this.SelectCellStatus;
            }
            set
            {
                this.SelectCellStatus = value;
            }
        }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public int U_WareHouseID
        {
            get { return this.intWareHouseID; }
            set { intWareHouseID = value; }
        }

        /// <summary>
        /// 缩放尺度_西充九天_2016-12-02
        /// </summary>
        public string U_ZoomScale
        {
            get { return this.strZoonScale; }
            set { this.strZoonScale = value; }
        }

        /// <summary>
        /// 已经选定的货位
        /// </summary>
        public Model.WH_CELL U_SelectedCell
        {
            get { return mWH_CELL_Selected; }
            set { mWH_CELL_Selected = value; }
        }

        #endregion

        public ucStoreCell()
        {
            InitializeComponent();

            this.ucWare.U_WareHouseChanged += new ucWareHouseQuery.U_WareHouseChangedHandler(ucWare_U_WareHouseChanged);
        }

        //初始化控件
        public void U_InitControl(string WAREHOUSE_TYPE)
        {
            //初始化仓库控件
            try
            {
                //仓库信息初始化
                this.InitWareHouseControl(WAREHOUSE_TYPE);

                //选定的货位不空
                //if (this.mWH_CELL_Selected != null)
                //{
                //    //获得仓库编号
                //    Model.WH_AREA mWH_AREA = MainApp.I_SystemService.cellget.AREA_GetModel(mWH_CELL_Selected.AREA_ID);
                //    if (mWH_AREA != null)
                //    {
                //        this.ucWare.U_WAREHOUSE_ID = mWH_AREA.WAREHOUSE_ID;
                //        this.ucWare.U_CELL_Z = mWH_CELL_Selected.CELL_Z;
                //    }
                //}
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException("初始化仓库控件异常！" + ex.Message);
            }
        }

        #region     ------仓库

        /// <summary>
        /// 初始化仓库控件
        /// </summary>
        private void InitWareHouseControl(string WAREHOUSE_TYPE)
        {
            this.ucWare.U_AllowShowArea = true;
            this.ucWare.U_AllowAreaNull = false;
            this.ucWare.U_AllowCellZNull = false;
            if (this.intWareHouseID > 0)
            {
                this.ucWare.U_WAREHOUSE_ID = this.intWareHouseID;
            }
            //初始化缩放比例_西充九天_2016-12-02
            if (this.strZoonScale != string.Empty)
            {
                int intTemp = 0;
                if(Int32.TryParse(this.strZoonScale,out intTemp))
                {
                    this.txtZoom.Text = this.strZoonScale;
                }                
            }
            this.ucWare.U_InitControl(WAREHOUSE_TYPE);
        }

        //选择排显示不同排的货位信息
        void ucWare_U_WareHouseChanged()
        {
            //初始化货位
            this.InitCell();
        }

        #endregion

        #region     ------初始化并绘制货位

        //初始化货位数据
        private void InitCell()
        {
            try
            {
                //选定货位
                this.mWH_CELL_Selected = null;

                //获得比例
                dPercent = Convert.ToDouble(this.txtZoom.Text) / 100;

                //获得存储状态
                listCELL_STATUS = MainApp.I_SystemService.ITEM_LIST_GetList_ITEM_CODE("CELL_STATUS");
                //获得该排货位的尺寸信息
                //this.tableCellSize = MainApp.I_WareHouseService.CELL_SIZE_GetTable(this.ucWare.U_WAREHOUSE_ID, this.ucWare.U_AREA_ID, this.ucWare.U_CELL_Z.ToString());
                //获得该排的所有货位
                this.listWH_CELL = MainApp.I_SystemService.CELL_GetList_Z(this.ucWare.U_WAREHOUSE_ID, this.ucWare.U_CELL_Z.ToString());

                //过滤掉拆除的货位
                this.listWH_CELL = this.listWH_CELL.Where(r => r.CELL_STATUS != Enum.CELL_STATUS.Remove.ToString()).ToList();


                //获得绘制货位的极限坐标信息
                //if (tableCellSize.Rows.Count == 0)
                //    return;
                //else
                //{
                //    this.intXMin = tableCellSize.Rows[0].IsNull("MIN_CELL_X") ? 0 : Convert.ToInt32(tableCellSize.Rows[0]["MIN_CELL_X"]);
                //    this.intYMin = tableCellSize.Rows[0].IsNull("MIN_CELL_Y") ? 0 : Convert.ToInt32(tableCellSize.Rows[0]["MIN_CELL_Y"]);
                //}
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException("初始化货位数据异常！" + ex.Message);
                return;
            }

            //绘制货位
            try
            {
                //绘制货位
                this.DrawCell();

                //绘制货位状态显示颜色
                this.DrawCellStatus();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException("绘制货位异常！" + ex.Message);
                return;
            }
        }

        //绘制货位
        private void DrawCell()
        {
            int currentPosionX = tickLeftBounds;                                //X坐标值

            //清除所有内容
            this.canvasCell.U_CleareVisual();

            //绘制图形
            foreach (var listGroupX in listWH_CELL.GroupBy(l => l.CELL_X))
            {
                //重新初始化绘制坐标
                int nextPositionY = tickTopBounds;
                int currentPositionY = 0;

                //获得该列最大的Y值
                int iMaxGroupY = listGroupX.Max(m => m.CELL_Y);
                int iMinGroupY = listGroupX.Min(m => m.CELL_Y);

                ////获得该列最大宽度
                int dMaxGroupWidth = Convert.ToInt32(Math.Ceiling(listGroupX.Max(w => w.CELL_WIDTH) * Convert.ToDecimal(this.dPercent)));

                try
                {
                    var query = from x in listGroupX orderby x.CELL_Y descending select x;
                    foreach (var mWH_CELL in query)
                    {
                        //坐标
                        int cell_x = mWH_CELL.CELL_X;
                        int cell_y = mWH_CELL.CELL_Y;

                        //fst add 2018-11-07 盐水箱
                        if(mWH_CELL.CELL_TYPE==Enum.CELL_TYPE.Tank.ToString())
                        {
                            iMaxGroupY = int.Parse(mWH_CELL.CELL_PROPERTY);
                        }


                        //货格占用尺寸
                        int areaWith = Convert.ToInt32(Convert.ToInt32(mWH_CELL.CELL_WIDTH) * this.dPercent);
                        int areaHeight = Convert.ToInt32(Convert.ToInt32(mWH_CELL.CELL_HEIGHT) * this.dPercent);

                        //判断Y值是否该列最大值
                        if (cell_y == iMaxGroupY || mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Tank.ToString())
                        {
                            currentPositionY = (iMaxGroupY - cell_y) * areaHeight + tickTopBounds;
                        }
                        else
                        {
                            currentPositionY = nextPositionY;
                        }
                        //获得下一个位置
                        nextPositionY = currentPositionY + areaHeight;

                        //当前区域坐标
                        int x = currentPosionX;
                        int y = currentPositionY;

                        //判断货位存储状态
                        SiaSun.LMS.Model.SYS_ITEM_LIST mCELL_STATUS = listCELL_STATUS.Single(r => r.ITEM_LIST_CODE == mWH_CELL.CELL_STATUS);
                        //绘制货位的存储状态
                        Color colorCellStatus = this.GetCellStatusColor(mCELL_STATUS.ITEM_LIST_REMARK);

                        //绘制图形
                        DrawCellSquare(mWH_CELL,
                                                    colorCellStatus,
                                                    new Pen(Brushes.Black, 1),
                                                    new Point(x, y),
                                                    new Size(areaWith, areaHeight),
                                                    (this.mWH_CELL_Selected != null && this.mWH_CELL_Selected.CELL_ID == mWH_CELL.CELL_ID));

                        #region     ------判断是否站台或工位、并绘制边缘即坐标

                        //判断货格类型
                        if (mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                        {
                            //绘X坐标
                            if (cell_y == intYMin)
                            {
                                this.DrawPosition(Colors.DarkBlue, new Point(x + areaWith / 2, y + areaHeight), cell_x.ToString());
                            }

                            //Y轴坐标
                            if (cell_x == intXMin)
                            {
                                this.DrawPosition(Colors.DarkBlue, new Point(x - tickClearanceX - tickFontSpac, y + areaHeight / 2 - this.FontSize / 2), cell_y.ToString());
                            }
                        }
                        else if (mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Tank.ToString())
                        {
                            this.DrawPosition(Colors.DarkBlue, new Point(x + areaWith / 2, y + areaHeight), cell_x.ToString());
                        }

                        #endregion

                        //设置宽度
                        if (this.canvasCell.Width.Equals(double.NaN) || this.canvasCell.Width < x + areaWith)
                        {
                            this.canvasCell.Width = x + areaWith + tickLeftBounds;
                        }

                        //判断是否超出界限
                        if (iMinGroupY == cell_y)
                        {
                            this.canvasCell.Height = (this.canvasCell.ActualHeight > y + areaHeight + tickFontSpac) ? this.canvasCell.MinHeight : (y + areaHeight + tickFontSpac);
                        }
                    }
                    currentPosionX += (listGroupX.Key % 5 == 0) ? Convert.ToInt32(dMaxGroupWidth) + tickClearanceX : Convert.ToInt32(dMaxGroupWidth);
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        //绘制可视对象
        private void DrawCellSquare(Model.WH_CELL mWH_CELL, Color drawingColor, Pen drawingPen, Point topLeftCorner, Size squareSize, bool IsSelected)
        {
            DrawingVisual visual = new DrawingVisual();
            using (DrawingContext dc = visual.RenderOpen())
            {
                //判断是否选中
                drawingPen = IsSelected ? new Pen(Brushes.Red, 1) : drawingPen;

                Point pointCell = new Point(topLeftCorner.X + tickClearanceX, topLeftCorner.Y + tickClearanceY);
                Size sizeCell = new Size(squareSize.Width - 2 * tickClearanceX, squareSize.Height - 2 * tickClearanceY);
                LinearGradientBrush brush = new LinearGradientBrush(drawingColor, Colors.White, new Point(0, 0), new Point(0, 1));
                Rect rect = new Rect(pointCell, sizeCell);

                //绘制边框
                dc.DrawRoundedRectangle(brush, drawingPen, rect, 1, 1);

                //运行状态
                if (mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL.CELL_TYPE == Enum.CELL_TYPE.Tank.ToString())
                {
                    if (false)
                    {
                        //特殊的状态可以指定不同的颜色，此处未启用
                        FormattedText textRunStatus = new FormattedText(mWH_CELL.RUN_STATUS.Substring(0, 1).ToUpper(),
                                                                        this.Language.GetEquivalentCulture(),
                                                                        System.Windows.FlowDirection.LeftToRight,
                                                                        new Typeface(this.FontFamily, this.FontStyle, FontWeights.ExtraBold, this.FontStretch),
                                                                        (squareSize.Height - tickClearanceY) / 2,
                                                                        Brushes.Red);
                        textRunStatus.TextAlignment = TextAlignment.Center;
                        dc.DrawText(textRunStatus, new Point(pointCell.X + sizeCell.Width / 2, pointCell.Y + tickClearanceY));
                    }
                    else
                    {
                        FormattedText textRunStatus = new FormattedText((mWH_CELL.RUN_STATUS == "Enable" ? string.Empty : mWH_CELL.RUN_STATUS.Substring(0, 1).ToUpper()),
                                                                                                        this.Language.GetEquivalentCulture(),
                                                                                                        System.Windows.FlowDirection.LeftToRight,
                                                                                                        new Typeface(this.FontFamily, this.FontStyle, FontWeights.Normal, this.FontStretch),
                                                                                                        (squareSize.Height - tickClearanceY) / 2,
                                                                                                        Brushes.Black);
                        textRunStatus.TextAlignment = TextAlignment.Center;
                        dc.DrawText(textRunStatus, new Point(pointCell.X + sizeCell.Width / 2, pointCell.Y + tickClearanceY));
                    }
                }
                else
                {
                    FormattedText textCellName = new FormattedText(mWH_CELL.CELL_NAME,
                                                                                                    this.Language.GetEquivalentCulture(),
                                                                                                    System.Windows.FlowDirection.LeftToRight,
                                                                                                    new Typeface(this.FontFamily, this.FontStyle, FontWeights.Bold, this.FontStretch),
                                                                                                    this.FontSize,
                                                                                                    Brushes.Black);
                    textCellName.TextAlignment = TextAlignment.Center;
                    dc.DrawText(textCellName, new Point(pointCell.X + sizeCell.Width / 2, pointCell.Y + sizeCell.Height / 2 - this.FontSize / 2));
                }

                this.canvasCell.U_AddVisual(visual, new DrawingCellObj(mWH_CELL, rect));
            }
        }

        /// <summary>
        /// 绘制坐标值
        /// </summary>
        private void DrawPosition(Color drawingColor, Point potinPosition, string Value)
        {
            DrawingVisual visual = new DrawingVisual();
            using (DrawingContext dc = visual.RenderOpen())
            {
                FormattedText text = new FormattedText(Value,
                                                        this.Language.GetEquivalentCulture(),
                                                        System.Windows.FlowDirection.LeftToRight,
                                                        new Typeface(this.FontFamily, this.FontStyle, FontWeights.Bold, this.FontStretch),
                                                        this.FontSize,
                                                        new SolidColorBrush(drawingColor));
                text.TextAlignment = TextAlignment.Center;
                dc.DrawText(text, potinPosition);
                this.canvasCell.U_AddVisual(visual, null);
            }
        }

        //绘制可视对象
        private void DrawSelectedCellSquare(Visual visual, bool IsSelected)
        {
            object objDrawing = this.canvasCell.U_GetObjFromVisual(visual);
            if (objDrawing != null)
            {
                DrawingCellObj obj = objDrawing as DrawingCellObj;
                Model.WH_CELL mWH_CELL = obj._WH_CELL;
                Rect rect = obj._Rect;

                //清除原有的对象
                this.canvasCell.U_DeleteVisual(visual);

                //判断货位存储状态
                SiaSun.LMS.Model.SYS_ITEM_LIST mCELL_STATUS = listCELL_STATUS.Single(r => r.ITEM_LIST_CODE == mWH_CELL.CELL_STATUS);
                //绘制货位的存储状态
                Color colorCellStatus = this.GetCellStatusColor(mCELL_STATUS.ITEM_LIST_REMARK);

                //绘制图形
                DrawCellSquare(mWH_CELL,
                                            colorCellStatus,
                                            new Pen(Brushes.Black, 1),
                                            new Point(rect.X - tickClearanceX, rect.Y - tickClearanceY),
                                            new Size(rect.Width + 2 * tickClearanceX, rect.Height + 2 * tickClearanceY),
                                            IsSelected);
                //设置选定值
                if (IsSelected)
                {
                    mWH_CELL_Selected = mWH_CELL;

                    //显示
                    SetCellInfo(mWH_CELL);
                }
            }
        }

        /// <summary>
        /// 显示坐标指示
        /// </summary>
        private void DrawDirection()
        {
            int intStartX = 10;
            int intStartY = 30;
            int intDistance = 15;

            DrawingVisual visual = new DrawingVisual();
            using (DrawingContext drawContext = visual.RenderOpen())
            {
                Brush brush = new SolidColorBrush(Colors.Gray);
                Pen pen = new Pen(brush, 1);
                pen.StartLineCap = PenLineCap.Flat;
                pen.EndLineCap = PenLineCap.Triangle;

                drawContext.DrawLine(pen, new Point(intStartX, intStartY), new Point(intStartX, intStartY - intDistance));
                drawContext.DrawLine(pen, new Point(intStartX, intStartY), new Point(intStartX + intDistance, intStartY));

                FormattedText formateTextY = new FormattedText("层", this.Language.GetEquivalentCulture(),
                                                                                System.Windows.FlowDirection.LeftToRight,
                                                                                new Typeface(this.FontFamily, this.FontStyle, FontWeights.Bold, this.FontStretch),
                                                                                this.FontSize, brush);
                drawContext.DrawText(formateTextY, new Point(intStartX - this.FontSize / 2, intStartY - intDistance - this.FontSize - tickClearanceY));

                FormattedText formateTextX = new FormattedText("列", this.Language.GetEquivalentCulture(),
                                                                            System.Windows.FlowDirection.LeftToRight,
                                                                            new Typeface(this.FontFamily, this.FontStyle, FontWeights.Bold, this.FontStretch),
                                                                            this.FontSize, brush);
                drawContext.DrawText(formateTextX, new Point(intStartX + intDistance + tickClearanceX, intStartY - this.FontSize / 2));

                this.canvasCell.U_AddVisual(visual, null);
            }
        }

        /// <summary>
        /// 获得颜色值
        /// </summary>
        private Color GetCellStatusColor(string ColorName)
        {
            Color colorStatus;
            //判断是否设置颜色
            if (!string.IsNullOrEmpty(ColorName))
            {
                ColorConverter colorConvert = new ColorConverter();
                colorStatus = (Color)colorConvert.ConvertFrom(ColorName);
                if (colorStatus == null)
                    colorStatus = (Color)this.Resources["colorDefaultCellStatus"];
            }
            else
            {
                colorStatus = (Color)this.Resources["colorDefaultCellStatus"];
            }
            return colorStatus;
        }

        /// <summary>
        /// 绘制显示状态
        /// </summary>
        private void DrawCellStatus()
        {
            try
            {
                //初始化显示
                this.panelCellStatus.Children.Clear();
                this.panelRunStatus.Children.Clear();

                //获得状态描述信息
                IList<SiaSun.LMS.Model.SYS_ITEM_LIST> list_RUN_STAUTS = MainApp.I_SystemService.ITEM_LIST_GetList_ITEM_CODE("RUN_STATUS");
                IList<SiaSun.LMS.Model.SYS_ITEM_LIST> list_CELL_STAUTS = MainApp.I_SystemService.ITEM_LIST_GetList_ITEM_CODE("CELL_STATUS");

                //获得运行状态字符串
                foreach (SiaSun.LMS.Model.SYS_ITEM_LIST mRUN_STAUTS in list_RUN_STAUTS.OrderBy(r => r.ITEM_LIST_ORDER))
                {
                    //获得该运行状态下的货位数量
                    int intRunStatusCount = this.listWH_CELL.Count(r => r.RUN_STATUS == mRUN_STAUTS.ITEM_LIST_CODE);

                    TextBlock txtRunStatus = new TextBlock();
                    txtRunStatus.Margin = new Thickness(5);
                    //txtRunStatus.Foreground = Brushes.Red;   //可以指定字体颜色
                    this.panelRunStatus.Children.Add(txtRunStatus);
                    txtRunStatus.Text = string.Format("{0} - {1}[{2}]",
                        (mRUN_STAUTS.ITEM_LIST_CODE.Substring(0, 1).ToUpper()),
                        mRUN_STAUTS.ITEM_LIST_NAME,
                        intRunStatusCount <= 0 ? 0 : intRunStatusCount);
                }

                //绘制存储状态
                foreach (SiaSun.LMS.Model.SYS_ITEM_LIST mCELL_STATUS in list_CELL_STAUTS.Where(r=>r.ITEM_LIST_FLAG=="1").OrderBy(r => r.ITEM_LIST_ORDER))
                {
                    StackPanel panelItem = new StackPanel();
                    panelItem.Orientation = Orientation.Horizontal;
                    panelItem.Margin = new Thickness(5);
                    this.panelCellStatus.Children.Add(panelItem);

                    //获得货位状态颜色
                    Color colorStatus = this.GetCellStatusColor(mCELL_STATUS.ITEM_LIST_REMARK);

                    //显示颜色
                    Border border = new Border();
                    border.Margin = new Thickness(2);
                    border.CornerRadius = new CornerRadius(2);
                    border.Width = 25;
                    border.BorderThickness = new Thickness(1, 1.5, 2, 1);
                    border.BorderBrush = Brushes.Black;
                    border.Background = new LinearGradientBrush(colorStatus, Colors.White, new Point(0, 0), new Point(0, 1));
                    panelItem.Children.Add(border);

                    //获得该货位状态下的货位数量
                    int intCellStatusCount = this.listWH_CELL.Count(r => r.CELL_STATUS == mCELL_STATUS.ITEM_LIST_CODE);

                    //显示描述
                    TextBlock txtCellStatus = new TextBlock();
                    txtCellStatus.VerticalAlignment = System.Windows.VerticalAlignment.Center;
                    txtCellStatus.Margin = new Thickness(1);
                    txtCellStatus.Text = string.Format(" - {0}[{1}]",
                                                                                    mCELL_STATUS.ITEM_LIST_NAME,
                                                                                    intCellStatusCount <= 0 ? 0 : intCellStatusCount);
                    panelItem.Children.Add(txtCellStatus);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(string.Format("绘制货位运行状态或存储状态描述失败！") + ex.Message);
            }
        }

        #endregion

        #region     ------鼠标事件

        /// <summary>
        /// 鼠标事件
        /// </summary>
        private void canvasCell_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                try
                {
                    Visual visual = this.canvasCell.U_GetVisual(e.GetPosition(this.canvasCell));
                    if (visual != null)
                    {
                        Visual visualSelected = null;

                        //取消选中
                        if (mWH_CELL_Selected != null)
                        {
                            Dictionary<Visual, object> dic = this.canvasCell.U_DicVisual;
                            int c = dic.Count(r => r.Value != null && (r.Value as DrawingCellObj)._WH_CELL.CELL_ID == mWH_CELL_Selected.CELL_ID);
                            if (dic.Count(r => r.Value != null && (r.Value as DrawingCellObj)._WH_CELL.CELL_ID == mWH_CELL_Selected.CELL_ID) > 0)
                            {
                                visualSelected = dic.First(r => r.Value != null && (r.Value as DrawingCellObj)._WH_CELL.CELL_ID == mWH_CELL_Selected.CELL_ID).Key;
                            }
                        }

                        //判断是是否相同节点
                        if (!visual.Equals(visualSelected))
                        {
                            //取消选中
                            if (visualSelected != null)
                            {
                                DrawSelectedCellSquare(visualSelected, false);
                            }

                            //选中
                            DrawSelectedCellSquare(visual, true);
                        }

                        int intClickCount = e.ClickCount;
                        switch (intClickCount)
                        {
                            case 1:
                                //调用事件
                                if (this.U_CellClick != null)
                                {
                                    this.U_CellClick(mWH_CELL_Selected);
                                }
                                break;
                            case 2:
                                if (this.U_SelectCellConfirm != null)
                                {
                                    this.U_SelectCellConfirm(mWH_CELL_Selected);
                                }
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
            else if (e.RightButton == MouseButtonState.Pressed)
            {
                if(mWH_CELL_Selected!=null && mWH_CELL_Selected.CELL_TYPE==Enum.CELL_TYPE.Tank.ToString())
                {
                    //this.ContextMenu.PlacementTarget=this.menuCell
                }
            }

        }

        /// <summary>
        /// 显示货位信息
        /// </summary>
        /// <param name="mWH_CELL"></param>
        private void SetCellInfo(Model.WH_CELL mWH_CELL)
        {
            this.txtCellCode.Text = mWH_CELL.CELL_CODE;
        }

        #endregion

        //按钮事件
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnZoomOut":
                        this.txtZoom.Text = (Convert.ToInt32(this.txtZoom.Text) < 100 ? Convert.ToInt32(this.txtZoom.Text) + 1 : 100).ToString();
                        this.InitCell();
                        break;
                    case "btnZoomIn":
                        this.txtZoom.Text = (Convert.ToInt32(this.txtZoom.Text) > 1 ? Convert.ToInt32(this.txtZoom.Text) - 1 : 1).ToString();
                        this.InitCell();
                        break;
                    case "btnZoom":
                        this.txtZoom.Text = "100";
                        this.InitCell();
                        break;
                    case "btnUpdate":
                        this.InitCell();
                        break;
                }
            }
        }

        private void ContextMenu_Click(object sender, RoutedEventArgs e)
        {
            MenuItem menuItem = e.OriginalSource as MenuItem;

            if (menuItem == null)
            {
                return;
            }

            //只有超级管理员和管理员允许使用
            if (MainApp._ROLE.ROLE_ID < 1)
            {
                switch (menuItem.Name)
                {
                    case "menuResetCell":
                        MainApp.I_DatabaseService.ExecuteNonQuery($"update WH_CELL set RUN_STATUS ='{Enum.RUN_STATUS.Enable.ToString()}',CELL_STATUS ='{Enum.CELL_STATUS.Nohave.ToString()}' where CELL_ID ={U_SelectedCell.CELL_ID}");
                        this.InitCell();
                        break;
                    default:
                        break;
                }
            }
            else
            {
                MainApp._MessageDialog.ShowResult(false, "功能不可用");
            }            
        }

    }

    /// <summary>
    /// 绘制对象
    /// </summary>
    public class DrawingCellObj
    {
        private Model.WH_CELL mWH_CELL;
        private Rect rec;

        public Model.WH_CELL _WH_CELL
        {
            get { return mWH_CELL; }
            set { mWH_CELL = value; }
        }

        public Rect _Rect
        {
            get { return rec; }
            set { rec = value; }
        }

        public DrawingCellObj(Model.WH_CELL _mWH_CELL, Rect _rec)
        {
            this.mWH_CELL = _mWH_CELL;
            this.rec = _rec;
        }
    }
}
