﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace UnitTest
{
    [TestClass]
    public class UnitTest_Interface
    {
        /*
         * 出库需求下发
         * {"Header":{"AppToken":"123","TimeStamp":"2020-04-28 08:50:30"},"DataField":{"SeqNo":"i00001","TaskNo":"TaskNo101","OutArea":"12051","GroupStatus":0,"TaskType":"3","FirstDetails":[{"ItemNo":1,"GoodsCode":"product","BatchNo":123,"Quantity":11000}]}}
         *
         * 出库需求确认
         * {"Header":{"AppToken":"123","TimeStamp":"2020-04-15 16:11:35"},"DataField":{"SeqNo":"i00002","TaskNo":"TaskNo001","Priority":"5","FirstDetails":[{"ItemNo":1,"GoodsCode":"battery","SecondDetails":[{"StorageListId":"42","PalletNo":"T00006","PickArea":"成品立库区","CellCode":"19-02-01","PickQty":10000},{"StorageListId":"43","PalletNo":"T00007","PickArea":"素电站台区","CellCode":"32757","PickQty":100}]}]}}
         * 
         * 空托盘出库需求下发
         * {"Header":{"AppToken":"123","TimeStamp":"2020-04-15 16:11:35"},"DataField":{"SeqNo":"i00001","TaskNo":"TaskNo002","FirstDetails":[{"ItemNo":1,"GoodsCode":"emptyPallet","Unit":"个","Quantity":100,"OutArea":12111}]}}
         * 
         * 
         * 
         */


        //[TestMethod]
        //public void TestMethod1()
        //{
        //    string result = new SiaSun.LMS.Implement.Interface.WMS.TaskOutRequest().IntefaceMethod("{\"Header\":{\"AppToken\":\"123\",\"TimeStamp\":\"2020 - 04 - 28 08:50:30\"},\"DataField\":{\"SeqNo\":\"i00001\",\"TaskNo\":\"TaskNo101\",\"OutArea\":\"12051\",\"GroupStatus\":0,\"TaskType\":\"3\",\"FirstDetails\":[{\"ItemNo\":1,\"GoodsCode\":\"product\",\"BatchNo\":123,\"Quantity\":11000}]}}");
        //    Assert.IsTrue(result.Contains("\"ResultFlag\":\"1\""));
        //}

        //[TestMethod]
        //public void TestMethod2()
        //{
        //    var result = new SiaSun.LMS.Implement.Interface.iWMS.NoticeCheckResult().IntefaceMethod(out string message);
        //    Assert.IsTrue(result);
        //}

        //[TestMethod]
        //public void TestMethod3()
        //{
        //    var result = new SiaSun.LMS.Implement.Interface.iWMS.PalletStackInfo().IntefaceMethod("", "T000000001|T000000002|T000000003|T000000004|T000000005", out string message);
        //    Assert.IsTrue(result);
        //}
    }
}
