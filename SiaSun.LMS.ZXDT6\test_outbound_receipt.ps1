# Test script for OutboundReceiptSync interface
Write-Host "Testing OutboundReceiptSync interface..." -ForegroundColor Green

# Test 1: Valid outbound receipt data
Write-Host "`nTest 1: Valid outbound receipt data" -ForegroundColor Yellow
$validJson = Get-Content "test_outbound_receipt.json" -Raw
Write-Host "Input JSON:" -ForegroundColor Cyan
Write-Host $validJson

# Test 2: Invalid outbound type
Write-Host "`nTest 2: Invalid outbound type (should fail)" -ForegroundColor Yellow
$invalidOutboundTypeHash = @{
    deliverAddress = "配送地点测试"
    isDeliver = 1
    outboundType = 5  # Invalid type (should be 1, 2, or 3)
    applyType = 1
    operatorId = "operator001"
    applyDate = "2025-07-30"
    applyCode = "OUT20250730002"
    outGoodsInfoList = @(
        @{
            goodsCode = "GOODS001"
            goodsName = "物资名称"
            goodsNum = 50
            unitId = "unit001"
        }
    )
}
$invalidOutboundType = $invalidOutboundTypeHash | ConvertTo-Json -Depth 10
Write-Host "Input JSON with invalid outbound type:" -ForegroundColor Cyan
Write-Host $invalidOutboundType

# Test 3: Missing required fields
Write-Host "`nTest 3: Missing required fields (should fail)" -ForegroundColor Yellow
$missingFieldsHash = @{
    deliverAddress = "配送地点测试"
    isDeliver = 1
    outboundType = 1
    # Missing applyCode, operatorId, applyDate
    outGoodsInfoList = @(
        @{
            goodsCode = "GOODS001"
            goodsName = "物资名称"
            goodsNum = 50
            unitId = "unit001"
        }
    )
}
$missingFields = $missingFieldsHash | ConvertTo-Json -Depth 10
Write-Host "Input JSON with missing required fields:" -ForegroundColor Cyan
Write-Host $missingFields

# Test 4: Empty outGoodsInfoList
Write-Host "`nTest 4: Empty outGoodsInfoList (should fail)" -ForegroundColor Yellow
$emptyListHash = @{
    deliverAddress = "配送地点测试"
    isDeliver = 1
    outboundType = 1
    applyType = 1
    operatorId = "operator001"
    applyDate = "2025-07-30"
    applyCode = "OUT20250730003"
    outGoodsInfoList = @()  # Empty array
}
$emptyList = $emptyListHash | ConvertTo-Json -Depth 10
Write-Host "Input JSON with empty outGoodsInfoList:" -ForegroundColor Cyan
Write-Host $emptyList

# Test 5: Invalid apply type
Write-Host "`nTest 5: Invalid apply type (should fail)" -ForegroundColor Yellow
$invalidApplyTypeHash = @{
    deliverAddress = "配送地点测试"
    isDeliver = 1
    outboundType = 1
    applyType = 5  # Invalid type (should be 1 or 2)
    operatorId = "operator001"
    applyDate = "2025-07-30"
    applyCode = "OUT20250730004"
    outGoodsInfoList = @(
        @{
            goodsCode = "GOODS001"
            goodsName = "物资名称"
            goodsNum = 50
            unitId = "unit001"
        }
    )
}
$invalidApplyType = $invalidApplyTypeHash | ConvertTo-Json -Depth 10
Write-Host "Input JSON with invalid apply type:" -ForegroundColor Cyan
Write-Host $invalidApplyType

Write-Host "`nTest cases prepared. The OutboundReceiptSync implementation should:" -ForegroundColor Green
Write-Host "- Accept Test 1 (valid data)" -ForegroundColor White
Write-Host "- Reject Test 2 (invalid outbound type)" -ForegroundColor White
Write-Host "- Reject Test 3 (missing required fields)" -ForegroundColor White
Write-Host "- Reject Test 4 (empty goods list)" -ForegroundColor White
Write-Host "- Reject Test 5 (invalid apply type)" -ForegroundColor White