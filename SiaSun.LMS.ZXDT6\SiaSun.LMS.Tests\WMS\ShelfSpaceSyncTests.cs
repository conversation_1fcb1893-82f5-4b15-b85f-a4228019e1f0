using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement.Interface.WMS;
using System;
using System.Text.Json;

namespace SiaSun.LMS.Tests.WMS
{
    [TestClass]
    public class ShelfSpaceSyncTests
    {
        private ShelfSpaceSync _shelfSpaceSync;

        [TestInitialize]
        public void Setup()
        {
            _shelfSpaceSync = new ShelfSpaceSync();
        }

        [TestMethod]
        public void IntefaceMethod_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var validInput = new
            {
                isHaveMaterials = 1,
                belongingPlace = "归属地点",
                gsId = "GS001",
                gsName = "公司名称",
                lineId = "LINE001",
                lineName = "线路名称",
                remark = "备注",
                addressType = "地址类型",
                hasGoods = true,
                warehouseCharge = "CHARGE001",
                warehouseChargeName = "仓库负责人",
                warehouseAddress = "仓库地址",
                warehouseType = "仓库类型",
                qrCode = "QR001",
                shelfStatus = 1,
                describe = "货架描述",
                warehouseCode = "WH001",
                warehouseName = "仓库名称",
                warehouseId = "WH001",
                name = "货架名称",
                code = "SHELF001",
                sourceType = "SOURCE001",
                entityModelId = "MODEL001",
                processDefinitionId = "PROCESS001",
                processInstanceId = "INSTANCE001",
                processInstanceKey = "KEY001",
                nextProcessUserList = new { },
                attachmentFileName = "file.pdf",
                attachmentFileUrl = "http://example.com/file.pdf",
                createDate = "2025-01-08 10:00:00",
                createUser = "USER001",
                createName = "创建人",
                updateDate = "2025-01-08 10:00:00",
                updateUser = "USER001",
                updateName = "更新人",
                id = "ID001",
                status = 1,
                billCode = "BILL001"
            };

            string inputJson = JsonSerializer.Serialize(validInput);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Length > 0);
            Assert.IsTrue(response.GetProperty("traceId").GetString().Length > 0);
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Code_ReturnsError()
        {
            // Arrange
            var inputWithMissingCode = new
            {
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001"
                // code is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingCode);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("code"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Name_ReturnsError()
        {
            // Arrange
            var inputWithMissingName = new
            {
                code = "SHELF001",
                warehouseId = "WH001",
                warehouseCode = "WH001"
                // name is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingName);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("name"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_WarehouseId_ReturnsError()
        {
            // Arrange
            var inputWithMissingWarehouseId = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseCode = "WH001"
                // warehouseId is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingWarehouseId);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("warehouseId"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_WarehouseCode_ReturnsError()
        {
            // Arrange
            var inputWithMissingWarehouseCode = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseId = "WH001"
                // warehouseCode is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingWarehouseCode);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("warehouseCode"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidShelfStatus_ReturnsError()
        {
            // Arrange
            var inputWithInvalidShelfStatus = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001",
                shelfStatus = -1 // Invalid negative status
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidShelfStatus);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("shelfStatus"));
        }

        [TestMethod]
        public void IntefaceMethod_ValidShelfStatusZero_ReturnsSuccess()
        {
            // Arrange
            var inputWithValidShelfStatus = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001",
                shelfStatus = 0 // Valid zero status
            };

            string inputJson = JsonSerializer.Serialize(inputWithValidShelfStatus);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_ValidShelfStatusPositive_ReturnsSuccess()
        {
            // Arrange
            var inputWithValidShelfStatus = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001",
                shelfStatus = 1 // Valid positive status
            };

            string inputJson = JsonSerializer.Serialize(inputWithValidShelfStatus);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_EmptyStringFields_ReturnsError()
        {
            // Arrange
            var inputWithEmptyFields = new
            {
                code = "",
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001"
            };

            string inputJson = JsonSerializer.Serialize(inputWithEmptyFields);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("code"));
        }

        [TestMethod]
        public void IntefaceMethod_OptionalFieldsOnly_ReturnsSuccess()
        {
            // Arrange
            var inputWithOptionalFields = new
            {
                code = "SHELF001",
                name = "货架名称",
                warehouseId = "WH001",
                warehouseCode = "WH001",
                // Only optional fields below
                describe = "货架描述",
                qrCode = "QR001",
                remark = "备注信息"
            };

            string inputJson = JsonSerializer.Serialize(inputWithOptionalFields);

            // Act
            string result = _shelfSpaceSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }
    }
}