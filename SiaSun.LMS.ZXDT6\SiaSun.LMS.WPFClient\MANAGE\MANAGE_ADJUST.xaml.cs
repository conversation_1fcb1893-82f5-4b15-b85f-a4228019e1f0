﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_ADJUST : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;
        int goodsId = 0;
        int currentCellID = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_ADJUST(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_DatabaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_ADJUST(int MANAGE_ID ,string MANAGE_TYPE_CODE):this(MANAGE_TYPE_CODE)
        {
            DataTable dtMANAGE_MAIN = MainApp.I_DatabaseService.GetList(string.Format("select * from MANAGE_MAIN where MANAGE_ID={0}", MANAGE_ID));
            DataTable dtMANAGE_LIST = MainApp.I_DatabaseService.GetList(string.Format("select * from MANAGE_LIST where MANAGE_ID={0}", MANAGE_ID));
            if (dtMANAGE_MAIN != null && dtMANAGE_LIST != null && dtMANAGE_LIST.Rows.Count != 0)
            {
                IList<Model.MANAGE_LIST> lsMANAGE_LIST = new Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(dtMANAGE_LIST, null);
                var listGroup = lsMANAGE_LIST.Where(r => r.PLAN_LIST_ID > 0).GroupBy(r => r.GOODS_ID);
                if (listGroup.Count() == 1)
                {
                    //任务列表中包含多个PLAN_LIST_ID时不做处理
                    this.goodsId = listGroup.ElementAt(0).Key;
                    this.ucManagePosition.U_STOCK_BARCODE = dtMANAGE_MAIN.Rows[0]["STOCK_BARCODE"].ToString();
                }
            }

        }

        /// <summary>
        /// 窗体加载
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            this.ucManagePosition.U_StockBarcodeKeyDown += new UC.ucManagePosition.U_StockBarcodeKeyDownHandler
                (() =>
                    {
                        //校验托盘条码是否合法
                        if (!Common.RegexValid.IsValidate(this.ucManagePosition.U_STOCK_BARCODE, MainApp.I_SystemService.GetSysParameter("BoxBarcodeValidRegex", "")))
                        {
                            MainApp._MessageDialog.Show(Enum.MessageConverter.CheckStockBarCode, this.ucManagePosition.U_STOCK_BARCODE);
                            return;
                        }
                        //显示库存
                        StorageListBind();
                    }
                );

            this.ucManagePosition.U_StartPositionChanged += new UC.ucManagePosition.U_StartPositionValueChangedHandler
                (() =>
                    {
                        //显示库存
                        currentCellID = this.ucManagePosition.U_START_POSITION_ID;
                        StorageListBind();
                    }
                );

            this.ucManagePosition.U_EndPositionChanged += new UC.ucManagePosition.U_EndPositionValueChangedHandler
                (() =>
                    {
                        //显示库存
                        currentCellID = this.ucManagePosition.U_END_POSITION_ID;
                        StorageListBind();
                    }
                );
        }



        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            //设置输送任务控件参数
            this.ucManagePosition.U_AllowAutoEndPostion = true;

            this.ucManagePosition.U_AllowAutoStartPostion = false;
            //初始化
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);

        }

        /// <summary>
        /// 库存明细绑定
        /// </summary>
        private void StorageListBind()
        {
            this.gridStorageList.U_WindowName = this.GetType().Name;
            this.gridStorageList.U_TableName = "V_STORAGE_LIST";
            this.gridStorageList.U_XmlTableName = "V_STORAGE_LIST";
            this.gridStorageList.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.gridStorageList.U_OrderField = "STORAGE_LIST_ID";
            this.gridStorageList.U_Where = string.Format("  STOCK_BARCODE='{0}' {1}", this.ucManagePosition.U_STOCK_BARCODE.TrimEnd(), goodsId == 0 ? "" : String.Format(" and GOODS_ID ={0} ", goodsId));
            this.gridStorageList.U_AllowOperatData = false;
            this.gridStorageList.U_AllowChecked = true;
            this.gridStorageList.U_AllowShowPage = false;

            //拆分列属性
            this.gridStorageList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridStorageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridStorageList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridStorageList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.gridStorageList.U_InitControl();
        }


        /// <summary>
        /// 点击按钮确认
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            string sResult = string.Empty;

            try
            {
                if(this.gridStorageList.U_DataSource == null)
                {
                    MainApp._MessageDialog.ShowResult(false, "请点击[刷新]按钮查询库存信息");
                    return;
                }

                MainWindow.mainWin.Cursor = Cursors.Wait;

                //结束当前编辑操作
                this.gridStorageList.U_EndCurrentEdit();

                DataRowView[] dataRowItems =  this.gridStorageList.U_GetCheckedDataRows();

                DataTable tableAdjust= this.gridStorageList.U_DataSource.Copy();

                tableAdjust.Clear();

                foreach (DataRowView dr in dataRowItems)
                {
                    tableAdjust.ImportRow(dr.Row);
                }

                foreach (DataRow dr in tableAdjust.Rows)
                {
                    if (Convert.ToDecimal(dr["MANAGE_LIST_QUANTITY"]) == 0 &&
                        MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.IsContinue, "[实际数量]") != Sid.Windows.Controls.TaskDialogResult.Ok)
                    {
                        return;
                    }
                    dr["MANAGE_LIST_QUANTITY"] = Convert.ToDecimal(dr["STORAGE_LIST_QUANTITY"]) - Convert.ToDecimal(dr["MANAGE_LIST_QUANTITY"]);
                    dr["PLAN_LIST_ID"] = 0;
                }

                //根据数据源获得数据列表
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableAdjust, null);

                var list = from v in listMANAGE_LIST
                           where v.MANAGE_LIST_QUANTITY != 0
                           select v;

                listMANAGE_LIST = list.ToList<Model.MANAGE_LIST>();

                #region     ------校验合法性

                //判断是否填写数据
                if (listMANAGE_LIST.Count==0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }


                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                #endregion
                

                //提示确认
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmAssembly, this.ucManagePosition.U_STOCK_BARCODE) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    var cellId = int.Parse(tableAdjust.Rows[0]["CELL_ID"].ToString());

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN()
                    {
                        BACKUP_FIELD1 = "",
                        BACKUP_FIELD2 = "",
                        BACKUP_FIELD3 = "",
                        BACKUP_FIELD4 = "",
                        BACKUP_FIELD5 = "",
                        FULL_FLAG = "",
                        GOODS_TEMPLATE_ID = 0,
                        MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                        MANAGE_CONFIRM_TIME = "",
                        MANAGE_END_TIME = "",
                        MANAGE_ID = 0,
                        MANAGE_LEVEL = "",
                        MANAGE_OPERATOR = MainApp._USER.USER_NAME,
                        MANAGE_RELATE_CODE = "",
                        MANAGE_REMARK = "",
                        MANAGE_SOURCE = Enum.SystemName.SSWMS.ToString(),
                        MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString(),
                        MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE,
                        PLAN_ID = 0,
                        PLAN_TYPE_CODE = "",
                        START_CELL_ID = cellId,
                        STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE,
                        CELL_MODEL = tableAdjust.Rows[0]["CELL_MODEL"].ToString(),
                        END_CELL_ID = cellId,
                        PICK_SEQ = "",
                        STOCK_WEIGHT = int.Parse(tableAdjust.Rows[0]["STOCK_WEIGHT"].ToString())
                    };

                    bool bResult = MainApp.I_ManageService.ManageCreate(
                        mMANAGE_MAIN: mMANAGE_MAIN,
                        lsMANAGE_LIST: listMANAGE_LIST,
                        raiseTrans: true,
                        checkStorage: false,
                        checkManage: true,
                        checkCellStatus: true,
                        autoComplete: true,
                        autoControl: false,
                        doubleInAutoMove: false,
                        out sResult);

                    //检验执行结果
                    if (bResult)
                    {
                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, sResult);

                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            if (string.IsNullOrEmpty(this.ucManagePosition.U_STOCK_BARCODE.TrimEnd()))
            {
                MainApp._MessageDialog.ShowResult(false, "请扫描或输入容器条码");
                return;
            }

            this.StorageListBind();

            this.ucManagePosition.U_Update();
        }


    }
}
