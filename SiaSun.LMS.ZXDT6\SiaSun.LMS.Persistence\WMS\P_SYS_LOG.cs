﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// SYS_LOG
    /// </summary>
    public class P_SYS_LOG : P_Base_House
    {
        public P_SYS_LOG()
        {
            //
            // TODO: 此处添加SYS_LOG的构造函数
            //
        }

        /// <summary>
        /// 得到模型
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SYS_LOG GetModel(System.Int32 LOG_ID)
        {
            return this.ExecuteQueryForObject<SYS_LOG>("SYS_LOG_SELECT_BY_ID", LOG_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IList<SYS_LOG> GetList(string THREAD)
        {
            return this.ExecuteQueryForList<SYS_LOG>("SYS_LOG_SELECT_BY_THREAD", THREAD);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_LOG> GetList()
        {
            return this.ExecuteQueryForList<SYS_LOG>("SYS_LOG_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(SYS_LOG SYS_LOG, bool isOracle = false)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_LOG");
                SYS_LOG.LOG_ID = id;
            }
            this.ExecuteInsert("SYS_LOG_INSERT", SYS_LOG);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public void Update(SYS_LOG SYS_LOG)
        {
            this.ExecuteUpdate("SYS_LOG_UPDATE", SYS_LOG);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(System.Int32 LOG_ID)
        {
            this.ExecuteDelete("SYS_LOG_DELETE", LOG_ID);
        }


        /// <summary>
        /// 执行写入日志存储过程(自治事务)
        /// bjwdz add 2018-01-27
        /// </summary>
        /// <param name="sysLog"></param>
        public void AddLogProduce(Model.SYS_LOG sysLog)
        {
            Hashtable ht = new Hashtable();
            ht.Add("LOGTHREAD", sysLog.LOG_THREAD);
            ht.Add("LOGGER", sysLog.LOG_LOGGER);
            ht.Add("LOGDATE", sysLog.LOG_DATE);
            ht.Add("LOGLEVEL", sysLog.LOG_LEVEL);
            ht.Add("LOGMESSAGE", sysLog.LOG_MESSAGE);

            _sqlMap.Insert("P_INSERT_LOG", ht);
        }
    }
}
