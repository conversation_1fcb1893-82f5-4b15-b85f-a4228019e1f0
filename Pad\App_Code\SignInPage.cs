﻿using System;
using System.Web.UI;

public class SignInPage : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            if (!Page.IsPostBack && this.SessionName != null)
            {
                Page.DataBind();
                PageLoad();
            }
        }
        catch
        {
        }
    }

    protected virtual void PageLoad()
    {
    }

    protected string SessionName
    {
        get
        {
            object name = Session["name"];
            if (name == null)
            {
                Response.Redirect(string.Format("/SignIn.aspx?url={0}", this.Page.Request.Path.Trim('/')));
            }
            return name.ToString();
        }
    }
}