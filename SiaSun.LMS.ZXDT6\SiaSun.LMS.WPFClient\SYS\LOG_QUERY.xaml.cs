﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.IO;
using System.Xml;
using System.Data;
using System.Reflection;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// LOG_QUERY.xaml 的交互逻辑
    /// </summary>
    public partial class LOG_QUERY : AvalonDock.DocumentContent
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public LOG_QUERY()
        {
            InitializeComponent();
            this.tvwNode.U_ItemSelectedChanged += new UC.ucTreeView.U_ItemSelectedChangedHandler(tvwNode_U_ItemSelectedChanged);
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //加载日志文件列表
            this.LoadLogFileList();
        }
        
        #region     ------打开指定文件

        /// <summary>
        /// 打开文件
        /// </summary>
        private void btnOpenFile_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.Forms.OpenFileDialog ofDg = new System.Windows.Forms.OpenFileDialog();
            ofDg.Filter = "log|*.log|All|*.*";
            if (ofDg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                string strFileName =ofDg.FileName;
                this.txtFilePath.Text = strFileName;

                try
                {
                    //读取文件
                    string strLogContent = this.ReadFile(strFileName);
                    //数据源
                    this.gridLog.ItemsSource = null;// MainApp.I_BaseService.GetLogFromContent(strLogContent);

                    //显示标题
                    this.grpLog.Header = string.Format(this.grpLog.Tag.ToString(),ofDg.FileName);
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        /// <summary>
        /// 读取文件
        /// </summary>
        private string ReadFile(string FileName)
        {
            string strContent = null;
            try
            {
                using (FileStream flStream = File.OpenRead(FileName))
                {
                    Encoding encode = Encoding.GetEncoding("gb2312");
                    using (StreamReader reader = new StreamReader(flStream, encode))
                    {
                        strContent = reader.ReadToEnd();

                        //释放
                        reader.Close();
                        reader.Dispose();
                        flStream.Close();
                        flStream.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            return strContent;
        }

        #endregion

        #region     ------加载所有服务端的日志信息

        /// <summary>
        /// 加载日志文件列表
        /// </summary>
        private void LoadLogFileList()
        {
            //try
            //{
            //    this.tvwNode.U_Header = "请选择日志文件";
            //    foreach (string strFileName in MainApp.I_BaseService.ReadLogFileList())
            //    {
            //        this.tvwNode.U_AddTreeViewItem(null, strFileName, strFileName);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    MainApp._MessageDialog.ShowException(ex.Message);
            //}
        }

        //更改选项
        void tvwNode_U_ItemSelectedChanged(TreeViewItem itemSelected)
        {
            this.LoadLogFile(itemSelected);
        }
        /// <summary>
        /// 加载日志文件
        /// </summary>
        private void LoadLogFile(TreeViewItem SelectedItem)
        {
            //if (SelectedItem == null)
            //    return;
            //try
            //{
            //    //获得文件名
            //    string strFileName = SelectedItem.Header.ToString();
            //    //数据源
            //    this.gridLog.ItemsSource = MainApp.I_BaseService.GetLogFromFile(strFileName);
            //    //显示标题
            //    this.grpLog.Header = string.Format(this.grpLog.Tag.ToString(), strFileName);
            //}
            //catch (Exception ex)
            //{
            //    MainApp._MessageDialog.ShowException(ex.Message);
            //}
        }

        #endregion

        #region     ------按钮事件

        /// <summary>
        /// 查询
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnQuery":
                        this.Query();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                    case "btnExport":
                        this.Export();
                        break;
                }
            }
        }

        /// <summary>
        /// 查询
        /// </summary>
        private void Query()
        {
            if (gridLog.ItemsSource == null)
                return;
            if (!string.IsNullOrEmpty(this.txtTime.Text))
            {
                try
                {
                    IList<Model.Log> listLog = this.gridLog.ItemsSource as IList<Model.Log>;
                    if (listLog != null)
                    {
                        //查询
                        var query = from log in listLog where log.TimeStamp.Contains(this.txtTime.Text) select log;
                        this.gridLog.ItemsSource = query;
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex.Message);
                }
            }
        }

        /// <summary>
        /// 导出日志文件
        /// </summary>
        private void Export()
        {
            IList<Model.Log> listLog = this.gridLog.ItemsSource as IList<Model.Log>;
            if (listLog != null)
            {
                //获得数据源结构
                using (DataTable tableLog = new DataTable())
                {
                    try
                    {
                        Type type = typeof(Model.Log);
                        foreach (PropertyInfo pInfo in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
                        {
                            tableLog.Columns.Add(pInfo.Name);
                        }

                        //复制相关值
                        foreach (Model.Log mLog in listLog)
                        {
                            DataRow rowLog = tableLog.NewRow();
                            rowLog = new SiaSun.LMS.Common.CloneObjectValues().CloneDataRowValue(mLog, rowLog, null);
                            tableLog.Rows.Add(rowLog);
                        }

                        //导出EXCEL
                        new SiaSun.LMS.Common.Excel().ExportAllToExcel(tableLog, string.Format("Log-{0}", DateTime.Now.ToString()));
                    }
                    catch (Exception ex)
                    {
                        MainApp._MessageDialog.ShowException(ex);
                    }
                }            
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //清空
            this.txtFilePath.Clear();
            this.txtTime.Clear();

            //重新加载
            this.LoadLogFile(this.tvwNode.U_SelectedItem);
        }

        #endregion

    }
}
