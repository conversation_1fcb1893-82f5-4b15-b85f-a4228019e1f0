﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Microsoft_Windows_Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Luna"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ControlTemplate x:Key="WindowTemplateKey" TargetType="{x:Type Window}">
        <Border
            Background="{TemplateBinding Background}"
            BorderBrush="{TemplateBinding BorderBrush}"
            BorderThickness="{TemplateBinding BorderThickness}">
            <Grid>
                <AdornerDecorator>
                    <ContentPresenter />
                </AdornerDecorator>
                <ResizeGrip
                    x:Name="WindowResizeGrip"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Bottom"
                    IsTabStop="false"
                    Visibility="Collapsed" />
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="ResizeMode" Value="CanResizeWithGrip" />
                    <Condition Property="WindowState" Value="Normal" />
                </MultiTrigger.Conditions>
                <Setter TargetName="WindowResizeGrip" Property="Visibility" Value="Visible" />
            </MultiTrigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="CustomWindowControlTemplate" TargetType="{x:Type Window}">
        <Border
            d:DesignHeight="106.166"
            d:DesignWidth="565.541"
            Background="{TemplateBinding Background}"
            BorderBrush="DarkGray"
            BorderThickness="1"
            CornerRadius="10,10,0,0">
            <Grid
                Width="Auto"
                Height="Auto"
                VerticalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <Border
                    x:Name="topborder"
                    Grid.Column="0"
                    Grid.ColumnSpan="1"
                    Margin="0"
                    BorderBrush="{x:Null}"
                    BorderThickness="0"
                    CornerRadius="10,10,0,0">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                            <GradientStop Offset="1" Color="#FF343434" />
                            <GradientStop Color="#FF8A8A8A" />
                        </LinearGradientBrush>
                    </Border.Background>
                    <Grid
                        Width="Auto"
                        Height="Auto"
                        Margin="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="5" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="5" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            x:Name="txtTitle"
                            Grid.Column="1"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            FontFamily="微软雅黑"
                            FontWeight="Bold"
                            Foreground="White" />
                        <Button x:Name="btnMin" Grid.Column="3">
                            <!--<Image Source="/@Images/winmin.png" Width="25" Stretch="Fill"></Image>-->
                        </Button>
                        <Button x:Name="btnMax" Grid.Column="4">
                            <!--<Image Source="/@Images/winmax.png" Width="25" Stretch="Fill"></Image>-->
                        </Button>
                        <Button
                            x:Name="btnClose"
                            Grid.Column="5"
                            Style="{DynamicResource CloseButtonStyle}">
                            <!--<Image Source="/@Images/close.png" Width="25" Stretch="Fill"></Image>-->
                        </Button>
                    </Grid>
                </Border>
                <Grid Grid.Row="1" Margin="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition />
                    </Grid.RowDefinitions>
                    <ContentPresenter
                        Grid.RowSpan="1"
                        Grid.Column="0"
                        Grid.ColumnSpan="1"
                        Width="Auto"
                        Margin="0"
                        HorizontalAlignment="Stretch" />
                </Grid>
            </Grid>
        </Border>
    </ControlTemplate>

    <Style x:Key="CustomWindowStyle" TargetType="{x:Type Window}">
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}" />
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="Template" Value="{StaticResource CustomWindowControlTemplate}" />
        <Style.Triggers>
            <Trigger Property="ResizeMode" Value="CanResizeWithGrip">
                <Setter Property="Template" Value="{StaticResource WindowTemplateKey}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle
                        Margin="3"
                        SnapsToDevicePixels="true"
                        Stroke="Black"
                        StrokeDashArray="1 2"
                        StrokeThickness="1" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <LinearGradientBrush x:Key="ButtonNormalBackgroundFill" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="0.9" Color="#FFF0F0EA" />
    </LinearGradientBrush>

    <SolidColorBrush x:Key="ButtonBorder" Color="#FF003C74" />
    <Style x:Key="CloseButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource ButtonFocusVisual}" />
        <Setter Property="Background" Value="{StaticResource ButtonNormalBackgroundFill}" />
        <Setter Property="BorderBrush" Value="{StaticResource ButtonBorder}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Microsoft_Windows_Themes:ButtonChrome
                        x:Name="Chrome"
                        BorderBrush="{DynamicResource ButtonBorder}"
                        RenderDefaulted="{TemplateBinding IsDefaulted}"
                        RenderMouseOver="{TemplateBinding IsMouseOver}"
                        RenderPressed="{TemplateBinding IsPressed}"
                        SnapsToDevicePixels="true"
                        ThemeColor="NormalColor">
                        <Microsoft_Windows_Themes:ButtonChrome.Fill>
                            <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                <GradientStop Offset="0.234" Color="#FFFF4747" />
                                <GradientStop Offset="1" Color="#FFFFA5A5" />
                            </LinearGradientBrush>
                        </Microsoft_Windows_Themes:ButtonChrome.Fill>
                        <ContentPresenter
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Microsoft_Windows_Themes:ButtonChrome>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="Chrome" Property="RenderDefaulted" Value="true" />
                        </Trigger>
                        <Trigger Property="ToggleButton.IsChecked" Value="true">
                            <Setter TargetName="Chrome" Property="RenderPressed" Value="true" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>