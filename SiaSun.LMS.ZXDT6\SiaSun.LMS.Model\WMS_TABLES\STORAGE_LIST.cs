﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// STORAGE_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class STORAGE_LIST
	{
		public STORAGE_LIST()
		{
			
		}
		
		private int _storage_list_id;
		private int _storage_id;
		private int _plan_list_id;
		private System.Decimal _storage_list_quantity;
		private int _goods_id;
		private string _entry_time;
		private string _update_time;
		private string _storage_list_remark;
		private string _box_barcode;
		private string _goods_property1;
		private string _goods_property2;
		private string _goods_property3;
		private string _goods_property4;
		private string _goods_property5;
		private string _goods_property6;
		private string _goods_property7;
		private string _goods_property8;
        private string _goods_property9;
        private string _goods_property10;
        private string _goods_property11;
        private string _goods_property12;
        private string _goods_property13;
        private string _goods_property14;
        private string _goods_property15;
        private string _goods_property16;
        private string _goods_property17;
        private string _goods_property18;
        private string _goods_property19;
        private string _goods_property20;
        private string _goods_property21;
        private string _goods_property22;
        private string _goods_property23;
        private string _goods_property24;
        private string _goods_property25;
        private string _goods_property26;
        private string _goods_property27;
        private string _goods_property28;
        private string _goods_property29;
        private string _goods_property30;

        ///<sumary>
        /// 库存列表编号
        ///</sumary>
        [DataMember]
		public int STORAGE_LIST_ID
		{
			get{return _storage_list_id;}
			set{_storage_list_id = value;}
		}
		///<sumary>
		/// 库存编号
        ///</sumary>
        [DataMember]
		public int STORAGE_ID
		{
			get{return _storage_id;}
			set{_storage_id = value;}
		}
		///<sumary>
		/// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal STORAGE_LIST_QUANTITY
		{
			get{return _storage_list_quantity;}
			set{_storage_list_quantity = value;}
		}
		///<sumary>
		/// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}
		///<sumary>
		/// 入库时间
        ///</sumary>
        [DataMember]
		public string ENTRY_TIME
		{
			get{return _entry_time;}
			set{_entry_time = value;}
		}
		///<sumary>
		/// 修改时间
        ///</sumary>
        [DataMember]
		public string UPDATE_TIME
		{
			get{return _update_time;}
			set{_update_time = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string STORAGE_LIST_REMARK
		{
			get{return _storage_list_remark;}
			set{_storage_list_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY1
		{
			get{return _goods_property1;}
			set{_goods_property1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY2
		{
			get{return _goods_property2;}
			set{_goods_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY3
		{
			get{return _goods_property3;}
			set{_goods_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY4
		{
			get{return _goods_property4;}
			set{_goods_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY5
		{
			get{return _goods_property5;}
			set{_goods_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY6
		{
			get{return _goods_property6;}
			set{_goods_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY7
		{
			get{return _goods_property7;}
			set{_goods_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY8
		{
			get{return _goods_property8;}
			set{_goods_property8 = value;}
		}
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY9
        {
            get { return _goods_property9; }
            set { _goods_property9 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY10
        {
            get { return _goods_property10; }
            set { _goods_property10 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY11
        {
            get { return _goods_property11; }
            set { _goods_property11 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY12
        {
            get { return _goods_property12; }
            set { _goods_property12 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY13
        {
            get { return _goods_property13; }
            set { _goods_property13 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY14
        {
            get { return _goods_property14; }
            set { _goods_property14 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY15
        {
            get { return _goods_property15; }
            set { _goods_property15 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY16
        {
            get { return _goods_property16; }
            set { _goods_property16 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY17
        {
            get { return _goods_property17; }
            set { _goods_property17 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY18
        {
            get { return _goods_property18; }
            set { _goods_property18 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY19
        {
            get { return _goods_property19; }
            set { _goods_property19 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY20
        {
            get { return _goods_property20; }
            set { _goods_property20 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY21
        {
            get { return _goods_property21; }
            set { _goods_property21 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY22
        {
            get { return _goods_property22; }
            set { _goods_property22 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY23
        {
            get { return _goods_property23; }
            set { _goods_property23 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY24
        {
            get { return _goods_property24; }
            set { _goods_property24 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY25
        {
            get { return _goods_property25; }
            set { _goods_property25 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY26
        {
            get { return _goods_property26; }
            set { _goods_property26 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY27
        {
            get { return _goods_property27; }
            set { _goods_property27 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY28
        {
            get { return _goods_property28; }
            set { _goods_property28 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY29
        {
            get { return _goods_property29; }
            set { _goods_property29 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY30
        {
            get { return _goods_property30; }
            set { _goods_property30 = value; }
        }
    }
}
