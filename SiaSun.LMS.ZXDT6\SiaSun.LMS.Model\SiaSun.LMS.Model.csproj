﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Model</RootNamespace>
    <AssemblyName>SiaSun.LMS.Model</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <PublishUrl>http://localhost/SiaSun.LMS.Model/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="WMS_SUPPORT\StorageLockParam.cs" />
    <Compile Include="WMS_TABLES\LCD_LIST.cs" />
    <Compile Include="WMS_TABLES\STORAGE_LOCK.cs" />
    <Compile Include="WMS_SUPPORT\PdaMessage.cs" />
    <Compile Include="WMS_TABLES\INTERFACE_QUEUE.cs" />
    <Compile Include="WMS_TABLES\APPLY_TYPE.cs" />
    <Compile Include="WMS_SUPPORT\APPLY_TYPE_PARAM.cs" />
    <Compile Include="WMS_SUPPORT\Enum.cs" />
    <Compile Include="WMS_SUPPORT\EnumMessage.cs" />
    <Compile Include="BPMS\TB_TRAYINFO_UPLOAD.cs" />
    <Compile Include="WMS_SUPPORT\FIELD_DESCRIPTION.cs" />
    <Compile Include="WMS_TABLES\FLOW_ACTION.cs" />
    <Compile Include="WMS_TABLES\FLOW_NODE.cs" />
    <Compile Include="WMS_TABLES\FLOW_PARA.cs" />
    <Compile Include="WMS_TABLES\FLOW_TYPE.cs" />
    <Compile Include="WMS_TABLES\GOODS_CLASS.cs" />
    <Compile Include="WMS_TABLES\GOODS_MAIN.cs" />
    <Compile Include="BPMS\TB_TRAYINFO_CHECK.cs" />
    <Compile Include="WMS_TABLES\GOODS_PROPERTY.cs" />
    <Compile Include="WMS_TABLES\GOODS_TEMPLETE.cs" />
    <Compile Include="WMS_TABLES\GOODS_TEMPLATE_LIST.cs" />
    <Compile Include="WMS_TABLES\GOODS_TYPE.cs" />
    <Compile Include="WMS_TABLES\IO_CONTROL_APPLY.cs" />
    <Compile Include="WMS_TABLES\IO_CONTROL.cs" />
    <Compile Include="WMS_TABLES\IO_CONTROL_APPLY_HIS.cs" />
    <Compile Include="WMS_TABLES\IO_CONTROL_ROUTE.cs" />
    <Compile Include="WMS_TABLES\LCD_MAIN.cs" />
    <Compile Include="WMS_TABLES\LED_LIST.cs" />
    <Compile Include="WMS_TABLES\LED_MAIN.cs" />
    <Compile Include="WMS_SUPPORT\Log.cs" />
    <Compile Include="WMS_SUPPORT\MANAGE_ACTION_EXCUTE.cs" />
    <Compile Include="WMS_TABLES\MANAGE_DETAIL.cs" />
    <Compile Include="WMS_TABLES\MANAGE_LIST.cs" />
    <Compile Include="WMS_TABLES\MANAGE_MAIN.cs" />
    <Compile Include="WMS_TABLES\MANAGE_TYPE.cs" />
    <Compile Include="WMS_SUPPORT\EnumMessageConverter.cs" />
    <Compile Include="WMS_SUPPORT\MANAGE_TYPE_PARAM.cs" />
    <Compile Include="WMS_SUPPORT\PLAN_ACTION_EXCUTE.cs" />
    <Compile Include="WMS_TABLES\PLAN_DETAIL.cs" />
    <Compile Include="WMS_TABLES\PLAN_LIST.cs" />
    <Compile Include="WMS_TABLES\PLAN_MAIN.cs" />
    <Compile Include="WMS_TABLES\PLAN_TYPE.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WMS_SUPPORT\QueryObject.cs" />
    <Compile Include="WMS_TABLES\RECORD_DETAIL.cs" />
    <Compile Include="WMS_TABLES\RECORD_LIST.cs" />
    <Compile Include="WMS_TABLES\RECORD_MAIN.cs" />
    <Compile Include="WMS_SUPPORT\RequestData.cs" />
    <Compile Include="WMS_SUPPORT\ServiceMessage.cs" />
    <Compile Include="WMS_SUPPORT\ServiceResponse.cs" />
    <Compile Include="WMS_SUPPORT\STOCK_BOX.cs" />
    <Compile Include="WMS_TABLES\STORAGE_DETAIL.cs" />
    <Compile Include="WMS_TABLES\STORAGE_LIST.cs" />
    <Compile Include="WMS_TABLES\STORAGE_MAIN.cs" />
    <Compile Include="WMS_TABLES\SYS_ITEM.cs" />
    <Compile Include="WMS_TABLES\SYS_ITEM_LIST.cs" />
    <Compile Include="WMS_TABLES\SYS_LOG.cs" />
    <Compile Include="WMS_TABLES\SYS_MENU.cs" />
    <Compile Include="WMS_TABLES\SYS_RELATION.cs" />
    <Compile Include="WMS_TABLES\SYS_RELATION_LIST.cs" />
    <Compile Include="WMS_TABLES\SYS_ROLE.cs" />
    <Compile Include="WMS_TABLES\SYS_ROLE_WINDOW.cs" />
    <Compile Include="WMS_TABLES\SYS_TABLE_CONVERTER_LIST.cs" />
    <Compile Include="WMS_TABLES\SYS_USER.cs" />
    <Compile Include="WMS_TABLES\WH_AREA.cs" />
    <Compile Include="WMS_TABLES\WH_CELL.cs" />
    <Compile Include="WMS_TABLES\WH_DESCRIPTION.cs" />
    <Compile Include="WMS_TABLES\WH_LOGIC.cs" />
    <Compile Include="WMS_TABLES\WH_WAREHOUSE.cs" />
    <Compile Include="WMS_TABLES\SYS_TABLE_CONVERTER.cs" />
    <Compile Include="WMS_SUPPORT\RequestDataList.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>