﻿<UserControl
    x:Class="SiaSun.LMS.WPFClient.UC.ucQuickQuery"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="67"
    d:DesignWidth="153"
    mc:Ignorable="d">
    <Border>
        <ScrollViewer Margin="1">
            <Grid>
                <WrapPanel Name="panelQuery" Margin="1">
                    <WrapPanel
                        Name="panelButton"
                        Margin="1"
                        ButtonBase.Click="StackPanel_Click">
                        <Button
                            Name="btnOK"
                            Width="50"
                            Margin="3,2,3,2">
                            查询
                        </Button>
                        <Button
                            Name="btnClear"
                            Width="50"
                            Margin="3,2,3,2">
                            重置
                        </Button>
                        <Button
                            Name="btnAdd"
                            Width="50"
                            Margin="3,2,3,2">
                            添加
                        </Button>
                    </WrapPanel>
                </WrapPanel>
            </Grid>
        </ScrollViewer>
    </Border>
</UserControl>
