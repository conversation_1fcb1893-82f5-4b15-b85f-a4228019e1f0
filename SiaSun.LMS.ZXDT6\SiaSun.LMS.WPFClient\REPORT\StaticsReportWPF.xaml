﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.REPORT.StaticsReportWPF"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:wf="clr-namespace:Microsoft.Reporting.WinForms;assembly=Microsoft.ReportViewer.WinForms" 
        Title="统计报表" Height="500" Width="1100">
    <Grid Loaded="Grid_Loaded">
        <GroupBox Header="选择报表类型和时间段" Height="63" HorizontalAlignment="Left" Margin="12,11,0,0"  Name="groupBox1" VerticalAlignment="Top" Width="1045">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="54*" />
                    <ColumnDefinition Width="673*" />
                </Grid.ColumnDefinitions>
                <Label Content="报表类型" Height="28" HorizontalAlignment="Left" Margin="6,6,0,0" Name="label1" VerticalAlignment="Top" Width="59" />
                <ComboBox Grid.Column="1" Height="23" HorizontalAlignment="Left" Margin="20,11,0,0" Name="cmbReportType" VerticalAlignment="Top" Width="225"></ComboBox>
                <Label Content="时间段" Grid.Column="1" Height="28" HorizontalAlignment="Left" Margin="442,11,0,0" Name="label2" VerticalAlignment="Top" Width="61" />
                <DatePicker Grid.Column="1" Height="25" HorizontalAlignment="Left" Margin="494,11,0,0" Name="datePickerFrom" VerticalAlignment="Top" Width="115" />
                <Label Content="-" Grid.Column="1" Height="28" HorizontalAlignment="Left" Margin="615,8,0,0" Name="label3" VerticalAlignment="Top" FontSize="16" />
                <DatePicker Grid.Column="1" Height="25" HorizontalAlignment="Left" Margin="638,11,0,0" Name="datePickerTo" VerticalAlignment="Top" Width="115" />
                <Button Content="查询" Grid.Column="1" Height="23" HorizontalAlignment="Left" Margin="769,11,0,0" Name="btnQuery" VerticalAlignment="Top" Width="75" Click="btnQuery_Click" />
                <Label Content="所属域" Grid.Column="1" Height="28" HorizontalAlignment="Left" Margin="267,8,0,0" Name="label4" VerticalAlignment="Top" />
                <ComboBox Grid.Column="1" Height="23" HorizontalAlignment="Left" Margin="309,11,0,0" Name="cmbContract" IsEditable="True" VerticalAlignment="Top" Width="120">
                </ComboBox>
                <Button Content="重置" Grid.Column="1" Height="23" HorizontalAlignment="Left" Margin="862,11,0,0" Name="btnReset" VerticalAlignment="Top" Width="75" Click="btnReset_Click" />
            </Grid>
        </GroupBox>
        <my:WindowsFormsHost Margin="12,80,12,12" Name="windowsFormsHost1"  
                             xmlns:my="clr-namespace:System.Windows.Forms.Integration;assembly=WindowsFormsIntegration" OverridesDefaultStyle="False" UseLayoutRounding="False">
            <wf:ReportViewer Name="rp"></wf:ReportViewer>
        </my:WindowsFormsHost>
    </Grid>
</ad:DocumentContent>
