﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.WH.WAREHOUSE_AREA_MANAGE"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
      mc:Ignorable="d" 
      d:DesignHeight="300" d:DesignWidth="548"
	Title="WAREHOUSE_AREA_MANAGE" Loaded="DocumentContent_Loaded">
    <ad:DocumentContent.Resources>
        <ContextMenu x:Key="menuMain"  MenuItem.Click="ContextMenu_Click">
            <MenuItem Name="menuItemRefresh" Header="刷新"></MenuItem>
        </ContextMenu>
    </ad:DocumentContent.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>

        <uc:ucTreeView x:Name="tvwWareHouse" Grid.Column="0" Grid.Row="0" ContextMenu="{StaticResource menuMain}" U_ItemSelectedChanged="tvwWareHouse_U_ItemSelectedChanged"></uc:ucTreeView>
        <GridSplitter Grid.Column="1" Grid.RowSpan="2"  VerticalAlignment="Stretch" Width="3"></GridSplitter>
        <GroupBox Name="grpChild" Grid.Column="2" Grid.Row="0" Header="列表" Tag=" {0}-列表" >
            <uc:ucCommonDataGrid x:Name="gridChild" ></uc:ucCommonDataGrid>
        </GroupBox>
        <GroupBox Grid.Row="1" Grid.ColumnSpan="3" Header="操作区" >
            <Border >
                <WrapPanel Name="panelButton" HorizontalAlignment="Center" Visibility="Visible" ButtonBase.Click="WrapPanel_Click">
                    <Button Name="btnInitSystem" Margin="8" >系统初始化</Button>
                    <Button Name="btnCreateCell" Margin="50,0,5,0" >货位初始化</Button>                  
                </WrapPanel>
            </Border>
        </GroupBox>
    </Grid>
</ad:DocumentContent >
