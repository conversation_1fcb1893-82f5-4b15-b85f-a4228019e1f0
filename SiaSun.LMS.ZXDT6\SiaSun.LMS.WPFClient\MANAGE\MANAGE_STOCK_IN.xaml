﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_STOCK_IN"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_STOCK_IN" Height="378" Width="455" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition MaxWidth="300" MinWidth="300"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>        
        <uc:ucStockIn x:Name="ucStockIn" Grid.Column="0" Grid.Row="1" Margin="1,1,1,1"></uc:ucStockIn>       
        <GridSplitter Width="2"  VerticalAlignment="Stretch" Grid.Column="1" Grid.RowSpan="3"></GridSplitter>
        <GroupBox Name="grpbManage" Grid.Column="2" Grid.RowSpan="2"  Header="任务列表" Tag=" {0}-任务列表">
            <uc:ucSplitPropertyGridTab x:Name="ucStockStorageGroup" Grid.Row="2" ></uc:ucSplitPropertyGridTab>
        </GroupBox>
        <WrapPanel Grid.Row="2" Grid.Column="2" HorizontalAlignment="Left" ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition"></uc:ucManagePosition>
            <Button Name="btnCreateTask"  MinWidth="70" Margin="5">下达任务</Button>
            <Button Name="btnRefresh"  MinWidth="70" Margin="5">刷新</Button>
        </WrapPanel>

    </Grid>
</ad:DocumentContent >
