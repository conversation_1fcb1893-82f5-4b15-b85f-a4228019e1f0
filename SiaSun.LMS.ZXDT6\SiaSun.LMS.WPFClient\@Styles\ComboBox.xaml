<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <ControlTemplate x:Key="ComboBoxToggleButton" TargetType="{x:Type ToggleButton}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation
                    Storyboard.TargetName="rectangleOver"
                    Storyboard.TargetProperty="Opacity"
                    To="0.8"
                    Duration="00:00:00.1000000" />
                <ColorAnimation
                    Storyboard.TargetName="Background"
                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                    To="#FFFFFFFF"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation
                    Storyboard.TargetName="rectangleOver"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
                <ColorAnimation
                    Storyboard.TargetName="Background"
                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                    To="#FFEAF2FB"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <DoubleAnimation
                    Storyboard.TargetName="rectanglePress"
                    Storyboard.TargetProperty="Opacity"
                    To="0.8"
                    Duration="00:00:00.1000000" />
                <ColorAnimation
                    Storyboard.TargetName="Background"
                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                    To="#FFFFFFFF"
                    Duration="00:00:00.1000000" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <DoubleAnimation
                    Storyboard.TargetName="rectanglePress"
                    Storyboard.TargetProperty="Opacity"
                    To="0"
                    Duration="00:00:00.4000000" />
                <ColorAnimation
                    Storyboard.TargetName="Background"
                    Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                    To="#FFEAF2FB"
                    Duration="00:00:00.4000000" />
            </Storyboard>
            <Storyboard x:Key="CheckedOn">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="BackgroundChecked"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="CheckedOff">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="BackgroundChecked"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <Grid x:Name="grid">
            <Rectangle
                x:Name="Background"
                Fill="#FFEAF2FB"
                IsHitTestVisible="false"
                Stroke="#FF9EBBDE" />
            <Rectangle
                x:Name="BackgroundChecked"
                Margin="1"
                IsHitTestVisible="false"
                Opacity="0">
                <Rectangle.Fill>
                    <SolidColorBrush Color="{DynamicResource WhiteColor}" />
                </Rectangle.Fill>
            </Rectangle>
            <Rectangle
                x:Name="rectangleOver"
                Width="15"
                HorizontalAlignment="Right"
                Fill="{DynamicResource MouseOverBrush}"
                Opacity="0"
                Stroke="#FFE8E8E8" />
            <Rectangle
                x:Name="rectanglePress"
                Width="15"
                HorizontalAlignment="Right"
                Fill="{DynamicResource PressedBrush}"
                Opacity="0"
                Stroke="#FC9E9D9B" />
            <Rectangle
                x:Name="DisabledVisualElement"
                Margin="1"
                Fill="{DynamicResource DisabledBackgroundBrush}"
                IsHitTestVisible="false"
                Visibility="Collapsed" />
            <Path
                x:Name="BtnArrow"
                Width="6"
                Margin="0,0,4,0"
                HorizontalAlignment="Right"
                Data="F1 M 301.14,-189.041L 311.57,-189.041L 306.355,-182.942L 301.14,-189.041 Z "
                Fill="{DynamicResource GlyphBrush}"
                Stretch="Uniform" />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsPressed" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="PressedOff_BeginStoryboard" Storyboard="{StaticResource PressedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="PressedOn_BeginStoryboard" Storyboard="{StaticResource PressedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>

            </Trigger>
            <Trigger Property="IsChecked" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="CheckedOff_BeginStoryboard" Storyboard="{StaticResource CheckedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard" Storyboard="{StaticResource CheckedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                <Setter TargetName="DisabledVisualElement" Property="Visibility" Value="Visible" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxTextBox" TargetType="{x:Type TextBox}">
        <Border
            x:Name="PART_ContentHost"
            Background="{TemplateBinding Background}"
            Focusable="False" />
    </ControlTemplate>

    <Style TargetType="{x:Type ComboBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="6,2,25,2" />
        <Setter Property="Template" Value="{DynamicResource ComboBoxTemplate}" />
    </Style>

    <ControlTemplate x:Key="ComboBoxTemplate" TargetType="{x:Type ComboBox}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames
                    BeginTime="00:00:00"
                    Storyboard.TargetName="FocusVisualElement"
                    Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <Grid>
            <ToggleButton
                x:Name="ToggleButton"
                Grid.Column="2"
                ClickMode="Press"
                Focusable="false"
                IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                Template="{DynamicResource ComboBoxToggleButton}" />
            <ContentPresenter
                x:Name="ContentSite"
                Margin="1,3,10,3"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Content="{TemplateBinding SelectionBoxItem}"
                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                IsHitTestVisible="False" />

            <TextBox
                x:Name="PART_EditableTextBox"
                Margin="1,3,10,3"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Background="Transparent"
                Focusable="True"
                IsReadOnly="{TemplateBinding IsReadOnly}"
                Style="{x:Null}"
                Template="{DynamicResource ComboBoxTextBox}"
                Visibility="Hidden" />
            <Rectangle
                x:Name="DisabledVisualElement"
                Fill="{DynamicResource DisabledBackgroundBrush}"
                IsHitTestVisible="false"
                Opacity="0"
                RadiusX="0"
                RadiusY="0"
                Stroke="{DynamicResource DisabledBorderBrush}" />
            <Rectangle
                x:Name="FocusVisualElement"
                Margin="-1"
                IsHitTestVisible="false"
                Opacity="0"
                Stroke="{DynamicResource FocusBrush}"
                StrokeThickness="1" />
            <Popup
                x:Name="Popup"
                Margin="0,1,0,0"
                AllowsTransparency="True"
                Focusable="False"
                IsOpen="{TemplateBinding IsDropDownOpen}"
                Placement="Bottom"
                PopupAnimation="Slide">
                <Grid
                    x:Name="DropDown"
                    MinWidth="{TemplateBinding ActualWidth}"
                    MaxHeight="{TemplateBinding MaxDropDownHeight}"
                    SnapsToDevicePixels="True">
                    <Border
                        x:Name="DropDownBorder"
                        Margin="0,-1,0,0"
                        Background="{DynamicResource ControlBackgroundBrush}"
                        BorderBrush="{DynamicResource ControlBorderBrush}"
                        BorderThickness="1"
                        CornerRadius="0,0,3,3">
                        <ScrollViewer
                            Margin="4,6,4,6"
                            CanContentScroll="True"
                            HorizontalScrollBarVisibility="Auto"
                            SnapsToDevicePixels="True"
                            VerticalScrollBarVisibility="Auto">

                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />

                        </ScrollViewer>
                    </Border>
                </Grid>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="HasItems" Value="false">
                <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                <Setter TargetName="DisabledVisualElement" Property="Opacity" Value="1" />
            </Trigger>
            <Trigger Property="IsGrouping" Value="true">
                <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
            </Trigger>
            <Trigger SourceName="Popup" Property="AllowsTransparency" Value="true">
                <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="4" />
                <Setter TargetName="DropDownBorder" Property="Margin" Value="0,2,0,0" />
            </Trigger>
            <Trigger Property="IsEditable" Value="true">
                <Setter Property="IsTabStop" Value="false" />
                <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible" />
                <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style d:IsControlPart="True" TargetType="{x:Type ComboBoxItem}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBoxItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HighlightOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0.73"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="HighlightOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0.84"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid SnapsToDevicePixels="true">
                        <Rectangle
                            x:Name="BackgroundGradientOver"
                            Fill="{DynamicResource MouseOverBrush}"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource MouseOverBorderBrush}" />
                        <Rectangle
                            x:Name="BackgroundGradientSelected"
                            Fill="{DynamicResource PressedBrush}"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource PressedBorderBrush}"
                            StrokeThickness="1" />
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Selector.IsSelected" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="SelectedOff_BeginStoryboard" Storyboard="{StaticResource SelectedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="SelectedOn_BeginStoryboard1" Storyboard="{StaticResource SelectedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HighlightOff_BeginStoryboard1" Storyboard="{StaticResource HighlightOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HighlightOn}" />
                            </Trigger.EnterActions>
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>