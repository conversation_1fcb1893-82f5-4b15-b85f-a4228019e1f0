﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="GOODS_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="GOODS_MAIN" type="SiaSun.LMS.Model.GOODS_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_MAIN">
			<result property="GOODS_ID" column="goods_id" />
			<result property="GOODS_CLASS_ID" column="goods_class_id" />
			<result property="LOGIC_ID" column="logic_id" />
			<result property="GOODS_CODE" column="goods_code" />
			<result property="GOODS_NAME" column="goods_name" />
			<result property="GOODS_UNITS" column="goods_units" />
			<result property="GOODS_CONST_PROPERTY1" column="goods_const_property1" />
			<result property="GOODS_CONST_PROPERTY2" column="goods_const_property2" />
			<result property="GOODS_CONST_PROPERTY3" column="goods_const_property3" />
			<result property="GOODS_CONST_PROPERTY4" column="goods_const_property4" />
			<result property="GOODS_CONST_PROPERTY5" column="goods_const_property5" />
			<result property="GOODS_CONST_PROPERTY6" column="goods_const_property6" />
			<result property="GOODS_CONST_PROPERTY7" column="goods_const_property7" />
			<result property="GOODS_CONST_PROPERTY8" column="goods_const_property8" />
			<result property="GOODS_LIMIT_UPPER_QUANTITY" column="goods_limit_upper_quantity" />
			<result property="GOODS_LIMIT_LOWER_QUANTITY" column="goods_limit_lower_quantity" />
			<result property="GOODS_REMARK" column="goods_remark" />
			<result property="GOODS_ORDER" column="goods_order" />
			<result property="GOODS_FLAG" column="goods_flag" />
			<result property="GOODS_COLOR" column="goods_color" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="GOODS_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			goods_id,
			goods_class_id,
			logic_id,
			goods_code,
			goods_name,
			goods_units,
			goods_const_property1,
			goods_const_property2,
			goods_const_property3,
			goods_const_property4,
			goods_const_property5,
			goods_const_property6,
			goods_const_property7,
			goods_const_property8,
			goods_limit_upper_quantity,
			goods_limit_lower_quantity,
			goods_remark,
			goods_order,
			goods_flag,
			goods_color
			From GOODS_MAIN
		</select>

		<select id="GOODS_MAIN_SELECT_BY_ID" parameterClass="int" extends = "GOODS_MAIN_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_id=#GOODS_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="GOODS_MAIN_SELECT_BY_GOODS_CODE" parameterClass="string" extends = "GOODS_MAIN_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_code=#GOODS_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="GOODS_MAIN_SELECT_BY_GOODS_CODE_CONTRACT" parameterClass="string" extends = "GOODS_MAIN_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_code=#GOODS_CODE# AND GOODS_CONST_PROPERTY3=#CONTRACT#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="GOODS_MAIN_SELECT_BY_GOODS_CLASS_ID" parameterClass="int" extends = "GOODS_MAIN_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_class_id=#GOODS_CLASS_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="GOODS_MAIN_INSERT" parameterClass="GOODS_MAIN">
			Insert Into GOODS_MAIN (
			<!--goods_id,-->
			goods_class_id,
			logic_id,
			goods_code,
			goods_name,
			goods_units,
			goods_const_property1,
			goods_const_property2,
			goods_const_property3,
			goods_const_property4,
			goods_const_property5,
			goods_const_property6,
			goods_const_property7,
			goods_const_property8,
			goods_limit_upper_quantity,
			goods_limit_lower_quantity,
			goods_remark,
			goods_order,
			goods_flag,
			goods_color
			)Values(
			<!--#GOODS_ID#,-->
			#GOODS_CLASS_ID#,
			#LOGIC_ID#,
			#GOODS_CODE#,
			#GOODS_NAME#,
			#GOODS_UNITS#,
			#GOODS_CONST_PROPERTY1#,
			#GOODS_CONST_PROPERTY2#,
			#GOODS_CONST_PROPERTY3#,
			#GOODS_CONST_PROPERTY4#,
			#GOODS_CONST_PROPERTY5#,
			#GOODS_CONST_PROPERTY6#,
			#GOODS_CONST_PROPERTY7#,
			#GOODS_CONST_PROPERTY8#,
			#GOODS_LIMIT_UPPER_QUANTITY#,
			#GOODS_LIMIT_LOWER_QUANTITY#,
			#GOODS_REMARK#,
			#GOODS_ORDER#,
			#GOODS_FLAG#,
			#GOODS_COLOR#
			)
			<selectKey  resultClass="int" type="post" property="GOODS_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="GOODS_MAIN_UPDATE" parameterClass="GOODS_MAIN">
			Update GOODS_MAIN Set
			<!--goods_id=#GOODS_ID#,-->
			goods_class_id=#GOODS_CLASS_ID#,
			logic_id=#LOGIC_ID#,
			goods_code=#GOODS_CODE#,
			goods_name=#GOODS_NAME#,
			goods_units=#GOODS_UNITS#,
			goods_const_property1=#GOODS_CONST_PROPERTY1#,
			goods_const_property2=#GOODS_CONST_PROPERTY2#,
			goods_const_property3=#GOODS_CONST_PROPERTY3#,
			goods_const_property4=#GOODS_CONST_PROPERTY4#,
			goods_const_property5=#GOODS_CONST_PROPERTY5#,
			goods_const_property6=#GOODS_CONST_PROPERTY6#,
			goods_const_property7=#GOODS_CONST_PROPERTY7#,
			goods_const_property8=#GOODS_CONST_PROPERTY8#,
			goods_limit_upper_quantity=#GOODS_LIMIT_UPPER_QUANTITY#,
			goods_limit_lower_quantity=#GOODS_LIMIT_LOWER_QUANTITY#,
			goods_remark=#GOODS_REMARK#,
			goods_order=#GOODS_ORDER#,
			goods_flag=#GOODS_FLAG#,
			goods_color=#GOODS_COLOR#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_id=#GOODS_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="GOODS_MAIN_DELETE" parameterClass="int">
			Delete From GOODS_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_id=#GOODS_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>