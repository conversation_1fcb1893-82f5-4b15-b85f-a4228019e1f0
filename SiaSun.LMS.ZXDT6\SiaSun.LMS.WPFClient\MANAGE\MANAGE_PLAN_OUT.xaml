﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_PLAN_OUT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_PLAN_DOWNT" Height="372" Width="508" Loaded="DocumentContent_Loaded">
    <Grid >
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*" ></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <uc:ucSplitPropertyGridTab x:Name="gridPlanList" Grid.Row="0"></uc:ucSplitPropertyGridTab>
        
        <GridSplitter Height="2"  HorizontalAlignment="Stretch" Grid.Row="1"></GridSplitter>
        <uc:ucSplitPropertyGridTab x:Name="ucStorageGroup" Grid.Row="2"></uc:ucSplitPropertyGridTab>
        <WrapPanel Grid.Row="3" Margin="3" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition"  Grid.Row="0"></uc:ucManagePosition>
            <Button Name="btnConfirm"  Width="60" Margin="0,0,5,0">下达任务</Button>
            <Button Name="btnQuery"  Width="60">查询</Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent>
