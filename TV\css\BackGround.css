﻿

.linear{ 

 width:100%; 

 height:100%;

border:1px solid #000;

position:fixed;

/*FILTER: progid:DXImageTransform.Microsoft.Gradient(gradientType=0,startColorStr=#b8c4cb,endColorStr=red); /*IE 6 7 8*/ 

/*background: -ms-linear-gradient(top,#b8c4cb,#f6f6f8);        /* IE 10 */

/*background:-moz-linear-gradient(top,#b8c4cb,#f6f6f8);/*火狐*/ 

/*background:-webkit-gradient(linear, 0% 0%, 0% 100%,from(#b8c4cb), to(#f6f6f8));/*谷歌*/ 

/*background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#b8c4cb), to(#f6f6f8));      /* Safari 4-5, Chrome 1-9*/

/*background: -webkit-linear-gradient(top, #b8c4cb, #f6f6f8);   /*Safari5.1 Chrome 10+*/

/*background: -o-linear-gradient(top, #b8c4cb, #f6f6f8);  /*Opera 11.10+*/ 
 
} 

