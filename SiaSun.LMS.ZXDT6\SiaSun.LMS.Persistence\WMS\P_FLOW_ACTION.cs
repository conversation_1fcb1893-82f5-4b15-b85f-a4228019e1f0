﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// FLOW_ACTION
	/// </summary>
	public class P_FLOW_ACTION : P_Base_House
	{
		public P_FLOW_ACTION ()
		{
			//
			// TODO: 此处添加FLOW_ACTION的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<FLOW_ACTION> GetList()
		{
			return ExecuteQueryForList<FLOW_ACTION>("FLOW_ACTION_SELECT",null);
		}

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(FLOW_ACTION flow_action)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("FLOW_ACTION");
                flow_action.FLOW_ACTION_ID = id;
            }
            return ExecuteInsert("FLOW_ACTION_INSERT", flow_action);
        }

		/// <summary>
		/// 修改
		/// </summary>
		public int Update(FLOW_ACTION flow_action)
		{
			return ExecuteUpdate("FLOW_ACTION_UPDATE",flow_action);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public FLOW_ACTION GetModel(System.Int32 FLOW_ACTION_ID)
		{
			return ExecuteQueryForObject<FLOW_ACTION>("FLOW_ACTION_SELECT_BY_ID",FLOW_ACTION_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 FLOW_ACTION_ID)
		{
			return ExecuteDelete("FLOW_ACTION_DELETE",FLOW_ACTION_ID);
		}
		

	}
}
