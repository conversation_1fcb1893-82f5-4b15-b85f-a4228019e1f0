﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Media;
using System.Windows;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.UC
{
    public class DrawingCanvas:Canvas
    {
        private List<Visual> visuals = new List<Visual>();
        private Dictionary<Visual, object> dicVisual = new Dictionary<Visual, object>();

        public Dictionary<Visual, object> U_DicVisual
        {
            get { return dicVisual; }
        }

        protected override Visual GetVisualChild(int index)
        {
            return visuals[index];
        }
        protected override int VisualChildrenCount
        {
            get
            {
                return visuals.Count;
            }
        }

        public object U_GetObjFromVisual(Visual visual)
        {
            return (dicVisual.ContainsKey(visual)) ? dicVisual[visual] : null;
        }

        public void U_AddVisual(Visual visual,object obj)
        {
            visuals.Add(visual);
            dicVisual.Add(visual, obj);

            base.AddVisualChild(visual);
            base.AddLogicalChild(visual);
        }

        public void U_DeleteVisual(Visual visual)
        {
            visuals.Remove(visual);
            dicVisual.Remove(visual);

            base.RemoveVisualChild(visual);
            base.RemoveLogicalChild(visual);
        }

        public void U_CleareVisual()
        {
            for (int i = this.visuals.Count - 1; i >= 0; i--)
            {
                U_DeleteVisual(visuals[i]);
            }
        }

        public DrawingVisual U_GetVisual(Point point)
        {
            HitTestResult hitResult = VisualTreeHelper.HitTest(this, point);
            return hitResult.VisualHit as DrawingVisual;
        }

        private List<DrawingVisual> hits = new List<DrawingVisual>();
        public List<DrawingVisual> U_GetVisuals(Geometry region)
        {
            hits.Clear();
            GeometryHitTestParameters parameters = new GeometryHitTestParameters(region);
            HitTestResultCallback callback = new HitTestResultCallback(this.U_HitTestCallback);
            VisualTreeHelper.HitTest(this, null, callback, parameters);
            return hits;
        }

        private HitTestResultBehavior U_HitTestCallback(HitTestResult result)
        {
            GeometryHitTestResult geometryResult = (GeometryHitTestResult)result;
            DrawingVisual visual = result.VisualHit as DrawingVisual;
            if (visual != null &&
                geometryResult.IntersectionDetail == IntersectionDetail.FullyInside)
            {
                hits.Add(visual);
            }
            return HitTestResultBehavior.Continue;
        }
    }
}
