﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     FST
 *       日期：     2018/10/18
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// TB_TRAYINFO_CHECK
    /// </summary>
    public class P_TB_TRAYINFO_CHECK : P_Base_Bpms
    {
        public P_TB_TRAYINFO_CHECK()
        {
            //
            // TODO: 此处添加TB_TRAYINFO_CHECK的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<TB_TRAYINFO_CHECK> GetList()
        {
            return ExecuteQueryForList<TB_TRAYINFO_CHECK>("TB_TRAYINFO_CHECK_SELECT", null);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("TB_TRAYINFO_CHECK_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(TB_TRAYINFO_CHECK tb_trayinfo_check)
        {
            ExecuteInsert("TB_TRAYINFO_CHECK_INSERT", tb_trayinfo_check);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(TB_TRAYINFO_CHECK tb_trayinfo_check)
        {
            ExecuteUpdate("TB_TRAYINFO_CHECK_UPDATE", tb_trayinfo_check);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TB_TRAYINFO_CHECK GetModel(System.String TRAY_NO)
        {
            return ExecuteQueryForObject<TB_TRAYINFO_CHECK>("TB_TRAYINFO_CHECK_SELECT_BY_ID", TRAY_NO);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(System.String TRAY_NO)
        {
            ExecuteDelete("TB_TRAYINFO_CHECK_DELETE", TRAY_NO);
        }
    }
}
