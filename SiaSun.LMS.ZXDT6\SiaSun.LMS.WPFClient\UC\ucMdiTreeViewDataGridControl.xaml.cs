﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucMdiTreeViewDataGridControl.xaml 的交互逻辑
    /// </summary>
    public partial class ucMdiTreeViewDataGridControl : UserControl
    {
        public delegate void U_DoubleClickEventHandler();
        public event U_DoubleClickEventHandler U_DoubleClick;

        #region     ------树控件属性

        private string strLeftHeader = string.Empty;
        private string strLeftTable = string.Empty;
        private string strLeftNodeRelativeColumn = string.Empty;
        private string strLeftNodeTagColumn = string.Empty;
        private string strLeftNodeTextColumn = string.Empty;
        private string strLeftWhere = string.Empty;

        /// <summary>
        /// 左侧显示标题
        /// </summary>
        public string U_LeftHeader
        {
            get { return strLeftHeader; }
            set { strLeftHeader = value; }
        }
        /// <summary>
        /// 左侧树控件数据源
        /// </summary>
        public string U_LeftTable
        {
            get { return strLeftTable; }
            set { strLeftTable = value; }
        }

        /// <summary>
        /// 左侧树控件节点绑定值
        /// </summary>
        public string U_LeftNodeTagColumn
        {
            get { return strLeftNodeTagColumn; }
            set { strLeftNodeTagColumn = value; }
        }

        /// <summary>
        /// 左侧树控件节点显示列
        /// </summary>
        public string U_LeftNodeTextColumn
        {
            get { return strLeftNodeTextColumn; }
            set { strLeftNodeTextColumn = value; }
        }

        /// <summary>
        /// 左侧树控件节点显示列
        /// </summary>
        public string U_LeftNodeRelativeColumn
        {
            get { return strLeftNodeRelativeColumn; }
            set { strLeftNodeRelativeColumn = value; }
        }

        /// <summary>
        /// 左侧树控件筛选条件
        /// </summary>
        public string U_LeftWhere
        {
            get { return strLeftWhere; }
            set { strLeftWhere = value; }
        }

        #endregion

        #region     ------右侧数据显示控件属性

        private bool boolAllowDoubleClick = true;
        private bool boolAllowChecked = true;
        private bool boolAllowEdit = true;

        private string strRightTagColumn = string.Empty;
        private string strRightTable = string.Empty;
        private string strRightWhere = string.Empty;

        private string strSplitGroupHeader = string.Empty;
        private string strSplitGroupColumn = string.Empty;
        private string strSplitPropertyColumn = string.Empty;
        private string strSplitPropertyType = string.Empty;

        /// <summary>
        /// 是否允许双击选择行记录
        /// </summary>
        public bool U_AllowDoubleClick
        {
            get { return boolAllowDoubleClick; }
            set { boolAllowDoubleClick = value; }
        }

        /// <summary>
        /// 是否显示选择框
        /// </summary>
        public bool U_AllowChecked
        {
            get { return boolAllowChecked; }
            set { boolAllowChecked = value; }
        }

        /// <summary>
        /// 是否允许编辑数据
        /// </summary>
        public bool U_AllowOperation
        {
            get { return boolAllowEdit; }
            set { boolAllowEdit = value; }
        }

        /// <summary>
        /// 右侧数据源选依据列，与左侧节点关联列
        /// </summary>
        public string U_RigthTagColumn
        {
            get { return strRightTagColumn; }
            set { strRightTagColumn = value; }
        }

        /// <summary>
        /// 右侧数据源
        /// </summary>
        public string U_RightTable
        {
            get { return strRightTable; }
            set { strRightTable = value; }
        }

        /// <summary>
        /// 右侧数据源筛选条件
        /// </summary>
        public string U_RightWhere
        {
            get { return strRightWhere; }
            set { strRightWhere = value; }
        }

        /// <summary>
        /// 不同TapPage页显示的数据不同的分组依据,如：表V_PLAN_LIST中的GOODS_TYPE_ID
        /// </summary>
        public string U_SplitGroupColumn
        {
            get { return strSplitGroupColumn; }
            set { strSplitGroupColumn = value; }
        }

        /// <summary>
        /// 不同TapPage页显示的标题,如：表GOODS_TYPE中的GOODS_TYPE_NAME
        /// </summary>
        public string U_SplitGroupHeader
        {
            get { return strSplitGroupHeader; }
            set { strSplitGroupHeader = value; }
        }

        /// <summary>
        /// 数据源中所对应的拆分属性列名，如：PLAN_LIST表中的GOODS_PROPERTY
        /// </summary>
        public string U_SplitPropertyColumn
        {
            get { return strSplitPropertyColumn; }
            set { strSplitPropertyColumn = value; }
        }

        /// <summary>
        /// 属性划分依据类别,不同类型对应显示的属性不同，如：表GOODS_TYPE
        /// </summary>
        public string U_SplitPropertyType
        {
            get { return strSplitPropertyType; }
            set { strSplitPropertyType = value; }
        }

        #endregion

        public ucMdiTreeViewDataGridControl()
        {
            InitializeComponent();

            this.tvwParent.U_ItemSelectedChanged += new ucTreeView.U_ItemSelectedChangedHandler(tvwParent_U_ItemSelectedChanged);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //加载树控件
            this.tvwParent.U_Header = strLeftHeader;
            this.tvwParent.U_LoadTreeViewItems(null,strLeftTable, strLeftNodeTextColumn, strLeftNodeTagColumn, strLeftWhere, null);
        }

        /// <summary>
        /// 选项更改
        /// </summary>
        void tvwParent_U_ItemSelectedChanged(TreeViewItem itemSelected)
        {
            //显示标题
            this.grpboxChild.Header = string.Format(this.grpboxChild.Tag.ToString(), itemSelected.Header.ToString());

            //获得筛选条件
            string strWhere = string.Format("{0}='{1}'", strRightTagColumn, itemSelected.Tag.ToString());
            strWhere = (strRightWhere.Length == 0 ? strWhere : strRightWhere + " AND " + strWhere);

            //设置默认值
            Dictionary<string, string> dicDefaultValues = new Dictionary<string, string>();
            if (strLeftNodeRelativeColumn.Length > 0)
            {
                dicDefaultValues.Add(strRightTagColumn, itemSelected.Tag.ToString());
            }
            else
            {
                dicDefaultValues.Add(strRightTagColumn, itemSelected.Tag.ToString());
            }

            //判断是否有组合属性列
            if (strSplitPropertyColumn.Length > 0)
            {
                LoadSplitPropertyTabControl(strWhere, dicDefaultValues);
            }
            else
            {
                LoadCommonDataGrid(strWhere, dicDefaultValues);
            }
        }

        /// <summary>
        /// 加载ucCommonDataGrid控件
        /// </summary>
        private void LoadCommonDataGrid(string Where, Dictionary<string, string> DefaultValues)
        {
            ucCommonDataGrid control = null;
            if (this.grpboxChild.Content == null)
            {
                control = new ucCommonDataGrid();
                control.Margin = new Thickness(1);
                this.grpboxChild.Content = control;

                control.U_TableName = strRightTable;
                control.U_Where = Where;
                control.U_TotalColumnName = string.Empty;

                control.U_DefaultRowValues = DefaultValues;
                control.U_AllowOperatData = boolAllowEdit;

                //初始化
                control.U_InitControl();

                //判断是否响应双击事件
                control.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
            }
            else
            {
                    control = this.grpboxChild.Content as ucCommonDataGrid;
                    control.U_Where = Where;
                    control.U_Update();
            }
        }

        /// <summary>
        /// 加载ucSplitPropertyTabControl控件
        /// </summary>
        private void LoadSplitPropertyTabControl(string Where, Dictionary<string, string> DefaultValues)
        {
            ucSplitPropertyGridTab control = null;
            if (!this.grpboxChild.HasContent)
            {
                control = new ucSplitPropertyGridTab();
                this.grpboxChild.Content = control;

                control.U_TableName = strRightTable;
                control.U_Where = Where;
                control.U_TotalColumnName = string.Empty;

                control.U_DefaultRowValues = DefaultValues;
                control.U_AllowChecked = boolAllowChecked;
                control.U_AllowOperatData = boolAllowEdit;

                control.U_SplitGroupColumn = strSplitGroupColumn;
                control.U_SplitGroupHeader = strSplitGroupHeader;
                //control.U_SplitPropertyColumn = strSplitPropertyColumn;
                control.U_SplitPropertyType = strSplitPropertyType;

                //初始化
                control.U_InitControl();

                //判断是否响应双击事件
                foreach (TabItem page in control.tabSplitProperty.Items)
                {
                    if (page.HasContent)
                    {
                        ucCommonDataGrid gridControl = page.Content as ucCommonDataGrid;
                        if (gridControl != null)
                        {
                            gridControl.gridApp.MouseDoubleClick+=new MouseButtonEventHandler(gridApp_MouseDoubleClick);
                        }
                    }
                }
            }
            else
            {
                if (this.grpboxChild.HasContent)
                {
                    control = grpboxChild.Content as ucSplitPropertyGridTab;
                    control.U_Where = Where;
                    control.U_Update();
                }
            }
        }

        /// <summary>
        /// 双击右侧数据记录
        /// </summary>
        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            UC.DataGridTemplate grid = e.Source as DataGridTemplate;
            if (grid != null && grid.SelectedItem != null)
            {
                //选中行
                grid.U_CheckedRow(true, (grid.SelectedItem as DataRowView));

                if (this.U_DoubleClick != null)
                {
                    //双击选中行
                    this.U_DoubleClick();
                }
            }
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        public void U_Update()
        {
            if (strSplitPropertyColumn.Length > 0)
            {
            }
            else
            {
                ucCommonDataGrid grid = this.grpboxChild.Content as ucCommonDataGrid;
                if (grid != null)
                {
                    grid.U_Update();
                }
            }
        }

        #region     ------CheckBox选中

        /// <summary>
        /// 获得所有选定的记录
        /// </summary>
        public DataRowView[] U_GetCheckedDataRows()
        {
            //判断是否有组合属性列
            if (strSplitPropertyColumn.Length > 0)
            {
                ucSplitPropertyGridTab control = this.grpboxChild.Content as ucSplitPropertyGridTab;
                if (control != null)
                {
                    return control.U_GetCheckedDataRows();
                }
            }
            else
            {
                ucCommonDataGrid control = this.grpboxChild.Content as ucCommonDataGrid;
                if (control != null)
                {
                    return control.U_GetCheckedDataRows();
                }
            }
            return null;
        }

        /// <summary>
        /// 取消选中行
        /// </summary>
        public void U_RejectCheckedRow()
        {
            //判断是否有组合属性列
            if (strSplitPropertyColumn.Length > 0)
            {
                ucSplitPropertyGridTab control = this.grpboxChild.Content as ucSplitPropertyGridTab;
                control.U_RejectCheckedRow();
            }
            else
            {
                ucCommonDataGrid control = this.grpboxChild.Content as ucCommonDataGrid;
                control.U_RejectCheckedRow();
            }
        }
        #endregion
    }
}
