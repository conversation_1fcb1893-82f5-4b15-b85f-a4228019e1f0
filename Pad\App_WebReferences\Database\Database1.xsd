<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
  <xs:element name="ExecuteNonQuery">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="strSQL" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ExecuteNonQueryResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ExecuteNonQueryResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="strSQL" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetListResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTableXmlSql">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="statementsql" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="paramObject" nillable="true" type="xs:anyType" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTableXmlSqlResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetTableXmlSqlResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="Save">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="dt" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element minOccurs="0" name="tablename" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SaveResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetModel">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="statementName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parameterObject" nillable="true" type="xs:anyType" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetModelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetModelResult" nillable="true" type="q1:ObjectT" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetModel1">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="statementName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parameterObject" nillable="true" type="xs:anyType" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetModel1Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetModel1Result" nillable="true" type="xs:anyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListObject">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="statementName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parameterObject" nillable="true" type="xs:anyType" />
        <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListObjectResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetListObjectResult" nillable="true" type="q2:ObjectTList" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsExistData">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="querySql" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="returnIndex" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsExistDataResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="IsExistDataResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>