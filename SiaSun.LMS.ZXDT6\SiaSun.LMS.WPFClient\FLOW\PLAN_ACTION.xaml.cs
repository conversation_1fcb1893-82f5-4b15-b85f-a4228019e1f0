﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.FLOW_ACTION
{
    /// <summary>
    /// FLOW_ACTION.xaml 的交互逻辑
    /// </summary>
    public partial class PLAN_ACTION : AvalonDock.DocumentContent
    {
        string strExceptPlanStatus = string.Empty;
        private IList<SiaSun.LMS.Model.FLOW_PARA> _lsFLOW_PARA;
        private string[] _aFLOW_PARA;

        //wdz add 2018-02-09
        string strStartDateTimeColumn = string.Empty;
        string strEndDateTimeColumn = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PLAN_ACTION()
        {
            InitializeComponent();

            this.ucQueryPlan.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                                       ( (QueryWhere)=>
                                           {
                                               try
                                               {
                                                   string strDateWhere = (this.panelDateTime.Visibility == System.Windows.Visibility.Visible ? this.GetDateStr() : string.Empty);
                                                   this.gridPlan.U_AppendWhere = string.Format("{0} AND {1}",
                                                                            string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                                                                            string.IsNullOrEmpty(strDateWhere) ? "1=1" : strDateWhere);
                                                   this.gridPlan.U_InitControl();
                                                   this.gridPlanList.U_DataSource = null;
                                                   this.ucFlowPlanAction.U_Clear();

                                                   this.gridPlanList.U_Clear();
                                                   this.SelectPlanDefault();
                                               }
                                               catch (Exception ex)
                                               {
                                                   MainApp._MessageDialog.ShowException(ex);
                                               }
                                           }
                                       );

            this.ucFlowPlanAction.U_ExecuteUpdate += new UC.ucStatusFlowActionsPanel.ExecuteUpdateEventHandler
                                                  (() =>
                                                    {
                                                        try
                                                        {
                                                            this.gridPlan.U_AppendWhere = this.ucQueryPlan.U_GetQuery();
                                                           
                                                            this.gridPlan.U_InitControl();

                                                            this.SelectPlanDefault();
                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            MainApp._MessageDialog.ShowException(ex);
                                                        }

                                                    }
                                                   ); 
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PLAN_ACTION(string EXCEPT_STATUS): this()
        {
            strExceptPlanStatus = EXCEPT_STATUS;
        }

        /// <summary>
        /// 构造函数
        /// wdz add 2018-02-09
        /// </summary>
        public PLAN_ACTION(string EXCEPT_STATUS, string startDateTimeColumn, string endDateTimeColumn) : this()
        {
            strExceptPlanStatus = EXCEPT_STATUS;
            strStartDateTimeColumn = startDateTimeColumn;
            strEndDateTimeColumn = endDateTimeColumn;
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //默认日期
            this.dtpStart.SelectedDate = DateTime.Now;
            this.dtpEnd.SelectedDate = DateTime.Now;
            this.chkboxDate.IsChecked = false;
            //是否显示时间段
            this.panelDateTime.Visibility = (string.IsNullOrEmpty(this.strStartDateTimeColumn) && string.IsNullOrEmpty(this.strEndDateTimeColumn)) ? Visibility.Collapsed : System.Windows.Visibility.Visible;

            this._lsFLOW_PARA = MainApp.I_SystemService.FlowGetParameters("FLOW_PLAN");

            this._aFLOW_PARA = new string[this._lsFLOW_PARA.Count];

            this.PLAN_Query_Bind();

            this.PLAN_Bind();

            this.gridPlan.gridApp.GotFocus += new RoutedEventHandler(gridApp_GotFocus);
        }

        void gridApp_GotFocus(object sender, RoutedEventArgs e)
        {
            this.gridPlan.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
            this.gridPlan.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        /// <summary>
        /// 
        /// </summary>
        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.gridPlan.gridApp.SelectedItem == null)
            {
                this.gridPlanList.U_DataSource = null;
                return;
            }

            DataRowView drvPlan = this.gridPlan.gridApp.SelectedItem as DataRowView;

            int intPlanID = drvPlan == null ? 0 : Convert.ToInt32(drvPlan["PLAN_ID"].ToString());

            this.PLAN_LIST_Bind(intPlanID);

            this.PLAN_ACTION_Bind(drvPlan.Row);

            this.gridPlan.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);

        }

        /// <summary>
        /// 
        /// </summary>
        private void PLAN_Query_Bind()
        {
            try
            {
                this.ucQueryPlan.U_XmlTableName = "V_PLAN";
                this.ucQueryPlan.U_WindowName = this.GetType().Name;
                this.ucQueryPlan.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void PLAN_Bind()
        {
            try
            {
                //设置数据源
                this.gridPlan.U_WindowName = this.GetType().Name;
                this.gridPlan.U_TableName = "V_PLAN";
                this.gridPlan.U_OrderField = "PLAN_ID";
                //设置筛选条件
                this.gridPlan.U_Where = strExceptPlanStatus.Length > 0 ? "PLAN_STATUS NOT IN('" + strExceptPlanStatus.Replace("|", "','") + "')" : string.Empty;

                this.gridPlan.U_AllowChecked = false;
                this.gridPlan.U_AllowOperatData = true;
                this.gridPlan.U_AllowAdd = Visibility.Collapsed;
                this.gridPlan.U_AllowDelete = Visibility.Collapsed;
                this.gridPlan.U_AllowEdit = Visibility.Collapsed;
                this.gridPlan.U_AllowCancel = Visibility.Collapsed;
                this.gridPlan.U_AllowSave = Visibility.Visible;
                this.gridPlan.U_SaveDataTable = "PLAN_MAIN";

                this.gridPlan.U_AllowPage = true;

                //初始化控件
                this.gridPlan.U_InitControl();

                //加载默认数据
                this.SelectPlanDefault();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 加载默认数据
        /// </summary>
        private void SelectPlanDefault()
        {
            if (this.gridPlan.gridApp.SelectedItem != null)
            {
                DataRowView drvPlan = this.gridPlan.gridApp.SelectedItem as DataRowView;
                int intPlanID = drvPlan == null ? 0 : Convert.ToInt32(drvPlan["PLAN_ID"].ToString());

                //加载计划清单
                this.PLAN_LIST_Bind(intPlanID);

                //流程行为按钮绑定
                this.PLAN_ACTION_Bind(drvPlan.Row);
            }
        }


        /// <summary>
        /// 加载
        /// </summary>
        private void PLAN_LIST_Bind(int PLAN_ID)
        {
            //数据源属性
            this.gridPlanList.U_Clear();
            this.gridPlanList.U_WindowName = this.GetType().Name;
            this.gridPlanList.U_TableName = "V_PLAN_LIST";
            this.gridPlanList.U_XmlTableName = "V_PLAN_LIST";
            this.gridPlanList.U_Fields = "*";
            this.gridPlanList.U_Where = string.Format("PLAN_ID ={0}", PLAN_ID);
            this.gridPlanList.U_OrderField = "PLAN_LIST_ID";

            this.gridPlanList.U_AllowChecked = false;
            this.gridPlanList.U_AllowOperatData = false;
            this.gridPlanList.U_AllowShowPage =false;
            
            //拆分列属性
            this.gridPlanList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridPlanList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridPlanList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridPlanList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";

            //明细属性
            this.gridPlanList.U_DetailTableName = "PLAN_DETAIL";
            this.gridPlanList.U_DetailRelatvieColumn = "PLAN_LIST_ID";

            try
            {
                //初始化控件
                this.gridPlanList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }




        /// <summary>
        /// 状态流按钮加载
        /// </summary>
        private void PLAN_ACTION_Bind(DataRow rowPlan)
        {
            //初始化控件
            this.ucFlowPlanAction.U_AddLog = true;
            this.ucFlowPlanAction.U_FLOW_TYPE = "FLOW_PLAN";
            this.ucFlowPlanAction.U_FLOW_SOURCE_ROW = rowPlan;
            this.ucFlowPlanAction.U_DataSource = MainApp.I_SystemService.PlanGetAction(rowPlan["PLAN_ID"].ToString());
            this.ucFlowPlanAction.U_InitControl();
        }


        /// <summary>
        /// 获得时间查询条件
        /// </summary>
        private string GetDateStr()
        {
            string strDateWhere = string.Empty;

            //判断是否添加时间查询
            if (chkboxDate.IsChecked == true)
            {
                //判断选择的值
                if (this.dtpStart.SelectedDate.HasValue && !this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00')",
                                                                               strStartDateTimeColumn,
                                                                               this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (!this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}<='{1} 24:60:60')",
                                                                              strEndDateTimeColumn,
                                                                              this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00' AND {2}<='{3} 24:60:60')",
                                                                            strStartDateTimeColumn,
                                                                            this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"),
                                                                            strEndDateTimeColumn,
                                                                            this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));

                }
            }
            return strDateWhere;
        }
    }
}
