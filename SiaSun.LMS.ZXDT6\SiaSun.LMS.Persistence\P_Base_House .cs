﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using IBatisNet.DataMapper;
using System.Collections;
using System.Data;
using IBatisNet.DataMapper.Configuration;

namespace SiaSun.LMS.Persistence
{
    public class P_Base_House : P_Base
    {
        public new static ISqlMapper _sqlMap;
        public static bool _isOracleProvider;

        public P_Base_House()
        {
            _isOracleProvider = Common.StringUtil.GetConfig("DatabaseType").ToLower().Contains("oracle");

            if (_sqlMap == null)
            {
                if (_sqlMap == null)
                {
                    DomSqlMapBuilder builder = new DomSqlMapBuilder();
                    _sqlMap = builder.Configure(_isOracleProvider ? "HouseMap_Oracle.config" : "HouseMap_SQLServer.config");
                }
            }

            base._sqlMap = _sqlMap;
        }



    
    }
}