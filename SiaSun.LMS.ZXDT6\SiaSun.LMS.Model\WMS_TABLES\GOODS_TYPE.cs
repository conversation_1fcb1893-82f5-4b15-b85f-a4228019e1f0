﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_TYPE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_TYPE
	{
		public GOODS_TYPE()
		{
			
		}
		
		private int _goods_type_id;
		private string _goods_type_code;
		private string _goods_type_name;
		private string _goods_type_remark;
		private int _goods_type_order;
		private string _goods_type_flag;
		
		///<sumary>
		/// 物料类型编号
        ///</sumary>
        [DataMember]
		public int GOODS_TYPE_ID
		{
			get{return _goods_type_id;}
			set{_goods_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string GOODS_TYPE_CODE
		{
			get{return _goods_type_code;}
			set{_goods_type_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string GOODS_TYPE_NAME
		{
			get{return _goods_type_name;}
			set{_goods_type_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string GOODS_TYPE_REMARK
		{
			get{return _goods_type_remark;}
			set{_goods_type_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int GOODS_TYPE_ORDER
		{
			get{return _goods_type_order;}
			set{_goods_type_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string GOODS_TYPE_FLAG
		{
			get{return _goods_type_flag;}
			set{_goods_type_flag = value;}
		}
	}
}
