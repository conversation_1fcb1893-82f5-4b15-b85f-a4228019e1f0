﻿<UserControl
    x:Class="SiaSun.LMS.WPFClient.UC.ucCommonDataGrid"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:exa="clr-namespace:ExampleClassLibrary;assembly=ExampleClassLibrary"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    d:DesignHeight="300"
    d:DesignWidth="806"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ContextMenu x:Key="menuGrid" MenuItem.Click="menuGrid_Click">
            <MenuItem Name="menuItemCopy" Header="复制" />
            <!--<MenuItem Name="menuItemDeepCopy" Header="深度复制"></MenuItem>-->
            <MenuItem Name="menuItemSetDefault" Header="重置" />
        </ContextMenu>
    </UserControl.Resources>

    <Grid Name="aa">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <!--<ExtendedGridControl:ExtendedDataGrid x:Name="gridApp" AutoGenerateColumns="False" Grid.Row="0" Margin="1,1,1,0"  MouseDoubleClick="gridApp_MouseDoubleClick"></ExtendedGridControl:ExtendedDataGrid>-->
        <!--<uc:DataGridTemplate x:Name="gridApp" AutoGenerateColumns="False" Grid.Row="0" Margin="1,1,1,0"  MouseDoubleClick="gridApp_MouseDoubleClick"  />-->

        <uc:DataGridTemplate
            x:Name="gridApp"
            Grid.Row="0"
            Margin="1,1,1,0"
            AutoGenerateColumns="False"
            ContextMenu="{StaticResource menuGrid}"
            MouseDoubleClick="gridApp_MouseDoubleClick" />


        <Border
            Grid.Row="1"
            Margin="1"
            BorderBrush="#FF888888"
            BorderThickness="1">
            <WrapPanel
                Name="panelToolbar"
                Height="23"
                VerticalAlignment="Bottom">
                <WrapPanel.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Offset="0.2" Color="White" />
                        <GradientStop Offset="0.6" Color="#FFCDDBF5" />
                        <GradientStop Offset="1" Color="LightBlue" />
                    </LinearGradientBrush>
                </WrapPanel.Background>
                <CheckBox
                    Name="chkBoxCheckAll"
                    Margin="2,0,5,0"
                    VerticalAlignment="Center"
                    Click="chkBoxCheckAll_Checked"
                    Visibility="Collapsed" />

                <StackPanel
                    Name="tbarButton"
                    ButtonBase.Click="tbarButton_Click"
                    Orientation="Horizontal">
                    <Button Name="btnAdd" Template="{StaticResource templateImageButtonAdd}" />
                    <Button Name="btnEdit" Template="{StaticResource templateImageButtonEdit}" />
                    <Button Name="btnDelete" Template="{StaticResource templateImageButtonDelete}" />
                    <Button Name="btnCancel" Template="{StaticResource templateImageButtonCancel}" />
                    <Button Name="btnSave" Template="{StaticResource templateImageButtonSave}" />

                </StackPanel>

                <StackPanel
                    Name="tarCommon"
                    Margin="1"
                    ButtonBase.Click="tarCommon_Click"
                    Orientation="Horizontal">
                    <GridSplitter
                        Width="1"
                        Margin="2"
                        HorizontalAlignment="Stretch"
                        Background="LightSlateGray" />
                    <!--<Button Name="btnDetail" Template="{StaticResource templateImageButtonDetail}"></Button>-->
                    <!--<Button Name="btnPrint" Template="{StaticResource templateImageButtonPrint}" ></Button>-->
                    <Button Name="btnExportExcel" Template="{StaticResource templateImageButtonExportExcel}" />
                    <Button Name="btnQuery" Template="{StaticResource templateImageButtonSearch}" />
                    <Button Name="btnRefresh" Template="{StaticResource templateImageButtonUpdate}" />
                    <GridSplitter
                        Width="1"
                        Margin="2"
                        HorizontalAlignment="Stretch"
                        Background="LightSlateGray" />
                </StackPanel>

                <StackPanel
                    Name="tbarPage"
                    ButtonBase.Click="tbarPage_Click"
                    Orientation="Horizontal">
                    <Button Name="btnFirstPage" Template="{StaticResource templateImageButtonFirst}" />
                    <Button Name="btnPreviousPage" Template="{StaticResource templateImageButtonBack}" />
                    <Button Name="btnNextPage" Template="{StaticResource templateImageButtonNext}" />
                    <Button Name="btnLastPage" Template="{StaticResource templateImageButtonLast}" />

                    <TextBox
                        Name="txtChangeRowPrePage"
                        Width="60"
                        Margin="5,0,1,0"
                        VerticalAlignment="Center"
                        KeyDown="PageTextBox_KeyDown" />
                    <TextBlock
                        Name="txtLineNote"
                        Margin="1,1,3,1"
                        VerticalAlignment="Center"
                        Tag="行/每页   共{0}行">
                        行/每页   共0行
                    </TextBlock>

                    <Separator />
                    <TextBlock Margin="5,1,1,1" VerticalAlignment="Center">第</TextBlock>
                    <TextBox
                        Name="txtJumpPage"
                        Width="60"
                        Margin="5,0,1,0"
                        VerticalAlignment="Center"
                        KeyDown="PageTextBox_KeyDown" />
                    <TextBlock
                        Name="txtPageNote"
                        Margin="5,1,1,1"
                        VerticalAlignment="Center"
                        Tag="页   共{0}页">
                        页   共0页
                    </TextBlock>
                </StackPanel>
                <TextBlock
                    Name="txtTotal"
                    Margin="10,1,1,1"
                    VerticalAlignment="Center"
                    Tag="合计:{0} 总计:{1}"
                    Visibility="Collapsed">
                    合计:0  总计:0
                </TextBlock>
            </WrapPanel>
        </Border>
    </Grid>
</UserControl>
