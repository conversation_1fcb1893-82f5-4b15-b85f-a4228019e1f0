﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
    /// LED_MAIN
	/// </summary>
    public class P_LED_MAIN : P_Base_House
	{
        public P_LED_MAIN()
		{
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<LED_MAIN> GetList()
        {
            return this.ExecuteQueryForList<LED_MAIN>("LED_MAIN_SELECT_BY_ID", null);
        }

        /// <summary>
        /// 得到列表-根据LED_IP,DEVICE_CODE
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IList<LED_MAIN> GetList_LED_IP_DEVICE_CODE(string LED_IP, string DEVICE_CODE)
        {
            Hashtable ht = new Hashtable();
            ht.Add("LED_IP", LED_IP);
            ht.Add("DEVICE_CODE", DEVICE_CODE);
            return this.ExecuteQueryForList<LED_MAIN>("LED_MAIN_SELECT_BY_LED_IP_DEVICE_CODE", ht);
        }

        /// <summary>
        /// 得到列表-根据DEVICE_CODE,AUTO_FLAG
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IList<LED_MAIN> GetList_DEVICE_CODE_AUTO_FLAG(string DEVICE_CODE, string AUTO_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("DEVICE_CODE", DEVICE_CODE);
            ht.Add("AUTO_FLAG", AUTO_FLAG);
            return this.ExecuteQueryForList<LED_MAIN>("LED_MAIN_SELECT_BY_DEVICE_CODE_AUTO_FLAG", ht);
        }

        /// <summary>
        /// 得到列表-根据AUTO_FLAG,LED_STATUS
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IList<LED_MAIN> GetList_AUTO_FLAG_LED_STATUS(string AUTO_FLAG, string LED_STATUS)
        {
            Hashtable ht = new Hashtable();
            ht.Add("AUTO_FLAG", AUTO_FLAG);
            ht.Add("LED_STATUS", LED_STATUS);
            return this.ExecuteQueryForList<LED_MAIN>("LED_MAIN_SELECT_BY_AUTO_FLAG_LED_STATUS", ht);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public LED_MAIN GetModel(int LED_ID)
        {
            return this.ExecuteQueryForObject<LED_MAIN>("LED_MAIN_SELECT_BY_ID", LED_ID);
        }


		/// <summary>
		/// 新建
		/// </summary>
        public void Add(LED_MAIN led_main)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("LED_MAIN");
                led_main.LED_ID = id;
            }
            this.ExecuteInsert("LED_MAIN_INSERT", led_main);
		}

		/// <summary>
		/// 修改
		/// </summary>
        public void Update(LED_MAIN led_main)
		{
            this.ExecuteUpdate("LED_MAIN_UPDATE", led_main);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
        public void Delete(System.Int32 LED_ID)
		{
            this.ExecuteDelete("LED_MAIN_DELETE", LED_ID);
		}
		

	}
}
