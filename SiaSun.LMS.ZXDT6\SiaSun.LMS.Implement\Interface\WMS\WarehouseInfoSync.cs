using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 仓库信息同步接口
    /// </summary>
    public class WarehouseInfoSync : InterfaceBase
    {
        class InputParam
        {
            public int isHaveMaterials { get; set; }
            public string contactId { get; set; }
            public string contactName { get; set; }
            public string gsId { get; set; }
            public string gsName { get; set; }
            public string bmId { get; set; }
            public string bmName { get; set; }
            public string teamName { get; set; }
            public string warehouseStatus { get; set; }
            public string remark { get; set; }
            public string warehouseLevel { get; set; }
            public string describe { get; set; }
            public string isDeliveryAddress { get; set; }
            public string isVirtualWarehouse { get; set; }
            public string physicalAddress { get; set; }
            public string stationPlace { get; set; }
            public string roomPlace { get; set; }
            public string floor { get; set; }
            public string teamId { get; set; }
            public decimal warehouseArea { get; set; }
            public decimal warehouseOutRoomArea { get; set; }
            public string trainStation { get; set; }
            public string belongingPlace { get; set; }
            public decimal warehouseRoomArea { get; set; }
            public string phone { get; set; }
            public string addressType { get; set; }
            public string warehouseChargeId { get; set; }
            public string warehouseChargeName { get; set; }
            public string warehouseAddress { get; set; }
            public string warehouseType { get; set; }
            public string lineId { get; set; }
            public string lineName { get; set; }
            public string mangeUserId { get; set; }
            /// <summary>
            /// 仓库名称(WAREHOUSE_NAME)
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 仓库编码(WAREHOUSE_CODE)
            /// </summary>
            public string code { get; set; }
            public List<WarehouseShelfRel> warehouseShelfRelList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class WarehouseShelfRel
        {
            public string shelfCode { get; set; }
            /// <summary>
            /// 仓库ID（WAREHOUSE_REMARK）
            /// </summary>
            public string warehouseId { get; set; }
            public string qrCode { get; set; }
            public int shelfStatus { get; set; }
            public string describe { get; set; }
            public string shelfName { get; set; }
            public string shelfId { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                // JSON parsing
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    resultCode = 2;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Required field validation
                if (string.IsNullOrEmpty(inputParam.code))
                {
                    resultCode = 2;
                    message = "接口入参必填项[code]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    resultCode = 2;
                    message = "接口入参必填项[name]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate warehouse status (ENABLED/DISABLED)
                if (!string.IsNullOrEmpty(inputParam.warehouseStatus) && 
                    inputParam.warehouseStatus != "ENABLED" && 
                    inputParam.warehouseStatus != "DISABLED")
                {
                    resultCode = 2;
                    message = "接口入参[warehouseStatus]值无效，必须为ENABLED或DISABLED";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate nested array items
                if (inputParam.warehouseShelfRelList != null && inputParam.warehouseShelfRelList.Count > 0)
                {
                    foreach (var shelfRel in inputParam.warehouseShelfRelList)
                    {
                        if (string.IsNullOrEmpty(shelfRel.shelfCode))
                        {
                            resultCode = 2;
                            message = "接口入参warehouseShelfRelList中必填项[shelfCode]存在空值";
                            return FormatResponse(resultCode, message, traceId);
                        }

                        if (string.IsNullOrEmpty(shelfRel.shelfName))
                        {
                            resultCode = 2;
                            message = "接口入参warehouseShelfRelList中必填项[shelfName]存在空值";
                            return FormatResponse(resultCode, message, traceId);
                        }
                    }
                }

                // Database operations - Process warehouse information
                // Since there's no specific warehouse model in the current system, we'll use SYS_ITEM
                // which appears to be used for system configuration items including warehouses
                
                // Check if warehouse already exists by code
                //var existingWarehouses = S_Base.sBase.pSYS_ITEM.GetList($"ITEM_CODE = '{inputParam.code}' AND ITEM_TYPE = 'WAREHOUSE'");
                
                //if (existingWarehouses != null && existingWarehouses.Count > 0)
                //{
                //    // Update existing warehouse
                //    var warehouseItem = existingWarehouses[0];
                //    warehouseItem.ITEM_NAME = inputParam.name;
                //    warehouseItem.ITEM_REMARK = $"Status:{inputParam.warehouseStatus}|Type:{inputParam.warehouseType}|Address:{inputParam.warehouseAddress}";
                //    warehouseItem.ITEM_FLAG = inputParam.status.ToString();
                    
                //    S_Base.sBase.pSYS_ITEM.Update(warehouseItem);
                //    message = "仓库信息更新成功";
                //}
                //else
                //{
                //    // Create new warehouse
                //    var newWarehouse = new Model.SYS_ITEM()
                //    {
                //        ITEM_CODE = inputParam.code,
                //        ITEM_NAME = inputParam.name,
                //        ITEM_TYPE = "WAREHOUSE",
                //        ITEM_REMARK = $"Status:{inputParam.warehouseStatus}|Type:{inputParam.warehouseType}|Address:{inputParam.warehouseAddress}",
                //        ITEM_FLAG = inputParam.status.ToString(),
                //        ITEM_ORDER = 0
                //    };
                    
                //    S_Base.sBase.pSYS_ITEM.Add(newWarehouse);
                //    message = "仓库信息创建成功";
                //}

                // Process warehouse-shelf relationships
                //if (inputParam.warehouseShelfRelList != null && inputParam.warehouseShelfRelList.Count > 0)
                //{
                //    foreach (var shelfRel in inputParam.warehouseShelfRelList)
                //    {
                //        // Check if shelf relationship already exists
                //        var existingShelves = S_Base.sBase.pSYS_ITEM.GetList($"ITEM_CODE = '{shelfRel.shelfCode}' AND ITEM_TYPE = 'SHELF'");
                        
                //        if (existingShelves != null && existingShelves.Count > 0)
                //        {
                //            // Update existing shelf
                //            var shelfItem = existingShelves[0];
                //            shelfItem.ITEM_NAME = shelfRel.shelfName;
                //            shelfItem.ITEM_REMARK = $"WarehouseId:{shelfRel.warehouseId}|QRCode:{shelfRel.qrCode}|Status:{shelfRel.shelfStatus}|Desc:{shelfRel.describe}";
                //            shelfItem.ITEM_PARENT_ID = string.IsNullOrEmpty(inputParam.id) ? 0 : int.TryParse(inputParam.id, out int warehouseId) ? warehouseId : 0;
                            
                //            S_Base.sBase.pSYS_ITEM.Update(shelfItem);
                //        }
                //        else
                //        {
                //            // Create new shelf relationship
                //            var newShelf = new Model.SYS_ITEM()
                //            {
                //                ITEM_CODE = shelfRel.shelfCode,
                //                ITEM_NAME = shelfRel.shelfName,
                //                ITEM_TYPE = "SHELF",
                //                ITEM_REMARK = $"WarehouseId:{shelfRel.warehouseId}|QRCode:{shelfRel.qrCode}|Status:{shelfRel.shelfStatus}|Desc:{shelfRel.describe}",
                //                ITEM_FLAG = shelfRel.shelfStatus.ToString(),
                //                ITEM_ORDER = 0,
                //                ITEM_PARENT_ID = string.IsNullOrEmpty(inputParam.id) ? 0 : int.TryParse(inputParam.id, out int warehouseId) ? warehouseId : 0
                //            };
                            
                //            S_Base.sBase.pSYS_ITEM.Add(newShelf);
                //        }
                //    }
                //}
                
                message = "仓库信息处理成功";
                
                // Log the operation
                S_Base.sBase.Log.Info($"WarehouseInfoSync处理成功_入参[{inputJson}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"WarehouseInfoSync处理异常_入参[{inputJson}]_异常[{ex.Message}]_traceId[{traceId}]");
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = resultCode,
                    msg = message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = code,
                msg = msg,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}