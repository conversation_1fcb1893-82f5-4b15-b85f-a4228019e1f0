﻿using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement;

namespace UnitTest
{
    [TestClass]
    public class UnitTest_Implement
    {
        [TestMethod]
        public void Test_S_System()
        {
            S_System sSystem = new S_System();
            //sSystem.PrintCellCode("select CELL_CODE from WH_CELL t where area_id=3 and cell_code like '01-02-01' order by cell_id");

            //var result = sSystem.UnitSeparater("default", "box", 1111);
            //Assert.IsNotNull(result);
            //result = sSystem.UnitSeparater("default", "pcs", 111111);
            //Assert.IsNotNull(result);

            //var result1 = sSystem.UnitConverter("default", "carton", "pcs", 1);
            //Assert.AreEqual(result1, 1000);
            //result1 = sSystem.UnitConverter("default", "carton", "box", 1);
            //Assert.AreEqual(result1, 10);

            //var result2 = sSystem.CellInit();
            //Assert.IsTrue(result2);
        }

        [TestMethod]
        public void Test_S_Device()
        {
            S_Device sDevice = new S_Device();

            //var result = sDevice.CartonGetStorageInfo("test", "T000000134");
            //Assert.IsTrue(result.DataList.Count > 0);

            //var result1 = sDevice.CartonBackToProduct("test", "T000000134");
            //Assert.IsTrue(result1.Flag);

            //var result2 = sDevice.CartonGetCellTaskInfo("test", "02-50-01");
            //Assert.IsTrue(result2.Flag);
        }

        [TestMethod]
        public void Test_S_Plan()
        {
            S_Plan sPlan = new S_Plan();

            var result = sPlan.PlanOutDownLoad(13242, "test", true, "12111", out string message);

            Assert.IsTrue(result);
        }
    }
}
