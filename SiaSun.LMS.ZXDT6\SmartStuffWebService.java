package com.wuxicloud.admin;

import com.alibaba.fastjson.JSONObject;
import com.wuxicloud.admin.ws.prearchives.WebServiceInfo;
import com.wuxicloud.admin.ws.prearchives.WebServiceUtil;
import com.wuxicloud.common.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmartStuffWebService {

    private final String URL = "http://172.10.6.35:9008/webservice/AuthService?wsdl";


    /**
     * 物资系统接口webservice auth api step 1
     *
     * @return publicKey
     */
    private String regist(String systemIdentify) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("systemIdentify", systemIdentify);
        WebServiceInfo webServiceInfo = WebServiceUtil.invokeWebservice(URL, requestJson.toJSONString(), "regist");
        Object responseMsg = webServiceInfo.getResponseMsg();
        JSONObject responseJsonObj = JSONObject.parseObject(responseMsg.toString());
        String code = responseJsonObj.getString("code");
        if ("200".equals(code)) {
            return responseJsonObj.getString("data");
        } else {
            String errorMsg = responseJsonObj.getString("message");
            log.error("调用物资系统【regist】服务出错，错误原因{}", errorMsg);
            webServiceInfo.setErrorMsg(errorMsg);
            throw new BaseException("调用物资系统【regist】服务出错，错误原因"+errorMsg);
        }
    }


    /**
     * 物资系统系统 webservice auth api step 2
     *
     * @param systemIdentify    系统标识
     * @param interfaceIdentify 接口标识
     * @param publicKey         step 1 返回的密钥
     * @return token
     */
    private String token(String systemIdentify, String interfaceIdentify, String publicKey) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("systemIdentify", systemIdentify);
        requestJson.put("interfaceIdentify", interfaceIdentify);
        requestJson.put("publicKey", publicKey);
        WebServiceInfo webServiceInfo = WebServiceUtil.invokeWebservice(URL, requestJson.toJSONString(), "getCertificate");
        Object responseMsg = webServiceInfo.getResponseMsg();
        JSONObject responseJsonObj = JSONObject.parseObject(responseMsg.toString());
        String code = responseJsonObj.getString("code");
        if ("200".equals(code)) {
            return responseJsonObj.getString("data");
        } else {
            String errorMsg = responseJsonObj.getString("message");
            log.error("调用物资系统【getCertificate】服务出错，错误原因{}", errorMsg);
            webServiceInfo.setErrorMsg(errorMsg);
            throw new BaseException("调用物资系统【getCertificate】服务出错，错误原因"+errorMsg);
        }
    }

    /**
     * 调用物资系统webservice服务
     *
     * @return json
     */
    private String accessInterface(String systemIdentify, String interfaceIdentify, String token, String request) {
        // 封装请求体
        JSONObject requestJson = new JSONObject();
        requestJson.put("systemIdentify", systemIdentify);
        requestJson.put("interfaceIdentify", interfaceIdentify);
        requestJson.put("certificate", token);
        requestJson.put("json", request);

        // 调用请求
        WebServiceInfo webServiceInfo = WebServiceUtil.invokeWebservice(URL, requestJson.toJSONString(), "accessInterface");
        Object responseMsg = webServiceInfo.getResponseMsg();
        JSONObject responseJsonObj = JSONObject.parseObject(responseMsg.toString());
        String code = responseJsonObj.getString("code");
        if ("200".equals(code)) {
            return responseJsonObj.getString("data");
        } else {
            String errorMsg = responseJsonObj.getString("message");
            log.error("调用物资系统【accessInterface】服务出错，错误原因{}", errorMsg);
            webServiceInfo.setErrorMsg(errorMsg);
            throw new BaseException("调用物资系统【accessInterface】服务出错，错误原因"+errorMsg);
        }
    }

//    @Async
    public void invoke(String systemIdentify, String interfaceIdentify, String request) {
        log.info("+++++++++++++++++++++++++开始调用物资webservice++++++++++++++++++++++++++");
        String publicKey = regist(systemIdentify);
        log.info("获取PUBLIC_KEY成功:{}",publicKey);
        String token = token(systemIdentify, interfaceIdentify, publicKey);
        log.info("获取TOKEN成功:{}",token);
        log.info("++++++++++调用accessInterface++++++++++++");
        accessInterface(systemIdentify, interfaceIdentify, token, request);
        System.out.println("服务调用成功!");
    }


    public static void main(String[] args) {
        SmartStuffWebService smartStuffWebService = new SmartStuffWebService();
        String json = "{\n" +
                "    \"stockTakeId\": \"84748e90507646b98cf3ea46017e2f7a\",\n" +
                "    \"stockTakeName\": \"20250619盘点计划\",\n" +
                "    \"stockTakeCode\": null,\n" +
                "    \"stockTakeResultGoodsList\": [\n" +
                "        {\n" +
                "            \"createDate\": \"2025-06-19 16:31:28\",\n" +
                "            \"createUser\": \"\",\n" +
                "            \"createName\": \"\",\n" +
                "            \"updateDate\": \"2025-06-19 16:31:28\",\n" +
                "            \"updateUser\": \"\",\n" +
                "            \"updateName\": \"\",\n" +
                "            \"id\": \"0d0c0476d1104331838ceb989f95ba87\",\n" +
                "            \"status\": null,\n" +
                "            \"billCode\": null,\n" +
                "            \"inventoryId\": \"a88dd2765e46497bb014d81e448f5879\",\n" +
                "            \"stockTakeTaskId\": \"749b237528a5497083b1071e5ebe12ba\",\n" +
                "            \"stockTakeTaskName\": \"20250619盘点计划-盘点任务\",\n" +
                "            \"stockTakeTaskCode\": \"PDRW20250619055\",\n" +
                "            \"stockTakeResultId\": \"6b91fbfe4e224d64a7db8bf59ef08b76\",\n" +
                "            \"goodsId\": \"977f5b63ba01445cbb83a137015bc10c\",\n" +
                "            \"goodsCode\": \"0102090100114001\",\n" +
                "            \"goodsName\": \"牵引电机加油嘴\",\n" +
                "            \"goodsVersion\": \"加油嘴（6号线）\",\n" +
                "            \"storageNum\": 150,\n" +
                "            \"lockNum\": null,\n" +
                "            \"stockTakeNum\": 150,\n" +
                "            \"stockTakeOverNum\": null,\n" +
                "            \"stockTakeFloorNum\": null,\n" +
                "            \"unitId\": \"55927cb0062a491db40d9eb4a6edac9f\",\n" +
                "            \"unitName\": \"个\",\n" +
                "            \"brand\": \"博士\",\n" +
                "            \"warehouseId\": \"bbd71e62c4294eb1a8374a82d4f53c23\",\n" +
                "            \"warehouseName\": \"徐铁总库\",\n" +
                "            \"warehouseCode\": \"XZDT00001\",\n" +
                "            \"shelfId\": \"52ce757dff3a4c849ec1a725d9719eea\",\n" +
                "            \"shelfName\": \"机电部货架0001\",\n" +
                "            \"shelfCode\": \"YY-JDB0001\",\n" +
                "            \"stockTakeUserId\": \"03d08490a23e46d2b20f669126d72dc6\",\n" +
                "            \"stockTakeUserName\": \"刘为\",\n" +
                "            \"stockTakePlanStartDate\": \"2025-06-19\",\n" +
                "            \"stockTakePlanEndDate\": \"2025-06-27\",\n" +
                "            \"sourceType\": null,\n" +
                "            \"entityModelId\": \"2a4053a615ce47a29fb876df3e7dd24e\",\n" +
                "            \"processDefinitionId\": null,\n" +
                "            \"processInstanceId\": null,\n" +
                "            \"processInstanceKey\": null,\n" +
                "            \"nextProcessUserList\": null,\n" +
                "            \"attachmentFileName\": null,\n" +
                "            \"attachmentFileUrl\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"createDate\": \"2025-06-19 16:31:28\",\n" +
                "            \"createUser\": \"\",\n" +
                "            \"createName\": \"\",\n" +
                "            \"updateDate\": \"2025-06-19 16:31:28\",\n" +
                "            \"updateUser\": \"\",\n" +
                "            \"updateName\": \"\",\n" +
                "            \"id\": \"7c0a5e622b164cddbde8dfd00d308804\",\n" +
                "            \"status\": null,\n" +
                "            \"billCode\": null,\n" +
                "            \"inventoryId\": \"3e6b39aea493411585222be4d11930b5\",\n" +
                "            \"stockTakeTaskId\": \"749b237528a5497083b1071e5ebe12ba\",\n" +
                "            \"stockTakeTaskName\": \"20250619盘点计划-盘点任务\",\n" +
                "            \"stockTakeTaskCode\": \"PDRW20250619055\",\n" +
                "            \"stockTakeResultId\": \"6b91fbfe4e224d64a7db8bf59ef08b76\",\n" +
                "            \"goodsId\": \"13636b8e12664337b7c71fef9bba5d85\",\n" +
                "            \"goodsCode\": \"0102090100150301\",\n" +
                "            \"goodsName\": \"转向架-空气弹簧\",\n" +
                "            \"goodsVersion\": \"SYS505C6\",\n" +
                "            \"storageNum\": 100,\n" +
                "            \"lockNum\": null,\n" +
                "            \"stockTakeNum\": 110,\n" +
                "            \"stockTakeOverNum\": null,\n" +
                "            \"stockTakeFloorNum\": null,\n" +
                "            \"unitId\": \"55927cb0062a491db40d9eb4a6edac9f\",\n" +
                "            \"unitName\": \"个\",\n" +
                "            \"brand\": \"中车\",\n" +
                "            \"warehouseId\": \"bbd71e62c4294eb1a8374a82d4f53c23\",\n" +
                "            \"warehouseName\": \"徐铁总库\",\n" +
                "            \"warehouseCode\": \"XZDT00001\",\n" +
                "            \"shelfId\": \"52ce757dff3a4c849ec1a725d9719eea\",\n" +
                "            \"shelfName\": \"机电部货架0001\",\n" +
                "            \"shelfCode\": \"YY-JDB0001\",\n" +
                "            \"stockTakeUserId\": \"03d08490a23e46d2b20f669126d72dc6\",\n" +
                "            \"stockTakeUserName\": \"刘为\",\n" +
                "            \"stockTakePlanStartDate\": \"2025-06-19\",\n" +
                "            \"stockTakePlanEndDate\": \"2025-06-27\",\n" +
                "            \"sourceType\": null,\n" +
                "            \"entityModelId\": \"2a4053a615ce47a29fb876df3e7dd24e\",\n" +
                "            \"processDefinitionId\": null,\n" +
                "            \"processInstanceId\": null,\n" +
                "            \"processInstanceKey\": null,\n" +
                "            \"nextProcessUserList\": null,\n" +
                "            \"attachmentFileName\": null,\n" +
                "            \"attachmentFileUrl\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"createDate\": \"2025-06-19 16:31:42\",\n" +
                "            \"createUser\": \"\",\n" +
                "            \"createName\": \"\",\n" +
                "            \"updateDate\": \"2025-06-19 16:31:42\",\n" +
                "            \"updateUser\": \"\",\n" +
                "            \"updateName\": \"\",\n" +
                "            \"id\": \"e2e37bc39c324994a627d8824d76cec0\",\n" +
                "            \"status\": null,\n" +
                "            \"billCode\": null,\n" +
                "            \"inventoryId\": \"90dd8a4f019c412eac0a5147e8683bd4\",\n" +
                "            \"stockTakeTaskId\": \"a2213034244e40b687543ee1dd946f3f\",\n" +
                "            \"stockTakeTaskName\": \"20250619盘点计划-盘点任务\",\n" +
                "            \"stockTakeTaskCode\": \"PDRW20250619056\",\n" +
                "            \"stockTakeResultId\": \"6b91fbfe4e224d64a7db8bf59ef08b76\",\n" +
                "            \"goodsId\": \"428fb16cf763415aba9c0ccb296423cf\",\n" +
                "            \"goodsCode\": \"0102090100149701\",\n" +
                "            \"goodsName\": \"转向架-动车轮对\",\n" +
                "            \"goodsVersion\": \"动车轮对（6号线）\",\n" +
                "            \"storageNum\": 100,\n" +
                "            \"lockNum\": null,\n" +
                "            \"stockTakeNum\": 98,\n" +
                "            \"stockTakeOverNum\": null,\n" +
                "            \"stockTakeFloorNum\": null,\n" +
                "            \"unitId\": \"55927cb0062a491db40d9eb4a6edac9f\",\n" +
                "            \"unitName\": \"个\",\n" +
                "            \"brand\": \"中车\",\n" +
                "            \"warehouseId\": \"bbd71e62c4294eb1a8374a82d4f53c23\",\n" +
                "            \"warehouseName\": \"徐铁总库\",\n" +
                "            \"warehouseCode\": \"XZDT00001\",\n" +
                "            \"shelfId\": \"52ce757dff3a4c849ec1a725d9719eea\",\n" +
                "            \"shelfName\": \"机电部货架0001\",\n" +
                "            \"shelfCode\": \"YY-JDB0001\",\n" +
                "            \"stockTakeUserId\": \"46da1318c9a244fbb1f20ed1082bb9ad\",\n" +
                "            \"stockTakeUserName\": \"姚博文\",\n" +
                "            \"stockTakePlanStartDate\": \"2025-06-19\",\n" +
                "            \"stockTakePlanEndDate\": \"2025-06-27\",\n" +
                "            \"sourceType\": null,\n" +
                "            \"entityModelId\": \"2a4053a615ce47a29fb876df3e7dd24e\",\n" +
                "            \"processDefinitionId\": null,\n" +
                "            \"processInstanceId\": null,\n" +
                "            \"processInstanceKey\": null,\n" +
                "            \"nextProcessUserList\": null,\n" +
                "            \"attachmentFileName\": null,\n" +
                "            \"attachmentFileUrl\": null\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        smartStuffWebService.invoke("ZYK", "stockTakeResultCallback", json);
    }
}
