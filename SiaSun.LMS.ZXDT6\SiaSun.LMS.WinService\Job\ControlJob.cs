﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class ControlJob : IJob
    {  
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                Program.hostLog.Debug("进入ControlJob");

                Program.BaseService.sManage.HandleControlTask();
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("轮询处理调度任务异常", ex);
            }
            finally
            {
                Program.hostLog.Debug("离开ControlJob");
            }
        }
    }
}
