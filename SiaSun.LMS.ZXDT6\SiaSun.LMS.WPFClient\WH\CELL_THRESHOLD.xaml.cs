﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.WH
{
    /// <summary>
    /// CELL_THRESHOLD.xaml 的交互逻辑
    /// </summary>
    public partial class CELL_THRESHOLD : AvalonDock.DocumentContent
    {
        public CELL_THRESHOLD()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.rbtnGroupByArea.IsChecked = true;
        }

        /// <summary>
        /// RadioButton选择变化事件
        /// </summary>
        private void StackPanel_Checked(object sender, RoutedEventArgs e)
        {
            this.commonDataGrid.U_Clear();

            if (this.rbtnGroupByArea.IsChecked == true)
            {
                this.commonDataGrid.U_WindowName = this.GetType().Name;
                this.commonDataGrid.U_TableName = "V_CELL_THRESHOLD_AREA";
                this.commonDataGrid.U_XmlTableName = "V_CELL_THRESHOLD_AREA";
                this.commonDataGrid.U_OrderField = "";
                this.commonDataGrid.U_Where = "";
                this.commonDataGrid.U_AllowOperatData = false;
                this.commonDataGrid.U_AllowChecked = false;
            }
            else if (this.rbtnGroupByLaneWay.IsChecked == true)
            {
                this.commonDataGrid.U_WindowName = this.GetType().Name;
                this.commonDataGrid.U_TableName = "V_CELL_THRESHOLD_LANEWAY";
                this.commonDataGrid.U_XmlTableName = "V_CELL_THRESHOLD_LANEWAY";
                this.commonDataGrid.U_OrderField = "DEVICE_CODE";
                this.commonDataGrid.U_Where = "";
                this.commonDataGrid.U_AllowOperatData = false;
                this.commonDataGrid.U_AllowChecked = false;
            }
            this.commonDataGrid.U_InitControl();


        }
    }
}
