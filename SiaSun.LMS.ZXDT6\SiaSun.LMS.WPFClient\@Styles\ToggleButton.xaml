<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type ToggleButton}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource NuclearButtonFocusVisual}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Foreground" Value="{DynamicResource OutsideFontColor}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundOver"
                                Storyboard.TargetProperty="Opacity"
                                To="1"
                                Duration="00:00:00.1000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundOver_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0.65"
                                Duration="00:00:00.1000000" />

                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundOver_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />

                        </Storyboard>
                        <Storyboard x:Key="CheckedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundChecked"
                                Storyboard.TargetProperty="Opacity"
                                To="0.84"
                                Duration="00:00:00.1000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgoundChecked_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0.65"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="CheckedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundChecked"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgoundChecked_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>

                        <Storyboard x:Key="PressedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundPressed"
                                Storyboard.TargetProperty="Opacity"
                                To="0.84"
                                Duration="00:00:00.0010000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgoundPressed_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0.65"
                                Duration="00:00:00.0010000" />
                        </Storyboard>
                        <Storyboard x:Key="PressedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundPressed"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.0010000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgoundPressed_Highlight"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.0010000" />
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>

                    </ControlTemplate.Resources>
                    <Grid x:Name="grid1">
                        <Border
                            x:Name="BackgroundNorm"
                            Background="{DynamicResource NormalBrush}"
                            BorderBrush="{DynamicResource NormalBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="1.75" />
                        <Border
                            x:Name="BackgroundNorm_highlight"
                            Margin="1"
                            BorderBrush="{DynamicResource NormalHighlightBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="1"
                            Opacity="0.65" />
                        <Border
                            x:Name="BackgroundChecked"
                            Background="{DynamicResource PressedBrush}"
                            BorderBrush="{DynamicResource PressedBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="1.75"
                            Opacity="0" />
                        <Border
                            x:Name="BackgoundChecked_Highlight"
                            Margin="1"
                            BorderBrush="{DynamicResource PressedHighlightBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="1"
                            Opacity="0" />
                        <Border
                            x:Name="BackgroundOver"
                            Background="{DynamicResource MouseOverBrush}"
                            BorderBrush="{DynamicResource MouseOverBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="1.75"
                            Opacity="0" />
                        <Border
                            x:Name="BackgroundOver_Highlight"
                            Margin="1"
                            BorderBrush="{DynamicResource MouseOverHighlightBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="1"
                            Opacity="0" />
                        <Border
                            x:Name="BackgroundPressed"
                            Background="{DynamicResource PressedBrush}"
                            BorderBrush="{DynamicResource PressedBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="1.75"
                            Opacity="0" />
                        <Border
                            x:Name="BackgoundPressed_Highlight"
                            Margin="1"
                            BorderBrush="{DynamicResource PressedHighlightBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="1"
                            Opacity="0" />
                        <Border
                            x:Name="DisabledVisualElement"
                            Background="{DynamicResource DisabledBackgroundBrush}"
                            BorderBrush="{DynamicResource DisabledBorderBrush}"
                            BorderThickness="1"
                            IsHitTestVisible="false"
                            Opacity="0" />
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}" />
                        <TextBlock
                            x:Name="DisabledOverlay"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Panel.ZIndex="1"
                            Foreground="#FF8E96A2"
                            Text="{TemplateBinding Content}"
                            Visibility="Collapsed" />
                        <Border
                            x:Name="FocusVisualElement"
                            Grid.RowSpan="2"
                            Margin="-1"
                            BorderBrush="{DynamicResource FocusBrush}"
                            BorderThickness="1"
                            CornerRadius="2.75"
                            IsHitTestVisible="false"
                            Opacity="0" />
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="PressedOff_BeginStoryboard" Storyboard="{StaticResource PressedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="PressedOn_BeginStoryboard" Storyboard="{StaticResource PressedOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="FocusedOn_BeginStoryboard" Storyboard="{StaticResource FocusedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="CheckedOff_BeginStoryboard" Storyboard="{StaticResource CheckedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="CheckedOn_BeginStoryboard" Storyboard="{StaticResource CheckedOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="#ADADAD" />
                            <Setter TargetName="DisabledVisualElement" Property="Visibility" Value="Visible" />
                            <Setter TargetName="DisabledOverlay" Property="Visibility" Value="Visible" />
                            <Setter TargetName="contentPresenter" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="DisabledVisualElement" Property="Opacity" Value="1" />

                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>