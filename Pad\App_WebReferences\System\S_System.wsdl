<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="S_System" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd3" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd0" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/System.Data" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd5" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/System" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="I_System_USER_LOGIN_InputMessage">
    <wsdl:part name="parameters" element="tns:USER_LOGIN" />
  </wsdl:message>
  <wsdl:message name="I_System_USER_LOGIN_OutputMessage">
    <wsdl:part name="parameters" element="tns:USER_LOGINResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_USER_PASSWORD_InputMessage">
    <wsdl:part name="parameters" element="tns:USER_PASSWORD" />
  </wsdl:message>
  <wsdl:message name="I_System_USER_PASSWORD_OutputMessage">
    <wsdl:part name="parameters" element="tns:USER_PASSWORDResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_GetList_ROLE_MENU_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetList_ROLE_MENU" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_GetList_ROLE_MENU_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetList_ROLE_MENUResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_GetModel_MENU_CONTROL_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetModel_MENU_CONTROL" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_GetModel_MENU_CONTROL_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_GetModel_MENU_CONTROLResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_Save" />
  </wsdl:message>
  <wsdl:message name="I_System_ROLE_WINDOW_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:ROLE_WINDOW_SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ITEM_LIST_GetDictionary_InputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetDictionary" />
  </wsdl:message>
  <wsdl:message name="I_System_ITEM_LIST_GetDictionary_OutputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetDictionaryResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ITEM_LIST_GetList_ITEM_CODE_InputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetList_ITEM_CODE" />
  </wsdl:message>
  <wsdl:message name="I_System_ITEM_LIST_GetList_ITEM_CODE_OutputMessage">
    <wsdl:part name="parameters" element="tns:ITEM_LIST_GetList_ITEM_CODEResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetList_ROLE_Select_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList_ROLE_Select" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetList_ROLE_Select_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetList_ROLE_SelectResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetModel" />
  </wsdl:message>
  <wsdl:message name="I_System_MENU_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:MENU_GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_GetModel" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_LIST_GetList_ID1_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_GetList_ID1" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_LIST_GetList_ID1_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_GetList_ID1Response" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_LIST_Add_InputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_Add" />
  </wsdl:message>
  <wsdl:message name="I_System_RELATION_LIST_Add_OutputMessage">
    <wsdl:part name="parameters" element="tns:RELATION_LIST_AddResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_TABLE_CONVERTER_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_TABLE_CONVERTER_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_SYS_TABLE_CONVERTER_Import_InputMessage">
    <wsdl:part name="parameters" element="tns:SYS_TABLE_CONVERTER_Import" />
  </wsdl:message>
  <wsdl:message name="I_System_SYS_TABLE_CONVERTER_Import_OutputMessage">
    <wsdl:part name="parameters" element="tns:SYS_TABLE_CONVERTER_ImportResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_TABLE_CONVERTER_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_Save" />
  </wsdl:message>
  <wsdl:message name="I_System_TABLE_CONVERTER_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:TABLE_CONVERTER_SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_FlowGetParameters_InputMessage">
    <wsdl:part name="parameters" element="tns:FlowGetParameters" />
  </wsdl:message>
  <wsdl:message name="I_System_FlowGetParameters_OutputMessage">
    <wsdl:part name="parameters" element="tns:FlowGetParametersResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_PlanGetAction_InputMessage">
    <wsdl:part name="parameters" element="tns:PlanGetAction" />
  </wsdl:message>
  <wsdl:message name="I_System_PlanGetAction_OutputMessage">
    <wsdl:part name="parameters" element="tns:PlanGetActionResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_PlanGetAction1_InputMessage">
    <wsdl:part name="parameters" element="tns:PlanGetAction1" />
  </wsdl:message>
  <wsdl:message name="I_System_PlanGetAction1_OutputMessage">
    <wsdl:part name="parameters" element="tns:PlanGetAction1Response" />
  </wsdl:message>
  <wsdl:message name="I_System_ManageGetAction_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageGetAction" />
  </wsdl:message>
  <wsdl:message name="I_System_ManageGetAction_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageGetActionResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_ManageGetAction1_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageGetAction1" />
  </wsdl:message>
  <wsdl:message name="I_System_ManageGetAction1_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageGetAction1Response" />
  </wsdl:message>
  <wsdl:message name="I_System_ControlGetAction_InputMessage">
    <wsdl:part name="parameters" element="tns:ControlGetAction" />
  </wsdl:message>
  <wsdl:message name="I_System_ControlGetAction_OutputMessage">
    <wsdl:part name="parameters" element="tns:ControlGetActionResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsID_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsID" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsID_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsIDResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsCode_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsCode" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsCode_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsCodeResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsClassGetModelGoodsClassID_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsClassGetModelGoodsClassID" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsClassGetModelGoodsClassID_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsClassGetModelGoodsClassIDResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetListGoodsClassID_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetListGoodsClassID" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetListGoodsClassID_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetListGoodsClassIDResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsPropertyGetListGoodsTypeID_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsPropertyGetListGoodsTypeID" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsPropertyGetListGoodsTypeID_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsPropertyGetListGoodsTypeIDResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateGetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateGetModel" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateGetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateGetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateGetList_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateGetList" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateGetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateGetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateListGetList_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateListGetList" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsTemplateListGetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsTemplateListGetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsCodeContract_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsCodeContract" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsGetModelGoodsCodeContract_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsGetModelGoodsCodeContractResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsClassGetModelGoodsClassCode_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsClassGetModelGoodsClassCode" />
  </wsdl:message>
  <wsdl:message name="I_System_GoodsClassGetModelGoodsClassCode_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsClassGetModelGoodsClassCodeResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_WAREHOUSE_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:WAREHOUSE_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_WAREHOUSE_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:WAREHOUSE_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_AREA_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:AREA_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_AREA_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:AREA_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_Z_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:CELL_Z_GetList" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_Z_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:CELL_Z_GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_Z_GetList_AREA_InputMessage">
    <wsdl:part name="parameters" element="tns:CELL_Z_GetList_AREA" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_Z_GetList_AREA_OutputMessage">
    <wsdl:part name="parameters" element="tns:CELL_Z_GetList_AREAResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_GetList_Z_InputMessage">
    <wsdl:part name="parameters" element="tns:CELL_GetList_Z" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_GetList_Z_OutputMessage">
    <wsdl:part name="parameters" element="tns:CELL_GetList_ZResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_GetList_Z_ByArea_InputMessage">
    <wsdl:part name="parameters" element="tns:CELL_GetList_Z_ByArea" />
  </wsdl:message>
  <wsdl:message name="I_System_CELL_GetList_Z_ByArea_OutputMessage">
    <wsdl:part name="parameters" element="tns:CELL_GetList_Z_ByAreaResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CellInit_InputMessage">
    <wsdl:part name="parameters" element="tns:CellInit" />
  </wsdl:message>
  <wsdl:message name="I_System_CellInit_OutputMessage">
    <wsdl:part name="parameters" element="tns:CellInitResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_DynamicInvoke_InputMessage">
    <wsdl:part name="parameters" element="tns:DynamicInvoke" />
  </wsdl:message>
  <wsdl:message name="I_System_DynamicInvoke_OutputMessage">
    <wsdl:part name="parameters" element="tns:DynamicInvokeResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CreateLog_InputMessage">
    <wsdl:part name="parameters" element="tns:CreateLog" />
  </wsdl:message>
  <wsdl:message name="I_System_CreateLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreateLogResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_GetSysParameter_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSysParameter" />
  </wsdl:message>
  <wsdl:message name="I_System_GetSysParameter_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSysParameterResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_MessageConverter_GetKeyValue_InputMessage">
    <wsdl:part name="parameters" element="tns:MessageConverter_GetKeyValue" />
  </wsdl:message>
  <wsdl:message name="I_System_MessageConverter_GetKeyValue_OutputMessage">
    <wsdl:part name="parameters" element="tns:MessageConverter_GetKeyValueResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CloneGoodsProperty_InputMessage">
    <wsdl:part name="parameters" element="tns:CloneGoodsProperty" />
  </wsdl:message>
  <wsdl:message name="I_System_CloneGoodsProperty_OutputMessage">
    <wsdl:part name="parameters" element="tns:CloneGoodsPropertyResponse" />
  </wsdl:message>
  <wsdl:message name="I_System_CLog_InputMessage">
    <wsdl:part name="parameters" element="tns:CLog" />
  </wsdl:message>
  <wsdl:message name="I_System_CLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:CLogResponse" />
  </wsdl:message>
  <wsdl:portType name="I_System">
    <wsdl:operation name="USER_LOGIN">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/USER_LOGIN" message="tns:I_System_USER_LOGIN_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/USER_LOGINResponse" message="tns:I_System_USER_LOGIN_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="USER_PASSWORD">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/USER_PASSWORD" message="tns:I_System_USER_PASSWORD_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/USER_PASSWORDResponse" message="tns:I_System_USER_PASSWORD_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ROLE_GetList" message="tns:I_System_ROLE_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ROLE_GetListResponse" message="tns:I_System_ROLE_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetList_ROLE_MENU">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_GetList_ROLE_MENU" message="tns:I_System_ROLE_WINDOW_GetList_ROLE_MENU_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_GetList_ROLE_MENUResponse" message="tns:I_System_ROLE_WINDOW_GetList_ROLE_MENU_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetModel_MENU_CONTROL">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_GetModel_MENU_CONTROL" message="tns:I_System_ROLE_WINDOW_GetModel_MENU_CONTROL_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_GetModel_MENU_CONTROLResponse" message="tns:I_System_ROLE_WINDOW_GetModel_MENU_CONTROL_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_Save" message="tns:I_System_ROLE_WINDOW_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ROLE_WINDOW_SaveResponse" message="tns:I_System_ROLE_WINDOW_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetDictionary">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ITEM_LIST_GetDictionary" message="tns:I_System_ITEM_LIST_GetDictionary_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ITEM_LIST_GetDictionaryResponse" message="tns:I_System_ITEM_LIST_GetDictionary_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetList_ITEM_CODE">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ITEM_LIST_GetList_ITEM_CODE" message="tns:I_System_ITEM_LIST_GetList_ITEM_CODE_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ITEM_LIST_GetList_ITEM_CODEResponse" message="tns:I_System_ITEM_LIST_GetList_ITEM_CODE_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/MENU_GetList" message="tns:I_System_MENU_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/MENU_GetListResponse" message="tns:I_System_MENU_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList_ROLE_Select">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/MENU_GetList_ROLE_Select" message="tns:I_System_MENU_GetList_ROLE_Select_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/MENU_GetList_ROLE_SelectResponse" message="tns:I_System_MENU_GetList_ROLE_Select_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MENU_GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/MENU_GetModel" message="tns:I_System_MENU_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/MENU_GetModelResponse" message="tns:I_System_MENU_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/RELATION_GetModel" message="tns:I_System_RELATION_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/RELATION_GetModelResponse" message="tns:I_System_RELATION_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_GetList_ID1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/RELATION_LIST_GetList_ID1" message="tns:I_System_RELATION_LIST_GetList_ID1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/RELATION_LIST_GetList_ID1Response" message="tns:I_System_RELATION_LIST_GetList_ID1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_Add">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/RELATION_LIST_Add" message="tns:I_System_RELATION_LIST_Add_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/RELATION_LIST_AddResponse" message="tns:I_System_RELATION_LIST_Add_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/TABLE_CONVERTER_GetList" message="tns:I_System_TABLE_CONVERTER_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/TABLE_CONVERTER_GetListResponse" message="tns:I_System_TABLE_CONVERTER_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SYS_TABLE_CONVERTER_Import">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/SYS_TABLE_CONVERTER_Import" message="tns:I_System_SYS_TABLE_CONVERTER_Import_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/SYS_TABLE_CONVERTER_ImportResponse" message="tns:I_System_SYS_TABLE_CONVERTER_Import_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/TABLE_CONVERTER_Save" message="tns:I_System_TABLE_CONVERTER_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/TABLE_CONVERTER_SaveResponse" message="tns:I_System_TABLE_CONVERTER_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FlowGetParameters">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/FlowGetParameters" message="tns:I_System_FlowGetParameters_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/FlowGetParametersResponse" message="tns:I_System_FlowGetParameters_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PlanGetAction">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/PlanGetAction" message="tns:I_System_PlanGetAction_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/PlanGetActionResponse" message="tns:I_System_PlanGetAction_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PlanGetAction1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/PlanGetAction1" message="tns:I_System_PlanGetAction1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/PlanGetAction1Response" message="tns:I_System_PlanGetAction1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageGetAction">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ManageGetAction" message="tns:I_System_ManageGetAction_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ManageGetActionResponse" message="tns:I_System_ManageGetAction_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageGetAction1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ManageGetAction1" message="tns:I_System_ManageGetAction1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ManageGetAction1Response" message="tns:I_System_ManageGetAction1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ControlGetAction">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/ControlGetAction" message="tns:I_System_ControlGetAction_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/ControlGetActionResponse" message="tns:I_System_ControlGetAction_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsID">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsID" message="tns:I_System_GoodsGetModelGoodsID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsIDResponse" message="tns:I_System_GoodsGetModelGoodsID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsCode">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsCode" message="tns:I_System_GoodsGetModelGoodsCode_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsCodeResponse" message="tns:I_System_GoodsGetModelGoodsCode_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsClassGetModelGoodsClassID">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassID" message="tns:I_System_GoodsClassGetModelGoodsClassID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassIDResponse" message="tns:I_System_GoodsClassGetModelGoodsClassID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsGetListGoodsClassID">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsGetListGoodsClassID" message="tns:I_System_GoodsGetListGoodsClassID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsGetListGoodsClassIDResponse" message="tns:I_System_GoodsGetListGoodsClassID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsPropertyGetListGoodsTypeID">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsPropertyGetListGoodsTypeID" message="tns:I_System_GoodsPropertyGetListGoodsTypeID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsPropertyGetListGoodsTypeIDResponse" message="tns:I_System_GoodsPropertyGetListGoodsTypeID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateGetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsTemplateGetModel" message="tns:I_System_GoodsTemplateGetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsTemplateGetModelResponse" message="tns:I_System_GoodsTemplateGetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateGetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsTemplateGetList" message="tns:I_System_GoodsTemplateGetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsTemplateGetListResponse" message="tns:I_System_GoodsTemplateGetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateListGetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsTemplateListGetList" message="tns:I_System_GoodsTemplateListGetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsTemplateListGetListResponse" message="tns:I_System_GoodsTemplateListGetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsCodeContract">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsCodeContract" message="tns:I_System_GoodsGetModelGoodsCodeContract_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsGetModelGoodsCodeContractResponse" message="tns:I_System_GoodsGetModelGoodsCodeContract_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsClassGetModelGoodsClassCode">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassCode" message="tns:I_System_GoodsClassGetModelGoodsClassCode_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassCodeResponse" message="tns:I_System_GoodsClassGetModelGoodsClassCode_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="WAREHOUSE_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/WAREHOUSE_GetList" message="tns:I_System_WAREHOUSE_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/WAREHOUSE_GetListResponse" message="tns:I_System_WAREHOUSE_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AREA_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/AREA_GetList" message="tns:I_System_AREA_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/AREA_GetListResponse" message="tns:I_System_AREA_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CELL_Z_GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CELL_Z_GetList" message="tns:I_System_CELL_Z_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CELL_Z_GetListResponse" message="tns:I_System_CELL_Z_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CELL_Z_GetList_AREA">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CELL_Z_GetList_AREA" message="tns:I_System_CELL_Z_GetList_AREA_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CELL_Z_GetList_AREAResponse" message="tns:I_System_CELL_Z_GetList_AREA_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CELL_GetList_Z">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CELL_GetList_Z" message="tns:I_System_CELL_GetList_Z_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CELL_GetList_ZResponse" message="tns:I_System_CELL_GetList_Z_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CELL_GetList_Z_ByArea">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CELL_GetList_Z_ByArea" message="tns:I_System_CELL_GetList_Z_ByArea_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CELL_GetList_Z_ByAreaResponse" message="tns:I_System_CELL_GetList_Z_ByArea_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CellInit">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CellInit" message="tns:I_System_CellInit_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CellInitResponse" message="tns:I_System_CellInit_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DynamicInvoke">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/DynamicInvoke" message="tns:I_System_DynamicInvoke_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/DynamicInvokeResponse" message="tns:I_System_DynamicInvoke_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreateLog">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CreateLog" message="tns:I_System_CreateLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CreateLogResponse" message="tns:I_System_CreateLog_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSysParameter">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/GetSysParameter" message="tns:I_System_GetSysParameter_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/GetSysParameterResponse" message="tns:I_System_GetSysParameter_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MessageConverter_GetKeyValue">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/MessageConverter_GetKeyValue" message="tns:I_System_MessageConverter_GetKeyValue_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/MessageConverter_GetKeyValueResponse" message="tns:I_System_MessageConverter_GetKeyValue_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CloneGoodsProperty">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CloneGoodsProperty" message="tns:I_System_CloneGoodsProperty_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CloneGoodsPropertyResponse" message="tns:I_System_CloneGoodsProperty_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CLog">
      <wsdl:input wsaw:Action="http://tempuri.org/I_System/CLog" message="tns:I_System_CLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_System/CLogResponse" message="tns:I_System_CLog_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_System" type="tns:I_System">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="USER_LOGIN">
      <soap:operation soapAction="http://tempuri.org/I_System/USER_LOGIN" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="USER_PASSWORD">
      <soap:operation soapAction="http://tempuri.org/I_System/USER_PASSWORD" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/ROLE_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetList_ROLE_MENU">
      <soap:operation soapAction="http://tempuri.org/I_System/ROLE_WINDOW_GetList_ROLE_MENU" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_GetModel_MENU_CONTROL">
      <soap:operation soapAction="http://tempuri.org/I_System/ROLE_WINDOW_GetModel_MENU_CONTROL" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ROLE_WINDOW_Save">
      <soap:operation soapAction="http://tempuri.org/I_System/ROLE_WINDOW_Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetDictionary">
      <soap:operation soapAction="http://tempuri.org/I_System/ITEM_LIST_GetDictionary" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ITEM_LIST_GetList_ITEM_CODE">
      <soap:operation soapAction="http://tempuri.org/I_System/ITEM_LIST_GetList_ITEM_CODE" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/MENU_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetList_ROLE_Select">
      <soap:operation soapAction="http://tempuri.org/I_System/MENU_GetList_ROLE_Select" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MENU_GetModel">
      <soap:operation soapAction="http://tempuri.org/I_System/MENU_GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_GetModel">
      <soap:operation soapAction="http://tempuri.org/I_System/RELATION_GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_GetList_ID1">
      <soap:operation soapAction="http://tempuri.org/I_System/RELATION_LIST_GetList_ID1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RELATION_LIST_Add">
      <soap:operation soapAction="http://tempuri.org/I_System/RELATION_LIST_Add" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/TABLE_CONVERTER_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SYS_TABLE_CONVERTER_Import">
      <soap:operation soapAction="http://tempuri.org/I_System/SYS_TABLE_CONVERTER_Import" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TABLE_CONVERTER_Save">
      <soap:operation soapAction="http://tempuri.org/I_System/TABLE_CONVERTER_Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FlowGetParameters">
      <soap:operation soapAction="http://tempuri.org/I_System/FlowGetParameters" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PlanGetAction">
      <soap:operation soapAction="http://tempuri.org/I_System/PlanGetAction" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PlanGetAction1">
      <soap:operation soapAction="http://tempuri.org/I_System/PlanGetAction1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageGetAction">
      <soap:operation soapAction="http://tempuri.org/I_System/ManageGetAction" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageGetAction1">
      <soap:operation soapAction="http://tempuri.org/I_System/ManageGetAction1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ControlGetAction">
      <soap:operation soapAction="http://tempuri.org/I_System/ControlGetAction" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsID">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsGetModelGoodsID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsCode">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsGetModelGoodsCode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsClassGetModelGoodsClassID">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsGetListGoodsClassID">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsGetListGoodsClassID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsPropertyGetListGoodsTypeID">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsPropertyGetListGoodsTypeID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateGetModel">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsTemplateGetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateGetList">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsTemplateGetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsTemplateListGetList">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsTemplateListGetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsGetModelGoodsCodeContract">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsGetModelGoodsCodeContract" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsClassGetModelGoodsClassCode">
      <soap:operation soapAction="http://tempuri.org/I_System/GoodsClassGetModelGoodsClassCode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WAREHOUSE_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/WAREHOUSE_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AREA_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/AREA_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CELL_Z_GetList">
      <soap:operation soapAction="http://tempuri.org/I_System/CELL_Z_GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CELL_Z_GetList_AREA">
      <soap:operation soapAction="http://tempuri.org/I_System/CELL_Z_GetList_AREA" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CELL_GetList_Z">
      <soap:operation soapAction="http://tempuri.org/I_System/CELL_GetList_Z" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CELL_GetList_Z_ByArea">
      <soap:operation soapAction="http://tempuri.org/I_System/CELL_GetList_Z_ByArea" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CellInit">
      <soap:operation soapAction="http://tempuri.org/I_System/CellInit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DynamicInvoke">
      <soap:operation soapAction="http://tempuri.org/I_System/DynamicInvoke" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateLog">
      <soap:operation soapAction="http://tempuri.org/I_System/CreateLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSysParameter">
      <soap:operation soapAction="http://tempuri.org/I_System/GetSysParameter" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MessageConverter_GetKeyValue">
      <soap:operation soapAction="http://tempuri.org/I_System/MessageConverter_GetKeyValue" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CloneGoodsProperty">
      <soap:operation soapAction="http://tempuri.org/I_System/CloneGoodsProperty" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CLog">
      <soap:operation soapAction="http://tempuri.org/I_System/CLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_System">
    <wsdl:port name="BasicHttpBinding_I_System" binding="tns:BasicHttpBinding_I_System">
      <soap:address location="http://127.0.0.1:8002/Service/System" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>