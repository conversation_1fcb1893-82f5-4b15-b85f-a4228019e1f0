<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type ListBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBox}">
                    <Grid>
                        <Border
                            x:Name="Border"
                            Background="{DynamicResource ControlBackgroundBrush}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="1">
                            <ScrollViewer
                                Margin="1"
                                Focusable="false"
                                Foreground="{TemplateBinding Foreground}">
                                <StackPanel Margin="2" IsItemsHost="true" />
                            </ScrollViewer>
                        </Border>
                        <Border
                            x:Name="DisabledVisualElement"
                            Background="#A5FFFFFF"
                            BorderBrush="#66FFFFFF"
                            BorderThickness="1"
                            IsHitTestVisible="false"
                            Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="DisabledVisualElement" Property="Opacity" Value="1" />
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style d:IsControlPart="True" TargetType="{x:Type ListBoxItem}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0.73"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0.84"
                                Duration="00:00:00.1000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelectedDisabled"
                                Storyboard.TargetProperty="Opacity"
                                To="0.55"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelectedDisabled"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid SnapsToDevicePixels="true">
                        <Rectangle
                            x:Name="BackgroundGradientOver"
                            Fill="{DynamicResource MouseOverBrush}"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource MouseOverBorderBrush}" />
                        <Rectangle
                            x:Name="BackgroundGradientSelectedDisabled"
                            Fill="{DynamicResource ListItemSelectedBrush}"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource ListItemSelectedBorderBrush}" />
                        <Rectangle
                            x:Name="BackgroundGradientSelected"
                            Fill="{DynamicResource PressedBrush}"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource PressedBorderBrush}"
                            StrokeThickness="1" />
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}" />
                    </Grid>
                    <ControlTemplate.Triggers>

                        <Trigger Property="IsSelected" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="SelectedOff_BeginStoryboard" Storyboard="{StaticResource SelectedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="SelectedOn_BeginStoryboard" Storyboard="{StaticResource SelectedOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>