# Test script for refactored GoodsInfoSync
Write-Host "Testing refactored GoodsInfoSync implementation..." -ForegroundColor Green

# Read the test JSON data
$jsonContent = Get-Content -Path "test_goods_info_refactored.json" -Raw
Write-Host "Test JSON loaded successfully" -ForegroundColor Yellow

# Display the JSON structure
Write-Host "`nTest JSON structure:" -ForegroundColor Cyan
Write-Host $jsonContent

# Test JSON parsing
try {
    $jsonObject = $jsonContent | ConvertFrom-Json
    Write-Host "`nJSON parsing successful!" -ForegroundColor Green
    
    # Validate key fields
    Write-Host "`nValidating key fields:" -ForegroundColor Cyan
    Write-Host "goodsCode: $($jsonObject.goodsCode)"
    Write-Host "name: $($jsonObject.name)"
    Write-Host "goodsType: $($jsonObject.goodsType)"
    Write-Host "code: $($jsonObject.code)"
    Write-Host "goodsStatus: $($jsonObject.goodsStatus)"
    Write-Host "fixedAssetFlag: $($jsonObject.fixedAssetFlag)"
    Write-Host "professionalAssetFlag: $($jsonObject.professionalAssetFlag)"
    Write-Host "isLaborInsuranceMaterials: $($jsonObject.isLaborInsuranceMaterials)"
    
    # Validate nested objects
    if ($jsonObject.unitInformationEntityDTO) {
        Write-Host "`nUnit Information DTO found:" -ForegroundColor Yellow
        Write-Host "goodsId: $($jsonObject.unitInformationEntityDTO.goodsId)"
        Write-Host "grossWeight: $($jsonObject.unitInformationEntityDTO.grossWeight)"
        Write-Host "netWeight: $($jsonObject.unitInformationEntityDTO.netWeight)"
        Write-Host "conversionDetailVOs count: $($jsonObject.unitInformationEntityDTO.conversionDetailVOs.Count)"
    }
    
    if ($jsonObject.brandVOs) {
        Write-Host "`nBrand VOs found:" -ForegroundColor Yellow
        Write-Host "brandVOs count: $($jsonObject.brandVOs.Count)"
        foreach ($brand in $jsonObject.brandVOs) {
            Write-Host "Brand: $($brand.brandName), Quantity: $($brand.inboundQuantity)"
        }
    }
    
    Write-Host "`nAll validations passed!" -ForegroundColor Green
    
} catch {
    Write-Host "`nJSON parsing failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed." -ForegroundColor Green