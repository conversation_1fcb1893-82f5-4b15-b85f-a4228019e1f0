﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.WINDOW_STYLE"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="WINDOW_STYLE" Name="winWINDOW_STYLE" Height="300" Width="536" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="230"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <GroupBox Margin="5" Grid.Column="0" Grid.Row="0"  Header="页面列表">
            <TreeView Name="tvwWinStyles" SelectedItemChanged="tvwWinStyles_SelectedItemChanged">
                <TreeView.ContextMenu>
                    <ContextMenu>
                        <MenuItem x:Name="menuItemDelete" Click="menuItemDelete_Click" Header="删除">
                        </MenuItem>
                    </ContextMenu>
                </TreeView.ContextMenu>
            </TreeView>
        </GroupBox>
        <GroupBox Margin="5" Grid.Row="1" Grid.Column="0" Header="新增页面样式">
            <StackPanel>
                <TextBlock Margin="5,10,5,5">新增页面名称：</TextBlock>
                <TextBox Name="txtWinName" Margin="5"></TextBox>
                <WrapPanel>
                <Button Name="btnWinName" Margin="2" HorizontalAlignment="Left" Click="btnWinName_Click">添加样式</Button>
                <Button Name="btnRefresh" Margin="2" HorizontalAlignment="Left" Click="btnRefresh_Click">刷新</Button>
                </WrapPanel>
            </StackPanel>
        </GroupBox>
        <GridSplitter Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Stretch" FlowDirection="LeftToRight"></GridSplitter>
        <GroupBox Name="grpBoxStyle" Grid.Column="2" Grid.RowSpan="2" Margin="5" Header="样式设置">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>
                <ToolBar Grid.Row="0"  VerticalAlignment="Top" >
                    <TextBlock Margin="5,2,5,2" VerticalAlignment="Center">数据表名：</TextBlock>
                    <TextBox Name="txtStyleName" Margin="1"  Width="100">
                    </TextBox>
                    <Button Name="btnLoadStyle" Margin="5,1,5,1" Template="{StaticResource templateImageButtonLoad}" Click="btnLoadStyle_Click">加载样式</Button>
                </ToolBar>
                <uc:DataGridTemplate x:Name="gridStyles" Grid.Row="1" Margin="1" Grid.ColumnSpan="2"></uc:DataGridTemplate>
                <ToolBar Grid.Row="2" VerticalAlignment="Bottom" Height="30" ButtonBase.Click="ToolBar_Click" Grid.ColumnSpan="2" >
                    <Button x:Name="btnAdd" Template="{StaticResource templateImageButtonAdd}">添加</Button>
                    <Button x:Name="btnDelete" Template="{StaticResource templateImageButtonDelete}">删除</Button>
                    <Button x:Name="btnSave" Template="{StaticResource templateImageButtonSave}">保存</Button>
                </ToolBar>
            </Grid>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
