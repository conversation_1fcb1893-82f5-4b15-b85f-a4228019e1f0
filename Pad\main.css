html {
  width: 100%;
  height: 100%;
}

body {
  padding: 15px 0;
}

html * {
  font-size: 16px !important;
}

.layui-this {
  background-color: #1E9FFF !important;
}

.layui-form-checked i, .layui-form-checked:hover i {
  color: #1E9FFF !important;
}

.layui-form-checkbox i {
  border: 1px solid #d2d2d2 !important;
  height: 20px !important;
  width: 20px !important;
  line-height: 20px !important;
  font-weight: bold !important;
}

#dlWenJianXiangHao .layui-form-checkbox {
  height: 24px !important;
  line-height: 24px !important;
  margin-top: 0 !important;
}

#gvGoods th, #gvGoods td, #gvInStorage th, #gvInStorage td, #gvOutStorage th, #gvOutStorage td {
  text-align: left;
}

#gvInStorage input,
#gvOutStorage input {
  width: 100%;
  border: 0;
  background-color: transparent;
}

#gvInStorage .t-value4,
#gvOutStorage .t-value1 {
  width: 80px;
}

#gvGoods .t-value1,
#gvGoods .t-value2,
#gvGoods .t-value3,
#gvInStorage .t-value1 {
  min-width: 80px;
}

#gvInStorage .t-value4,
#gvOutStorage .t-value6 {
  min-width: 30px;
}

th, td {
  padding: 2px 5px;
}

th, td span {
  word-wrap: break-word;
}

.d-none {
  display: none;
}

.layui-layer-btn a {
  height: 30px !important;
  line-height: 30px !important;
}

.layui-card {
  height: 100px;
  text-align: center;
  line-height: 100px;
  margin-bottom: 0 !important;
}

.layui-form-item .layui-input-inline {
  margin: 0 10px 0 10px !important;
  float: left !important;
  left: 0 !important;
}

.input-width {
  height: 30px !important;
  width: 200px !important;
  margin: 0 10px !important;
}

.button-width {
  margin-top: 10px;
  width: 120px !important;
}

.layui-btn {
  margin: 10px 10px 0 10px !important;
  width: 150px !important;
}

#tbSearch {
  float: left !important;
  margin: 0 !important;
}

.layui-form-item .search-button-width {
  width: 60px !important;
  float: left !important;
  padding: 0 !important;
  margin: 0 0 0 5px !important;
  height: 28px !important;
  line-height: 28px !important;
}

.menu-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #000;
  float: left;
  margin: 20px 0 0 20px;
}

.menu-header {
  display: inline-block;
  margin-top: 20px;
  width: 100%;
}

.menu-btn {
  border: 0px !important;
  width: 300px !important;
  margin: 0 !important;
  text-align: left !important;
  color: #000000 !important;
  height: 50px !important;
  font-size: 20px !important;
}

.menu-block, .android-menu {
  width: 50%;
  float: left;
  height: 150px;
  text-align: center;
}

.menu-img {
  height: 60px;
  width: 60px;
}

.line-item {
  word-break: break-all;
}

.layui-form-item {
  margin-bottom: 5px;
  padding: 0 15px;
}

.signin-img {
  height: 58px;
  width: 320px;
}

.signin-input-width {
  width: 240px !important;
}

.layui-form-label {
  width: 70px;
  line-height: 28px;
  text-align: left !important;
  padding: 0;
}

.line-item-title {
  display: inline-block;
  width: 130px;
  padding-right: 10px;
  text-align: right;
}

#dlTuoPan td, #dlXiangHao td {
  padding-left: 50px;
}

.wms-col {
  width: 50%;
  float: left;
  position: relative;
  display: block;
  box-sizing: border-box;
}