using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 入库红冲单接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class InventoryReversalReceiptSync : InterfaceBase
    {
        class InputParam
        {
            public int redStorageType { get; set; }
            public string initId { get; set; }
            public string buyOrderCode { get; set; }
            public string contractCode { get; set; }
            public string reason { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            public string storageDate { get; set; }
            public string sourceUserName { get; set; }
            public string sourceUserId { get; set; }
            public string sourceUnitName { get; set; }
            public string sourceUnitId { get; set; }
            public string operatorName { get; set; }
            public string operatorId { get; set; }
            public string storageOperatorName { get; set; }
            public string storageOperatorId { get; set; }
            public string storageCode { get; set; }
            public string storageName { get; set; }
            public string storageGoodsSource { get; set; }
            public decimal money { get; set; }
            public decimal taxMoney { get; set; }
            public string redStorageName { get; set; }
            public List<RedStorageDetailItem> redStorageDetailList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class RedStorageDetailItem
        {
            public string reason { get; set; }
            public string lineId { get; set; }
            public string lineName { get; set; }
            public string orderIndex { get; set; }
            public string initId { get; set; }
            public decimal tax { get; set; }
            public string goodsType { get; set; }
            public string goodsVersion { get; set; }
            public string redStorageId { get; set; }
            public string remark { get; set; }
            public string localSend { get; set; }
            public string gkDeptName { get; set; }
            public string gkDeptId { get; set; }
            public string manageDeptName { get; set; }
            public string manageDeptId { get; set; }
            public string batch { get; set; }
            public string gbName { get; set; }
            public string gbId { get; set; }
            public string bzName { get; set; }
            public string bzId { get; set; }
            public string deptName { get; set; }
            public string deptId { get; set; }
            public string orgName { get; set; }
            public string orgId { get; set; }
            public string zbCycle { get; set; }
            public string produceDate { get; set; }
            public string shelfName { get; set; }
            public string shelfId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            public decimal taxAllPrice { get; set; }
            public decimal taxPrice { get; set; }
            public string brand { get; set; }
            public string unitName { get; set; }
            public string unitId { get; set; }
            public int redStorageNum { get; set; }
            public int storageNum { get; set; }
            public string goodsName { get; set; }
            public string goodsCode { get; set; }
            public string goodsId { get; set; }
            public string storageType { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.warehouseId) || string.IsNullOrEmpty(inputParam.storageCode) ||
                    string.IsNullOrEmpty(inputParam.reason) || string.IsNullOrEmpty(inputParam.operatorId))
                {
                    result = false;
                    message = "接口入参必填项存在空值：warehouseId, storageCode, reason, operatorId";
                    return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                }

                // 验证红冲类型
                //if (inputParam.redStorageType != 1 && inputParam.redStorageType != 2)
                //{
                //    result = false;
                //    message = "红冲类型错误，必须为1(冲销)或2(退货)";
                //    return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                //}

                // 验证日期格式
                if (!string.IsNullOrEmpty(inputParam.storageDate))
                {
                    DateTime storageDate;
                    if (!DateTime.TryParse(inputParam.storageDate, out storageDate))
                    {
                        result = false;
                        message = $"入库日期格式错误：{inputParam.storageDate}";
                        return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                    }
                }

                // 验证红冲明细列表
                if (inputParam.redStorageDetailList == null || inputParam.redStorageDetailList.Count == 0)
                {
                    result = false;
                    message = "红冲明细列表不能为空";
                    return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                }

                // 验证红冲明细项必填字段
                foreach (var item in inputParam.redStorageDetailList)
                {
                    if (string.IsNullOrEmpty(item.goodsCode) || string.IsNullOrEmpty(item.goodsName) ||
                        item.redStorageNum <= 0 || string.IsNullOrEmpty(item.unitId))
                    {
                        result = false;
                        message = "红冲明细项必填字段存在空值或无效值：goodsCode, goodsName, redStorageNum, unitId";
                        return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                    }

                    // 验证红冲数量不能超过入库数量
                    if (item.redStorageNum > item.storageNum)
                    {
                        result = false;
                        message = $"物资[{item.goodsCode}]红冲数量[{item.redStorageNum}]不能超过入库数量[{item.storageNum}]";
                        return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                    }

                    // 验证生产日期格式
                    if (!string.IsNullOrEmpty(item.produceDate))
                    {
                        DateTime produceDate;
                        if (!DateTime.TryParse(item.produceDate, out produceDate))
                        {
                            result = false;
                            message = $"物资[{item.goodsCode}]生产日期格式错误：{item.produceDate}";
                            return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                        }
                    }
                }

                // 处理入库红冲单主表数据
                // 这里应该根据实际的数据库模型进行数据处理
                // 由于没有具体的数据库模型定义，这里只做基本的逻辑处理

                // 检查入库红冲单是否已存在
                // var existingReversalReceipt = S_Base.sBase.pSTORAGE_MAIN.GetModel(inputParam.billCode);

                // 处理入库红冲单数据
                // 这里应该包含：
                // 1. 创建或更新入库红冲单主表记录
                // 2. 处理红冲明细数据
                // 3. 更新库存信息（减少库存）
                // 4. 记录操作日志

                // 检查出库单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.redStorageName);

                if (existingReceipt != null)
                {
                    result = false;
                    message = $"已存在入库红冲单-{inputParam.redStorageName}";
                    return FormatInventoryReversalReceiptErrorMessage(message, traceId);
                }
                else
                {
                    // 处理入库单数据
                    // 这里应该包含：
                    // 1. 创建或更新入库单主表记录
                    // 2. 处理入库明细数据
                    // 3. 更新库存信息
                    // 4. 记录操作日志
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.redStorageName,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanReversalOut.ToString(),
                        PLAN_CREATE_TIME = inputParam.createDate,
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                        PLAN_REMARK = inputParam.reason,
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (RedStorageDetailItem item in inputParam.redStorageDetailList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST()
                        {
                            PLAN_LIST_QUANTITY = item.storageNum,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(item.goodsCode).GOODS_ID,
                            PLAN_LIST_REMARK = item.reason,

                        };
                        lsPLAN_LIST.Add(mPLAN_LIST);
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }

                // 模拟数据处理成功
                string redStorageTypeDesc = inputParam.redStorageType == 1 ? "冲销" : "退货";
                S_Base.sBase.Log.Info($"入库红冲单同步成功 - 红冲类型: {redStorageTypeDesc}, 来源单号: {inputParam.storageCode}, 仓库ID: {inputParam.warehouseId}, 明细数量: {inputParam.redStorageDetailList.Count}");

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"入库红冲单同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化入库红冲单错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatInventoryReversalReceiptErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}