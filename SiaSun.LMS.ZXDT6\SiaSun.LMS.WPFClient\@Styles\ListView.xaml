<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="{x:Static GridView.GridViewScrollViewerStyleKey}" TargetType="{x:Type ScrollViewer}">
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid Background="{TemplateBinding Background}" SnapsToDevicePixels="true">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <DockPanel Margin="{TemplateBinding Padding}">
                            <ScrollViewer
                                DockPanel.Dock="Top"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden">
                                <GridViewHeaderRowPresenter
                                    Margin="2,0,2,0"
                                    AllowsColumnReorder="{Binding Path=TemplatedParent.View.AllowsColumnReorder, RelativeSource={RelativeSource TemplatedParent}}"
                                    ColumnHeaderContainerStyle="{Binding Path=TemplatedParent.View.ColumnHeaderContainerStyle, RelativeSource={RelativeSource TemplatedParent}}"
                                    ColumnHeaderContextMenu="{Binding Path=TemplatedParent.View.ColumnHeaderContextMenu, RelativeSource={RelativeSource TemplatedParent}}"
                                    ColumnHeaderTemplate="{Binding Path=TemplatedParent.View.ColumnHeaderTemplate, RelativeSource={RelativeSource TemplatedParent}}"
                                    ColumnHeaderTemplateSelector="{Binding Path=TemplatedParent.View.ColumnHeaderTemplateSelector, RelativeSource={RelativeSource TemplatedParent}}"
                                    ColumnHeaderToolTip="{Binding Path=TemplatedParent.View.ColumnHeaderToolTip, RelativeSource={RelativeSource TemplatedParent}}"
                                    Columns="{Binding Path=TemplatedParent.View.Columns, RelativeSource={RelativeSource TemplatedParent}}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

                            </ScrollViewer>
                            <ScrollContentPresenter
                                x:Name="PART_ScrollContentPresenter"
                                CanContentScroll="{TemplateBinding CanContentScroll}"
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                KeyboardNavigation.DirectionalNavigation="Local"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </DockPanel>
                        <ScrollBar
                            x:Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Cursor="Arrow"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Minimum="0.0"
                            Orientation="Horizontal"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{Binding Path=HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <ScrollBar
                            x:Name="PART_VerticalScrollBar"
                            Grid.Column="1"
                            Cursor="Arrow"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            Minimum="0.0"
                            Orientation="Vertical"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{Binding Path=VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <DockPanel
                            Grid.Row="1"
                            Grid.Column="1"
                            Background="{Binding Path=Background, ElementName=PART_VerticalScrollBar}"
                            LastChildFill="false">
                            <Rectangle
                                Width="1"
                                DockPanel.Dock="Left"
                                Fill="White"
                                Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" />
                            <Rectangle
                                Height="1"
                                DockPanel.Dock="Top"
                                Fill="White"
                                Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" />
                        </DockPanel>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ListView}">
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="BorderBrush" Value="#FFB1703C" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListView}">
                    <Border
                        x:Name="Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="1">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0.056,0.5" EndPoint="1.204,0.5">
                                <GradientStop Offset="0" Color="#FFFFFFFF" />
                                <GradientStop Offset="1" Color="#FFD4D7DB" />
                            </LinearGradientBrush>
                        </Border.Background>

                        <ScrollViewer Padding="{TemplateBinding Padding}" Style="{DynamicResource {x:Static GridView.GridViewScrollViewerStyleKey}}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </ScrollViewer>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false" />
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ListViewItemFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle
                        RadiusX="2"
                        RadiusY="2"
                        Stroke="#8E6EA6F5"
                        StrokeThickness="1" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ListViewItem}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource ListViewItemFocusVisual}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Margin" Value="0,0,0,1" />
        <Setter Property="Padding" Value="5,2,5,2" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0.73"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientOver"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0.84"
                                Duration="00:00:00.1000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelectedDisabled"
                                Storyboard.TargetProperty="Opacity"
                                To="0.55"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelected"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                            <DoubleAnimation
                                Storyboard.TargetName="BackgroundGradientSelectedDisabled"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Border
                        x:Name="border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="2"
                        SnapsToDevicePixels="true">

                        <Grid Margin="2,0,2,0">
                            <Rectangle
                                x:Name="BackgroundGradientOver"
                                Fill="{DynamicResource MouseOverBrush}"
                                Opacity="0"
                                RadiusX="1"
                                RadiusY="1"
                                Stroke="{DynamicResource MouseOverBorderBrush}" />
                            <Rectangle
                                x:Name="BackgroundGradientSelectedDisabled"
                                Fill="{DynamicResource ListItemSelectedBrush}"
                                Opacity="0"
                                RadiusX="1"
                                RadiusY="1"
                                Stroke="{DynamicResource ListItemSelectedBorderBrush}" />
                            <Rectangle
                                x:Name="BackgroundGradientSelected"
                                Fill="{DynamicResource PressedBrush}"
                                Opacity="0"
                                RadiusX="1"
                                RadiusY="1"
                                Stroke="{DynamicResource PressedBorderBrush}"
                                StrokeThickness="1" />
                            <GridViewRowPresenter
                                Margin="0,2,0,2"
                                VerticalAlignment="Stretch"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Grid>

                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="SelectedOff_BeginStoryboard" Storyboard="{StaticResource SelectedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="SelectedOn_BeginStoryboard" Storyboard="{StaticResource SelectedOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="Selector.IsSelectionActive" Value="false" />
                            </MultiTrigger.Conditions>

                        </MultiTrigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="{DynamicResource OutsideFontColor}" />
    </Style>

</ResourceDictionary>