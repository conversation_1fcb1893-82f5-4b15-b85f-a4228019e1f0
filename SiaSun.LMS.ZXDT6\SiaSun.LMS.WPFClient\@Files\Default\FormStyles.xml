﻿<?xml version="1.0" encoding="utf-8"?>
<Styles>
	<Form Name="MANAGE_IN">
		<!--MANAGE_IN组盘列表-->
		<Table Name="V_MANAGE_LIST">
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY" DbType="String" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />-->
			<Field Column="GOODS_REMARK" DbType="String" Header="物料备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
		</Table>
	</Form>

	<!--<Form Name="MANAGE_OUT">
    <Table Name="V_STORAGE_LIST">
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="出库数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="102" AllowQuery="0" QueryOperation="" />
      -->
	<!--<Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />-->
	<!--
      -->
	<!--<Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="105" AllowQuery="0" QueryOperation="" />-->
	<!--
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="子托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="高度" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="1" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="170" AllowQuery="0" QueryOperation="=" />
      <Field Column="AREA_NAME" DbType="String" Header="区域" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      -->
	<!--<Field Column="AREA_ID" DbType="String" Header="区域" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value ,AREA_NAME as name from WH_AREA" Remark="" Order="180" AllowQuery="0" QueryOperation="" />-->
	<!--
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
      -->
	<!--<Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />-->
	<!--
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    </Table> 
  </Form>-->

	<Form Name="MANAGE_ADJUST">
		<Table Name="V_STORAGE_LIST">
			<Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="105" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="1" QueryOperation="like" />
			<Field Column="BOX_BARCODE" DbType="String" Header="物料条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="1" QueryOperation="" />
			<Field Column="CELL_MODEL" DbType="String" Header="高度" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CLASS_ID" DbType="String" Header="物料类别" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select GOODS_CLASS_ID as value ,GOODS_CLASS_NAME as name from GOODS_CLASS where GOODS_CLASS_FLAG =1 " Remark="" Order="127" AllowQuery="1" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
			<Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
			<Field Column="STORAGE_LIST_QUANTITY_UNLOCK" DbType="Decimal" Header="可用数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="155" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="157" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="1" QueryOperation="" />
			<Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="170" AllowQuery="1" QueryOperation="=" />
			<Field Column="AREA_ID" DbType="String" Header="区域" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value ,AREA_NAME as name from WH_AREA" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
			<Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
			<Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
			<Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
			<Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
		</Table>
	</Form>

	<Form Name="MANAGE_PLAN_BIND">
		<Table Name="V_PLAN_LIST">
			<Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_PICKED_QUANTITY" DbType="String" Header="拣选绑定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="任务下达数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
		</Table>

		<Table Name="V_STORAGE_LIST">
			<Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="拣配数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="80" AllowQuery="0" QueryOperation="" />
			<Field Column="STORAGE_LIST_QUANTITY_UNLOCK" DbType="Decimal" Header="可用库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="90" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />-->
			<Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="BOX_BARCODE" DbType="String" Header="子托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_MODEL" DbType="String" Header="高度" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="0" QueryOperation="" />
			<Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="170" AllowQuery="0" QueryOperation="=" />
			<Field Column="AREA_NAME" DbType="String" Header="区域" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="select AREA_CODE as value ,AREA_NAME as name from WH_AREA" Order="180" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="AREA_ID" DbType="String" Header="区域" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value ,AREA_NAME as name from WH_AREA" Remark="" Order="180" AllowQuery="0" QueryOperation="" />-->
			<Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
			<Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
			<Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />-->
			<Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
		</Table>
	</Form>

	<Form Name="MANAGE_PLAN_OUT">
		<!--<Table Name="V_PLAN_LIST">
			<Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_PICKED_QUANTITY" DbType="String" Header="拣选绑定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="任务下达数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="175" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
		</Table>-->

		<Table Name="V_STORAGE_LIST">
			<Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="拣选数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="80" AllowQuery="0" QueryOperation="" />
			<Field Column="STORAGE_LIST_QUANTITY_UNLOCK" DbType="Decimal" Header="可用库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="90" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="STORAGE_LIST_ID" DbType="String" Header="库存单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />-->
			<Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="BOX_BARCODE" DbType="String" Header="子托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_MODEL" DbType="String" Header="高度" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_WEIGHT" DbType="String" Header="重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="125" AllowQuery="0" QueryOperation="" />-->
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_NAME" DbType="String" Header="货位名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="CELL_CODE" DbType="String" Header="货位编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="165" AllowQuery="0" QueryOperation="" />
			<Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="170" AllowQuery="0" QueryOperation="=" />
			<Field Column="AREA_NAME" DbType="String" Header="区域" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="select AREA_CODE as value ,AREA_NAME as name from WH_AREA" Order="180" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="AREA_ID" DbType="String" Header="区域" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value ,AREA_NAME as name from WH_AREA" Remark="" Order="180" AllowQuery="0" QueryOperation="" />-->
			<Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
			<Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
			<Field Column="UPDATE_TIME" DbType="String" Header="更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />-->
			<Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
		</Table>
	</Form>

	<Form Name="DateSectSplitQueryWindow">
		<Table Name="V_PLAN_LIST">
			<Field Column="PLAN_ID" DbType="String" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_GROUP" DbType="String" Header="拣配单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="1" QueryOperation="" />
			<Field Column="PLAN_CODE" DbType="string" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="1" QueryOperation="" />
			<Field Column="PLAN_TYPE_CODE" DbType="String" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="SELECT PLAN_TYPE_CODE as value,PLAN_TYPE_NAME as name FROM PLAN_TYPE where PLAN_TYPE_FLAG='1' order by PLAN_TYPE_ORDER" Remark="" Order="140" AllowQuery="1" QueryOperation="" />
			<Field Column="PLAN_CREATE_TIME" DbType="string" Header="制单时间" ControlType="DateTimePicker" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_BEGIN_TIME" DbType="string" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_END_TIME" DbType="string" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="PLAN_STATUS" Remark="" Order="180" AllowQuery="1" QueryOperation="" />
			<Field Column="PLAN_LIST_ID" DbType="String" Header="计划单ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_CODE" DbType="String" Header="需求行号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_PICKED_QUANTITY" DbType="String" Header="拣配绑定数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="任务下达数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_QUANTITY_APPEND" DbType="String" Header="输送完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
			<!--<Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />-->

			<!--<Field Column="PLAN_CREATER" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="1" QueryOperation="like" />-->
			<Field Column="PLAN_FLAG" DbType="String" Header="源系统" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="300" AllowQuery="1" QueryOperation="like" />
			<Field Column="PLAN_LEVEL" DbType="String" Header="优先级" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_INOUT_STATION" DbType="String" Header="整托出口" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="330" AllowQuery="1" QueryOperation="like" />
			<Field Column="BACKUP_FIELD1" DbType="String" Header="箱拣出口" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
			<Field Column="OUT_STATION" DbType="string" Header="出库口(按列表)" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="350" AllowQuery="0" QueryOperation="" />
			<Field Column="BACKUP_FIELD2" DbType="String" Header="物流公司" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="360" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_FROM_USER" DbType="String" Header="送达方" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="365" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_CONFIRM_USER" DbType="String" Header="送达城市" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="370" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_CONFIRM_TIME" DbType="String" Header="送货日期" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="380" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_TO_USER" DbType="String" Header="收货人" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="390" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_TO_DEPT" DbType="String" Header="送达地址" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="400" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_FROM_DEPT" DbType="Int32" Header="当前拣选人" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="410" AllowQuery="0" QueryOperation="" />
			<Field Column="BACKUP_FIELD3" DbType="String" Header="暂停前状态" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="420" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_REMARK" DbType="string" Header="计划备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="430" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_LIST_REMARK" DbType="String" Header="计划单备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="440" AllowQuery="0" QueryOperation="" />

		</Table>
	</Form>

	<Form Name="MANAGE_BULK_MERGE">
		<Table Name="V_MANAGE_LIST">
			<Field Column="PLAN_ID" DbType="Decimal" Header="计划ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="90" AllowQuery="0" QueryOperation="" />
			<Field Column="PLAN_TYPE_CODE" DbType="Decimal" Header="计划类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_ID" DbType="Decimal" Header="任务ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="115" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_TYPE_CODE" DbType="Decimal" Header="任务类型" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_STATUS" DbType="Decimal" Header="任务状态" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
			<Field Column="BOX_BARCODE" DbType="String" Header="子托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
			<Field Column="STOCK_WEIGHT" DbType="String" Header="托盘重量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
			<Field Column="START_CELL_ID" DbType="String" Header="起始位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
			<Field Column="END_CELL_ID" DbType="String" Header="终点位置" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_OPERATOR" DbType="String" Header="操作者" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_BEGIN_TIME" DbType="String" Header="开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
			<Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
			<Field Column="MANAGE_REMARK" DbType="" Header="任务备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />

		</Table>
	</Form>

	<Form Name="MANAGE_ADJUST">
		<!--<Table Name="V_STORAGE_LIST">
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="0" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY1" DbType="String" Header="ABC分类" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CONST_PROPERTY2" DbType="String" Header="SN码拣选" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="=" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATE_TIME" DbType="String" Header="流程开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="库存数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="string" Header="实际数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="235" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>

	<Form Name="PLAN_EDIT">
		<!--<Table Name="PLAN_MAIN">
      <Field Column="PLAN_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="100" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_RELATIVE_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_ROOT_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_TYPE_CODE" DbType="Int32" Header="计划类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="SELECT PLAN_TYPE_CODE AS value, PLAN_TYPE_NAME AS name FROM PLAN_TYPE WHERE PLAN_TYPE_FLAG='1'" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_INOUT_STATION" DbType="String" Header="出入站台" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select CELL_CODE as value , CELL_NAME as name from WH_CELL where CELL_TYPE='Station' and CELL_FLAG='1'" Remark="" Order="145" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FROM_DEPT" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="-1" ReadOnly="0" Validation="请选择类型|nonenull" DataBind="select Convert(varchar(50),project_id) AS value,project_qkcode+project_name as name from project_main where PROJECT_FLAG='1'" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATE_TIME" DbType="String" Header="创建时间" ControlType="DateTimePicker" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_BEGIN_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_END_TIME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="311" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_STATUS" DbType="String" Header="计划状态" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="PLAN_STATUS" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CREATER" DbType="String" Header="操作员" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FROM_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_TO_USER" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="900" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_FLAG" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="V_PLAN_LIST">
      <Field Column="PLAN_LIST_ID" DbType="String" Header="计划列表ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="基本单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="string" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_QUANTITY" DbType="String" Header="计划数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_ORDERED_QUANTITY" DbType="String" Header="分配数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_FINISHED_QUANTITY" DbType="String" Header="完成数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_LIST_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>

	<Form Name="MANAGE_STORAGE_IN">
		<!--<Table Name="V_STORAGE_LIST_LOCAL">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_REMARK" DbType="String" Header="物料备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="V_STORAGE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_REMARK" DbType="String" Header="物料备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>

	<Form Name="MANAGE_STOCK_OUT">
		<!--<Table Name="V_STORAGE_LIST">
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="货位编码 " ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="111" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="箱类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_MODEL" Remark="" Order="112" AllowQuery="0" QueryOperation="" />
      <Field Column="BOX_BARCODE" DbType="String" Header="所在箱格" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="WDZ_BOX_GRID" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_PROPERTY" DbType="" Header="物料属性" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_NAME" DbType="string" Header="仓库" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="LANEWAY_NAME" Remark="" Order="190" AllowQuery="0" QueryOperation="=" />
      <Field Column="ENTRY_TIME" DbType="String" Header="入库时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATE_TIME" DbType="String" Header="流程开始时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="225" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_QUANTITY" DbType="Decimal" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="STORAGE_LIST_REMARK" DbType="string" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>

	<Form Name="GOODS_TEMPLATE">
		<!--<Table Name="V_GOODS_TEMPLATE">
      <Field Column="GOODS_TEMPLATE_CODE" DbType="String" Header="方案编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_NAME" DbType="String" Header="方案名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_CODE" DbType="string" Header="产品编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="20" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="产品名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
      <Field Column="TEMPLATE_FLAG" DbType="String" Header="启用标记" ControlType="CheckBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="FLAG" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="31" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="V_GOODS_TEMPLATE_LIST">
      <Field Column="GOODS_CODE" DbType="String" Header="物料编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="21" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_NAME" DbType="String" Header="物料名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="22" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_UNITS" DbType="string" Header="单位" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="23" AllowQuery="0" QueryOperation="" />
      <Field Column="GOODS_TEMPLATE_QUANTITY" DbType="String" Header="数量" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="30" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>

	<Form Name="DataGridWindow">
		<!--<Table Name="WH_LANEWAY">
      <Field Column="LANEWAY_ID" DbType="Int32" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_ID" DbType="Int32" Header="存储仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="select warehouse_id as value,warehouse_name as name from wh_warehouse" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
      <Field Column="LANEWAY_TYPE" DbType="String" Header="巷道类型" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="LANEWAY_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="LANEWAY_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="请输入巷道编码" DataBind="" Remark="" Order="4" AllowQuery="0" QueryOperation="" />
      <Field Column="LANEWAY_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="请输入巷道名称" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
      <Field Column="LANEWAY_FLAG" DbType="String" Header="标记" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="DEVICE_FLAG" DbType="Int32" Header="巷道设备激活" ControlType="CheckBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_CODE" DbType="数据类型" Header="设备仓库编码" ControlType="控件类型" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="WH_CELL">
      <Field Column="CELL_ID" DbType="Int32" Header="货位ID" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="110" AllowQuery="0" QueryOperation="" />
      <Field Column="WAREHOUSE_ID" DbType="Int32" Header="仓库" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select WAREHOUSE_ID as value , WAREHOUSE_NAME as name from WH_WAREHOUSE where WAREHOUSE_FLAG='1'" Remark="" Order="120" AllowQuery="0" QueryOperation="" />
      <Field Column="AREA_ID" DbType="Int32" Header="库区" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select AREA_ID as value , AREA_NAME as name from WH_AREA" Remark="" Order="130" AllowQuery="0" QueryOperation="" />
      <Field Column="LOGIC_ID" DbType="Int32" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="140" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_NAME" DbType="String" Header="名称" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="150" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_CODE" DbType="String" Header="编码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="160" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_TYPE" DbType="String" Header="类型" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="CELL_TYPE" Remark="" Order="170" AllowQuery="0" QueryOperation="" />
      <Field Column="DEVICE_CODE" DbType="String" Header="设备" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="180" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_Z" DbType="Int32" Header="排" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="190" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_X" DbType="Int32" Header="列" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="200" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_Y" DbType="Int32" Header="层" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="210" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_INOUT" DbType="String" Header="出入库" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_INOUT" Remark="" Order="220" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_MODEL" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="230" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_STATUS" DbType="String" Header="货位状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="CELL_STATUS" Remark="" Order="240" AllowQuery="0" QueryOperation="" />
      <Field Column="RUN_STATUS" DbType="String" Header="运行状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="RUN_STATUS" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_FORK_TYPE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_LOGICAL_NAME" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
      <Field Column="LANE_WAY" DbType="String" Header="巷道" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="280" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_GROUP" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="290" AllowQuery="0" QueryOperation="" />
      <Field Column="SHELF_TYPE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="300" AllowQuery="0" QueryOperation="" />
      <Field Column="SHELF_NEIGHBOUR" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="310" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_STORAGE_TYPE" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="320" AllowQuery="0" QueryOperation="" />
      <Field Column="LOCK_DEVICE_CODE" DbType="String" Header="锁定编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="330" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_WIDTH" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="340" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_HEIGTH" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="350" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_PROPERTY" DbType="String" Header="站台属性" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="360" AllowQuery="0" QueryOperation="" />
      <Field Column="CELL_FLAG" DbType="String" Header="是否启用" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="FLAG" Remark="" Order="370" AllowQuery="0" QueryOperation="" />
      <Field Column="GET_IN_STATION" DbType="String" Header="入库站台" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="380" AllowQuery="0" QueryOperation="" />
      <Field Column="ONLINE" DbType="Int32" Header="杭可设备在线" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="390" AllowQuery="0" QueryOperation="" />
      <Field Column="UNIT_STATUS" DbType="string" Header="杭可设备状态" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="400" AllowQuery="0" QueryOperation="" />
      <Field Column="UPDATETIME" DbType="string" Header="杭可更新时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="410" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="V_RECORD">
      <Field Column="RECORD_ID" DbType="String" Header="" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_ID" DbType="String" Header="" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="RECORD_MESSAGE" DbType="String" Header="记录消息" ControlType="控件类型" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="MES_ORDER_ID" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
      <Field Column="TECHNICS_DESCRIPTION" DbType="String" Header="流程名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_TYPE_CODE" DbType="String" Header="" ControlType="ComboBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select MANAGE_TYPE_CODE as value,MANAGE_TYPE_NAME as name FROM MANAGE_TYPE" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="STOCK_BARCODE" DbType="String" Header="托盘条码" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="START_CELL_ID" DbType="string" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select cast(CELL_ID as varchar2(50)) AS value , CELL_NAME AS name FROM WH_CELL " Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="END_CELL_ID" DbType="string" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="select cast(CELL_ID as varchar2(50)) AS value , CELL_NAME AS name FROM WH_CELL " Remark="" Order="7" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_BEGIN_TIME" DbType="string" Header="下达时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="260" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_END_TIME" DbType="string" Header="完成时间" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="270" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_CONFIRM_TIME" DbType="String" Header="确认时间" ControlType="" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="250" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_OPERATOR" DbType="String" Header="操作人员" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="46" AllowQuery="0" QueryOperation="" />
      <Field Column="MANAGE_STATUS" DbType="String" Header="状态" ControlType="ComboBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="MANAGE_STATUS" Remark="" Order="45" AllowQuery="0" QueryOperation="" />
      <Field Column="PLAN_CODE" DbType="String" Header="计划单号" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="50" AllowQuery="0" QueryOperation="" />
      <Field Column="START_CELL_NAME" DbType="String" Header="开始位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="END_CELL_NAME" DbType="String" Header="终止位置" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
      <Field Column="STACK_NO" DbType="String" Header="" ControlType="TextBox" DefaultValue="" ReadOnly="1" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
    </Table>-->

		<!--<Table Name="SYS_TABLE_CONVERTER">
      <Field Column="TABLE_CONVERTER_ID" DbType="Decimal" Header="" ControlType="text" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="1" AllowQuery="0" QueryOperation="" />
      <Field Column="TABLE_CONVERTER_CODE" DbType="String" Header="转换编码" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="2" AllowQuery="0" QueryOperation="" />
      <Field Column="TABLE_CONVERTER_NAME" DbType="String" Header="转换名称" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="3" AllowQuery="0" QueryOperation="" />
      <Field Column="PARENT_TABLE" DbType="String" Header="父级表名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="5" AllowQuery="0" QueryOperation="" />
      <Field Column="PARENT_KEY" DbType="String" Header="父级表主键名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="6" AllowQuery="0" QueryOperation="" />
      <Field Column="CHILD_TABLE" DbType="String" Header="子级表名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="7" AllowQuery="0" QueryOperation="" />
      <Field Column="CHILD_FOREIGN_KEY" DbType="String" Header="子级表外键名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="8" AllowQuery="0" QueryOperation="" />
      <Field Column="REMARK" DbType="String" Header="备注" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="13" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_TYPE" DbType="String" Header="拆分类型" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="10" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_KEY" DbType="String" Header="拆分类型值" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="11" AllowQuery="0" QueryOperation="" />
      <Field Column="SPLIT_PROPERTY_COLUMN" DbType="String" Header="拆分列名" ControlType="TextBox" DefaultValue="" ReadOnly="0" Validation="" DataBind="" Remark="" Order="12" AllowQuery="0" QueryOperation="" />
    </Table>-->
	</Form>
</Styles>