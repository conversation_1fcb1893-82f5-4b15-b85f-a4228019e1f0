﻿<?xml version="1.0" encoding="utf-8" ?>
<sqlMap namespace="SYS_LOG" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="SYS_LOG" type="SiaSun.LMS.Model.SYS_LOG, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_LOG">
			<result property="LOG_ID" column="log_id" />
			<result property="LOG_THREAD" column="log_thread" />
			<result property="LOG_LEVEL" column="log_level" />
			<result property="LOG_LOGGER" column="log_logger" />
			<result property="LOG_DATE" column="log_date" />
			<result property="LOG_MESSAGE" column="log_message" />
			<result property="LOG_METHOD" column="log_method" />
		</resultMap>
	</resultMaps>
	<statements>

		<select id="SYS_LOG_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			log_id,
			log_thread,
			log_level,
			log_logger,
			log_date,
			log_message,
			log_method
			From SYS_LOG
		</select>

		<select id="SYS_LOG_SELECT_BY_ID" parameterClass="int" extends = "SYS_LOG_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					log_id=#LOG_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="SYS_LOG_SELECT_BY_THREAD" parameterClass="string" extends = "SYS_LOG_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					log_thread=#LOG_THREAD#
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="SYS_LOG_INSERT" parameterClass="SYS_LOG">
			Insert Into SYS_LOG (
			<!--log_id,-->
			log_thread,
			log_level,
			log_logger,
			log_date,
			log_message,
			log_method
			)Values(
			<!--#LOG_ID#,-->
			#LOG_THREAD#,
			#LOG_LEVEL#,
			#LOG_LOGGER#,
			#LOG_DATE#,
			#LOG_MESSAGE#,
			#LOG_METHOD#
			)
			<selectKey  resultClass="int" type="post" property="LOG_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="SYS_LOG_UPDATE" parameterClass="SYS_LOG">
			Update SYS_LOG Set
			<!--log_id=#LOG_ID#,-->
			log_thread=#LOG_THREAD#,
			log_level=#LOG_LEVEL#,
			log_logger=#LOG_LOGGER#,
			log_date=#LOG_DATE#,
			log_message=#LOG_MESSAGE#,
			log_method=#LOG_METHOD#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					log_id=#LOG_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="SYS_LOG_DELETE" parameterClass="int">
			Delete From SYS_LOG
			<dynamic prepend="WHERE">
				<isParameterPresent>
					log_id=#LOG_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>