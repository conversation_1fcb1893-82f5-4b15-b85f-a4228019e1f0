﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_PLAN_DOWN"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="MANAGE_PLAN_DOWN"
    Width="500"
    Height="561"
    Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <uc:ucSplitPropertyPanel x:Name="ucSplitPanel" Grid.Row="0" />
        <uc:ucQuickQuery
            x:Name="ucQuery"
            Grid.Row="1"
            MaxWidth="{Binding ElementName=ucTechPosition, Path=MaxWidth}" />
        <!--<uc:ucDataGridRowGroupControl Grid.Row="2" x:Name="ucStorageGroup" ></uc:ucDataGridRowGroupControl>-->
        <uc:ucSplitPropertyGridTab x:Name="ucStorageGroup" Grid.Row="2" />

        <WrapPanel
            Grid.Row="3"
            Margin="3"
            HorizontalAlignment="Left"
            VerticalAlignment="Center"
            ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition" Grid.Row="3" />
            <CheckBox
                Name="cbxCheckCellStatus"
                Width="150"
                VerticalAlignment="Center"
                Checked="cbxCheckCellStatus_CheckChanged"
                Content="检查起止货位状态"
                IsChecked="True"
                Unchecked="cbxCheckCellStatus_CheckChanged"
                Visibility="Collapsed" />
            <CheckBox
                Name="cbxDownloadControl"
                Width="150"
                VerticalAlignment="Center"
                Content="下达控制任务"
                IsChecked="True"
                Visibility="Collapsed" />
            <Button Name="btnConfirm" Width="60">下达任务</Button>
            <Button Name="btnRefresh" Width="60">刷新</Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent>
