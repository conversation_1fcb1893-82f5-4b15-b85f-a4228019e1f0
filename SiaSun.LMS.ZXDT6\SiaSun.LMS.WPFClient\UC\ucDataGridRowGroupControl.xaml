﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucDataGridRowGroupControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             d:DesignHeight="165" d:DesignWidth="300">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <uc:DataGridTemplate Grid.Row="0" x:Name="gridStorageGroup" Background="White"  AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False">
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="DataGridRow" >
                                <Expander Background="{TemplateBinding Background}" IsExpanded="False" BorderThickness="1" BorderBrush="Silver" Expanded="Expander_Expanded" Loaded="Expander_Loaded">
                                        <Expander.Header>
                                            <WrapPanel Name="panelHeader" MaxWidth="{Binding ElementName= gridStorageGroup, Path=ActualWidth}">
                                                <CheckBox Name="chkBoxSelect" Margin="5,2,5,2" VerticalAlignment="Center" Checked="chkBoxSelect_Checked" Unchecked="chkBoxSelect_Checked"></CheckBox>
                                                <uc:ucModelValuePanel x:Name="ucValuePanel" Margin="10,3,15,3"  MaxWidth="{Binding ElementName= gridStorageGroup, Path=ActualWidth}"></uc:ucModelValuePanel>
                                            </WrapPanel>
                                        </Expander.Header>
                                    <ItemsPresenter></ItemsPresenter>
                                        <!--<uc:ucSplitPropertyGridTab x:Name="ucSplitGrid" Margin="5"></uc:ucSplitPropertyGridTab>-->
                                    </Expander>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <!--<Setter Property="BorderBrush" Value="LightGreen" />
                            <Setter Property="BorderThickness" Value="1" />-->
                        </Trigger>
                        <!--<Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="PaleGoldenrod"/>
                        </Trigger>-->
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>
        </uc:DataGridTemplate>

        <WrapPanel Grid.Row="1" Background="{StaticResource NormalBorderBrush}" MinHeight="21" ButtonBase.Click="WrapPanel_Click">
            <Button Name="btnRefresh" Template="{StaticResource templateImageButtonUpdate}" Margin="5,3,5,3"></Button>
            <Separator Width="1"></Separator>
            <TextBlock Text="选中" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
            <TextBlock Text="0" Name="txtChecked" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
            <TextBlock Text="行" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
            <Separator Width="1"></Separator>
            <TextBlock Text="共" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
            <TextBlock Text="{Binding ElementName=gridStorageGroup, Path=Items.Count}" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
            <TextBlock Text="行" VerticalAlignment="Center" Margin="5,3,5,3"></TextBlock>
        </WrapPanel>
    </Grid>
</UserControl>
