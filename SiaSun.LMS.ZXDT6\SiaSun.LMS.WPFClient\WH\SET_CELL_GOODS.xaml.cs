﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.WH
{
    /// <summary>
    /// SET_CELL_GOODS.xaml 的交互逻辑
    /// </summary>
    public partial class SET_CELL_GOODS : AvalonDock.DocumentContent
    {
        public SET_CELL_GOODS()
        {
            InitializeComponent();

            this.ucQueryCell.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                                       ((QueryWhere) =>
                                       {
                                           try
                                           {
                                               this.gridCellConfig.U_AppendWhere = string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere;
                                               this.gridCellConfig.U_InitControl();      
                                           }
                                           catch (Exception ex)
                                           {
                                               MainApp._MessageDialog.ShowException(ex);
                                           }
                                       }
                                       );
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            QuickQueryInit();
            CellConfigList();
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void QuickQueryInit()
        {
            try
            {
                this.ucQueryCell.U_XmlTableName = "V_CELL_GOODS_CONFIG";
                this.ucQueryCell.U_WindowName = this.GetType().Name;
                this.ucQueryCell.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 初始化列表
        /// </summary>
        private void CellConfigList()
        {
            //数据源属性
            this.gridCellConfig.U_Clear();
            this.gridCellConfig.U_WindowName = this.GetType().Name;
            this.gridCellConfig.U_TableName = "V_CELL_GOODS_CONFIG";
            this.gridCellConfig.U_XmlTableName = "V_CELL_GOODS_CONFIG";
            this.gridCellConfig.U_Fields = "*";
            this.gridCellConfig.U_Where = "GOODS_TYPE_ID = 4";
            this.gridCellConfig.U_OrderField = "CELL_ID";

            this.gridCellConfig.U_AllowChecked = false;
            this.gridCellConfig.U_AllowOperatData = true;
            this.gridCellConfig.U_AllowAdd = Visibility.Collapsed;
            this.gridCellConfig.U_AllowDelete = Visibility.Collapsed;
            this.gridCellConfig.U_SaveDataTable = "CELL_GOODS_CONFIG";

            //拆分列属性
            this.gridCellConfig.U_SplitPropertyType = "GOODS_TYPE";
            this.gridCellConfig.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridCellConfig.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridCellConfig.U_SplitGroupHeader = "GOODS_TYPE_NAME";

            //明细属性
            this.gridCellConfig.U_DetailTableName = "";
            this.gridCellConfig.U_DetailRelatvieColumn = "";

            try
            {
                //初始化控件
                this.gridCellConfig.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
