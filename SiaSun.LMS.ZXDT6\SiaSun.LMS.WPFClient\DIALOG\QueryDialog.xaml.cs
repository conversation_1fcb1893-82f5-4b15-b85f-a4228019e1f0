﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// QueryDialog.xaml 的交互逻辑
    /// </summary>
    public partial class QueryDialog : Window
    {
        string strTableName = null;
        string windowName = null;
        string strWhere = string.Empty;

        /// <summary>
        /// 关联XML表名
        /// </summary>
        public string U_XmlTableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 关联窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return windowName; }
            set { windowName = value; }
        }

        /// <summary>
        /// 查询筛选条件
        /// </summary>
        public string U_Where
        {
            get { return strWhere; }
            set { strWhere = value; }
        }

        public QueryDialog()
        {
            InitializeComponent();
            this.ucWinHeader.DataContext = this;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.ucQueryControl.U_WindowName = this.windowName;
            this.ucQueryControl.U_XmlTableName = this.strTableName;
            this.ucQueryControl.U_InitControl();
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            switch (btn.Name)
            {
                case "btnOK":
                    this.strWhere = this.ucQueryControl.U_GetQueryWhere();
                    this.DialogResult = true;
                    break;
                case "btnClose":
                    this.DialogResult = false;
                    break;
            }
            this.Close();
            return;
        }
    }
}
