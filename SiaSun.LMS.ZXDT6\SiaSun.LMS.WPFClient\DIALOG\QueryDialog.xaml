﻿<Window x:Class="SiaSun.LMS.WPFClient.Dialog.QueryDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="查询窗口" Height="300" MinWidth="500" MaxWidth="500" WindowStartupLocation="CenterScreen"  Loaded="Window_Loaded" WindowStyle="None" SizeToContent="WidthAndHeight">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucWindowTopHeader x:Name="ucWinHeader" Grid.Row="0"></uc:ucWindowTopHeader>
        <TabControl Grid.Row="1" >
            <TabItem Header="基本查询" >
                <uc:ucQuery x:Name="ucQueryControl" Margin="1" ></uc:ucQuery>
            </TabItem>
        </TabControl>
        <WrapPanel Orientation="Horizontal" Grid.Row="2" Margin="5" HorizontalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
            <Button Name="btnOK"  Width="60" IsDefault="True" IsEnabled="True">确定</Button>
            <Button Name="btnClose"  Width="60" IsCancel="True">关闭</Button>
        </WrapPanel>
    </Grid>
</Window>
