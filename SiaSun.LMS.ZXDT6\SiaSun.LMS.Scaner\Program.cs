﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;
using log4net;
using System.Reflection;
using SiaSun.LMS.Interface;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "Log4Net.config", Watch = true)]
namespace SiaSun.LMS.Scaner
{
    class Program
    {
        public delegate bool ControlCtrlDelegate(int ctrlType);
        [DllImport("kernel32.dll")]
        private static extern bool SetConsoleCtrlHandler(ControlCtrlDelegate HandlerRoutine, bool Add);
        private static ControlCtrlDelegate cancelHandler = new ControlCtrlDelegate(HandlerRoutine);

        public static TcpScaners scanerList;
        public static ILog _log = log4net.LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public static string _BaseUrl = SiaSun.LMS.Common.StringUtil.GetConfig("SrvUrl");
        public static SiaSun.LMS.Interface.I_Manage _I_ManageService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_Manage>(string.Format(_BaseUrl + "/ManageService")); }
        }

        static void Main(string[] args)
        {
            bool showDisConnect = true;
            string sLogString = string.Empty;

            bool startupFlag = false;
            Mutex mutex = new Mutex(true, "SiaSun.LMS.Scaner", out startupFlag);
            if (!startupFlag)
            {
                MessageBox.Show("程序已启动");
                Environment.Exit(0);
            }

            try
            {
                SetConsoleCtrlHandler(cancelHandler, true);

                //加载扫描器配置
                scanerList = LoadScanerConf("ScanerConfig.xml");

                foreach (TcpScaner itemScaner in scanerList.Clients)
                {
                    AsynTcpClient asynScaner = new AsynTcpClient(itemScaner.clientIp, itemScaner.clientPort, 10000);

                    //改为同步连接设备
                    //asynScaner.AsynConnect();
                    string connResult = asynScaner.SyncConnect();
                    
                    if (!connResult.StartsWith("成功"))
                    {
                        LogAndPrint(string.Format("[{0}]_采集设备[{1}]_连接失败_{2}", Common.StringUtil.GetExactDateTime(), itemScaner.clientName, connResult));
                        continue;
                    }
                    else
                    {
                        LogAndPrint(string.Format("[{0}]_采集设备[{1}]_连接成功", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                    }
                    
                    //接收回调
                    asynScaner.OnReceive += new AsynTcpClient.ReceivedEventHandler(t =>
                    {
                        string sBoxBarcode = string.Empty;

                        try
                        {
                            sBoxBarcode = t.Replace("\0", "");
                            if (string.IsNullOrEmpty(sBoxBarcode))
                            {
                                LogAndPrint(string.Format("[{0}]_采集设备[{1}]_接收方法被触发_但未接收到条码", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                                return;
                            }

                            _log.Info(string.Format("[{0}]_采集设备[{1}]_接收到条码[{2}]", Common.StringUtil.GetExactDateTime(), itemScaner.clientName, sBoxBarcode));


                            //debug
                            //_I_BaseService.CreateSysLog(Enum.LogThread.Scaner, "Scaner", Enum.LOG_LEVEL.Information, string.Format("SiaSun.LMS.Scaner.Program.Main():接收到采集设备上报条码_调用系统处理服务前_条码[{0}]", sBoxBarcode));

                            string result = Program._I_ManageService.WsControlApply(itemScaner.applyType, itemScaner.stationCode, sBoxBarcode, "0");

                            //debug
                            //_I_BaseService.CreateSysLog(Enum.LogThread.Scaner, "Scaner", Enum.LOG_LEVEL.Information, string.Format("SiaSun.LMS.Scaner.Program.Main():接收到采集设备上报条码_调用系统处理服务后_条码[{0}]_服务返回值[{1}]", sBoxBarcode, result));

                            LogAndPrint(string.Format("[{0}]_采集设备[{1}]_接收到条码[{2}]_上报申请处理[{3}]", Common.StringUtil.GetExactDateTime(), itemScaner.clientName, sBoxBarcode, result));
                        }
                        catch (Exception ex)
                        {
                            LogAndPrint(string.Format("[{0}]_采集设备[{1}]_处理异步接收事件时发生异常_[{2}]", Common.StringUtil.GetExactDateTime(), itemScaner.clientName, ex.Message));
                        }
                        finally
                        {
                            asynScaner.SbReceivedData = new StringBuilder();
                        }
                    });

                    //关闭连接回调
                    asynScaner.OnDisConnect += new AsynTcpClient.DisConnectEventHandler(()=> 
                    {
                        LogAndPrint(string.Format("[{0}]_采集设备[{1}]_丢失连接", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                    });

                    //心跳回调
                    asynScaner.OnHeartBeat+=new AsynTcpClient.HeartBeatEventHandler((isAlive,isConnected) =>
                    {
                        if (isAlive && isConnected)
                        {
                            _log.Info(string.Format("[{0}]_采集设备[{1}]_心跳正常_连接正常", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                        }
                        else if (isAlive && !isConnected)
                        {
                            LogAndPrint(string.Format("[{0}]_采集设备[{1}]_心跳正常_连接异常_尝试重新连接", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));

                            string reConnResult = asynScaner.SyncConnect();

                            if (!reConnResult.StartsWith("成功"))
                            {
                                LogAndPrint(string.Format("[{0}]_采集设备[{1}]_重新连接失败_{2}", Common.StringUtil.GetExactDateTime(), itemScaner.clientName, reConnResult));
                            }
                            else
                            {
                                LogAndPrint(string.Format("[{0}]_采集设备[{1}]_重新连接成功", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                                showDisConnect = true;
                            }
                        }
                        else
                        {
                            if (showDisConnect)
                            {
                                LogAndPrint(string.Format("[{0}]_采集设备[{1}]_心跳异常_连接异常", Common.StringUtil.GetExactDateTime(), itemScaner.clientName));
                                showDisConnect = false;
                            }
                        }
                    });
                }
                LogAndPrint(string.Format("[{0}]_采集程序启动成功", Common.StringUtil.GetExactDateTime()));
            }
            catch (Exception ex)
            {
                LogAndPrint(string.Format("[{0}]_采集程序启动时发生异常_{1}", Common.StringUtil.GetExactDateTime(), ex.Message));
            }
            finally
            {
            }
            Console.ReadLine();
        }

        public static TcpScaners LoadScanerConf(string filename)
        {
            if (!File.Exists(AppDomain.CurrentDomain.BaseDirectory + @"\" + filename))
            {
                return new TcpScaners();
            }

            XmlSerializer ser = new XmlSerializer(typeof(TcpScaners));
            using (FileStream fs = File.OpenRead(AppDomain.CurrentDomain.BaseDirectory + @"\" + filename))
            {
                return (TcpScaners)ser.Deserialize(fs);
            }
        }


        public static void LogAndPrint(string message)
        {
            Console.WriteLine(message);
            _log.Info(message);
        }


        public static bool HandlerRoutine(int ctrlType)
        {
            _log.Warn("采集系统被关闭");
            return false;
        }

    }
}
