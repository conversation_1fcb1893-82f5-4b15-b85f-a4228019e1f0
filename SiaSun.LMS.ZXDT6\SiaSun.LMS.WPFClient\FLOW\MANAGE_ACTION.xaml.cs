﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.FLOW_ACTION
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_ACTION : AvalonDock.DocumentContent
    {
        string strStatus = string.Empty;
        //wdz add 2018-02-09
        string strStartDateTimeColumn = string.Empty;
        string strEndDateTimeColumn = string.Empty;

        public MANAGE_ACTION()
        {
            InitializeComponent();

            this.ucQueryManage.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                (  (QueryWhere) =>
                    {
                        try
                        {
                            string strDateWhere = (this.panelDateTime.Visibility == System.Windows.Visibility.Visible ? this.GetDateStr() : string.Empty);
                            this.gridManage.U_AppendWhere = string.Format("{0} AND {1}",
                                                                            string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                                                                            string.IsNullOrEmpty(strDateWhere) ? "1=1" : strDateWhere);
                            this.gridManage.U_InitControl();
                            this.gridManageList.U_DataSource = null;
                            this.ucFlowManageAction.U_Clear();

                            this.gridManageList.U_Clear();
                            this.SelectManageDefault();

                            this.gridManage.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);

                        }
                        catch (Exception ex)
                        {
                            MainApp._MessageDialog.ShowException(ex);
                        }
                    }
                 );

            this.ucFlowManageAction.U_ExecuteUpdate += new UC.ucStatusFlowActionsPanel.ExecuteUpdateEventHandler
                (() =>
                    {
                        try
                        {
                            this.gridManage.U_InitControl();

                            this.gridManageList.U_Clear();

                            this.SelectManageDefault();
                        }
                        catch (Exception ex)
                        {
                            MainApp._MessageDialog.ShowException(ex);
                        }
                    }
                 );

            this.gridManage.gridApp.GotFocus += new RoutedEventHandler(gridApp_GotFocus);
        }

        void gridApp_GotFocus(object sender, RoutedEventArgs e)
        {
            this.gridManage.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
            this.gridManage.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (gridManage.gridApp.SelectedItem == null || gridManage.gridApp.SelectedItem.ToString()=="{NewItemPlaceholder}")
            {
                this.gridManageList.U_DataSource = null;
                return;
            }

            DataRow rowManage = (gridManage.gridApp.SelectedItem as DataRowView).Row;
            int intManageID = Convert.ToInt32(rowManage["MANAGE_ID"]);

            ManageListBind(intManageID);

            this.ManageActionBind(rowManage);

            this.gridManage.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

                
        public MANAGE_ACTION(string STATUS):this()
        {
            strStatus = STATUS;
        }

        /// <summary>
        /// 构造函数
        /// wdz add 2018-02-09
        /// </summary>
        public MANAGE_ACTION(string STATUS, string startDateTimeColumn, string endDateTimeColumn) : this()
        {
            strStatus = STATUS;
            strStartDateTimeColumn = startDateTimeColumn;
            strEndDateTimeColumn = endDateTimeColumn;
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //默认日期
            this.dtpStart.SelectedDate = DateTime.Now;
            this.dtpEnd.SelectedDate = DateTime.Now;
            this.chkboxDate.IsChecked = false;
            //是否显示时间段
            this.panelDateTime.Visibility = (string.IsNullOrEmpty(this.strStartDateTimeColumn) && string.IsNullOrEmpty(this.strEndDateTimeColumn)) ? Visibility.Collapsed : System.Windows.Visibility.Visible;

            this.InitQueryControl();
            this.Manage_Bind();
        }


        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            try
            {
                this.ucQueryManage.U_XmlTableName = "V_MANAGE";
                this.ucQueryManage.U_WindowName = this.GetType().Name;
                this.ucQueryManage.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private void Manage_Bind()
        {
            try
            {
                this.gridManage.U_WindowName = this.GetType().Name;
                this.gridManage.U_TableName = "V_MANAGE";
                this.gridManage.U_OrderField = "MANAGE_ID";
                this.gridManage.U_Where = (strStatus.Length == 0 ? string.Empty : "MANAGE_STATUS IN(" + strStatus.Replace('|', ',') + ")");
                this.gridManage.U_SaveDataTable = "MANAGE_MAIN";

                //this.gridManage.save
                this.gridManage.U_AllowChecked = false;
                this.gridManage.U_AllowOperatData = false;
                this.gridManage.U_AllowPage = true;
                this.gridManage.U_AllowDelete = Visibility.Collapsed;

                this.gridManage.U_InitControl();

                this.SelectManageDefault();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void SelectManageDefault()
        {
            if (this.gridManage.gridApp.SelectedItem != null && gridManage.gridApp.SelectedItem.ToString() != "{NewItemPlaceholder}")
            {
                DataRow rowManage = (gridManage.gridApp.SelectedItem as DataRowView).Row;
                
                int intManageID = Convert.ToInt32(rowManage["MANAGE_ID"]);

                ManageListBind(intManageID);

                this.ManageActionBind(rowManage);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void ManageListBind(int ManageID)
        {
            this.gridManageList.U_WindowName = this.GetType().Name;
            this.gridManageList.U_TableName = "V_MANAGE_LIST";
            this.gridManageList.U_OrderField = "MANAGE_LIST_ID";
            this.gridManageList.U_Where = string.Format("MANAGE_ID={0}", ManageID);

            this.gridManageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridManageList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.gridManageList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridManageList.U_SplitPropertyType = "GOODS_TYPE";

            this.gridManageList.U_AllowChecked = false;
            this.gridManageList.U_AllowOperatData = false;
            this.gridManageList.U_AllowShowPage = false;

            this.gridManageList.U_DetailTableName = "MANAGE_DETAIL";
            this.gridManageList.U_DetailRelatvieColumn = "MANAGE_LIST_ID";

            this.gridManageList.U_InitControl();
        }

        private void ManageActionBind(DataRow rowManage)
        {
            //初始化控件
            this.ucFlowManageAction.U_AddLog = true;
            this.ucFlowManageAction.U_FLOW_TYPE = "FLOW_MANAGE";
            this.ucFlowManageAction.U_FLOW_SOURCE_ROW = rowManage;
            this.ucFlowManageAction.U_DataSource = MainApp.I_SystemService.ManageGetAction(rowManage["MANAGE_ID"].ToString());
            this.ucFlowManageAction.U_InitControl();
        }


        /// <summary>
        /// 获得时间查询条件
        /// </summary>
        private string GetDateStr()
        {
            string strDateWhere = string.Empty;

            //判断是否添加时间查询
            if (chkboxDate.IsChecked == true)
            {
                //判断选择的值
                if (this.dtpStart.SelectedDate.HasValue && !this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00')",
                                                                               strStartDateTimeColumn,
                                                                               this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (!this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}<='{1} 24:60:60')",
                                                                              strEndDateTimeColumn,
                                                                              this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00' AND {2}<='{3} 24:60:60')",
                                                                            strStartDateTimeColumn,
                                                                            this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"),
                                                                            strEndDateTimeColumn,
                                                                            this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));

                }
            }
            return strDateWhere;
        }
    }
}
