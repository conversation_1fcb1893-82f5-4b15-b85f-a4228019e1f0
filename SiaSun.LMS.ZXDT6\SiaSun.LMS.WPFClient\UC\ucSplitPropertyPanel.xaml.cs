﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucSplitPropertyPanel.xaml 的交互逻辑
    /// </summary>
    public partial class ucSplitPropertyPanel : UserControl
    {
        #region     ------拆分组合列属性

        private DataTable tableSplit = null;
        private string strSplitGroupHeader = string.Empty;
        private string strSplitGroupColumn = string.Empty;
        private string strSplitPropertyColumn = string.Empty;
        private string strSplitPropertyType = string.Empty;

        /// <summary>
        /// 不同TapPage页显示的数据不同的分组依据,如：表V_PLAN_LIST中的GOODS_TYPE_ID
        /// </summary>
        public string U_SplitGroupColumn
        {
            get { return strSplitGroupColumn; }
            set { strSplitGroupColumn = value; }
        }

        /// <summary>
        /// 不同TapPage页显示的标题,如：表GOODS_TYPE中的GOODS_TYPE_NAME
        /// </summary>
        public string U_SplitGroupHeader
        {
            get { return strSplitGroupHeader; }
            set { strSplitGroupHeader = value; }
        }

        /// <summary>
        /// 数据源中所对应的拆分属性列名，如：PLAN_LIST表中的GOODS_PROPERTY
        /// </summary>
        public string U_SplitPropertyColumn
        {
            get { return strSplitPropertyColumn; }
            set { strSplitPropertyColumn = value; }
        }

        /// <summary>
        /// 属性划分依据类别,不同类型对应显示的属性不同，如：表GOODS_TYPE
        /// </summary>
        public string U_SplitPropertyType
        {
            get { return strSplitPropertyType; }
            set { strSplitPropertyType = value; }
        }

        #endregion

        public ucSplitPropertyPanel()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 获得拆分属性数据源
        /// </summary>
        private DataTable GetSplitTable(string SplitPropertyKey)
        {
            //获得拆分列数据源
            DataTable tableSplitSource = MainApp.I_DatabaseService.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", SplitPropertyKey));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";

            return tableSplitSource;
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //检查是否设置拆分列
            if (strSplitGroupColumn.Length == 0 || strSplitGroupHeader.Length == 0 || strSplitPropertyColumn.Length == 0 || strSplitPropertyType.Length == 0)
                return;       
       
            //设置数据源属性
            cmbGroup.DisplayMemberPath = strSplitGroupHeader;
            cmbGroup.SelectedValuePath = strSplitGroupColumn;

            try
            {
                using (DataTable tableSource = MainApp.I_DatabaseService.GetList(string.Format("SELECT {0},{1} FROM {2} WHERE GOODS_TYPE_REMARK = 'GOODS_TYPE'", strSplitGroupColumn, strSplitGroupHeader, strSplitPropertyType)))
                {
                    //插入新值
                    DataRow rowNew = tableSource.NewRow();
                    rowNew[0] = 0;
                    rowNew[1] = "全部";
                    tableSource.Rows.InsertAt(rowNew,0);

                    cmbGroup.ItemsSource = tableSource.DefaultView;
                    if (cmbGroup.HasItems)
                    {
                        cmbGroup.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //类型更改显示不同的属性信息
        private void cmbGroup_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            //清空
            this.U_Clear();

            //获得拆分数据源
            tableSplit = GetSplitTable(cmbGroup.SelectedValue.ToString());

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow  select rowSplit;
                StringBuilder strBuilderSplit = new StringBuilder();

                try
                {
                    //添加控件
                    foreach (DataRow rowSplit in query)
                    {
                        switch (rowSplit["ControlType"].ToString().ToLower())
                        {
                            case "bool":
                                AddCheckBoxControl(rowSplit);
                                break;
                            case "combobox":
                                AddComboBoxControl(rowSplit);
                                break;
                            case "element":
                                AddElementComboBoxControl(rowSplit);
                                break;
                            default:
                                AddTextBoxControl(rowSplit);
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        #region     ------Add Controls

        //添加标题
        private void AddHeader(string Header,StackPanel panel)
        {
            TextBlock txtHeader = new TextBlock();
            txtHeader.Text = string.Format("{0}：",Header);
            txtHeader.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            txtHeader.Margin = new Thickness(5);
            panel.Children.Add(txtHeader);
        }
        
        //Add CheckBox
        private void AddCheckBoxControl(DataRow rowSplit)
        {
            StackPanel panel = new StackPanel();
            panel.Orientation = Orientation.Horizontal;
            panel.Tag = rowSplit;
            this.panelItem.Children.Add(panel);

            //添加标题
            this.AddHeader(rowSplit["header"].ToString(),panel);

            //添加控件
            CheckBox chkBox = new CheckBox();
            chkBox.Margin = new Thickness(5);
            chkBox.IsChecked = (rowSplit.IsNull("defaultvalue") && Convert.ToInt32(rowSplit["defaultvalue"].ToString()) == 0) ? false : true;
            panel.Children.Add(chkBox);
        }

        //Add TextBox Control
        private void AddTextBoxControl(DataRow rowSplit)
        {
            StackPanel panel = new StackPanel();
            panel.Orientation = Orientation.Horizontal;
            panel.Tag = rowSplit;
            this.panelItem.Children.Add(panel);

            //添加标题
            this.AddHeader(rowSplit["header"].ToString(), panel);

            //添加控件
            TextBox textBox = new TextBox();
            textBox.Width = 150;
            textBox.Margin = new Thickness(5,0,5,5);
            //textBox.Text = rowSplit["defaultvalue"].ToString();
            panel.Children.Add(textBox);
        }

        //Add ComboBox Control
        private void AddComboBoxControl(DataRow rowSplit)
        {
            StackPanel panel = new StackPanel();
            panel.Orientation = Orientation.Horizontal;
            panel.Tag = rowSplit;
            this.panelItem.Children.Add(panel);

            //添加标题
            this.AddHeader(rowSplit["header"].ToString(), panel);

            //添加控件
            ComboBox cmb = new ComboBox();
            cmb.Width = 150;
            cmb.Margin = new Thickness(5, 0, 5, 5);
            cmb.DisplayMemberPath = "NAME".ToLower();
            cmb.SelectedValuePath = "VALUE".ToLower();
            panel.Children.Add(cmb);
            //设置数据源
            cmb.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowSplit["DataBind"].ToString()).DefaultView;

            //默认值
            //if (rowSplit.IsNull("defaultvalue"))
            //    cmb.SelectedIndex = -1;
            //else
            //    cmb.SelectedValue = rowSplit["defaultvalue"];
        }

        //Add ElementBox Control
        private void AddElementComboBoxControl(DataRow rowSplit)
        {
            StackPanel panel = new StackPanel();
            panel.Orientation = Orientation.Horizontal;
            panel.Tag = rowSplit;
            this.panelItem.Children.Add(panel);

            //添加标题
            this.AddHeader(rowSplit["header"].ToString(), panel);

            //添加控件
            ComboBox cmb = new ComboBox();
            cmb.Margin = new Thickness(5, 0, 5, 5);
            cmb.Width = 150;
            panel.Children.Add(cmb);

            //解析Item
            foreach (string s in rowSplit["key"].ToString().Split('|'))
            {
                //分解字符串添加ITEM数据
                if (s.TrimEnd().Length > 0)
                {
                    cmb.Items.Add(s.TrimEnd());
                }
            }
            //默认值
            //if (rowSplit.IsNull("defaultvalue"))
            //    cmb.SelectedIndex = -1;
            //else
            //    cmb.SelectedValue = rowSplit["defaultvalue"];
        }

        #endregion
        
        /// <summary>
        /// 清空控件
        /// </summary>
        public void U_Clear()
        {
            this.panelItem.Children.Clear();
        }

        /// <summary>
        /// 获得拆分属性组合值
        /// </summary>
        public string U_GetSplitPropertyValue()
        {
            StringBuilder strSql = new StringBuilder();
            foreach (DependencyObject control in this.panelItem.Children)
            {
                if (control is StackPanel)
                {
                    StackPanel panel = control as StackPanel;
                    if (panel.Children.Count == 2)
                    {
                        DataRow rowSplit = panel.Tag as DataRow;
                        if (rowSplit != null)
                        {
                            //获得控件类型
                            string strControlType = rowSplit["ControlType"].ToString().ToLower();
                            //判断控件类型
                            switch (strControlType)
                            {
                                case "bool":
                                    CheckBox chkBox = panel.Children[1] as CheckBox;
                                    if (chkBox != null)
                                    {
                                        //if (strSql.Length == 0)
                                        //    strSql.Append(Convert.ToInt32(chkBox.IsChecked.ToString()));
                                        //else
                                        //    strSql.Append("|").Append(Convert.ToInt32(chkBox.IsChecked.ToString()));
                                        strSql.Append(string.Format(" AND ISNULL({0},' ') {1}",
                                 rowSplit["Column"].ToString(),
                                 string.IsNullOrEmpty(chkBox.IsChecked.ToString()) ? " like '%'" : string.Format(" = '{0}'", Convert.ToInt32(chkBox.IsChecked.ToString()))));


                                        
                                    }
                                    break;
                                case "combobox":
                                case "element":
                                    ComboBox cmbBox = panel.Children[1] as ComboBox;
                                    if (cmbBox != null)
                                    {
                                        if (cmbBox.SelectedValue != null)
                                        {
                                            string strValue = strControlType == "combobox" ? cmbBox.SelectedValue.ToString() : cmbBox.Text;
                                            //if (strSql.Length == 0)
                                            //    strSql.Append(strValue);
                                            //else
                                            //    strSql.Append("|").Append(strValue);

                                            strSql.Append(string.Format(" AND ISNULL({0},' ') {1}",
                                                         rowSplit["Column"].ToString(),
                                                         string.IsNullOrEmpty(strValue) ? " like '%'" : string.Format(" = '{0}'", strValue)));
                                        }
                                    }
                                    break;
                                default:
                                    TextBox textBox = panel.Children[1] as TextBox;
                                    if (textBox != null)
                                    {
                                        //if (strSql.Length == 0)
                                        //    strSql.Append(textBox.Text);
                                        //else
                                        //    strSql.Append("|").Append(textBox.Text);

                                        strSql.Append(string.Format(" AND ISNULL({0},' ') {1}",
                                                         rowSplit["Column"].ToString(),
                                                         string.IsNullOrEmpty(textBox.Text)  ? " like '%'" : string.Format(" = '{0}'", textBox.Text)));
                                    }
                                    break;
                            }
                        }
                    }
                }
            }
            return strSql.Length == 0 ? "" : MainApp._DatabaseType.ToLower() == "oracle" ? strSql.ToString().Replace("ISNULL", "NVL"): strSql.ToString();
        }

        /// <summary>
        /// 获得组合属性查询条件
        /// </summary>
        public string U_GetSplitPropertyWhere()
        {
            string strWhere = Convert.ToInt32(this.cmbGroup.SelectedValue) == 0 ? "1=1" : string.Format("{0}='{1}'",strSplitGroupColumn,this.cmbGroup.SelectedValue.ToString());
            return string.Format("{0} {1}", strWhere, this.U_GetSplitPropertyValue());
        }

        public string U_GetSplitProertyTypeValue()
        {
            return Convert.ToInt32(this.cmbGroup.SelectedValue) == 0 ? string.Empty :  this.cmbGroup.SelectedValue.ToString();
        }

    }
}
