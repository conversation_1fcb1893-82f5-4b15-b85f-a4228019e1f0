<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>false</UseWindowsForms>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNet.WebApi.Owin" Version="5.2.7" />
    <PackageReference Include="Microsoft.Owin.Host.HttpListener" Version="4.2.0" />
    <PackageReference Include="Microsoft.Owin.Hosting" Version="4.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.7" />
    <PackageReference Include="Microsoft.Owin" Version="4.2.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Cors" Version="5.2.7" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.3" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.10.3" />
    <PackageReference Include="Microsoft.AspNet.WebApi" Version="5.2.7" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <!-- 暂时移除Common项目引用，避免COM引用问题 -->
  <!--
  <ItemGroup>
    <ProjectReference Include="..\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj" />
  </ItemGroup>
  -->
</Project>