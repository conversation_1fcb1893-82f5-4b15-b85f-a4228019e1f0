﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucStoreCell"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="798">
    <UserControl.Resources>
        <Color x:Key="colorDefaultCellStatus" A="192" B="192" G="192" R="192"></Color>
        <ContextMenu x:Key="menuCell"  MenuItem.Click="ContextMenu_Click">
            <!--<MenuItem Name="menuCellStatus" Header="存储状态"></MenuItem>-->
            <!--<MenuItem Name="menuRunStatus" Header="运行状态"></MenuItem>-->
            <!--<MenuItem Name="menuMove" Header="移库"></MenuItem>-->
            <MenuItem Name="menuResetCell" Header="重置"></MenuItem>
        </ContextMenu>
     </UserControl.Resources>
    
     <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <Grid Grid.Row="0" Grid.ColumnSpan="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <uc:ucWareHouseQuery x:Name="ucWare" Margin="1" Grid.Column="0"></uc:ucWareHouseQuery>
            <Border Grid.Column="1" >
                    <!--<Border.Effect>
                        <DropShadowEffect Color="DarkBlue" ShadowDepth="2"></DropShadowEffect>
                    </Border.Effect>-->
                <WrapPanel VerticalAlignment="Center" Margin="4" ButtonBase.Click="WrapPanel_Click">
                    <TextBlock Text="当前货位"  Margin="2"></TextBlock>
                    <TextBlock Name="txtCellCode" Margin="2"></TextBlock>

                    <GridSplitter VerticalAlignment="Stretch"  Background="DarkGray" Width="1" Margin="2,1,2,1"></GridSplitter>
                    
                    <TextBlock Text="缩放比例:"  VerticalAlignment="Center" Margin="2"></TextBlock>
                    <TextBlock Name="txtZoom" Text="100" FontStyle="Italic" Margin="2,0,0,0" VerticalAlignment="Center"></TextBlock>
                    <TextBlock Text="%"  VerticalAlignment="Center"  Margin="1,0,3,0"></TextBlock>

                    <Button Name="btnZoomOut" Template="{StaticResource templateImageButtonZoomOut}" ></Button>
                    <Button Name="btnZoomIn" Template="{StaticResource templateImageButtonZoomIn}"></Button>
                    <Button Name="btnZoom" Template="{StaticResource templateImageButtonZoom}"></Button>

                    <GridSplitter VerticalAlignment="Stretch"  Background="DarkGray" Width="1" Margin="2,1,2,1"></GridSplitter>

                    <Button Name="btnUpdate" Template="{StaticResource templateImageButtonUpdate}" ></Button>
                </WrapPanel>
            </Border>
        </Grid>

        <ScrollViewer Grid.Column="0" Grid.Row="1" VerticalScrollBarVisibility="Visible" ScrollViewer.CanContentScroll="True" ScrollViewer.PanningMode="Both"  HorizontalScrollBarVisibility="Visible">
            <!--tzyg alter 2017-03-12-->
            <uc:DrawingCanvas x:Name="canvasCell"  Margin="1" ContextMenu="{StaticResource menuCell}" MinHeight="300" MouseDown="canvasCell_MouseDown"/>
            <!--<uc:DrawingCanvas x:Name="canvasCell"  Margin="1"  MinHeight="300" MouseDown="canvasCell_MouseDown"/>-->
        </ScrollViewer>

        <Border Grid.Column="1" Grid.Row="1" BorderThickness="1" BorderBrush="DarkBlue">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <GroupBox Header="运行状态:" Grid.Row="0" Margin="2" >
                    <StackPanel Name="panelRunStatus" Grid.Row="0" Margin="2" Orientation="Vertical">  </StackPanel>                  
                </GroupBox>

                <GroupBox Header="存储状态:" Grid.Row="1" Margin="2" >
                    <StackPanel Name="panelCellStatus" Grid.Row="0" Margin="2" Orientation="Vertical"></StackPanel>                    
                </GroupBox>
            </Grid>
        </Border>
    </Grid>
</UserControl>
