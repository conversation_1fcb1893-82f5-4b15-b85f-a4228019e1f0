﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Net;
using System.Text.RegularExpressions;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class ManageIn : SignInPage
{
    protected override void PageLoad()
    {
        bRefresh_Click(null, null);
        if (!Page.IsPostBack)
        {
            dropdown.DataBind();
            dropdown.SelectedValue = "default";
        }
    }

    protected void bSearch_Click(object sender, EventArgs e)
    {
        try
        {
            string sSearchText = this.tbSearch.Text.Trim();
            DataTable dtGoods = Common._I_DatabaseClient.GetList(string.Format(@"
                select GOODS_ID, GOODS_CODE, GOODS_NAME from GOODS_MAIN where GOODS_CLASS_ID = 1 and GOODS_FLAG = '1' 
                and (GOODS_CODE like '%{0}%' or GOODS_NAME like '%{0}%') order by GOODS_CODE", sSearchText),"HouseMap");
            if (dtGoods.Rows.Count == 0)
            {
                Common.LayerFailed(this, string.Format("未找到物资 {0}", sSearchText));
                return;
            }
            DataTable dt = Common.NewDataTable(3);
            foreach (DataRow dr in dtGoods.Rows)
            {
                Common.AddDataTable(dt, dr["GOODS_ID"].ToString(), dr["GOODS_CODE"].ToString(), dr["GOODS_NAME"].ToString());
            }
            this.gvGoods.DataSource = dt.DefaultView;
            this.gvGoods.DataBind();
        }
        catch (WebException ex)
        {
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch (Exception ex)
        {
            bRefresh_Click(null, null);
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bSave_Click(object sender, EventArgs e)
    {
        try
        {
            string sStockBarcode = this.tbStockBarcode.Text.Trim();
            if (string.IsNullOrWhiteSpace(sStockBarcode))
            {
                Common.LayerFailed(this, "请输入托盘条码");
                return;
            }
            if (!Regex.Match(sStockBarcode, "^[A-E][0-9]{5}$").Success)
            {
                Common.LayerFailed(this, "请检查托盘条码是否合法!托盘条码格式为A-E+5位数字");
                return;
            }
            if (string.IsNullOrEmpty(dropdown.Text))
            {
                Common.LayerFailed(this, "请检查是否选择入库站台!");
                return;
            }
            string stationCode = dropdown.Text;
            switch (stationCode)
            {
                case "一号站台":
                    stationCode = "12002";
                    break;
                case "二号站台":
                    stationCode = "12004";
                    break;
                case "三号站台":
                    stationCode = "12006";
                    break;
                case "四号站台":
                    stationCode = "12008";
                    break;
                default:
                    Common.LayerFailed(this, "输入站台不合法, 请检查!");
                    return;
            }
            DataTable startId = Common._I_DatabaseClient.GetList(string.Format(@"select * from WH_CELL where CELL_CODE = '{0}'",stationCode), "HouseMap");
            if(startId == null || startId.Rows.Count == 0)
            {
                Common.LayerFailed(this, "未能找到绑定站台信息!");
                return;
            }
            ManageService.MANAGE_MAIN mMANAGE_MAIN = new ManageService.MANAGE_MAIN();
            mMANAGE_MAIN.MANAGE_TYPE_CODE = "ManageStorageInlocal";
            mMANAGE_MAIN.STOCK_BARCODE = sStockBarcode;
            mMANAGE_MAIN.CELL_MODEL = string.Empty;
            mMANAGE_MAIN.START_CELL_ID = Convert.ToInt32(startId.Rows[0]["CELL_ID"]);
            mMANAGE_MAIN.END_CELL_ID = mMANAGE_MAIN.START_CELL_ID;
            mMANAGE_MAIN.MANAGE_OPERATOR = SessionName;
            mMANAGE_MAIN.MANAGE_BEGIN_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            mMANAGE_MAIN.MANAGE_STATUS = "WaitingSend";
            mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;
            mMANAGE_MAIN.MANAGE_REMARK = string.Empty;
            List<ManageService.MANAGE_LIST> listMANAGE_LIST = new List<ManageService.MANAGE_LIST>();
            foreach (GridViewRow gvr in this.gvInStorage.Rows)
            {
                ManageService.MANAGE_LIST ml = new ManageService.MANAGE_LIST();
                ml.GOODS_ID = Convert.ToInt32((gvr.FindControl("value1") as Label).Text);
                TextBox tb = gvr.FindControl("value4") as TextBox;
                ml.MANAGE_LIST_QUANTITY = Convert.ToDecimal(tb.Text);
                if (ml.MANAGE_LIST_QUANTITY <= 0)
                {
                    Common.LayerFailed(this, string.Format("请输入物资 {0} 数量", (gvr.FindControl("value2") as Label).Text));
                    return;
                }
                listMANAGE_LIST.Add(ml);
            }
            string sResult = string.Empty;
            if (Common._I_ManageService.ManageInAndroid(out sResult, mMANAGE_MAIN, listMANAGE_LIST.ToArray()))
            {
                Common.LayerSuccess(this, string.Format("入库成功 托盘条码 {0}", sStockBarcode));
                bRefresh_Click(null, null);
            }
            else
            {
                Common.LayerFailed(this, sResult);
            }
        }
        catch (WebException ex)
        {
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bRefresh_Click(object sender, EventArgs e)
    {
        try
        {
            this.tbStockBarcode.Text = string.Empty;
            this.tbSearch.Text = string.Empty;
            DataTable dt = Common.NewDataTable(3);
            this.gvGoods.DataSource = dt.DefaultView;
            this.gvGoods.DataBind();
            dt = Common.NewDataTable(3);
            this.gvInStorage.DataSource = dt.DefaultView;
            this.gvInStorage.DataBind();
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void gvGoods_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dt2 = (DataTable)this.gvInStorage.DataSource;
        if (this.gvGoods.SelectedRow != null)
        {
            Label label1 = this.gvGoods.SelectedRow.FindControl("value1") as Label;
            Label label2 = this.gvGoods.SelectedRow.FindControl("value2") as Label;
            Label label3 = this.gvGoods.SelectedRow.FindControl("value3") as Label;
            if (label1 != null && label2 != null && label3 != null)
            {
                DataTable dt = Common.NewDataTable(4);
                int iCount = this.gvInStorage.Rows.Count;
                if (iCount > 0)
                {
                    foreach (GridViewRow gvr in this.gvInStorage.Rows)
                    {
                        Common.AddDataTable(dt, (gvr.FindControl("value1") as Label).Text, (gvr.FindControl("value2") as Label).Text,
                            (gvr.FindControl("value3") as Label).Text, (gvr.FindControl("value4") as TextBox).Text);
                    }
                }
                Common.AddDataTable(dt, label1.Text, label2.Text, label3.Text, "0");
                this.gvInStorage.DataSource = dt.DefaultView;
                this.gvInStorage.DataBind();
            }
        }
    }

    protected void gvGoods_SelectedDeleteChanged(object sender, EventArgs e)
    {
        DataTable dt2 = (DataTable)gvGoods.DataSource;
        if (this.gvGoods.SelectedRow != null)
        {
            Label label1 = this.gvGoods.SelectedRow.FindControl("value1") as Label;
            Label label2 = this.gvGoods.SelectedRow.FindControl("value2") as Label;
            Label label3 = this.gvGoods.SelectedRow.FindControl("value3") as Label;
            if (label1 != null && label2 != null && label3 != null)
            {
                DataTable dt = Common.NewDataTable(4);
                int iCount = this.gvInStorage.Rows.Count;
                if (iCount > 0)
                {
                    foreach (GridViewRow gvr in this.gvInStorage.Rows)
                    {
                        Common.AddDataTable(dt, (gvr.FindControl("value1") as Label).Text, (gvr.FindControl("value2") as Label).Text,
                            (gvr.FindControl("value3") as Label).Text, (gvr.FindControl("value4") as TextBox).Text);
                    }
                }
                dt.Rows.RemoveAt(this.gvInStorage.SelectedIndex);
                this.gvInStorage.DataSource = dt.DefaultView;
                this.gvInStorage.DataBind();
            }
        }
        

    }

    public DataTable GridViewToDataTable(GridView grid)
    {
        DataTable dt = new DataTable();

        DataColumn dc;

        DataRow dr;

        for (int i = 0; i < grid.Columns.Count; i++)
        {
            dc = new DataColumn();

            dc.ColumnName = grid.Columns[i].HeaderText;

            dt.Columns.Add(dc);
        }

        for (int i = 0; i < grid.Rows.Count; i++)
        {
            dr= dt.NewRow();

            for(int j = 0; j < grid.Columns.Count; j++)
            {
                dr[j] = grid.Rows[i].Cells[j].Text;
            }
            dt.Rows.Add(dr);
        }
        return dt;
    }

    protected void gvGoods_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        e.Row.Attributes.Add("ondblclick", Page.ClientScript.GetPostBackClientHyperlink(sender as GridView, "Select$" + e.Row.RowIndex.ToString()));
        if (e.Row.RowIndex >= 0)
        {
            e.Row.Attributes.Add("onmouseover", "currentcolor=this.style.backgroundColor;this.style.backgroundColor='#1E9FFF';");
            e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor=currentcolor;");
        }
    }

    protected void gvGoods_RowDataUnbound(object sender, GridViewRowEventArgs e)
    {
        e.Row.Attributes.Add("ondblclick", Page.ClientScript.GetPostBackClientHyperlink(sender as GridView, "Select$" + e.Row.RowIndex.ToString()));
        if (e.Row.RowIndex >= 0)
        {
            e.Row.Attributes.Add("onmouseover", "currentcolor=this.style.backgroundColor;this.style.backgroundColor='#1E9FFF';");
            e.Row.Attributes.Add("onmouseout", "this.style.backgroundColor=currentcolor;");
        }

    }

    protected void gvInStorage_RowDeleting(object sender, GridViewDeleteEventArgs e)
    {
        
    }
}
