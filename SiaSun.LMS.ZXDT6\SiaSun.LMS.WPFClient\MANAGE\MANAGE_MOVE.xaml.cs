﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE_OUT.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_MOVE : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        /// <summary>
        ///
        /// </summary>
        public MANAGE_MOVE(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_DatabaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

        }

        void ucManagePosition_U_StartPositionChanged()
        {
            this.StorageListBind(string.Format(" CELL_ID = {0}", this.ucManagePosition.U_START_POSITION_ID));
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            //fst add 2018-12-01
            //if (mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageMoveCellForce.ToString())
            //{
            //    this.cbxCheckCellStatus.Visibility = Visibility.Visible;
            //    this.cbxDownloadControl.Visibility = Visibility.Visible;
            //}

            this.ucManagePosition.U_StartPositionChanged += new UC.ucManagePosition.U_StartPositionValueChangedHandler(ucManagePosition_U_StartPositionChanged);

        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }

        /// <summary>
        /// fst add 2018-12-01 是否校验起止货位可用状态
        /// </summary>
        private void cbxCheckCellStatus_CheckChanged(object sender, RoutedEventArgs e)
        {
            this.ucManagePosition.U_IsCheckCellEnable = this.cbxCheckCellStatus.IsChecked == null ? true : (bool)this.cbxCheckCellStatus.IsChecked;
            
            if (mMANAGE_TYPE != null)
            {
                this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
        }

        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStorageGroup.U_Where = string.Format("AREA_TYPE= 'LiKu' AND {0} ", QueryWhere);
            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = false;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = false;

            string strResult = "\n";

            try
            {
                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                DataTable tableSource = this.ucStorageGroup.U_DataSource;
                if(tableSource == null || tableSource.Rows.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "选定的起始位置没有库存");
                    return;
                }

                List<string> lsStockBarcode = new List<string>();

                foreach (DataRow dr in tableSource.Rows)
                {
                    dr["MANAGE_LIST_QUANTITY"] = dr["STORAGE_LIST_QUANTITY"];
                    lsStockBarcode.Add(dr["STOCK_BARCODE"].ToString());
                }

                if(lsStockBarcode.Distinct().Count()>1)
                {
                    MainApp._MessageDialog.ShowResult(false, "只允许选择1条信息");
                    return;
                }

                //获得选中记录
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);
                
                //校验是否选中记录
                if (listMANAGE_LIST.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "未能得到任务列表信息");
                    return;
                }


                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //调用入库函数配盘入库
                    string index = this.ucStorageGroup.U_DataSource.Rows[0]["STOCK_BARCODE"].ToString().Substring(0, 1);
                    switch (index)
                    {
                        //偶数层
                        case "A":
                        case "C":
                        case "E":
                        case "G":
                            DataTable dt = MainApp.I_DatabaseService.GetList(string.Format(@"select * from WH_CELL where CELL_Y in ('2','4','6','8') and CELL_ID = {0}", this.ucManagePosition.U_END_POSITION_ID));
                            if(dt == null || dt.Rows.Count == 0)
                            {
                                MainApp._MessageDialog.ShowResult(false, "偶数层容器倒入奇数层");
                                return;
                            }
                            break;
                        //奇数层
                        case "B":
                        case "D":
                        case "F":
                        case "H":
                            DataTable dt2 = MainApp.I_DatabaseService.GetList(string.Format(@"select * from WH_CELL where CELL_Y in ('1','3','5','7') and CELL_ID = {0}", this.ucManagePosition.U_END_POSITION_ID));
                            if(dt2 == null || dt2.Rows.Count == 0)
                            {
                                MainApp._MessageDialog.ShowResult(false, "奇数层容器入偶数层");
                                return;
                            }
                            break;
                        default:
                            break;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = this.ucStorageGroup.U_DataSource.Rows[0]["CELL_MODEL"].ToString();
                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = MainApp.I_SystemService.GetSysParameter("ManualOutLevel", "0");
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.SystemName.SSWMS.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Waiting.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = this.ucStorageGroup.U_DataSource.Rows[0]["STOCK_BARCODE"].ToString();
                    mMANAGE_MAIN.STOCK_WEIGHT = int.TryParse(this.ucStorageGroup.U_DataSource.Rows[0]["STOCK_WEIGHT"].ToString(), out int stockWeight) ? stockWeight : 0;

                    boolResult = MainApp.I_ManageService.ManageCreate(
                        mMANAGE_MAIN,
                        listMANAGE_LIST,
                        raiseTrans: true,
                        checkStorage: this.ucManagePosition.U_CheckStockExistStorage,
                        checkManage: true,
                        checkCellStatus: this.ucManagePosition.U_IsCheckCellEnable,
                        autoComplete: this.cbxDownloadControl.IsChecked == null ? this.ucManagePosition.U_AutoCompleteTask : !(bool)this.cbxDownloadControl.IsChecked,
                        autoControl: this.cbxDownloadControl.IsChecked == null ? this.ucManagePosition.U_AutoDownloadControlTask : (bool)this.cbxDownloadControl.IsChecked,
                        doubleInAutoMove: true,
                        out strResult) ;

                    //检验执行结果
                    if (boolResult)
                    {
                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(boolResult, strResult);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(List<DataRowView> listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp.I_DatabaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //刷新
            this.ucManagePosition.U_Refresh();

            //wdz alter 2018-01-22
            //this.ucStorageGroup.U_InitControl();
            this.ucStorageGroup.U_Clear();
        }


    }
}
