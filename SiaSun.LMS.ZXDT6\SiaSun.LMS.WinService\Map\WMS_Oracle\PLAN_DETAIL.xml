﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="PLAN_DETAIL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="PLAN_DETAIL" type="SiaSun.LMS.Model.PLAN_DETAIL, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="PLAN_DETAIL">
			<result property="PLAN_DETAIL_ID" column="plan_detail_id" />
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_BARCODE" column="goods_barcode" />
			<result property="PLAN_DETAIL_REMARK" column="plan_detail_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="PLAN_DETAIL_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  plan_detail_id,
				  plan_list_id,
				  box_barcode,
				  goods_barcode,
				  plan_detail_remark
			From PLAN_DETAIL
		</select>
		
		<select id="PLAN_DETAIL_SELECT_BY_ID" parameterClass="int" extends = "PLAN_DETAIL_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_detail_id=#PLAN_DETAIL_ID# 
				</isParameterPresent>
			</dynamic>
		</select>


    <select id="PLAN_DETAIL_SELECT_BY_PLAN_LIST_ID" parameterClass="int" extends = "PLAN_DETAIL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id=#PLAN_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="PLAN_DETAIL_INSERT" parameterClass="PLAN_DETAIL">
      Insert Into PLAN_DETAIL (
      plan_detail_id,
      plan_list_id,
      box_barcode,
      goods_barcode,
      plan_detail_remark
      )Values(
      #PLAN_DETAIL_ID#,
      #PLAN_LIST_ID#,
      #BOX_BARCODE#,
      #GOODS_BARCODE#,
      #PLAN_DETAIL_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="PLAN_DETAIL_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="PLAN_DETAIL_UPDATE" parameterClass="PLAN_DETAIL">
      Update PLAN_DETAIL Set
      <!--plan_detail_id=#PLAN_DETAIL_ID#,-->
      plan_list_id=#PLAN_LIST_ID#,
      box_barcode=#BOX_BARCODE#,
      goods_barcode=#GOODS_BARCODE#,
      plan_detail_remark=#PLAN_DETAIL_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					plan_detail_id=#PLAN_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="PLAN_DETAIL_DELETE" parameterClass="int">
			Delete From PLAN_DETAIL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_detail_id=#PLAN_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="PLAN_DETAIL_DELETE_BY_PLAN_ID" parameterClass="int">
      Delete From PLAN_DETAIL
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_id=#PLAN_ID#
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>