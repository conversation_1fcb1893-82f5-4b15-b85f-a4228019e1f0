﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using NPOI.SS.UserModel;
using NPOI.XSSF.Streaming;
using NPOI.HSSF.UserModel;
using System.IO;
using NPOI.XSSF.UserModel;

namespace SiaSun.LMS.Common
{
    public class Excel
    {
        /// <summary>
        /// 导出查询的所有数据,忽略分页
        /// </summary>
        /// <param name="table">数据源</param>
        /// <param name="title">标题备注</param>
        public void ExportAllToExcel(DataTable tableSource, string title)
        {
            Microsoft.Office.Interop.Excel.Application objApp;
            Microsoft.Office.Interop.Excel._Workbook objBook;
            Microsoft.Office.Interop.Excel.Workbooks objBooks;
            Microsoft.Office.Interop.Excel.Sheets objSheets;
            Microsoft.Office.Interop.Excel._Worksheet objSheet;
            Microsoft.Office.Interop.Excel.Range range;

            int columnIndex = 1;
            int rowIndex = 1;

            try
            {
                objApp = new Microsoft.Office.Interop.Excel.Application();
                objBooks = objApp.Workbooks;
                objBook = objBooks.Add(System.Reflection.Missing.Value);
                objSheets = objBook.Worksheets;
                objSheet = (Microsoft.Office.Interop.Excel._Worksheet)objSheets.get_Item(1);

                //设置标题
                //objSheet.Cells[rowIndex, columnIndex] = title;
                //columnIndex++;
                //objSheet.Cells[rowIndex, columnIndex] = DateTime.Now.ToString();
                //rowIndex++;

                //设置标题列
                columnIndex = 0;

                //列头
                foreach (DataColumn col in tableSource.Columns)
                {
                    columnIndex++;
                    objSheet.Cells[rowIndex, columnIndex] = col.ColumnName;
                }

                string[,] saRet;
                saRet = new string[tableSource.Rows.Count, columnIndex];
                range = objSheet.get_Range("A2", System.Reflection.Missing.Value);
                range = range.get_Resize(tableSource.Rows.Count, columnIndex);
                range.WrapText = false;
                range.Columns.AutoFit();

                //内容                
                columnIndex = 0;
                foreach (DataColumn col in tableSource.Columns)
                {
                    rowIndex = 0;
                    foreach (DataRow row in tableSource.Rows)
                    {
                        saRet[rowIndex, columnIndex] = row[col].ToString();
                        rowIndex++;
                    }
                    columnIndex++;
                }

                range.set_Value(System.Reflection.Missing.Value, saRet);
                objApp.Visible = true;
            }
            catch(Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 获得
        /// </summary>
        public DataTable ImportToDataTable(string FileName,bool IsAllData)
        {
            string strConn = null;
            if (FileName.ToLower().Contains(".xlsx"))    //Excel 2007
            {
                strConn = string.Format(@"provider=Microsoft.ACE.OLEDB.12.0;data source={0};Extended Properties=Excel 12.0;", FileName);
            }
            else            
            {
                strConn = string.Format(@"provider=Microsoft.Jet.OLEDB.4.0;data source={0};Extended Properties=Excel 8.0", FileName);
            }

            DataTable tableExcel = new DataTable();
            using (System.Data.OleDb.OleDbConnection cnn = new System.Data.OleDb.OleDbConnection(strConn))
            {
                System.Data.OleDb.OleDbDataAdapter adp = new System.Data.OleDb.OleDbDataAdapter(string.Format("select * from [Sheet1$]",IsAllData?string.Empty:" where 0=1"), cnn);
                cnn.Open();
                adp.Fill(tableExcel);
                cnn.Close();
            }
            return tableExcel;
        }

           /// <summary>
        /// 导出查询的所有数据,可分页
        /// </summary>
        /// <param name="table">数据源</param>
        /// <param name="title">标题备注</param>
        public void ExportAllToExcel1(DataTable tableSource, string title)
        {
            Microsoft.Office.Interop.Excel.Application objApp;
            Microsoft.Office.Interop.Excel._Workbook objBook;
            Microsoft.Office.Interop.Excel.Workbooks objBooks;
            Microsoft.Office.Interop.Excel.Sheets objSheets;
            Microsoft.Office.Interop.Excel._Worksheet objSheet;
            Microsoft.Office.Interop.Excel.Range range;



            try
            {
                //spring
                int Num = 10000;//设置每个sheet页显示行数
                int RowNum = tableSource.Rows.Count;//传入表行数
                int SheetNum = (int)Math.Floor((decimal)RowNum / Num + 1);//所需sheet页数
                objApp = new Microsoft.Office.Interop.Excel.Application();
                objBooks = objApp.Workbooks;
                objBook = objBooks.Add(System.Reflection.Missing.Value);
                objSheets = objBook.Worksheets;

                for (int i = 1; i <= SheetNum; i++)
                {
                    int columnIndex = 1;
                    int rowIndex = 1;

                    objSheet = (Microsoft.Office.Interop.Excel._Worksheet)objSheets.get_Item(i);

                    //设置标题
                    objSheet.Cells[rowIndex, columnIndex] = title;
                    columnIndex++;
                    objSheet.Cells[rowIndex, columnIndex] = DateTime.Now.ToString();
                    rowIndex++;

                    //设置标题列
                    columnIndex = 0;

                    //列头
                    foreach (DataColumn col in tableSource.Columns)
                    {
                        columnIndex++;
                        objSheet.Cells[rowIndex, columnIndex] = col.ColumnName;
                    }

                    string[,] saRet;
                    saRet = new string[tableSource.Rows.Count, columnIndex];
                    range = objSheet.get_Range("A3", System.Reflection.Missing.Value);
                    range = range.get_Resize(Num, columnIndex);

                    //内容                
                    columnIndex = 0;
                    foreach (DataColumn col in tableSource.Columns)
                    {
                        int Index = 0;
                        //rowIndex = 0;
                        //foreach (DataRow row in tableSource.Rows)
                        //{
                        //    saRet[rowIndex, columnIndex] = row[col].ToString();
                        //    rowIndex++;
                        //}

                        for (rowIndex = (i - 1) * Num; rowIndex < (i * Num > RowNum ? RowNum : i * Num); rowIndex++)
                        {
                            DataRow row = tableSource.Rows[rowIndex];
                            saRet[Index, columnIndex] = row[col].ToString();
                            Index++;
                        }

                        columnIndex++;
                    }

                    range.set_Value(System.Reflection.Missing.Value, saRet);

                    object missing = System.Reflection.Missing.Value;
                    //objSheet = (Microsoft.Office.Interop.Excel._Worksheet)objBook.Worksheets.Add(missing, missing, missing, missing);//添加一个sheet  
                    objSheet = (Microsoft.Office.Interop.Excel._Worksheet)objBook.Worksheets.Add(missing, objBook.Sheets[i], missing, missing);

                }


                objApp.Visible = true;

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 导出查询的所有数据，不分页，NPOI，添加NPOI的NuGet包
        /// </summary>
        /// <param name="table">数据源</param>
        /// <param name="strFileName">文件保存路径 如：E:\入出库记录.xlsx</param>
        public void ExportExcel(DataTable dt, string strFileName)
        {

            bool result = false;
            try
            {

                int sheetIndex = 0;
                //根据输出路径的扩展名判断workbook的实例类型
                IWorkbook workbook = null;
                string pathExtensionName = strFileName.Trim().Substring(strFileName.Length - 5);

                if (pathExtensionName.Contains(".xlsx"))
                {
                    workbook = new SXSSFWorkbook(100);// keep 100 rows in memory, exceeding rows will be flushed to disk
                }
                else if (pathExtensionName.Contains(".xls"))
                {
                    workbook = new HSSFWorkbook();
                }
                else
                {
                    strFileName = strFileName.Trim() + ".xls";
                    workbook = new HSSFWorkbook();
                }
                ISheet sheet = workbook.CreateSheet("sheet" + sheetIndex);//创建一个名称为Sheet0的表

                //ICellStyle cellStyle = workbook.CreateCellStyle();

                sheetIndex++;
                if (dt != null && dt.Rows.Count > 0)
                {
                    //ISheet sheet = workbook.CreateSheet(string.IsNullOrEmpty(dt.TableName) ? ("sheet" + sheetIndex) : dt.TableName);//创建一个名称为Sheet0的表
                    int rowCount = dt.Rows.Count;//行数
                    int columnCount = dt.Columns.Count;//列数

                    //设置列头
                    IRow row = sheet.CreateRow(0);//excel第一行设为列头
                    for (int c = 0; c < columnCount; c++)
                    {
                        ICell cell = row.CreateCell(c);
                        cell.SetCellValue(dt.Columns[c].ColumnName);
                    }



                    //设置每行每列的单元格,
                    for (int i = 0; i < rowCount; i++)
                    {
                        row = sheet.CreateRow(i + 1);
                        for (int j = 0; j < columnCount; j++)
                        {
                            ICell cell = row.CreateCell(j);//excel第二行开始写入数据

                            //if (decimal.TryParse(dt.Rows[i][j].ToString(), out decimal decResult))
                            //{
                            //    cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("0.00");
                            //}
                            //cell.CellStyle = cellStyle;
                            cell.SetCellValue(dt.Rows[i][j].ToString());
                        }
                    }


                    using (FileStream fs = System.IO.File.OpenWrite(strFileName))
                    {
                        workbook.Write(fs);//向打开的这个xls文件中写入数据


                        result = true;
                    }
                }

            }
            catch (Exception ex)
            {
                //return false;
            }
        }

        /// <summary>
        /// 将excel中的数据导入到DataTable中
        /// </summary>
        /// <param name="sheetName">excel工作薄sheet的名称</param>
        /// <param name="isFirstRowColumn">第一行是否是DataTable的列名</param>
        /// <returns>返回的DataTable</returns>
        public DataTable ExcelToDataTable(string fileName, bool isFirstRowColumn, string sheetName= "Sheet1")
        {
            ISheet sheet = null;
            DataTable data = new DataTable();
            int startRow = 0;

            try
            {
                IWorkbook workbook =null;
                var fs = new FileStream(fileName, FileMode.Open, FileAccess.Read);

                if (fileName.IndexOf(".xlsx") > 0) // 2007版本
                {
                    workbook = new XSSFWorkbook(fs);
                }
                else if (fileName.IndexOf(".xls") > 0) // 2003版本
                {
                    workbook = new HSSFWorkbook(fs);
                }
                else
                {
                    return null;
                }

                if (sheetName != null)
                {
                    sheet = workbook.GetSheet(sheetName);
                    if (sheet == null) //如果没有找到指定的sheetName对应的sheet，则尝试获取第一个sheet
                    {
                        sheet = workbook.GetSheetAt(0);
                    }
                }
                else
                {
                    sheet = workbook.GetSheetAt(0);
                }

                if (sheet != null)
                {
                    IRow firstRow = sheet.GetRow(0);
                    int cellCount = firstRow.LastCellNum; //一行最后一个cell的编号 即总的列数
                    if (isFirstRowColumn)
                    {
                        for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                        {
                            ICell cell = firstRow.GetCell(i);
                            if (cell != null)
                            {
                                string cellValue = cell.StringCellValue;
                                if (cellValue != null)
                                {
                                    DataColumn column = new DataColumn(cellValue);
                                    data.Columns.Add(column);
                                }
                            }
                        }
                        startRow = sheet.FirstRowNum + 1;
                    }
                    else
                    {
                        startRow = sheet.FirstRowNum;
                    }

                    //最后一列的标号
                    int rowCount = sheet.LastRowNum;
                    for (int i = startRow; i <= rowCount; ++i)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue; //没有数据的行默认是null
                        DataRow dataRow = data.NewRow();
                        for (int j = row.FirstCellNum; j < cellCount; ++j)
                        {
                            if (row.GetCell(j) != null) //同理，没有数据的单元格都默认是null
                            {
                                dataRow[j] = row.GetCell(j).ToString();
                            }
                        }
                        data.Rows.Add(dataRow);
                    }
                }
                return data;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception: " + ex.Message);
                return null;
            }

        }
    }
}
