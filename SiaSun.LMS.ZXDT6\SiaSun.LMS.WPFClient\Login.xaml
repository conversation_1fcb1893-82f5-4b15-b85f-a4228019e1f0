﻿<Window
    x:Class="SiaSun.LMS.WPFClient.Login"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="新松智能物流管理系统"
    MinWidth="450"
    MinHeight="160"
    MaxWidth="460"
    MaxHeight="280"
    d:DesignHeight="250"
    d:DesignWidth="460"
    AllowsTransparency="True"
    ShowInTaskbar="False"
    SizeToContent="WidthAndHeight"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">

    <Border
        Width="460"
        Height="250"
        BorderBrush="Gray"
        BorderThickness="1"
        CornerRadius="4">
        <Border.Background>
            <LinearGradientBrush StartPoint="0.032,0.132" EndPoint="1.117,1.196">
                <GradientStop Color="#FF404E53" />
                <GradientStop Offset="0" Color="#B44F8EB1" />
            </LinearGradientBrush>
        </Border.Background>
        <Border.Effect>
            <DropShadowEffect Direction="1" ShadowDepth="1" />
        </Border.Effect>

        <Grid Width="460" Height="250">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="50" />
                <!--<RowDefinition Height="*"></RowDefinition>-->
                <RowDefinition Height="Auto" />
                <!--<RowDefinition Height="25"></RowDefinition>-->
            </Grid.RowDefinitions>

            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Bottom"
                    FontSize="24"
                    Foreground="Gainsboro">
                    新松智能物流管理系统
                </TextBlock>
            </StackPanel>

            <Grid Grid.Row="2" Margin="0,5,5,0">
                <!--<Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>-->
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="1.5*" />
                </Grid.ColumnDefinitions>

                <Image
                    Margin="0,3,0,8"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Stretch"
                    Source="/@Images/a2.jpg"
                    Stretch="Fill" />

                <Grid
                    Grid.Column="1"
                    Width="260"
                    Height="165"
                    VerticalAlignment="Top">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*" />
                        <RowDefinition Height="1*" />
                        <RowDefinition Height="1*" />
                        <RowDefinition Height="1*" />
                    </Grid.RowDefinitions>
                    <StackPanel
                        Grid.Row="0"
                        Margin="15,0,0,5"
                        Orientation="Horizontal">
                        <Label
                            MinWidth="65"
                            Content="用户名："
                            FontSize="13"
                            FontWeight="Medium"
                            Foreground="#FFFBFBFB" />
                        <TextBox
                            Name="txtName"
                            Width="160"
                            Height="30"
                            FontSize="13"
                            TabIndex="0"
                            Text="" />
                    </StackPanel>

                    <StackPanel
                        Grid.Row="1"
                        Margin="15,0,0,5"
                        Orientation="Horizontal">
                        <Label
                            MinWidth="65"
                            Content="密   码："
                            FontSize="13"
                            FontWeight="Medium"
                            Foreground="#FFEFF1F5" />
                        <PasswordBox
                            Name="txtPassword"
                            Width="160"
                            Height="30"
                            FontSize="13"
                            KeyUp="txtPassword_KeyUp"
                            Password=""
                            TabIndex="1" />
                    </StackPanel>

                    <StackPanel
                        Grid.Row="2"
                        Margin="15,0,0,5"
                        Orientation="Horizontal">
                        <Label
                            MinWidth="65"
                            Content="语   言："
                            FontSize="13"
                            FontWeight="Medium"
                            Foreground="#FFEFF1F5" />
                        <ComboBox
                            Name="cmbLanguage"
                            Width="160"
                            Height="30"
                            FontSize="13"
                            IsEnabled="False"
                            SelectedIndex="0"
                            TabIndex="2">
                            <ComboBoxItem Content="默认" Tag="Default" />
                            <ComboBoxItem Content="English" Tag="en-US" />
                        </ComboBox>
                    </StackPanel>

                    <StackPanel
                        Grid.Row="3"
                        Margin="12,0,0,5"
                        Orientation="Horizontal">
                        <Button
                            Name="BtnOK"
                            Width="110"
                            Height="30"
                            Margin="0,0,10,0"
                            VerticalAlignment="Center"
                            Click="BtnOK_Click">
                            确认
                        </Button>
                        <Button
                            Name="BtnCancel"
                            Width="110"
                            Height="30"
                            VerticalAlignment="Center"
                            Click="BtnCancel_Click">
                            取消
                        </Button>
                    </StackPanel>
                </Grid>
            </Grid>

        </Grid>
    </Border>
</Window>
