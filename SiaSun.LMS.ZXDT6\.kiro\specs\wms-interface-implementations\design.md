# Design Document

## Overview

This design outlines the implementation of 10 WMS interface methods that will handle various warehouse management operations including unit synchronization, warehouse management, organizational structure, shelf management, and various receipt processing operations. Each implementation will follow the established pattern from GoodsInfoSync.cs while using the exact data structures defined in the interface documentation.

## Architecture

The implementation follows a consistent architectural pattern:

```
SiaSun.LMS.Implement/Interface/WMS/
├── GoodsInfoSync.cs (existing)
├── UnitInfoSync.cs (new)
├── WarehouseInfoSync.cs (new)
├── OrganizationalStructureInfoSync.cs (new)
├── ShelfSpaceSync.cs (new)
├── InboundReceiptSync.cs (new)
├── InventoryReversalReceiptSync.cs (new)
├── OutboundReceiptSync.cs (new)
├── GoodsIssueSync.cs (new)
├── GoodsIssueReversalReceiptSync.cs (new)
└── InventoryPlanSync.cs (new)
```

Each class inherits from `InterfaceBase` and implements the `IntefaceMethod(string inputJson)` method.

## Components and Interfaces

### Base Structure Pattern

Each implementation class follows this structure:

```csharp
public class [MethodName]Sync : InterfaceBase
{
    class InputParam
    {
        // Direct mapping from interface documentation
    }
    
    class OutputParam
    {
        public int code { get; set; }
        public string msg { get; set; }
        public string traceId { get; set; }
    }

    internal override string IntefaceMethod(string inputJson)
    {
        // Implementation logic
    }
}
```

### Interface Method Implementations

#### 1. UnitInfoSync (计量单位)
- **Purpose**: Synchronize measurement unit information
- **Key Fields**: measuringUnitStatus, measuringUnitCode, code, name, parentId
- **Database Operations**: Create/Update unit records

#### 2. WarehouseInfoSync (仓库)
- **Purpose**: Synchronize warehouse information with shelf relationships
- **Key Fields**: warehouseStatus, name, code, warehouseShelfRelList (nested array)
- **Database Operations**: Create/Update warehouse and shelf relationship records

#### 3. OrganizationalStructureInfoSync (组织架构)
- **Purpose**: Synchronize organizational hierarchy information
- **Key Fields**: orgLevel, orgCode, orgName, parentOrgCode, orgStatus
- **Database Operations**: Create/Update organizational structure records

#### 4. ShelfSpaceSync (货架位)
- **Purpose**: Synchronize shelf/storage location information
- **Key Fields**: shelfStatus, name, code, warehouseId, qrCode
- **Database Operations**: Create/Update shelf location records

#### 5. InboundReceiptSync (入库单)
- **Purpose**: Synchronize inbound receipt information with detailed items
- **Key Fields**: storageStatus, storageDate, warehouseId, storageInfoList (nested array)
- **Database Operations**: Create/Update inbound receipts and detail records

#### 6. InventoryReversalReceiptSync (入库红冲单)
- **Purpose**: Synchronize inventory reversal receipt information
- **Key Fields**: redStorageType, reason, warehouseId, redStorageDetailList (nested array)
- **Database Operations**: Create/Update reversal receipts and detail records

#### 7. OutboundReceiptSync (出库单)
- **Purpose**: Synchronize outbound receipt information
- **Key Fields**: outboundType, applyType, operatorId, outGoodsInfoList (nested array)
- **Database Operations**: Create/Update outbound receipts and detail records

#### 8. GoodsIssueSync (总库领料/材料间领料)
- **Purpose**: Synchronize goods issue/material requisition information
- **Key Fields**: applyType, applyStatus, applyUserId, applyGoodsInfoList (nested array)
- **Database Operations**: Create/Update goods issue requests and detail records

#### 9. GoodsIssueReversalReceiptSync (出库红冲单)
- **Purpose**: Synchronize goods issue reversal receipt information
- **Key Fields**: outGoodsDate, reason, operatorId, redOutDetailList (nested array)
- **Database Operations**: Create/Update goods issue reversal records

#### 10. InventoryPlanSync (盘点计划表)
- **Purpose**: Synchronize inventory planning information
- **Key Fields**: stockTakeName, stockTakeType, stockTakeWarehouseList, stockTakeGoodsList (nested arrays)
- **Database Operations**: Create/Update inventory plans with warehouse and goods details

## Data Models

### Response Model
All implementations return a standardized response:

```csharp
class OutputParam
{
    public int code { get; set; }        // 0=success, 1=failure, 2=input error
    public string msg { get; set; }      // Response message
    public string traceId { get; set; }  // Trace ID for troubleshooting
}
```

### Input Data Mapping Strategy

1. **Direct Field Mapping**: Map JSON fields directly to C# properties using exact names from documentation
2. **Nested Array Handling**: Process nested arrays (like storageInfoList, warehouseShelfRelList) using List<T> collections
3. **Data Type Conversion**: Handle proper conversion between JSON types and C# types (string, int, decimal, DateTime)

## Error Handling

### Error Response Strategy

1. **JSON Parsing Errors**: Return code=2 with descriptive message
2. **Required Field Validation**: Return code=2 with field-specific error message
3. **Database Operation Errors**: Return code=1 with operation-specific error message
4. **General Exceptions**: Return code=1 with exception message

### Validation Rules

1. **Required Field Validation**: Check for null/empty values on mandatory fields
2. **Data Format Validation**: Validate date formats, numeric ranges, enum values
3. **Referential Integrity**: Validate foreign key relationships where applicable

## Testing Strategy

### Unit Testing Approach

1. **JSON Deserialization Tests**: Verify proper parsing of complex nested structures
2. **Validation Logic Tests**: Test required field validation and error responses
3. **Database Operation Tests**: Mock database operations to test create/update logic
4. **Error Handling Tests**: Verify proper error response formatting

### Integration Testing

1. **End-to-End Flow Tests**: Test complete request-response cycle
2. **Database Integration Tests**: Verify actual database operations
3. **Performance Tests**: Ensure acceptable response times for large data sets

### Test Data Strategy

1. Use sample JSON data from interface documentation
2. Create test cases for both valid and invalid input scenarios
3. Test nested array processing with various array sizes
4. Validate error scenarios and edge cases

## Implementation Considerations

### Performance Optimizations

1. **Batch Processing**: For nested arrays, consider batch database operations
2. **Connection Management**: Reuse database connections efficiently
3. **Memory Management**: Handle large JSON payloads appropriately

### Maintainability

1. **Code Consistency**: Follow established patterns from GoodsInfoSync.cs
2. **Documentation**: Include XML comments for all public methods
3. **Logging**: Implement consistent logging for troubleshooting

### Extensibility

1. **Modular Design**: Each implementation is independent and can be modified separately
2. **Configuration**: Support for configurable validation rules
3. **Versioning**: Design to support future API version changes