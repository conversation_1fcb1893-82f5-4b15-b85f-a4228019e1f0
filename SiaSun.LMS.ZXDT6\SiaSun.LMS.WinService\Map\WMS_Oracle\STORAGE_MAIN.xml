﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="STORAGE_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="STORAGE_MAIN" type="SiaSun.LMS.Model.STORAGE_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="STORAGE_MAIN">
			<result property="STORAGE_ID" column="storage_id" />
      <result property="GOODS_TEMPLATE_ID" column="goods_template_id" />
			<result property="CELL_ID" column="cell_id" />
			<result property="STOCK_BARCODE" column="stock_barcode" />
			<result property="CELL_MODEL" column="cell_model" />
			<result property="FULL_FLAG" column="full_flag" />
			<result property="STORAGE_REMARK" column="storage_remark" />
      <result property="IS_EXCEPTION" column="is_exception" />
      <result property="KITBOX_UP_COMPLETE" column="kitbox_up_complete" />
      <result property="STOCK_WEIGHT" column="stock_weight" />
      <result property="SPLIT_SOURCE_CELL_ID" column="split_source_cell_id" />
    </resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="STORAGE_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        storage_id,
        goods_template_id,
        cell_id,
        stock_barcode,
        cell_model,
        full_flag,
        storage_remark,
        is_exception,
        kitbox_up_complete,
        stock_weight,
        split_source_cell_id
        From STORAGE_MAIN
      </select>
		
		<select id="STORAGE_MAIN_SELECT_BY_ID" parameterClass="int" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">		
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_id=#STORAGE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="STORAGE_MAIN_SELECT_BY_STOCK_BARCODE" parameterClass="int" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          stock_barcode=#STOCK_BARCODE#
        </isParameterPresent>
      </dynamic>
    </select>


    <select id="STORAGE_MAIN_SELECT_BY_CELL_ID" parameterClass="int" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_id=#CELL_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_MAIN_SELECT_BY_TEMPLATE_ID" parameterClass="int" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_template_id=#GOODS_TEMPLATE_ID# order by
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_MAIN_SELECT_BY_CELL_ID_STOCK_BARCODE" parameterClass="Hashtable" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          CELL_ID=#CELL_ID#
          <isNotNull property="STOCK_BARCODE">
            AND STOCK_BARCODE = #STOCK_BARCODE#
          </isNotNull>
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_MAIN_SELECT_BY_PICK_STATION_ID_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_id
          in (
                select storage_id from storage_list
                where storage_list_id
                in (
                      select storage_list_id from storage_lock where plan_list_id
                      in (
                            select plan_list_id from plan_list where plan_id
                            in (
                                  select plan_id from t_pick_position_plan_bind where pick_station_id=#PICK_STATION_ID#
                                )
                          ) and storage_lock_flag=#STORAGE_LOCK_FLAG#
                    )
               )
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="STORAGE_MAIN_SELECT_BY_PICK_POSITION_ID_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_id
          in (
                select storage_id from storage_list
                where storage_list_id
                in (
                      select storage_list_id from storage_lock where plan_list_id
                      in (
                            select plan_list_id from plan_list where plan_id
                            in (
                                  select plan_id from t_pick_position_plan_bind where pick_position_id=#PICK_POSITION_ID#
                                )
                          ) and storage_lock_flag=#STORAGE_LOCK_FLAG#
                    )
               )
        </isParameterPresent>
      </dynamic>
    </select>
    <insert id="STORAGE_MAIN_INSERT" parameterClass="STORAGE_MAIN">
      Insert Into STORAGE_MAIN (
      storage_id,
      goods_template_id,
      cell_id,
      stock_barcode,
      cell_model,
      full_flag,
      storage_remark,
      is_exception,
      kitbox_up_complete,
      stock_weight,
      split_source_cell_id
      )Values(
      #STORAGE_ID#,
      #GOODS_TEMPLATE_ID#,
      #CELL_ID#,
      #STOCK_BARCODE#,
      #CELL_MODEL#,
      #FULL_FLAG#,
      #STORAGE_REMARK#,
      #IS_EXCEPTION#,
      #KITBOX_UP_COMPLETE#,
      #STOCK_WEIGHT#,
      #SPLIT_SOURCE_CELL_ID#
      )
      <!--<selectKey  resultClass="int" type="post" property="STORAGE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="STORAGE_MAIN_UPDATE" parameterClass="STORAGE_MAIN">
      Update STORAGE_MAIN Set
      <!--storage_id=#STORAGE_ID#,-->
      goods_template_id=#GOODS_TEMPLATE_ID#,
      cell_id=#CELL_ID#,
      stock_barcode=#STOCK_BARCODE#,
      cell_model=#CELL_MODEL#,
      full_flag=#FULL_FLAG#,
      storage_remark=#STORAGE_REMARK#,
      is_exception=#IS_EXCEPTION#,
      kitbox_up_complete=#KITBOX_UP_COMPLETE#,
      stock_weight=#STOCK_WEIGHT#,
      split_source_cell_id=#SPLIT_SOURCE_CELL_ID#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					storage_id=#STORAGE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="STORAGE_MAIN_DELETE" parameterClass="int">
			Delete From STORAGE_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_id=#STORAGE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>