using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement.Interface.WMS;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace SiaSun.LMS.Tests.WMS
{
    [TestClass]
    public class WarehouseInfoSyncTests
    {
        private WarehouseInfoSync _warehouseInfoSync;

        [TestInitialize]
        public void Setup()
        {
            _warehouseInfoSync = new WarehouseInfoSync();
        }

        [TestMethod]
        public void IntefaceMethod_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var validInput = new
            {
                isHaveMaterials = 1,
                contactId = "CONTACT001",
                contactName = "联系人",
                gsId = "GS001",
                gsName = "公司名称",
                bmId = "BM001",
                bmName = "部门名称",
                teamName = "团队名称",
                warehouseStatus = "ENABLED",
                remark = "备注",
                warehouseLevel = "1",
                describe = "描述",
                isDeliveryAddress = "1",
                isVirtualWarehouse = "0",
                physicalAddress = "物理地址",
                stationPlace = "站点位置",
                roomPlace = "房间位置",
                floor = "楼层",
                teamId = "TEAM001",
                warehouseArea = 1000.50m,
                warehouseOutRoomArea = 200.25m,
                trainStation = "火车站",
                belongingPlace = "归属地",
                warehouseRoomArea = 800.25m,
                phone = "13800138000",
                addressType = "地址类型",
                warehouseChargeId = "CHARGE001",
                warehouseChargeName = "负责人",
                warehouseAddress = "仓库地址",
                warehouseType = "仓库类型",
                lineId = "LINE001",
                lineName = "线路名称",
                mangeUserId = "MANAGE001",
                name = "测试仓库",
                code = "WH001",
                warehouseShelfRelList = new[]
                {
                    new
                    {
                        shelfCode = "SHELF001",
                        warehouseId = "WH001",
                        qrCode = "QR001",
                        shelfStatus = 1,
                        describe = "货架描述",
                        shelfName = "货架1",
                        shelfId = "SHELF001"
                    }
                },
                createDate = "2025-01-08 10:00:00",
                createUser = "USER001",
                createName = "创建人",
                updateDate = "2025-01-08 10:00:00",
                updateUser = "USER001",
                updateName = "更新人",
                id = "ID001",
                status = 1,
                billCode = "BILL001"
            };

            string inputJson = JsonSerializer.Serialize(validInput);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Length > 0);
            Assert.IsTrue(response.GetProperty("traceId").GetString().Length > 0);
        }

        [TestMethod]
        public void IntefaceMethod_InvalidWarehouseStatus_ReturnsError()
        {
            // Arrange
            var inputWithInvalidStatus = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "INVALID_STATUS"
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidStatus);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("warehouseStatus"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingShelfCode_ReturnsError()
        {
            // Arrange
            var inputWithMissingShelfCode = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "ENABLED",
                warehouseShelfRelList = new[]
                {
                    new
                    {
                        // shelfCode is missing
                        warehouseId = "WH001",
                        shelfName = "货架1"
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingShelfCode);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("shelfCode"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingShelfName_ReturnsError()
        {
            // Arrange
            var inputWithMissingShelfName = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "ENABLED",
                warehouseShelfRelList = new[]
                {
                    new
                    {
                        shelfCode = "SHELF001",
                        warehouseId = "WH001"
                        // shelfName is missing
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingShelfName);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("shelfName"));
        }

        [TestMethod]
        public void IntefaceMethod_EmptyShelfList_ReturnsSuccess()
        {
            // Arrange
            var inputWithEmptyShelfList = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "ENABLED",
                warehouseShelfRelList = new object[0]
            };

            string inputJson = JsonSerializer.Serialize(inputWithEmptyShelfList);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_NullShelfList_ReturnsSuccess()
        {
            // Arrange
            var inputWithNullShelfList = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "ENABLED",
                warehouseShelfRelList = (object[])null
            };

            string inputJson = JsonSerializer.Serialize(inputWithNullShelfList);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_MultipleShelfItems_ReturnsSuccess()
        {
            // Arrange
            var inputWithMultipleShelves = new
            {
                name = "测试仓库",
                code = "WH001",
                warehouseStatus = "ENABLED",
                warehouseShelfRelList = new[]
                {
                    new
                    {
                        shelfCode = "SHELF001",
                        warehouseId = "WH001",
                        qrCode = "QR001",
                        shelfStatus = 1,
                        describe = "货架1描述",
                        shelfName = "货架1",
                        shelfId = "SHELF001"
                    },
                    new
                    {
                        shelfCode = "SHELF002",
                        warehouseId = "WH001",
                        qrCode = "QR002",
                        shelfStatus = 1,
                        describe = "货架2描述",
                        shelfName = "货架2",
                        shelfId = "SHELF002"
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithMultipleShelves);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Code_ReturnsError()
        {
            // Arrange
            var inputWithMissingCode = new
            {
                name = "测试仓库"
                // code is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingCode);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("code"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Name_ReturnsError()
        {
            // Arrange
            var inputWithMissingName = new
            {
                code = "WH001"
                // name is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingName);

            // Act
            string result = _warehouseInfoSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("name"));
        }
    }
}