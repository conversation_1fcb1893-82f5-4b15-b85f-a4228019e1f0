# SiaSun LMS HTTP API Demo

这是一个演示如何为现有WCF服务外挂HTTP API的示例项目。

## 项目结构

```
SiaSun.LMS.HttpApiDemo/
├── Controllers/
│   └── DemoController.cs      # HTTP API控制器
├── WcfProxy.cs               # WCF服务代理类
├── Startup.cs                # OWIN启动配置
├── Program.cs                # 控制台启动程序
├── App.config                # 配置文件
├── test.http                 # HTTP测试用例
└── README.md                 # 说明文档
```

## 功能特性

- ✅ HTTP API外挂服务，不影响现有WCF服务
- ✅ 支持JSON格式的请求和响应
- ✅ 完整的错误处理和日志记录
- ✅ CORS跨域支持
- ✅ 健康检查接口
- ✅ 可配置的服务地址和端口

## 快速开始

### 1. 编译项目

```bash
cd SiaSun.LMS.HttpApiDemo
dotnet build
```

### 2. 运行服务

```bash
dotnet run
```

或者直接运行编译后的exe文件：

```bash
.\bin\Debug\net472\SiaSun.LMS.HttpApiDemo.exe
```

### 3. 测试接口

服务启动后，可以通过以下方式测试：

#### 使用curl命令

```bash
# 检查服务状态
curl http://localhost:9001/api/demo/status

# 健康检查
curl http://localhost:9001/api/demo/health

# 载具请求
curl -X POST http://localhost:9001/api/demo/carrier \
  -H "Content-Type: application/json" \
  -d '{"carrierId":"C001","action":"OUT","position":"A-01-01"}'

# 任务请求
curl -X POST http://localhost:9001/api/demo/task \
  -H "Content-Type: application/json" \
  -d '{"taskId":"T001","type":"PICK","items":["item1","item2"]}'
```

#### 使用test.http文件

在VS Code中安装REST Client插件，然后打开`test.http`文件，点击"Send Request"按钮即可测试。

## API接口说明

### 1. 获取服务状态

```
GET /api/demo/status
```

响应示例：
```json
{
  "status": "running",
  "wcfUrl": "http://127.0.0.1:8001/Service/Demo",
  "httpApiUrl": "http://127.0.0.1:9001",
  "timestamp": "2024-01-15T10:30:00",
  "version": "1.0.0"
}
```

### 2. 健康检查

```
GET /api/demo/health
```

### 3. 载具请求

```
POST /api/demo/carrier
Content-Type: application/json

{
  "carrierId": "C001",
  "action": "OUT",
  "position": "A-01-01"
}
```

### 4. 任务请求

```
POST /api/demo/task
Content-Type: application/json

{
  "taskId": "T001",
  "type": "PICK",
  "items": ["item1", "item2"]
}
```

## 配置说明

在`App.config`文件中可以配置：

- `HttpApiPort`: HTTP API服务端口（默认9001）
- `WcfServiceUrl`: WCF服务地址
- `LogLevel`: 日志级别
- `LogPath`: 日志文件路径

## 集成到现有项目

要将此HTTP API集成到现有的WinService项目中：

1. 在WinService项目中添加对此项目的引用
2. 在WMSService.cs的OnStart方法中启动HTTP API
3. 在OnStop方法中停止HTTP API

示例代码见下一节。

## 部署说明

1. 确保目标服务器已安装.NET Framework 4.7.2
2. 复制编译后的文件到目标目录
3. 修改App.config中的配置
4. 以管理员权限运行服务（如果使用80端口）

## 注意事项

- 此demo使用模拟数据，实际使用时需要替换WcfProxy中的模拟调用
- 确保防火墙允许配置的端口访问
- 生产环境建议使用HTTPS
- 建议添加身份验证和授权机制
