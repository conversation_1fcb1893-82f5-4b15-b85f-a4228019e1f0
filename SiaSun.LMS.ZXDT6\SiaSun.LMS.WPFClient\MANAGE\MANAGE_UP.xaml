﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_UP"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_UP" Height="470" Width="600" Loaded="DocumentContent_Loaded">
        <GroupBox Grid.Column="2" Header="库存信息" >
            <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>
                <uc:ucSplitPropertyGridTab Grid.Row="1" x:Name="gridStorageList" ></uc:ucSplitPropertyGridTab>
                <WrapPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="3" ButtonBase.Click="WrapPanel_Click">
                    <uc:ucManagePosition x:Name="ucManagePosition" Grid.Row="0" U_AllowShowCellModel="False"/>
                    <!--<CheckBox Name="cbxReDownloadHkTask" Width="150" VerticalAlignment="Center"  Content="重新与杭可交互" IsChecked="True"/>-->
                    <CheckBox Name="cbxCheckCellStatus" Width="150" VerticalAlignment="Center"  Content="检查起止货位状态" IsChecked="True" Visibility="Collapsed" Checked="cbxCheckCellStatus_CheckChanged" Unchecked="cbxCheckCellStatus_CheckChanged"/>
                    <CheckBox Name="cbxDownloadControl" Width="150" VerticalAlignment="Center"  Content="下达控制任务" IsChecked="True" Visibility="Collapsed"/>
                    <Button Name="btnConfirm"  Width="60" Margin="5 5 20 5">下达任务</Button>
                    <Button Name="btnRefresh"  Width="60">刷新</Button>
                </WrapPanel>             
            </Grid>
        </GroupBox>

</ad:DocumentContent>
