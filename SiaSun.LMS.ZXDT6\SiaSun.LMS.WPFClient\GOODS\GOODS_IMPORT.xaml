﻿<ad:DocumentContent  
      xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock" 
      x:Class="SiaSun.LMS.WPFClient.GOODS.GOODS_IMPORT"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.GOODS"
      xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
      Height="215" Width="475"
      Title="GOODS_IMPORT">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <GroupBox Grid.Row="0" Header="导入计划信息" Tag="">
            <uc:DataGridTemplate x:Name="gridImport" AutoGenerateColumns="True" Grid.Row="0" Margin="1,1,1,0" IsReadOnly="True"></uc:DataGridTemplate>
        </GroupBox>
        <GroupBox Grid.Row="1" Header="操作区">
            <Border>
                <WrapPanel HorizontalAlignment="Center" Margin="3" ButtonBase.Click="WrapPanel_Click">
                    <Button x:Name="btnOpen" Width="80" Margin="5">打开Excel</Button>
                    <Button x:Name="btnSave" Width="80" Margin="5">保存</Button>
                    <Button x:Name="btnRefresh" Width="90" Margin="5">刷新</Button>
                </WrapPanel>
            </Border>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
