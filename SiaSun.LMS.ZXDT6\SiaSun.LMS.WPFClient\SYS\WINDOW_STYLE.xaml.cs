﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Xml;
using System.Data;
using System.Reflection;
using AvalonDock;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// WINDOW_STYLE.xaml 的交互逻辑
    /// </summary>
    public partial class WINDOW_STYLE : DocumentContent
    {
        XmlDocument xmlDoc = null;
        XmlNode nodeRoot = null;
        DataTable tableDescription = null;

        public WINDOW_STYLE()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //加载XML文档
            LoadXml();
            //加载所有窗体样式
            LoadFormStyles();

            //初始化表结构
            InitDataTable();
        }

        //加载XML文档
        private void LoadXml()
        {
            try
            {
                xmlDoc = new XmlDocument();
                xmlDoc.Load(MainApp.File_FormStyles_Path);

                //判断文档是否加载
                if (xmlDoc != null)
                {
                    //判断根节点是否存在
                    nodeRoot = xmlDoc.SelectSingleNode("Styles");
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //加载所有存在的样式
        private void LoadFormStyles()
        {
            this.tvwWinStyles.Items.Clear();
            foreach (XmlNode nodeXml in nodeRoot.ChildNodes)
            {
                try
                {
                    if (nodeRoot.ChildNodes == null)
                        continue;
                    if (nodeXml.NodeType == XmlNodeType.Comment)
                        continue;

                    //添加列表
                    TreeViewItem itemWindow = new TreeViewItem();
                    itemWindow.Header = nodeXml.Attributes[0].InnerText;
                    itemWindow.Tag = itemWindow.Header;
                    this.tvwWinStyles.Items.Add(itemWindow);

                    //判断表节点是否存在
                    foreach (XmlNode nodeTable in nodeXml.SelectNodes("Table"))
                    {
                        TreeViewItem item = new TreeViewItem();
                        item.Header = nodeTable.Attributes[0].Value;
                        item.Tag = item.Header;
                        itemWindow.Items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        //初始化DataTable
        private DataTable InitDataTable()
        {
            //从实体类中加载属性信息
            Assembly assemblyModel = Assembly.LoadFrom(MainApp._APP_PATH + "SiaSun.LMS.Model.dll");
            if (assemblyModel == null)
                return null;
            Module moduleModel = assemblyModel.ManifestModule;
            Type typeModel = moduleModel.GetType(assemblyModel.GetName().Name + ".FIELD_DESCRIPTION");
            if (typeModel == null)
                return null;

            //声明datatable并添加列
            tableDescription = new DataTable("FIELD_DESCRIPTION");
            SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION = new SiaSun.LMS.Model.FIELD_DESCRIPTION();
            foreach (PropertyInfo propertyInfo in typeModel.GetProperties())
            {
                DataColumn column = new DataColumn(propertyInfo.Name, propertyInfo.PropertyType);
                column.DefaultValue = propertyInfo.GetValue(mFIELD_DESCRIPTION, null).ToString();
                tableDescription.Columns.Add(column);
            }
            return tableDescription;
        }

        //删除样式定义
        private void menuItemDelete_Click(object sender, RoutedEventArgs e)
        {
            if (this.tvwWinStyles.SelectedItem == null)
                return;

            if (MainApp._MessageDialog.ShowDialog("Delete",null) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                TreeViewItem itemSelected = this.tvwWinStyles.SelectedItem as TreeViewItem;
                TreeViewItem itemParent = itemSelected.Parent as TreeViewItem;

                try
                {
                    if (itemParent == null)
                    {
                        string strFormName = itemSelected.Header.ToString();
                        XmlNode nodeForm = nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", strFormName));
                        if (nodeForm != null)
                        {
                            nodeRoot.RemoveChild(nodeForm);
                        }

                        //移除节点
                        this.tvwWinStyles.Items.Remove(itemSelected);
                        this.gridStyles.ItemsSource = null;
                    }
                    else
                    {
                        string strFormName = itemParent.Header.ToString();
                        string strTableName = itemSelected.Header.ToString();

                        XmlNode nodeForm = nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", strFormName));
                        if (nodeForm != null)
                        {
                            XmlNode nodeTable = nodeForm.SelectSingleNode(string.Format("Table[@Name='{0}']", strTableName));
                            if (nodeForm != null)
                            {
                                nodeForm.RemoveChild(nodeTable);

                                //移除节点
                                itemParent.Items.Remove(itemSelected);
                                this.gridStyles.ItemsSource = null;
                            }
                        }
                    }

                    //保存并重新加载
                    xmlDoc.Save(MainApp.File_FormStyles_Path);
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        #region     ------点击节点显示窗体样式配置

        /// <summary>
        /// 选择节点显示该节点信息
        /// </summary>
        private void tvwWinStyles_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            //数据源设置空
            this.gridStyles.ItemsSource = null;

            TreeViewItem itemSelected = this.tvwWinStyles.SelectedItem as TreeViewItem;
            if (itemSelected != null)
            {
                TreeViewItem itemParent = itemSelected.Parent as TreeViewItem;
                //判断是否最小节点
                if (itemParent != null)
                {
                    //显示配置项
                    grpBoxStyle.Header = string.Format("{0}-配置项", itemSelected.Header);

                    //加载数据
                    LoadData(itemParent.Header.ToString(), itemSelected.Header.ToString());
                }
            }
        }

        //点击节点，加载并显示数据
        private void LoadData(string FormName, string TableName)
        {
            try
            {
                //获得窗体节点
                XmlNode nodeFormTable = nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", FormName));
                if (nodeFormTable != null)
                {
                    //获得表单节点
                    XmlNode nodeTable = nodeFormTable.SelectSingleNode(string.Format("Table[@Name='{0}']", TableName));
                    if (nodeTable != null)
                    {
                        LoadDataTableFromXml(nodeTable, TableName);
                    }
                }
            }
            catch (Exception ex)
            {
                 MainApp._MessageDialog.ShowException(ex);
            }

            //绑定并显示数据
            DataGrid_Bind();
        }

        //从XML中加载数据
        private void LoadDataTableFromXml(XmlNode nodeModel, string TableName)
        {
            //从XML中加载描述定义
            tableDescription.Rows.Clear();

            foreach (XmlNode nodeFile in nodeModel.SelectNodes("Field"))
            {
                try
                {
                    DataRow rowField = tableDescription.NewRow();
                    rowField["Column"] = nodeFile.Attributes["Column"] == null ? string.Empty : nodeFile.Attributes["Column"].Value;
                    rowField["DbType"] = nodeFile.Attributes["DbType"] == null ? string.Empty : nodeFile.Attributes["DbType"].Value;
                    rowField["Header"] = nodeFile.Attributes["Header"] == null ? string.Empty : nodeFile.Attributes["Header"].Value;
                    rowField["ControlType"] = nodeFile.Attributes["ControlType"] == null ? string.Empty : nodeFile.Attributes["ControlType"].Value;
                    rowField["DefaultValue"] = nodeFile.Attributes["DefaultValue"] == null ? string.Empty : nodeFile.Attributes["DefaultValue"].Value;
                    rowField["ReadOnly"] = (nodeFile.Attributes["ReadOnly"] == null || string.IsNullOrEmpty(nodeFile.Attributes["ReadOnly"].Value)) ? "0" : nodeFile.Attributes["ReadOnly"].Value;
                    rowField["Validation"] = nodeFile.Attributes["Validation"] == null ? string.Empty : nodeFile.Attributes["Validation"].Value;
                    rowField["DataBind"] = nodeFile.Attributes["DataBind"] == null ? string.Empty : nodeFile.Attributes["DataBind"].Value;
                    rowField["Remark"] = nodeFile.Attributes["Remark"] == null ? string.Empty : nodeFile.Attributes["Remark"].Value;
                    rowField["Order"] = nodeFile.Attributes["Order"] == null ? "1" : nodeFile.Attributes["Order"].Value;
                    rowField["AllowQuery"] = (nodeFile.Attributes["AllowQuery"] == null || string.IsNullOrEmpty(nodeFile.Attributes["AllowQuery"].Value)) ? "0" : nodeFile.Attributes["AllowQuery"].Value;
                    rowField["QueryOperation"] = nodeFile.Attributes["QueryOperation"] == null ? string.Empty : nodeFile.Attributes["QueryOperation"].Value;
                    tableDescription.Rows.Add(rowField);
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }


        //绑定数据并设置显示列信息
        private void DataGrid_Bind()
        {
            //设置数据源
            gridStyles.ItemsSource = tableDescription.DefaultView;
            //设置样式
            gridStyles.U_TranslateDataGridViewStyle(null, "FIELD_DESCRIPTION", null, true);
        }


        #endregion

        /// <summary>
        /// 添加样式名称
        /// </summary>
        private void btnWinName_Click(object sender, RoutedEventArgs e)
        {
            if (txtWinName.Text.TrimEnd().Length > 0)
            {
                string strFormName = txtWinName.Text;

                //检查该节点的名称是否存在
                if (nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", strFormName)) != null)
                {
                    MainApp._MessageDialog.ShowException(string.Format("名称为{0}的节点已经存在！", strFormName));
                    return;
                }

                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    XmlNode nodeForm = xmlDoc.CreateNode(XmlNodeType.Element, "Form", xmlDoc.NamespaceURI);
                    nodeRoot.AppendChild(nodeForm);
                    nodeForm.Attributes.Append(xmlDoc.CreateAttribute("Name")).Value = strFormName;
                    xmlDoc.Save(MainApp.File_FormStyles_Path);

                    //添加新节点
                    TreeViewItem itemNew = new TreeViewItem();
                    itemNew.Header = strFormName;
                    itemNew.Tag = strFormName;
                    this.tvwWinStyles.Items.Add(itemNew);

                    //选中节点
                    itemNew.IsSelected = true;

                    //重新加载
                    LoadXml();
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }

        //刷性重新加载列表
        private void btnRefresh_Click(object sender, RoutedEventArgs e)
        {
            MainWindow.mainWin.Cursor = Cursors.Wait;

            //加载XML文档
            LoadXml();
            //加载所有窗体样式
            LoadFormStyles();

            MainWindow.mainWin.Cursor = Cursors.Arrow;
        }

        /// <summary>
        /// 加载样式
        /// </summary>
        private void btnLoadStyle_Click(object sender, RoutedEventArgs e)
        {
            TreeViewItem itemSelected = this.tvwWinStyles.SelectedItem as TreeViewItem;
            if (itemSelected != null)
            {
                if (txtStyleName.Text.TrimEnd().Length == 0 || this.tvwWinStyles.SelectedItem == null )
                {
                    return;
                }

                string strFormName = itemSelected.Header.ToString();
                string strXmlNodeName = txtStyleName.Text;

                //FieldDescription中加载
                SiaSun.LMS.Common.XmlFiles xmlFile = new SiaSun.LMS.Common.XmlFiles(MainApp.File_FieldDescription_Path);

                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    //获得节点列表,查找属性匹配的节点
                    XmlNodeList listNode = xmlFile.SelectNodes("Tables/Table");

                    //判断是否存在节点
                    if (listNode.Cast<XmlNode>().Count(n => n.Attributes[0].Value == strXmlNodeName) == 0)
                    {
                        MainApp._MessageDialog.ShowException(string.Format("请检查样式文件中{0}的节点是否存在！", strXmlNodeName));
                        return;
                    }
                    else
                    {
                        //获得节点
                        XmlNode nodeTable = listNode.Cast<XmlNode>().Single(n => n.Attributes[0].Value == strXmlNodeName);

                        //加载节点数据
                        LoadDataTableFromXml(nodeTable, strXmlNodeName);

                        //显示数据信息
                        DataGrid_Bind();
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }

        /// <summary>
        /// 工具栏按钮
        /// </summary>
        private void ToolBar_Click(object sender, RoutedEventArgs e)
        {
            Button btnObj = e.Source as Button;
            if (btnObj != null)
            {
                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    //添加按钮
                    if (btnObj == btnAdd)
                    {
                        if (tableDescription != null)
                        {
                            DataRow rowNew = tableDescription.NewRow();
                            tableDescription.Rows.Add(rowNew);
                        }
                    }

                    //删除按钮
                    if (btnObj == btnDelete)
                    {
                        if (gridStyles.CurrentItem == null)
                            return;
                        DataGridRow rowGridCurrent = gridStyles.CurrentItem as DataGridRow;
                        if (rowGridCurrent != null)
                        {
                            gridStyles.Items.Remove(rowGridCurrent);
                        }

                        //for (int i = gridModel.SelectedItems.Count - 1; i >= 0; i--)
                        //{
                        //    DataRowView viewRow = gridModel.SelectedItems[i] as DataRowView;
                        //    if (viewRow != null)
                        //    {
                        //        viewRow.Delete();
                        //    }
                        //}

                    }

                    //保存按钮
                    if (btnObj == btnSave)
                    {
                        this.Save();
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }

        /// <summary>
        /// 保存操作
        /// </summary>
        private void Save()
        {
            TreeViewItem itemSelect = this.tvwWinStyles.SelectedItem as TreeViewItem;
            if (itemSelect == null)
                return;

            if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Save) == Sid.Windows.Controls.TaskDialogResult.Cancel)
                return;


            //结束编辑
            gridStyles.CommitEdit(DataGridEditingUnit.Row, true);

            string strFormName = string.Empty;
            string strTableName = string.Empty;

            //获得上级节点
            TreeViewItem itemParent = itemSelect.Parent as TreeViewItem;

            //判断选择的节点
            if (itemParent == null)
            {
                if (this.txtStyleName.Text.Length == 0)
                    return;

                strFormName = itemSelect.Header.ToString();
                strTableName = this.txtStyleName.Text;

                try
                {
                    //判断表单的节点是否存在
                    if(this.tvwWinStyles.Items.Cast<TreeViewItem>().Count(r=>r.Header.ToString() == strTableName) == 0)
                    {
                        //XML中添加节点
                        XmlNode nodeForm = nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", strFormName));
                        if (nodeForm != null)
                        {
                            //获得表名节点
                            XmlNode nodeTable = nodeForm.SelectSingleNode(string.Format("Table[@Name='{0}']", strTableName));
                            if (nodeTable == null)
                            {
                                //添加该表名的节点
                                XmlNode node = xmlDoc.CreateNode(XmlNodeType.Element, "Table", xmlDoc.NamespaceURI);
                                node.Attributes.Append(xmlDoc.CreateAttribute("Name")).Value = strTableName;
                                nodeForm.AppendChild(node);

                                //保存节点
                                SaveStyles(node);

                                //添加节点
                                TreeViewItem itemNew = new TreeViewItem();
                                itemNew.Header = strTableName;
                                itemNew.Tag = strTableName;
                                itemSelect.Items.Add(itemNew);

                                //选中新节点
                                itemNew.IsSelected = true;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(string.Format("节点{0}-{1}.}", strFormName, strTableName) + ex.Message);
                }
            }
            else
            {
                strFormName = (itemSelect.Parent as TreeViewItem).Header.ToString();
                strTableName = itemSelect.Header.ToString();

                try
                {
                    //XML中添加节点
                    XmlNode nodeForm = nodeRoot.SelectSingleNode(string.Format("Form[@Name='{0}']", strFormName));
                    if (nodeForm != null)
                    {
                        //获得表名节点
                        XmlNode nodeTable = nodeForm.SelectSingleNode(string.Format("Table[@Name='{0}']", strTableName));
                        if (nodeTable != null)
                        {
                            //清除节点信息
                            nodeTable.RemoveAll();

                            //设置属性
                            nodeTable.Attributes.Append(xmlDoc.CreateAttribute("Name")).Value = strTableName;

                            //保存节点
                            SaveStyles(nodeTable);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(string.Format("节点{0}-{1}.}", strFormName, strTableName) + ex.Message);
                }
            }

            //重新加载
            this.LoadXml();
        }

        /// <summary>
        /// 保存样式
        /// </summary>
        private void SaveStyles(XmlNode nodeTable)
        {
            foreach (DataRow rowField in tableDescription.Rows)
            {
                XmlNode nodeField = xmlDoc.CreateNode(XmlNodeType.Element, "Field", xmlDoc.NamespaceURI);
                nodeTable.AppendChild(nodeField);

                nodeField.Attributes.Append(xmlDoc.CreateAttribute("Column")).Value = rowField["Column"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("DbType")).Value = rowField["DbType"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("Header")).Value = rowField["Header"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("ControlType")).Value = rowField["ControlType"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("DefaultValue")).Value = rowField["DefaultValue"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("ReadOnly")).Value = string.IsNullOrEmpty(rowField["ReadOnly"].ToString()) ? "0" : rowField["ReadOnly"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("Validation")).Value = rowField["Validation"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("DataBind")).Value = rowField["DataBind"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("Remark")).Value = rowField["Remark"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("Order")).Value = rowField["Order"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("AllowQuery")).Value = string.IsNullOrEmpty(rowField["AllowQuery"].ToString()) ? "0" : rowField["AllowQuery"].ToString();
                nodeField.Attributes.Append(xmlDoc.CreateAttribute("QueryOperation")).Value = rowField["QueryOperation"].ToString();
            }
            xmlDoc.Save(MainApp.File_FormStyles_Path);
        }


    }
}
