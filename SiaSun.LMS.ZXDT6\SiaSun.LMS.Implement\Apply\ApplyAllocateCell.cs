﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 四楼入库分配货位申请
    /// </summary>
    public class ApplyAllocateCell : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.WH_CELL applyCell = this._P_WH_CELL.GetModelByDeviceCode(mIO_CONTROL_APPLY.DEVICE_CODE);
                if (applyCell == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到申请站台_站台号[{0}]",mIO_CONTROL_APPLY.DEVICE_CODE);
                    return bResult;
                }

                string[] arBoxBarcode = mIO_CONTROL_APPLY.STOCK_BARCODE.Split('_');

                foreach(var itemBoxBarcode in arBoxBarcode)
                {

                }





                if (applyCell.SHELF_TYPE == Enum.SHELF_TYPE.Single.ToString())
                {
                    if(arBoxBarcode.Count()!=1)
                    {
                        bResult = false;
                        sResult = string.Format("单伸申请站台_站台号[{0}]", mIO_CONTROL_APPLY.DEVICE_CODE);
                        return bResult;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);

                    if (mMANAGE_MAIN != null && mSTORAGE_MAIN == null)
                    {
                        Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                        if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                        {
                            bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoadTrans", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), false }, out sResult);
                        }
                        else
                        {
                            bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoad", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), true, true }, out sResult);
                        }
                        if (!bResult)
                        {
                            return bResult;
                        }
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("申请条码不满足[不存在库存并且存在类型为入库的任务]_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE);
                        return bResult;
                    }
                }
                else if (applyCell.SHELF_TYPE == Enum.SHELF_TYPE.Double.ToString())
                {

                }
                else
                {
                    bResult = false;
                    sResult = string.Format("申请站台未指定单双伸模式_站台号[{0}]", mIO_CONTROL_APPLY.DEVICE_CODE);
                    return bResult;
                }

                
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常_异常信息[{0}]", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult? Enum.LOG_LEVEL.Information: Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
