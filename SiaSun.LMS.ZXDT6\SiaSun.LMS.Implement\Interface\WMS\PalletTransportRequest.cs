﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 4.12 托盘搬运任务下发接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class PalletTransportRequest : InterfaceBase
    {
        class InputParam
        {
            public PublicHeader Header { get; set; }
            public InputDataField DataField { get; set; }
        }
        class InputDataField : PublicInputBody
        {
            public string TaskNo { get; set; }
            public string TaskType { get; set; }
            public string StartPosition { get; set; }
            public string EndPosition { get; set; }
            public string PalletNo { get; set; }
            public string StartPos1 { get; set; }
            public string EndPos1 { get; set; }
        }

        class OutputParam
        {
            public PublicHeader Header { get; set; }
            public PublicOutputBody DataField { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatErrorMessage(message);
                }
                if (inputParam.Header.AppToken != interfaceToken)
                {
                    result = false;
                    message = $"调用提供的Token未被允许";
                    return FormatErrorMessage(message);
                }

                if(!base.Three.Contains(inputParam.DataField.TaskType))
                {
                    result = false;
                    message = $"任务类型TaskType值[{inputParam.DataField.TaskType}]不在预期范围内";
                    return FormatErrorMessage(message);
                }
                if (base.One.Last() == inputParam.DataField.TaskType && 
                    (string.IsNullOrEmpty(inputParam.DataField.EndPosition) || string.IsNullOrEmpty(inputParam.DataField.PalletNo)))
                {
                    result = false;
                    message = $"任务类型为[{inputParam.DataField.TaskType}]的提升机搬运任务终点位置或者托盘条码不允许为空";
                    return FormatErrorMessage(message);
                }
                if(base.Two.Last() == inputParam.DataField.TaskType &&
                   (string.IsNullOrEmpty(inputParam.DataField.StartPosition) || string.IsNullOrEmpty(inputParam.DataField.EndPosition)))
                {
                    result = false;
                    message = $"任务类型为[{inputParam.DataField.TaskType}]的AGV搬运任务起点位置或者终点位置不允许为空";
                    return FormatErrorMessage(message);
                }
                if(base.Three.Last() == inputParam.DataField.TaskType &&
                   (string.IsNullOrEmpty(inputParam.DataField.StartPosition) || string.IsNullOrEmpty(inputParam.DataField.EndPosition)||
                    string.IsNullOrEmpty(inputParam.DataField.StartPos1) || string.IsNullOrEmpty(inputParam.DataField.EndPos1)))
                {
                    result = false;
                    message = $"任务类型为[{inputParam.DataField.TaskType}]的AGV搬运任务2个起点位置和2个终点位置不允许为空";
                    return FormatErrorMessage(message);
                }
                if (string.IsNullOrEmpty(inputParam.DataField.EndPosition) ||
                    (string.IsNullOrEmpty(inputParam.DataField.StartPosition) && string.IsNullOrEmpty(inputParam.DataField.PalletNo)) )
                {
                    result = false;
                    message = $"接口入参必填项存在空值";
                    return FormatErrorMessage(message);
                }

                var manageMain = S_Base.sBase.pMANAGE_MAIN.GetModelStockBarcode(inputParam.DataField.PalletNo);
                if(manageMain!=null)
                {
                    result = false;
                    message = $"托盘条码[{inputParam.DataField.PalletNo}]已存在任务";
                    return FormatErrorMessage(message);
                }

                var startCell = S_Base.sBase.pWH_CELL.GetModel("", inputParam.DataField.StartPosition);
                if(startCell == null && string.IsNullOrEmpty(inputParam.DataField.PalletNo))
                {
                    result = false;
                    message = $"托盘条码为空并且起点位置[{inputParam.DataField.StartPosition}]未找到";
                    return FormatErrorMessage(message);
                }
                var endCell = S_Base.sBase.pWH_CELL.GetModel("", inputParam.DataField.EndPosition);
                if (endCell == null)
                {
                    result = false;
                    message = $"终点位置[{inputParam.DataField.EndPosition}]未找到";
                    return FormatErrorMessage(message);
                }
                var virtualGoods = S_Base.sBase.pGOODS_MAIN.GetModel("transVirtualGoods");
                if (virtualGoods == null)
                {
                    result = false;
                    message = $"未找到输送任务的虚拟物料";
                    return FormatErrorMessage(message);
                }
                //AGV搬运任务如果终点位置是入库站台
                if (base.Two.Last() == inputParam.DataField.TaskType && endCell != null &&
                    S_Base.sBase.sSystem.GetSysParameter("AgvTaskCheckInRouteStation", out string config) &&
                    !string.IsNullOrEmpty(config))
                {
                    var configArray = config.Split('-');

                    if(configArray.Length == 2 && configArray[0] == endCell.CELL_CODE)
                    {
                        var applyStation = configArray[1];

                        if(S_Base.sBase.sDatabase.IsExistData($"select count(0) from IO_CONTROL_ROUTE where START_DEVICE='{applyStation}' and CONTROL_ROUTE_TYPE=1 and CONTROL_ROUTE_STATUS=1 and CONTROL_ROUTE_MANAGE=1") == "0")
                        {
                            result = false;
                            message = $"AGV输送任务终点位置[{endCell.CELL_CODE}]对应的入库申请站台[{applyStation}]的入库路径均不可用";
                            return FormatErrorMessage(message);
                        }
                    }
                }

                manageMain = new Model.MANAGE_MAIN()
                {
                    CELL_MODEL = "0",
                    END_CELL_ID = endCell.CELL_ID,
                    FULL_FLAG = "",
                    MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                    MANAGE_LEVEL = S_Base.sBase.sSystem.GetSysParameter("TransportLevel", "0"),
                    MANAGE_OPERATOR = Enum.SystemName.iWMS.ToString(),
                    MANAGE_RELATE_CODE = $"PalletTransport-{inputParam.DataField.TaskType}",
                    MANAGE_SOURCE = Enum.SystemName.iWMS.ToString(),
                    MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString(),
                    MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageTransport.ToString(),
                    PLAN_ID = 0,
                    PLAN_TYPE_CODE = inputParam.DataField.TaskNo,
                    STOCK_BARCODE = string.IsNullOrEmpty(inputParam.DataField.PalletNo) ?
                        $"Tran{DateTime.Now.ToString("yyyyMMddHHmmssfff")}" : inputParam.DataField.PalletNo,
                    MANAGE_ID = 0,
                    GOODS_TEMPLATE_ID = 0,
                    MANAGE_CONFIRM_TIME = "",
                    MANAGE_END_TIME = "",
                    MANAGE_REMARK = "",
                    PICK_SEQ = "",

                    START_CELL_ID = startCell == null ? endCell.CELL_ID : startCell.CELL_ID,
                    STOCK_WEIGHT = 0,

                    BACKUP_FIELD3 = $"{inputParam.DataField.StartPos1}|{inputParam.DataField.EndPos1}"
                };

                var manageLists = new List<Model.MANAGE_LIST>()
                {
                    new Model.MANAGE_LIST()
                    {
                        GOODS_ID = virtualGoods.GOODS_ID,
                        MANAGE_LIST_QUANTITY = 1,
                    }
                };

                result = S_Base.sBase.sManage.ManageCreate(
                    mMANAGE_MAIN: manageMain,
                    lsMANAGE_LIST: manageLists,
                    raiseTrans: true,
                    checkStorage: false,
                    checkManage: true,
                    checkCellStatus: true,
                    autoComplete: false,
                    autoControl: startCell != null && endCell != null,
                    doubleInAutoMove: false,
                    out message);
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    Header = new PublicHeader(),
                    DataField = new PublicOutputBody()
                    {
                        ResultFlag = result ? "1" : "0",
                        ErrorMessage = result ? "" : message
                    }
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }
    }

}
