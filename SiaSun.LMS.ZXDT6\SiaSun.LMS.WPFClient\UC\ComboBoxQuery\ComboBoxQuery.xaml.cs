﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ComboBoxQuery.xaml 的交互逻辑
    /// </summary>
    public partial class ComboBoxQuery : UserControl
    {
        #region 变量声明

        /// <summary>
        /// Int型 变量声明
        /// </summary>
        #region int_
        private int _comboBoxWidth = 150;//ComboBox控件宽度
        private int _comboBoxHeight = 20;//ComboxBox控件高度
        private int _buttonWidth = 50;//查询按钮宽度
        private int _buttonHeight = 20;//查询按钮高度
        #endregion

        /// <summary>
        /// String型 变量声明
        /// </summary>
        #region string_
        private string _buttonContent = "查询"; //查询按钮Content
        private string _comboBoxDisplayMemberPath = null;//ComboBox显示元素的路径


        //private string _comboBoxSelectIndexString = null;
        #endregion

        /// <summary>
        /// Bool型 变量声明
        /// </summary>
        #region bool_
        private bool _comboBoxIsTextSearchEnabled = false;//ComboBox.IsTextSearchEnabled 属性
        private bool _comboBoxIsTextSearchCaseSensitive = false;//ComboBox.IsTextSearchCaseSensitive属性
        private bool _comboBoxIsEditable = false;//ComboBox.IsEditable属性
        #endregion

        /// <summary>
        /// 其他类型 变量声明
        /// </summary>
        #region object_
        private System.Collections.IEnumerable _comboBoxItemsSource = null;//ComboBox.ItemsSource属性

        private DataTable _comboBoxDataTable = null;//需要传递的数据
        #endregion

        #endregion

        //test
        public static DependencyProperty ComboBoxDataTableProperty;
        public static DependencyProperty SelectItemProperty;
        //test


        /// <summary>
        /// 控件属性
        /// </summary>
        #region Attributes 控件属性
        /// <summary>
        /// ComboBox的宽度
        /// 默认为150
        /// </summary>
        public int ComboBoxWidth
        {
            set { this._comboBoxWidth = value; }
            get { return this._comboBoxWidth; }
        }

        /// <summary>
        /// ComboBox的高度
        /// 默认为20
        /// </summary>
        public int ComboBoxHeight
        {
            set { this._comboBoxHeight = value; }
            get { return this._comboBoxHeight; }
        }

        /// <summary>
        /// 查询按钮的宽度
        /// 默认为50
        /// </summary>
        public int ButtonWidth
        {
            set { this._buttonWidth = value; }
            get { return this._buttonWidth; }
        }

        /// <summary>
        /// 查询按钮的高度
        /// 默认为20
        /// </summary>
        public int ButtonHeight
        {
            set { this._buttonHeight = value; }
            get { return this._buttonHeight; }
        }

        /// <summary>
        /// 查询按钮显示的文本内容
        /// 默认为“查询”
        /// </summary>
        public string ButtonContent
        {
            set { this._buttonContent = value; }
            get { return this._buttonContent; }
        }

        /// <summary>
        /// ComboBox.IsTextSearchEnabled 属性
        /// 默认为false
        /// 是否支持文本检索
        /// </summary>
        public bool ComboBoxIsTextSearchEnabled
        {
            set { this._comboBoxIsTextSearchEnabled = value; }
            get { return this._comboBoxIsTextSearchEnabled; }
        }

        /// <summary>
        /// ComboBox.IsTextSearchCaseSensitive属性
        /// 默认为false
        /// ComboBox文本检索的敏感性
        /// </summary>
        public bool ComboBoxIsTextSearchCaseSensitive
        {
            set { this._comboBoxIsTextSearchCaseSensitive = value; }
            get { return this._comboBoxIsTextSearchCaseSensitive; }
        }

        /// <summary>
        /// ComboBox.IsEditable属性
        /// 默认为false
        /// ComboBox 文本是否可以编辑
        /// </summary>
        public bool ComboBoxIsEditable
        {
            set { this._comboBoxIsEditable = value; }
            get { return this._comboBoxIsEditable; }
        }

        /// <summary>
        /// ComboBox.ItemsSource属性
        /// 默认为null
        /// </summary>
        public System.Collections.IEnumerable ComboBoxItemsSource
        {
            set { this._comboBoxItemsSource = value; }
            get { return this._comboBoxItemsSource; }
        }

        /// <summary>
        /// ComboBox.DisplayMemberPath属性
        /// 默认为null
        /// </summary>
        public string ComboBoxDisplayMemberPath
        {
            set { this._comboBoxDisplayMemberPath = value; }
            get { return this._comboBoxDisplayMemberPath; }
        }

        /// <summary>
        /// 需要传递的数据
        /// 默认为null
        /// </summary>
        public DataTable ComboBoxDataTable
        {
            /*
            set { this._comboBoxDataTable = value; }
            get { return this._comboBoxDataTable; }
            */
            get { return (DataTable)GetValue(ComboBoxDataTableProperty); }
            set { SetValue(ComboBoxDataTableProperty, value); }
        }

        /// <summary>
        /// 
        /// </summary>
        public object SelectItem
        {
            get { return (object)GetValue(SelectItemProperty); }
            set { SetValue(SelectItemProperty, value); }
        }
        #endregion

        public ComboBoxQuery()
        {
            InitializeComponent();
        }

        static ComboBoxQuery()
        {
            ComboBoxDataTableProperty = DependencyProperty.Register("ComboBoxDataTable", typeof(DataTable), typeof(ComboBoxQuery));
            SelectItemProperty = DependencyProperty.Register("SelectItem", typeof(object), typeof(ComboBoxQuery));
        }

        /// <summary>
        /// 控件初始化
        /// 请在使用控件前在后台应用
        /// </summary>
        public void U_Init()
        {
            //定义ComboBox 与 Button
            _comboBoxDataTable = this.ComboBoxDataTable;


            this.myComboBox.Width = _comboBoxWidth;
            this.myComboBox.Height = _comboBoxHeight;
            this.myButton.Width = _buttonWidth;
            this.myButton.Height = _buttonHeight;
            this.myButton.Content = _buttonContent;

            this.myComboBox.IsEditable = this._comboBoxIsEditable;
            this.myComboBox.IsTextSearchCaseSensitive = this._comboBoxIsTextSearchCaseSensitive;
            this.myComboBox.IsTextSearchEnabled = this._comboBoxIsTextSearchEnabled;

            if (this._comboBoxDataTable != null)
            {
                this._comboBoxItemsSource = this._comboBoxDataTable.DefaultView;
                if (this._comboBoxDisplayMemberPath == null)
                {
                    this._comboBoxDisplayMemberPath = this._comboBoxDataTable.Columns[0].ToString();
                }
            }

            this.myComboBox.ItemsSource = this._comboBoxItemsSource;

            this.myComboBox.DisplayMemberPath = this._comboBoxDisplayMemberPath;
           
            //int j = this.myComboBox.Items.IndexOf(this.SelectItem);
            //IEnumerable<> list = this.myComboBox.ItemsSource;  
            DataView dv = this.myComboBox.ItemsSource as DataView;
            DataTable dt = dv.ToTable();
            StringBuilder sb = new StringBuilder();
            //sb.AppendFormat("1=1  ");
            if (this.SelectItem != null)
            {
                sb.AppendFormat("{0}='{1}'", dt.Columns[1].ColumnName, this.SelectItem.ToString());
            }
            string sbstr = sb.ToString();
            if (!string.IsNullOrEmpty(sbstr))
            {
                DataRow[] drlist = dt.Select(sbstr);
                if (drlist.Length > 0)
                {
                    this.myComboBox.SelectedIndex = dt.Rows.IndexOf(drlist[0]);
                    //this.SelectItem =drlist[0][dt.Columns[1].ColumnName];
                }
            }
            else
            {
                this.myComboBox.SelectedIndex = -1;
            }
            
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (ComboBoxDataTable != null)
                this.U_Init();
        }

        private void myComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
           //if (this.SelectItem != null)
           // {
           //     //this.SelectItem = this.myComboBox.Items[this.myComboBox.SelectedIndex];
           //     DataRowView drv = (DataRowView)(sender as ComboBox).SelectedItem as DataRowView;
           //     if (drv != null)
           //     {
           //         this.SelectItem = drv.Row[1];
           //     }
           //     //this.SelectItem = item.IsSelected.ToString();
           //     //ComboBox
           //     //MessageBox.Show(((ComboBoxItem)(sender as ComboBox).SelectedItem).Content.ToString());
           // }

            if ((sender as ComboBox).SelectedItem != null)
            {
                //this.SelectItem = this.myComboBox.Items[this.myComboBox.SelectedIndex];
                DataRowView drv = (DataRowView)(sender as ComboBox).SelectedItem as DataRowView;
                if (drv != null)
                {
                    this.SelectItem = drv.Row[1];
                }
                //this.SelectItem = item.IsSelected.ToString();
                //ComboBox
                //MessageBox.Show(((ComboBoxItem)(sender as ComboBox).SelectedItem).Content.ToString());
            }
        }

        private void myButton_Click(object sender, RoutedEventArgs e)
        {
            //激活可视化查询窗口
            InfoWindow iw = new InfoWindow();
            //传递数据
            if (this._comboBoxDataTable != null)
            {
                iw.dataGrid1.ItemsSource = this._comboBoxDataTable.DefaultView;
            }
            else if (this._comboBoxItemsSource != null)
            {
                iw.dataGrid1.ItemsSource = this._comboBoxItemsSource;
            }
            else
            {
                return;
            }

            iw.U_Init();

            iw.dataGrid1.IsReadOnly = true;

            iw.ShowDialog();

            if (iw.outstring != null)
            {
                myComboBox.SelectedItem = myComboBox.Items[Convert.ToInt32(iw.outstring)];
            }
        }
    }
}
