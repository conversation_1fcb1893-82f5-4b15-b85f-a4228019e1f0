﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     
 *       日期：     2020/4/17
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// INTERFACE_QUEUE
    /// </summary>
    public class P_INTERFACE_QUEUE : P_Base_House
    {
        public P_INTERFACE_QUEUE()
        {
            //
            // TODO: 此处添加INTERFACE_QUEUE的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<INTERFACE_QUEUE> GetList()
        {
            return ExecuteQueryForList<INTERFACE_QUEUE>("INTERFACE_QUEUE_SELECT", null);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<INTERFACE_QUEUE> GetList(int invokeStatus)
        {
            return ExecuteQueryForList<INTERFACE_QUEUE>("INTERFACE_QUEUE_SELECT_BY_STATUS", invokeStatus);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("INTERFACE_QUEUE_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(INTERFACE_QUEUE interface_queue)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("INTERFACE_QUEUE");
                interface_queue.QUEUE_ID = id;
            }

            ExecuteInsert("INTERFACE_QUEUE_INSERT", interface_queue);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(INTERFACE_QUEUE interface_queue)
        {
            ExecuteUpdate("INTERFACE_QUEUE_UPDATE", interface_queue);
        }



    }
}
