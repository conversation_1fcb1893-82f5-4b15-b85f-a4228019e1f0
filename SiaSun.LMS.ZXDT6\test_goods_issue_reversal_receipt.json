{"outGoodsDate": "2025-07-30", "reason": "Quality issue reversal", "warehouseName": "Central Warehouse", "warehouseId": "WH001", "operatorName": "<PERSON>", "operatorId": "OP001", "outOperatorName": "<PERSON>", "outOperatorId": "OP002", "outGoodsCode": "OUT20250730001", "outGoodsName": "Goods Issue Reversal - Quality Issue", "money": 1000.0, "taxMoney": 1130.0, "redOutName": "Quality Issue Reversal Receipt", "redOutDetailList": [{"remark": "Quality not qualified, need reversal", "goodsId": "G001", "forWay": "Production Line A", "forWayId": "LINE001", "reason": "Quality issue", "orderIndex": "1", "goodsNum": 10, "tax": 0.13, "redOutId": "RED001", "outGoodsInfoId": "OUT_DETAIL_001", "localSend": "No", "gkDeptName": "Production Dept", "gkDeptId": "DEPT001", "manageDeptName": "Material Management Dept", "manageDeptId": "DEPT002", "batch": "BATCH20250730", "gbName": "Team A", "gbId": "GB001", "bzName": "First Group", "bzId": "BZ001", "deptName": "Production Workshop", "deptId": "DEPT003", "orgName": "Test Company", "orgId": "ORG001", "zbCycle": "12 months", "produceDate": "2025-07-15", "shelfName": "Area A Shelf 1", "shelfId": "SHELF001", "warehouseName": "Central Warehouse", "warehouseId": "WH001", "taxAllPrice": 1130.0, "taxPrice": 113.0, "brand": "Brand A", "unitId": "UNIT001", "unitName": "pcs", "outStorageNum": 10, "goodsVersion": "V1.0", "goodsType": "Raw Material", "goodsName": "Test Material A", "goodsCode": "MAT001", "outStorageType": "Normal Outbound", "createDate": "2025-07-30 10:00:00", "createUser": "USER001", "createName": "Creator", "updateDate": "2025-07-30 10:00:00", "updateUser": "USER001", "updateName": "Updater", "id": "DETAIL001", "status": 1, "billCode": "BILL001"}], "createDate": "2025-07-30 10:00:00", "createUser": "USER001", "createName": "Creator", "updateDate": "2025-07-30 10:00:00", "updateUser": "USER001", "updateName": "Updater", "id": "MAIN001", "status": 1, "billCode": "BILL_MAIN001"}