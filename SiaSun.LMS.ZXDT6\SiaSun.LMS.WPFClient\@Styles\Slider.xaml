<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type Slider}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Slider}">
                    <Grid x:Name="GridRoot">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TickBar
                            x:Name="TopTick"
                            Height="4"
                            Fill="#FF405A78"
                            Placement="Top"
                            SnapsToDevicePixels="True"
                            Visibility="Collapsed" />
                        <Rectangle
                            Grid.Row="1"
                            Grid.Column="0"
                            Height="6"
                            Margin="7.5,0,7.5,0"
                            Fill="{DynamicResource SliderBackgroundBrush}"
                            RadiusX="3"
                            RadiusY="3" />

                        <Track x:Name="PART_Track" Grid.Row="1">
                            <Track.Thumb>
                                <Thumb Style="{DynamicResource NuclearSliderThumb}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Command="Slider.IncreaseLarge" Style="{DynamicResource NuclearScrollRepeatButtonStyle}" />
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Command="Slider.DecreaseLarge" Style="{DynamicResource NuclearScrollRepeatButtonStyle}" />
                            </Track.DecreaseRepeatButton>
                        </Track>

                        <TickBar
                            x:Name="BottomTick"
                            Grid.Row="2"
                            Height="4"
                            Fill="{TemplateBinding Foreground}"
                            Placement="Bottom"
                            SnapsToDevicePixels="True"
                            Visibility="Collapsed" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="TickPlacement" Value="TopLeft">
                            <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="TickPlacement" Value="BottomRight">
                            <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="TickPlacement" Value="Both">
                            <Setter TargetName="TopTick" Property="Visibility" Value="Visible" />
                            <Setter TargetName="BottomTick" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="GridRoot" Property="Opacity" Value="0.65" />
                        </Trigger>

                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter TargetName="GridRoot" Property="LayoutTransform">
                                <Setter.Value>
                                    <RotateTransform Angle="-90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Track" Property="Orientation" Value="Horizontal" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="NuclearSliderThumb"
        d:IsControlPart="True"
        TargetType="{x:Type Thumb}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Height" Value="21" />
        <Setter Property="Width" Value="15" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation
                                Storyboard.TargetName="Over"
                                Storyboard.TargetProperty="Opacity"
                                To="1"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation
                                Storyboard.TargetName="Over"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                        <Storyboard x:Key="PressedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="Press"
                                Storyboard.TargetProperty="Opacity"
                                To="1"
                                Duration="00:00:00.1000000" />
                        </Storyboard>
                        <Storyboard x:Key="PressedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="Press"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="00:00:00.4000000" />
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Path
                            x:Name="Base"
                            Margin="1,1.312,1,0.375"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4266928 L-6.6465902,1.7712332 L-9.9818907,1.433604 z"
                            Fill="{DynamicResource SliderThumbBrush}"
                            Stretch="Fill"
                            Stroke="{DynamicResource SliderThumbBorderBrush}"
                            StrokeLineJoin="Round"
                            StrokeThickness="1" />
                        <Path
                            x:Name="Over"
                            Margin="2,2.312,2,1.375"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z"
                            Fill="{DynamicResource MouseOverBrush}"
                            Opacity="0"
                            Stretch="Fill" />
                        <Path
                            x:Name="Press"
                            Margin="2,2.312,2,1.375"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z"
                            Fill="{DynamicResource PressedBrush}"
                            Opacity="0"
                            Stretch="Fill" />
                        <Path
                            x:Name="whiteGradient"
                            Margin="2,2.312,2,1.375"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z"
                            Stretch="Fill">
                            <Path.Fill>
                                <LinearGradientBrush StartPoint="0.563,0.979" EndPoint="0.5,0">
                                    <GradientStop Offset="0" Color="#5FFFFFFF" />
                                    <GradientStop Offset="0.259" Color="#5FFFFFFF" />
                                    <GradientStop Offset="0.393" Color="#00FFFFFF" />
                                    <GradientStop Offset="0.643" Color="#00FFFFFF" />
                                    <GradientStop Offset="0.75" Color="#75FFFFFF" />
                                    <GradientStop Offset="1" Color="#99FFFFFF" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path
                            x:Name="Line"
                            Width="1"
                            Height="10"
                            Margin="-1,-2,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M5.4375,2.6875 L5.4375,12.1875"
                            Stretch="Fill"
                            Stroke="#FF6B81A0"
                            StrokeThickness="1" />
                        <Path
                            x:Name="Line2"
                            Width="1"
                            Height="10"
                            Margin="0,-2,-1,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M5.4375,2.6875 L5.4375,12.1875"
                            Stretch="Fill"
                            Stroke="#FFFFFFFF"
                            StrokeThickness="1" />
                        <Path
                            x:Name="DisabledVisualElement"
                            Margin="1,1.312,1,0.375"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4266928 L-6.6465902,1.7712332 L-9.9818907,1.433604 z"
                            Fill="#FFFFFFFF"
                            IsHitTestVisible="false"
                            Opacity="0"
                            Stretch="Fill"
                            Stroke="#FFFFFFFF"
                            StrokeLineJoin="Round"
                            StrokeThickness="1" />
                        <Path
                            x:Name="FocusVisualElement"
                            Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z"
                            IsHitTestVisible="false"
                            Opacity="0"
                            Stretch="Fill"
                            Stroke="{DynamicResource FocusBrush}"
                            StrokeLineJoin="Round"
                            StrokeThickness="1" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="FocusedOff_BeginStoryboard1" Storyboard="{StaticResource FocusedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="FocusedOn_BeginStoryboard1" Storyboard="{StaticResource FocusedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="PressedOff_BeginStoryboard" Storyboard="{StaticResource PressedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="PressedOn_BeginStoryboard" Storyboard="{StaticResource PressedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">

                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="DisabledVisualElement" Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="NuclearScrollRepeatButtonStyle"
        d:IsControlPart="True"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Grid>
                        <Rectangle
                            Fill="{TemplateBinding Background}"
                            Stroke="{TemplateBinding BorderBrush}"
                            StrokeThickness="{TemplateBinding BorderThickness}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>