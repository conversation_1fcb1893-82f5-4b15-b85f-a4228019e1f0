﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class StackerCrane3 : System.Web.UI.Page
{
    public string DATATIME;

    string connctionString = System.Web.Configuration.WebConfigurationManager.ConnectionStrings["myConn"].ToString();

    public void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            try
            {
                this.get_data_manage();
            }
            catch (Exception ex)
            {
                throw new NotImplementedException(ex.Message);
            }
        }

    }

    protected void Timer_Tick(object sender, EventArgs e)
    {
        try
        {
            this.get_data_manage();
        }
        catch (Exception)
        {
        }

        System.GC.Collect();
    }

    public void get_data_manage()
    {
        try
        {
            SqlConnection mycon = new SqlConnection(connctionString);
            mycon.Open();

            string CurrentDate = System.DateTime.Now.ToString("yyyy-MM-dd");

            string sql_manage_main1 = string.Format(@"select * from V_MANAGE_LIST where START_CELL_ID in (select CELL_ID from WH_CELL where DEVICE_CODE = '18003' or CELL_LOGICAL_NAME = '18003') ");

            SqlDataAdapter myda_manage1 = new SqlDataAdapter(sql_manage_main1, mycon);
            DataSet da_manage1 = new DataSet();
            myda_manage1.Fill(da_manage1);


            string sql_error_message = string.Format(@"select Top(1)* from IO_CONTROL_APPLY_HIS where DEVICE_CODE = '12006' order by CREATE_TIME desc");
            SqlDataAdapter error_message = new SqlDataAdapter(sql_error_message, mycon);
            DataSet da_error = new DataSet();
            error_message.Fill(da_error);


            managelist.DataSource = da_manage1.Tables[0];
            managelist.DataBind();
            ErrorList.DataSource = da_error.Tables[0];
            ErrorList.DataBind();

            #region
            //获取第一巷道内的货位总数量
            string sql_sum = string.Format(@"SELECT count(*) as num_empty FROM WH_CELL WHERE CELL_TYPE='Cell' AND DEVICE_CODE = '18003'");

            //获取当前巷道内可用货位的数量
            string sql_have = string.Format(@"SELECT count(*) as num_have FROM WH_CELL WHERE CELL_TYPE = 'Cell' AND RUN_STATUS = 'Enable' AND CELL_STATUS = 'Nohave' AND DEVICE_CODE = '18003'");

            //获取当前巷道空托盘数量
            string sql_tp = string.Format(@"select Count(*) as num_tp from WH_CELL where DEVICE_CODE = '18003' and CELL_STATUS = 'Pallet'");

            //获取当前巷道有货托盘数量
            string sql_manageNum = string.Format(@"select Count(*) as task_num from WH_CELL where DEVICE_CODE = '18003' and CELL_STATUS = 'Full'");

            SqlDataAdapter sum_DataAdapter = new SqlDataAdapter(sql_sum, mycon);
            DataTable da_sum = new DataTable();
            sum_DataAdapter.Fill(da_sum);

            SqlDataAdapter myda_have = new SqlDataAdapter(sql_have, mycon);
            DataTable da_have = new DataTable();
            myda_have.Fill(da_have);

            SqlDataAdapter myda_tp = new SqlDataAdapter(sql_tp, mycon);
            DataTable da_tp = new DataTable();
            myda_tp.Fill(da_tp);

            SqlDataAdapter myda_manageNum = new SqlDataAdapter(sql_manageNum, mycon);
            DataTable da_manageNum = new DataTable();
            myda_manageNum.Fill(da_manageNum);
            #endregion


            #region 
            if ((da_error.Tables[0].Rows.Count > 0))
            {
                foreach (DataRow dr in da_error.Tables[0].Rows)
                {
                    mycon.Close();

                    ErrorList.DataBind();
                }
            }
            else
            {
                ErrorList.DataSource = null;
                ErrorList.DataBind();
            }

            if ((da_manage1.Tables[0].Rows.Count > 0))
            {

                foreach (DataRow dr in da_manage1.Tables[0].Rows) //入库任务
                {
                    mycon.Close();

                    managelist.DataBind();

                }
            }
            else
            {
                managelist.DataSource = null;
                managelist.DataBind();
            }

            string empty = da_sum.Rows[0]["num_empty"].ToString();
            string have = da_have.Rows[0]["num_have"].ToString();
            string tp = da_tp.Rows[0]["num_tp"].ToString();
            string manageNum = da_manageNum.Rows[0]["task_num"].ToString();

            ScriptManager.RegisterStartupScript(this, this.GetType(), "", string.Format("fu('{0}','{1}','{2}','{3}');", da_sum.Rows[0]["num_empty"].ToString(), da_have.Rows[0]["num_have"].ToString(), da_tp.Rows[0]["num_tp"].ToString(), da_manageNum.Rows[0]["task_num"].ToString()), true);

            #endregion
        }
        catch (Exception ex)
        {
            string a = ex.Message;
        }


    }

    protected void GridView_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            int ID = e.Row.RowIndex + 1;
            e.Row.Cells[0].Text = (e.Row.RowIndex + 1).ToString();
        }
    }
}