﻿using System.Collections.Generic;
using System.Data;
using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.PLAN_MAIN))]
    [ServiceKnownType(typeof(DataTable))]
    [ServiceKnownType(typeof(List<object>))]
    [ServiceKnownType(typeof(List<SiaSun.LMS.Model.PLAN_LIST>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.GOODS_MAIN))]
    public interface I_Plan 
    {
        [OperationContract]
        SiaSun.LMS.Model.PLAN_MAIN PlanGetModel(int PLAN_ID);

        [OperationContract]
        SiaSun.LMS.Model.PLAN_DETAIL PlanDetailGetModel(int PLAN_DETAIL_ID);

        [OperationContract]
        SiaSun.LMS.Model.PLAN_LIST PlanListGetModel(int PLAN_LIST_ID);

        [OperationContract]
        bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, IList<Model.PLAN_LIST> lsPLAN_LIST, bool isTrans, out int PLAN_ID, out string sResult);

        [OperationContract]
        bool PlanComplete(int PLAN_ID, out string sResult);

        [OperationContract]
        bool PlanCancel(int planId, string operatorName, bool trans, out string message);

        [OperationContract]
        bool PlanExecute(int PLAN_ID, out string sResult);

        [OperationContract]
        bool PlanOutDownLoad(int planId, string operatorName, bool trans, string reActPalletEndCellCode, out string sResult, string actionArea = "", string actionBindId = "");

    }
}
