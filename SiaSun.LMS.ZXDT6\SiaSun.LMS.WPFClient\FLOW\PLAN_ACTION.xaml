﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.FLOW_ACTION.PLAN_ACTION"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="PLAN_ACTION"
    Width="550"
    Height="366"
    Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="2*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <WrapPanel Grid.Row="0">
            <Border
                Name="panelDateTime"
                MinWidth="140"
                Margin="0,0,10,0">
                <WrapPanel Name="panelDateSect" VerticalAlignment="Center">
                    <CheckBox
                        Name="chkboxDate"
                        Margin="5,5,0,5"
                        VerticalAlignment="Center"
                        IsChecked="True" />
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="5,5,1,5"
                            VerticalAlignment="Center"
                            Text="从" />
                        <DatePicker
                            Name="dtpStart"
                            Width="90"
                            Margin="0,5,0,5" />
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Margin="1,5,1,5"
                            VerticalAlignment="Center"
                            Text="至" />
                        <DatePicker
                            Name="dtpEnd"
                            Width="90"
                            Margin="0,5,0,5" />
                    </StackPanel>
                </WrapPanel>
            </Border>
            <uc:ucQuickQuery
                x:Name="ucQueryPlan"
                Margin="1,1,1,3"
                BorderBrush="Black" />
        </WrapPanel>

        <GroupBox
            Grid.Row="1"
            Margin="2,5,2,1"
            Header="计划">
            <uc:ucCommonDataGrid x:Name="gridPlan" />
        </GroupBox>

        <GridSplitter
            Grid.Row="2"
            Height="2"
            HorizontalAlignment="Stretch" />

        <GroupBox
            Grid.Row="3"
            Margin="2,5,2,1"
            Header="计划单">
            <uc:ucSplitPropertyGridTab x:Name="gridPlanList" />
        </GroupBox>

        <GroupBox
            Grid.Row="4"
            Margin="1,5,1,1"
            Header="操作区">
            <uc:ucStatusFlowActionsPanel x:Name="ucFlowPlanAction" />
        </GroupBox>
    </Grid>
</ad:DocumentContent>
