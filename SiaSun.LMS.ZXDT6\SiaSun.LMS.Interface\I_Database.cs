﻿using SiaSun.LMS.Model;
using System.ServiceModel;
using System.Data;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_Database
    {
        [OperationContract]
        int ExecuteNonQuery(string strSQL, string DataAccess = "HouseMap");

        [OperationContract]
        DataTable GetList(string strSQL, string DataAccess = "HouseMap");

        [OperationContract]
        DataTable GetTableXmlSql(string statementsql, object paramObject, string DataAccess = "HouseMap");

        [OperationContract]
        int Save(DataTable dt, string tablename, string DataAccess = "HouseMap");

        [OperationContract]
        ObjectT GetModel(string statementName, object parameterObject, string DataAccess = "HouseMap");
        [OperationContract]
        object GetModel1(string statementName, object parameterObject, string DataAccess = "HouseMap");


        [OperationContract]
        ObjectTList GetListObject(string statementName, object parameterObject, string DataAccess = "HouseMap");

        [OperationContract]
        string IsExistData(string querySql, int returnIndex = 0);

    }
}
