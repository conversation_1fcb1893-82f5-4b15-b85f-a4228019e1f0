<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions name="S_ManageService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing">
  <wsdl:types>
    <xs:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/">
      <xs:element name="anyType" nillable="true" type="xs:anyType"/>
      <xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
      <xs:element name="boolean" nillable="true" type="xs:boolean"/>
      <xs:element name="byte" nillable="true" type="xs:byte"/>
      <xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
      <xs:element name="decimal" nillable="true" type="xs:decimal"/>
      <xs:element name="double" nillable="true" type="xs:double"/>
      <xs:element name="float" nillable="true" type="xs:float"/>
      <xs:element name="int" nillable="true" type="xs:int"/>
      <xs:element name="long" nillable="true" type="xs:long"/>
      <xs:element name="QName" nillable="true" type="xs:QName"/>
      <xs:element name="short" nillable="true" type="xs:short"/>
      <xs:element name="string" nillable="true" type="xs:string"/>
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
      <xs:element name="char" nillable="true" type="tns:char"/>
      <xs:simpleType name="char">
        <xs:restriction base="xs:int"/>
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration"/>
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid"/>
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName"/>
      <xs:attribute name="Id" type="xs:ID"/>
      <xs:attribute name="Ref" type="xs:IDREF"/>
    </xs:schema>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model">
      <xs:complexType name="SYS_USER">
        <xs:sequence>
          <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="USER_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="USER_ORDER" type="xs:int"/>
          <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER"/>
      <xs:complexType name="MANAGE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="MANAGE_LIST_QUANTITY" type="xs:decimal"/>
          <xs:element minOccurs="0" name="MANAGE_LIST_REMARK" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST"/>
      <xs:complexType name="MANAGE_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="END_CELL_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="MANAGE_LEVEL" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_OPERATOR" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_REMARK" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_STATUS" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="START_CELL_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_MAIN" nillable="true" type="tns:MANAGE_MAIN"/>
      <xs:complexType name="ArrayOfMANAGE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfMANAGE_LIST" nillable="true" type="tns:ArrayOfMANAGE_LIST"/>
      <xs:complexType name="ArrayOfPLAN_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfPLAN_LIST" nillable="true" type="tns:ArrayOfPLAN_LIST"/>
      <xs:complexType name="PLAN_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="PLAN_LIST_CODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PLAN_LIST_FINISHED_QUANTITY" type="xs:decimal"/>
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="PLAN_LIST_ORDERED_QUANTITY" type="xs:decimal"/>
          <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY" type="xs:decimal"/>
          <xs:element minOccurs="0" name="PLAN_LIST_REMARK" nillable="true" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST"/>
      <xs:complexType name="ArrayOfMANAGE_TYPE_PARAM">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="MANAGE_TYPE_PARAM" nillable="true" type="tns:MANAGE_TYPE_PARAM"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfMANAGE_TYPE_PARAM" nillable="true" type="tns:ArrayOfMANAGE_TYPE_PARAM"/>
      <xs:complexType name="MANAGE_TYPE_PARAM">
        <xs:sequence>
          <xs:element minOccurs="0" name="MANAGE_TYPE_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="PARAM_CODE" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PARAM_FLAG" nillable="true" type="xs:string"/>
          <xs:element minOccurs="0" name="PARAM_ID" type="xs:int"/>
          <xs:element minOccurs="0" name="PARAM_VALUE" nillable="true" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_TYPE_PARAM" nillable="true" type="tns:MANAGE_TYPE_PARAM"/>
    </xs:schema>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
      <xs:element name="ControlTranslate">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="WAREHOUSE" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ControlTranslateResponse">
        <xs:complexType>
          <xs:sequence/>
        </xs:complexType>
      </xs:element>
      <xs:element name="RecordCreate">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="RecordCreateResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="RecordCreateResult" type="xs:boolean"/>
            <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageTypeParamGetList">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="MANAGE_TYPE_ID" type="xs:int"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageTypeParamGetListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ManageTypeParamGetListResult" nillable="true" type="q1:ArrayOfMANAGE_TYPE_PARAM" xmlns:q1="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageInAndroid">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="mm" nillable="true" type="q2:MANAGE_MAIN" xmlns:q2="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
            <xs:element minOccurs="0" name="lManageList" nillable="true" type="q3:ArrayOfMANAGE_LIST" xmlns:q3="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageInAndroidResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ManageInAndroidResult" type="xs:boolean"/>
            <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageOutAndroid">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="mm" nillable="true" type="q4:MANAGE_MAIN" xmlns:q4="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
            <xs:element minOccurs="0" name="lManageList" nillable="true" type="q5:ArrayOfMANAGE_LIST" xmlns:q5="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ManageOutAndroidResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ManageOutAndroidResult" type="xs:boolean"/>
            <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="I_ManageService_ControlTranslate_InputMessage">
    <wsdl:part name="parameters" element="tns:ControlTranslate"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ControlTranslate_OutputMessage">
    <wsdl:part name="parameters" element="tns:ControlTranslateResponse"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_RecordCreate_InputMessage">
    <wsdl:part name="parameters" element="tns:RecordCreate"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_RecordCreate_OutputMessage">
    <wsdl:part name="parameters" element="tns:RecordCreateResponse"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageTypeParamGetList_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageTypeParamGetList"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageTypeParamGetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageTypeParamGetListResponse"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageInAndroid_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageInAndroid"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageInAndroid_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageInAndroidResponse"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageOutAndroid_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageOutAndroid"/>
  </wsdl:message>
  <wsdl:message name="I_ManageService_ManageOutAndroid_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageOutAndroidResponse"/>
  </wsdl:message>
  <wsdl:portType name="I_ManageService">
    <wsdl:operation name="ControlTranslate">
      <wsdl:input wsaw:Action="http://tempuri.org/I_ManageService/ControlTranslate" message="tns:I_ManageService_ControlTranslate_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_ManageService/ControlTranslateResponse" message="tns:I_ManageService_ControlTranslate_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="RecordCreate">
      <wsdl:input wsaw:Action="http://tempuri.org/I_ManageService/RecordCreate" message="tns:I_ManageService_RecordCreate_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_ManageService/RecordCreateResponse" message="tns:I_ManageService_RecordCreate_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ManageTypeParamGetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_ManageService/ManageTypeParamGetList" message="tns:I_ManageService_ManageTypeParamGetList_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_ManageService/ManageTypeParamGetListResponse" message="tns:I_ManageService_ManageTypeParamGetList_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ManageInAndroid">
      <wsdl:input wsaw:Action="http://tempuri.org/I_ManageService/ManageInAndroid" message="tns:I_ManageService_ManageInAndroid_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_ManageService/ManageInAndroidResponse" message="tns:I_ManageService_ManageInAndroid_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ManageOutAndroid">
      <wsdl:input wsaw:Action="http://tempuri.org/I_ManageService/ManageOutAndroid" message="tns:I_ManageService_ManageOutAndroid_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_ManageService/ManageOutAndroidResponse" message="tns:I_ManageService_ManageOutAndroid_OutputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_ManageService" type="tns:I_ManageService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="ControlTranslate">
      <soap:operation soapAction="http://tempuri.org/I_ManageService/ControlTranslate" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RecordCreate">
      <soap:operation soapAction="http://tempuri.org/I_ManageService/RecordCreate" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageTypeParamGetList">
      <soap:operation soapAction="http://tempuri.org/I_ManageService/ManageTypeParamGetList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageInAndroid">
      <soap:operation soapAction="http://tempuri.org/I_ManageService/ManageInAndroid" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageOutAndroid">
      <soap:operation soapAction="http://tempuri.org/I_ManageService/ManageOutAndroid" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_ManageService">
    <wsdl:port name="BasicHttpBinding_I_ManageService" binding="tns:BasicHttpBinding_I_ManageService">
      <soap:address location="http://localhost:8001/Service/ManageService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>