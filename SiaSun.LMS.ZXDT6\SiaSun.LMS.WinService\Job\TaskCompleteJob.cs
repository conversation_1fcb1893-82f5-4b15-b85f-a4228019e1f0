﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class TaskCompleteJob : IJob
    {  
        public void Execute(IJobExecutionContext context)
        {
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                Program.sysLog.Debug("进入TaskCompleteJob");

                MainApp.BaseService._S_ManageService.ControlTranslate(3, out sResult);

                bResult = string.IsNullOrEmpty(sResult);
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("处理Control状态变化时异常", ex);
                throw;
            }
            finally
            {
                if (!bResult)
                {
                    Program.sysLog.WarnFormat("T处理Control状态变化失败_信息[{0}]", sResult);
                }

                Program.sysLog.Debug("离开TaskCompleteJob");
            }
        }


        ///// <summary>
        ///// 自定义写入日志方法
        ///// </summary>
        ///// <param name="logType">日志类别</param>
        ///// <param name="logOperator">日志操作者</param>
        ///// <param name="logMessage">日志信息</param>
        //public void YwzWarn(string logType,string logOperator ,string logMessage)
        //{
        //    log4net.LogicalThreadContext.Properties["LogType"] = logType;
        //    log4net.LogicalThreadContext.Properties["LogOperator"] = logOperator;

        //    Program.sysLog.Warn(logMessage);
        //}

        ///// <summary>
        ///// 自定义写入日志方法
        ///// </summary>
        ///// <param name="logType">日志类别</param>
        ///// <param name="logOperator">日志操作者</param>
        ///// <param name="logMessage">日志信息</param>
        ///// <param name="exception">异常信息</param>
        //public void YwzWarn(string logType, string logOperator, string logMessage, Exception exception)
        //{
        //    log4net.LogicalThreadContext.Properties["LogType"] = logType;
        //    log4net.LogicalThreadContext.Properties["LogOperator"] = logOperator;

        //    Program.sysLog.Warn("TaskCompleteJob.Execute:处理Control任务失败", exception);
        //}

        ///// <summary>
        ///// 自定义写入日志方法
        ///// </summary>
        ///// <param name="logType">日志类别</param>
        ///// <param name="logOperator">日志操作者</param>
        ///// <param name="messageFormat">日志信息</param>
        ///// <param name="args">格式化参数</param>
        //public void YwzWarnFormat(string logType, string logOperator, string messageFormat, params object[] args)
        //{
        //    log4net.LogicalThreadContext.Properties["LogType"] = logType;
        //    log4net.LogicalThreadContext.Properties["LogOperator"] = logOperator;

        //    Program.sysLog.WarnFormat("TaskCompleteJob.Execute:处理Control任务失败_{0}", args);

        //}
    }
}
