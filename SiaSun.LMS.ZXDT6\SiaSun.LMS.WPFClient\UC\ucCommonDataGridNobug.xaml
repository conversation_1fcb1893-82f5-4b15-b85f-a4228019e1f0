﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucCommonDataGridNobug"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:exa="clr-namespace:ExampleClassLibrary;assembly=ExampleClassLibrary"
             mc:Ignorable="d" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             d:DesignHeight="300" d:DesignWidth="806">
    
    <UserControl.Resources>
        <ContextMenu x:Key="menuGrid" MenuItem.Click="menuGrid_Click">
            <MenuItem Name="menuItemCopy" Header="复制"></MenuItem>
            <!--<MenuItem Name="menuItemDeepCopy" Header="深度复制"></MenuItem>-->
            <MenuItem Name="menuItemSetDefault" Header="重置"></MenuItem>
        </ContextMenu>
    </UserControl.Resources>
    
    <Grid Name="aa">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <!--<ExtendedGridControl:ExtendedDataGrid x:Name="gridApp" AutoGenerateColumns="False" Grid.Row="0" Margin="1,1,1,0"  MouseDoubleClick="gridApp_MouseDoubleClick"></ExtendedGridControl:ExtendedDataGrid>-->
        <!--<uc:DataGridTemplate x:Name="gridApp" AutoGenerateColumns="False" Grid.Row="0" Margin="1,1,1,0"  MouseDoubleClick="gridApp_MouseDoubleClick"  />-->

        <uc:DataGridTemplate x:Name="gridApp" AutoGenerateColumns="False" Grid.Row="0" Margin="1,1,1,0" ContextMenu="{StaticResource menuGrid}" MouseDoubleClick="gridApp_MouseDoubleClick"></uc:DataGridTemplate>

        
        <Border Grid.Row="1" Margin="1" BorderThickness="1" BorderBrush="#FF888888">
            <WrapPanel Name="panelToolbar" Height="23" VerticalAlignment="Bottom">
                <WrapPanel.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1" >
                        <GradientStop Color="White" Offset="0.2"/>
                        <GradientStop Color="#FFCDDBF5" Offset="0.6"/>
                        <GradientStop Color="LightBlue" Offset="1"/>
                    </LinearGradientBrush>
                </WrapPanel.Background>
                <CheckBox Name="chkBoxCheckAll" Margin="2,0,5,0" VerticalAlignment="Center"  Visibility="Collapsed" Click="chkBoxCheckAll_Checked"></CheckBox>

                <StackPanel Name="tbarButton" Orientation="Horizontal"  ButtonBase.Click="tbarButton_Click" >
                    <Button Name="btnAdd" Template="{StaticResource templateImageButtonAdd}"></Button>
                    <Button Name="btnEdit" Template="{StaticResource templateImageButtonEdit}"></Button>
                    <Button Name="btnDelete" Template="{StaticResource templateImageButtonDelete}"></Button>
                    <Button Name="btnCancel" Template="{StaticResource templateImageButtonCancel}"></Button>
                    <Button Name="btnSave" Template="{StaticResource templateImageButtonSave}"></Button>
                    
                </StackPanel>

                <StackPanel Name="tarCommon" Orientation="Horizontal"  ButtonBase.Click="tarCommon_Click" Margin="1">
                    <GridSplitter HorizontalAlignment="Stretch" Width="1" Margin="2" Background="LightSlateGray"></GridSplitter>
                    <Button Name="btnDetail" Template="{StaticResource templateImageButtonDetail}"></Button>
                    <Button Name="btnPrint" Template="{StaticResource templateImageButtonPrint}" ></Button>
                    <Button Name="btnExportExcel" Template="{StaticResource templateImageButtonExportExcel}"></Button>
                    <Button Name="btnQuery" Template="{StaticResource templateImageButtonSearch}"></Button>
                    <Button Name="btnRefresh" Template="{StaticResource templateImageButtonUpdate}" ></Button>
                    <GridSplitter HorizontalAlignment="Stretch" Width="1" Margin="2" Background="LightSlateGray"></GridSplitter>
                </StackPanel>

                <StackPanel Name="tbarPage" Orientation="Horizontal"  ButtonBase.Click="tbarPage_Click">
                    <Button Name="btnFirstPage" Template="{StaticResource templateImageButtonFirst}"></Button>
                    <Button Name="btnPreviousPage" Template="{StaticResource templateImageButtonBack}"></Button>
                    <Button Name="btnNextPage" Template="{StaticResource templateImageButtonNext}"></Button>
                    <Button Name="btnLastPage" Template="{StaticResource templateImageButtonLast}"></Button>

                    <TextBox Name="txtChangeRowPrePage" Margin="5,0,1,0" Width="60" VerticalAlignment="Center" KeyDown="PageTextBox_KeyDown"></TextBox>
                    <TextBlock Name="txtLineNote" VerticalAlignment="Center" Margin="1,1,3,1" Tag="行/每页   共{0}行">行/每页   共0行</TextBlock>

                    <Separator></Separator>
                    <TextBlock VerticalAlignment="Center" Margin="5,1,1,1">第</TextBlock>
                    <TextBox Name="txtJumpPage" Margin="5,0,1,0" Width="60"  VerticalAlignment="Center" KeyDown="PageTextBox_KeyDown"></TextBox>
                    <TextBlock Name="txtPageNote" VerticalAlignment="Center"  Margin="5,1,1,1" Tag="页   共{0}页" >页   共0页</TextBlock>
                </StackPanel>
                <TextBlock Name="txtTotal" VerticalAlignment="Center" Margin="10,1,1,1" Visibility="Collapsed" Tag="合计:{0} 总计:{1}">合计:0  总计:0</TextBlock>
            </WrapPanel>
        </Border>
    </Grid>
</UserControl>
