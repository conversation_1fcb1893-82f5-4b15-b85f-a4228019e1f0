using System.Web.Http;
using Newtonsoft.Json;
using System;

namespace SiaSun.LMS.HttpApiDemo.Controllers
{
    public class DemoController : ApiController
    {
        [HttpPost]
        [Route("api/demo/carrier")]
        public IHttpActionResult CarrierRequest([FromBody] object request)
        {
            try
            {
                string result = WcfProxy.CallWcfService("CarrierRequest", request);
                return Ok(JsonConvert.DeserializeObject(result));
            }
            catch (Exception ex)
            {
                return BadRequest($"处理载具请求失败: {ex.Message}");
            }
        }

        [HttpPost]
        [Route("api/demo/task")]
        public IHttpActionResult TaskRequest([FromBody] object request)
        {
            try
            {
                string result = WcfProxy.CallWcfService("TaskRequest", request);
                return Ok(JsonConvert.DeserializeObject(result));
            }
            catch (Exception ex)
            {
                return BadRequest($"处理任务请求失败: {ex.Message}");
            }
        }

        [HttpGet]
        [Route("api/demo/status")]
        public IHttpActionResult GetStatus()
        {
            try
            {
                return Ok(new {
                    status = "running",
                    wcfUrl = "http://127.0.0.1:8001/Service/Demo",
                    httpApiUrl = "http://127.0.0.1:9001",
                    timestamp = DateTime.Now,
                    version = "1.0.0"
                });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpGet]
        [Route("api/demo/health")]
        public IHttpActionResult HealthCheck()
        {
            return Ok(new {
                healthy = true,
                timestamp = DateTime.Now,
                uptime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });
        }
    }
}