﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_MENU
	/// </summary>
	public class P_SYS_MENU : P_Base_House
	{
		public P_SYS_MENU ()
		{
			//
			// TODO: 此处添加SYS_MENU的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_MENU> GetList()
		{
			return ExecuteQueryForList<SYS_MENU>("SYS_MENU_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_MENU> GetList_MENU_PARAMETER()
        {
            return this.ExecuteQueryForList<SYS_MENU>("SYS_MENU_SELECT_BY_MENU_PARAMETER", null);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_MENU> GetList_ROLE_ID(int ROLE_ID)
        {
            return this.ExecuteQueryForList<SYS_MENU>("SYS_MENU_SELECT_BY_ROLE_ID", ROLE_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_MENU> GetList_ROLE_ID_NO(int ROLE_ID)
        {
            return this.ExecuteQueryForList<SYS_MENU>("SYS_MENU_SELECT_NOTBY_ROLE_ID", ROLE_ID);
        }

        public IList<SYS_MENU> GetList_PARENT_ID(int PARENT_ID)
        {
            return this.ExecuteQueryForList<SYS_MENU>("SYS_MENU_SELECT_BY_PARENT_ID", PARENT_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_MENU sys_menu)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_MENU");
                sys_menu.MENU_ID = id;
            }

            return ExecuteInsert("SYS_MENU_INSERT",sys_menu);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_MENU sys_menu)
		{
			return ExecuteUpdate("SYS_MENU_UPDATE",sys_menu);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_MENU GetModel(System.Int32 MENU_ID)
		{
			return ExecuteQueryForObject<SYS_MENU>("SYS_MENU_SELECT_BY_ID",MENU_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 MENU_ID)
		{
			return ExecuteDelete("SYS_MENU_DELETE",MENU_ID);
		}
		

	}
}
