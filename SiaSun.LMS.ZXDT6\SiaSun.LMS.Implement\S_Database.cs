﻿using System;
using System.Collections.Generic;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Interface;
using SiaSun.LMS.Model;
using SiaSun.LMS.Persistence;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
                     InstanceContextMode = InstanceContextMode.Single,
                     ConcurrencyMode = ConcurrencyMode.Multiple,
                     MaxItemsInObjectGraph = int.MaxValue)]
    public class S_Database : I_Database
    {
        private static Dictionary<string, P_Base> dataAccessDic;

        public S_Database()
        {
            if (dataAccessDic == null)
            {
                dataAccessDic = new Dictionary<string, P_Base>();
                dataAccessDic.Add("HouseMap", new P_Base_House());
                dataAccessDic.Add("BpmsMap", new P_Base_Bpms());
            }
        }

        public int ExecuteNonQuery(string strSQL, string DataAccess = "HouseMap")
        {
            return dataAccessDic[DataAccess].ExecuteNonQuery(strSQL, "dynamicSQL");
        }

        public DataTable GetList(string strSQL, string DataAccess = "HouseMap")
        {
            DataTable dtResult = dataAccessDic[DataAccess].ExecuteQueryForDataTable("dynamicSQL", strSQL);
            return dtResult;
        }

        public DataTable GetTableXmlSql(string statementsql, object paramObject, string DataAccess = "HouseMap")
        {
            DataTable dtResult = dataAccessDic[DataAccess].ExecuteQueryForDataTable(statementsql, paramObject);
            return dtResult;
        }

        public int Save(DataTable dt, string tablename, string DataAccess = "HouseMap")
        {
            int iResult = dataAccessDic[DataAccess].SaveDataTable(dt, tablename);
            return iResult;
        }

        public ObjectT GetModel(string statementName, object parameterObject, string DataAccess = "HouseMap")
        {
            return dataAccessDic[DataAccess].ExecuteQueryForObject(statementName, parameterObject);
        }
        public object GetModel1(string statementName, object parameterObject, string DataAccess = "HouseMap")
        {
            return dataAccessDic[DataAccess].ExecuteQueryForObject1(statementName, parameterObject);
        }

        public ObjectTList GetListObject(string statementName, object parameterObject, string DataAccess = "HouseMap")
        {
            return dataAccessDic[DataAccess].ExecuteQueryForList(statementName, parameterObject);
        }

        /// <summary>
        /// 根据sql语句查询是否存在数据
        /// 2020-02-10 15:05
        /// </summary>
        /// <param name="querySql"></param>
        /// <param name="returnIndex">支持指定返回第一行的数据的列号</param>
        /// <returns></returns>
        public string IsExistData(string querySql, int returnIndex = 0)
        {
            DataTable dtQuery = this.GetList(querySql);
            if (dtQuery != null && dtQuery.Rows.Count > 0 && dtQuery.Rows.Count > returnIndex)
            {
                return dtQuery.Rows[0][returnIndex].ToString();
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 获得特定序列的值  2020-05-08 14:31:31
        /// </summary>
        /// <param name="seqName">序列名</param>
        /// <param name="length">除去首尾后的长度</param>
        /// <param name="header">首</param>
        /// <param name="footer">尾</param>
        /// <returns></returns>
        internal string GetSequenceValue(string seqName, int length, string header = "", string footer = "")
        {
            string result = string.Empty;

            try
            {
                DataTable dtInterfaceSeq = S_Base.sBase.sDatabase.GetList($"select {seqName}.nextval from dual");
                if (dtInterfaceSeq != null && dtInterfaceSeq.Rows.Count > 0)
                {
                    result = dtInterfaceSeq.Rows[0][0].ToString();
                }
            }
            catch(Exception ex)
            {
                S_Base.sBase.Log.Error("获取序列时异常", ex);
            }
            finally
            {
                if (string.IsNullOrEmpty(result))
                {
                    result = DateTime.Now.ToString("yyyyMMddHHmmssffff");
                }

                if (length > 0)
                {
                    if (result.Length > length)
                    {
                        result = result.Substring(result.Length - length, length);
                    }
                    else if (result.Length < length)
                    {
                        result = result.PadLeft(length, '0');
                    }
                }
            }

            return $"{header}{result}{footer}";
        }

        internal void BeginTransaction(bool enabled = true, string mapName= "HouseMap")
        {
            dataAccessDic[mapName].BeginTransaction(enabled);
        }

        internal void CommitTransaction(bool enabled = true, string mapName = "HouseMap")
        {
            dataAccessDic[mapName].CommitTransaction(enabled);
        }

        internal void RollBackTransaction(bool enabled = true, string mapName = "HouseMap")
        {
            dataAccessDic[mapName].RollBackTransaction(enabled);
        }
    }
}
