using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 出库单完成上报回调接口测试类
    /// </summary>
    public static class OutboundReceiptCompleteCallbackTest
    {
        /// <summary>
        /// 测试单个出库明细上报
        /// </summary>
        public static void TestSingleOutboundItem()
        {
            Console.WriteLine("=== 测试单个出库明细上报 ===");
            
            try
            {
                var callback = new OutboundReceiptCompleteCallback();
                
                bool result = callback.IntefaceMethod(
                    goodsNum: 100,
                    warehouseCode: "WH001", 
                    shelfCode: "SH001-A01",
                    oId: "OUTBOUND_DETAIL_001",
                    lId: "STEREO_WH_OUTBOUND_001", 
                    outboundType: 63,
                    out string message);
                
                Console.WriteLine($"结果: {(result ? "成功" : "失败")}");
                Console.WriteLine($"消息: {message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异常: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试批量出库明细上报
        /// </summary>
        public static void TestBatchOutboundItems()
        {
            Console.WriteLine("=== 测试批量出库明细上报 ===");
            
            try
            {
                var callback = new OutboundReceiptCompleteCallback();
                
                var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
                {
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 100,
                        warehouseCode = "WH001",
                        shelfCode = "SH001-A01", 
                        oId = "OUTBOUND_DETAIL_001",
                        lId = "STEREO_WH_OUTBOUND_001",
                        outboundType = 63  // 出库单
                    },
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 50,
                        warehouseCode = "WH002",
                        shelfCode = "SH002-B01",
                        oId = "BORROW_DETAIL_001", 
                        lId = "STEREO_WH_BORROW_001",
                        outboundType = 74  // 借用
                    },
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 25,
                        warehouseCode = "WH003",
                        shelfCode = "SH003-C01",
                        oId = "REVERSAL_DETAIL_001",
                        lId = "STEREO_WH_REVERSAL_001", 
                        outboundType = 72  // 入库红冲单
                    }
                };
                
                bool result = callback.IntefaceMethod(items, out string message);
                
                Console.WriteLine($"结果: {(result ? "成功" : "失败")}");
                Console.WriteLine($"消息: {message}");
                Console.WriteLine($"上报明细数量: {items.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异常: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试参数验证
        /// </summary>
        public static void TestParameterValidation()
        {
            Console.WriteLine("=== 测试参数验证 ===");
            
            var callback = new OutboundReceiptCompleteCallback();
            
            // 测试空列表
            Console.WriteLine("1. 测试空列表:");
            bool result1 = callback.IntefaceMethod(new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>(), out string message1);
            Console.WriteLine($"   结果: {(result1 ? "成功" : "失败")} - {message1}");
            
            // 测试null列表
            Console.WriteLine("2. 测试null列表:");
            bool result2 = callback.IntefaceMethod(null, out string message2);
            Console.WriteLine($"   结果: {(result2 ? "成功" : "失败")} - {message2}");
            
            // 测试无效的出库类型
            Console.WriteLine("3. 测试无效的出库类型:");
            var invalidItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 100,
                    warehouseCode = "WH001",
                    shelfCode = "SH001-A01",
                    oId = "OUTBOUND_DETAIL_001",
                    lId = "STEREO_WH_OUTBOUND_001",
                    outboundType = 999  // 无效类型
                }
            };
            bool result3 = callback.IntefaceMethod(invalidItems, out string message3);
            Console.WriteLine($"   结果: {(result3 ? "成功" : "失败")} - {message3}");
            
            // 测试缺少必填字段
            Console.WriteLine("4. 测试缺少必填字段:");
            var missingFieldItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
            {
                new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                {
                    goodsNum = 100,
                    warehouseCode = "", // 空的仓库编码
                    shelfCode = "SH001-A01",
                    oId = "OUTBOUND_DETAIL_001",
                    lId = "STEREO_WH_OUTBOUND_001",
                    outboundType = 63
                }
            };
            bool result4 = callback.IntefaceMethod(missingFieldItems, out string message4);
            Console.WriteLine($"   结果: {(result4 ? "成功" : "失败")} - {message4}");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行出库单完成上报回调接口测试...");
            Console.WriteLine("=".PadRight(50, '='));
            
            TestParameterValidation();
            TestSingleOutboundItem();
            TestBatchOutboundItems();
            
            Console.WriteLine("=".PadRight(50, '='));
            Console.WriteLine("测试完成！");
        }
    }
}
