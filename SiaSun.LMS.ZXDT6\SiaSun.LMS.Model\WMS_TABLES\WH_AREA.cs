﻿/***************************************************************************
 * 
 *       功能：     仓库区域规划实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_AREA 
	/// </summary>
    [Serializable]
    [DataContract]
	public class WH_AREA
	{
		public WH_AREA()
		{
			
		}
		
		private int _area_id;
		private int _warehouse_id;
		private string _area_type;
		private string _area_code;
		private string _area_name;
		private int _area_order;
		private string _area_flag;
		private string _area_remark;
		private int _area_group;
		
		///<sumary>
		/// 库区编号
        ///</sumary>
        [DataMember]
		public int AREA_ID
		{
			get{return _area_id;}
			set{_area_id = value;}
		}
		///<sumary>
		/// 仓库编号
        ///</sumary>
        [DataMember]
		public int WAREHOUSE_ID
		{
			get{return _warehouse_id;}
			set{_warehouse_id = value;}
		}
		///<sumary>
		/// 类型
        ///</sumary>
        [DataMember]
		public string AREA_TYPE
		{
			get{return _area_type;}
			set{_area_type = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string AREA_CODE
		{
			get{return _area_code;}
			set{_area_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string AREA_NAME
		{
			get{return _area_name;}
			set{_area_name = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int AREA_ORDER
		{
			get{return _area_order;}
			set{_area_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string AREA_FLAG
		{
			get{return _area_flag;}
			set{_area_flag = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string AREA_REMARK
		{
			get{return _area_remark;}
			set{_area_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int AREA_GROUP
		{
			get{return _area_group;}
			set{_area_group = value;}
		}
	}
}
