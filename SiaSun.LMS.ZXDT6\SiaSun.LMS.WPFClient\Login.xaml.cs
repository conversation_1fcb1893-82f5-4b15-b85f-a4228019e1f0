﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// Login.xaml 的交互逻辑
    /// </summary>
    public partial class Login : Window
    {
        public static Login loginWin;

        public Login()
        {
            InitializeComponent();

            loginWin = this;

            this.Loaded += new RoutedEventHandler(Login_Loaded);
        }

        /// <summary>
        /// 窗体加载时设置焦点
        /// </summary>
        void Login_Loaded(object sender, RoutedEventArgs e)
        {
            //获得默认语言
            foreach (ComboBoxItem item in this.cmbLanguage.Items)
            {
                if (item.Tag.ToString() == MainApp._Language)
                {
                    this.cmbLanguage.SelectedItem = item;
                    break;
                }
            }

            //获得焦点
            Keyboard.Focus(this.txtName);
        }
        
        /// <summary>
        /// 回车确认
        /// </summary>
        private void txtPassword_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginValidate();
            }
        }
        
        /// <summary>
        /// 验证登陆
        /// </summary>
        private void LoginValidate()
        {
            SiaSun.LMS.Model.SYS_USER user = new SiaSun.LMS.Model.SYS_USER();

            try
            {
                this.Cursor = Cursors.Wait;
                if (MainApp.I_SystemService.USER_LOGIN(this.txtName.Text, Common.Encrypt.GetHashCode(this.txtPassword.Password), out user))
                {
                    this.Hide();
                    //记录选择语言
                    MainApp._Language = (cmbLanguage.SelectedItem as ComboBoxItem).Tag.ToString();
                    SiaSun.LMS.Common.AppSettings.SetValue("Language", MainApp._Language);
                    //记录用户
                    MainApp._USER = user;
                    //角色选择界面
                    SelectRole _Role_Select = new SelectRole();
                    _Role_Select.Owner = this;
                    if (_Role_Select.ShowDialog() == true)
                    {
                        MainWindow mainWin = new MainWindow();
                        if (mainWin.ShowDialog() == false)
                        {
                            mainWin.Close();
                            MainApp.Current.Shutdown();
                        }
                    }
                    else
                    {
                        this.Visibility = System.Windows.Visibility.Visible;
                        this.ShowDialog();
                    }
                }
                else
                {
                    MainApp._MessageDialog.Show("CheckLoginUser", null);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                this.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 检查是否可以点击按钮
        /// </summary>
        private void CommandBinding_CanExecute(object sender, CanExecuteRoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(txtName.Text) && !string.IsNullOrEmpty(txtPassword.Password))
            {
                e.CanExecute = true;
            }
        }


        /// <summary>
        /// 关闭
        /// </summary>
        private void lblClose_MouseUp(object sender, MouseButtonEventArgs e)
        {
            this.Close();
            MainApp.Current.Shutdown();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            this.txtName.Clear();
            this.txtPassword.Clear();

            this.Close();
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            LoginValidate();
        }
    }
}
