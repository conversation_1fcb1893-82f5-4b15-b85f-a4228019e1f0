﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.ServiceModel;
using System.Reflection;
using SiaSun.LMS.Enum;
using SiaSun.LMS.Interface;
using System.Xml;
using System.Collections;
using System.Text;
using SiaSun.LMS.Common;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, 
        InstanceContextMode = InstanceContextMode.Single, 
        ConcurrencyMode = ConcurrencyMode.Multiple, 
        MaxItemsInObjectGraph = int.MaxValue)] 
    public class S_System : I_System
    {
        static XmlDocument xmlDocMessage = null;

        #region [ROLE RELATION]

        /// <summary>
        /// 角色-获取列表
        /// </summary>
        /// <param name="USER_ID"></param>
        /// <returns></returns>
        public IList<SiaSun.LMS.Model.SYS_ROLE> ROLE_GetList(int USER_ID)
        {
            return S_Base.sBase.pSYS_ROLE.GetList(USER_ID);
        }

        /// <summary>
        /// 获得角色的窗体控件设置列表
        /// </summary>
        public IList<Model.SYS_ROLE_WINDOW> ROLE_WINDOW_GetList_ROLE_MENU(int ROLE_ID, int MENU_ID)
        {
            return S_Base.sBase.pSYS_ROLE_WINDOW.GetListRoleIDMenuID(ROLE_ID, MENU_ID);
        }

        /// <summary>
        /// 获得ROLE_WINDOW实例
        /// </summary>
        public Model.SYS_ROLE_WINDOW ROLE_WINDOW_GetModel_MENU_CONTROL(int ROLE_ID,int MENU_ID,string CONTROL_NAME)
        {
            return S_Base.sBase.pSYS_ROLE_WINDOW.GetModelRoleIDMenuIDControlName(ROLE_ID,MENU_ID, CONTROL_NAME);

        }

        /// <summary>
        /// 保存ROLE_WINDOW更改
        /// </summary>
        /// <returns></returns>
        public bool ROLE_WINDOW_Save(int ROLE_ID,int MENU_ID,IList<Model.SYS_ROLE_WINDOW> listROLE_WINDOW,out string strResult)
        {
            bool boolResult = true;
            strResult = string.Empty;
            try
            {
                if (listROLE_WINDOW.Count == 0)
                {
                    //删除记录
                    foreach (Model.SYS_ROLE_WINDOW mROLE_WINDOW in S_Base.sBase.pSYS_ROLE_WINDOW.GetListRoleIDMenuID(ROLE_ID, MENU_ID))
                    {
                        S_Base.sBase.pSYS_ROLE_WINDOW.Delete(mROLE_WINDOW.ROLE_WINDOW_ID);
                    }
                }
                else
                {
                    foreach (Model.SYS_ROLE_WINDOW roleWin in listROLE_WINDOW)
                    {
                        Model.SYS_ROLE_WINDOW mROLE_WINDOW = S_Base.sBase.pSYS_ROLE_WINDOW.GetModelRoleIDMenuIDControlName(ROLE_ID, roleWin.MENU_ID, roleWin.CONTROL_NAME);
                        //判断是否存在记录
                        if (mROLE_WINDOW == null)
                        {
                            S_Base.sBase.pSYS_ROLE_WINDOW.Add(roleWin);
                        }
                        else
                        {
                            roleWin.ROLE_WINDOW_ID = mROLE_WINDOW.ROLE_WINDOW_ID;
                            S_Base.sBase.pSYS_ROLE_WINDOW.Update(roleWin);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                boolResult = false;
                strResult = ex.Message;
            }
            return boolResult;
        }
        
        /// <summary>
        /// 用户-登录
        /// </summary>
        /// <param name="USER_CODE"></param>
        /// <param name="USER_PASSWORD"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER user)
        {
            try
            {
                user = S_Base.sBase.pSYS_USER.Login(USER_CODE, USER_PASSWORD);
            }
            catch (Exception ex)
            {
                user = null;
                S_Base.sBase.Log.Error("用户登录时发生异常", ex);
            }                       
            return user != null;

        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="USER_CODE"></param>
        /// <param name="USER_PASSWORD_OLD"></param>
        /// <param name="USER_PASSWORD_NEW"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool USER_PASSWORD(string USER_CODE, string USER_PASSWORD_OLD, string USER_PASSWORD_NEW, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.SYS_USER USER_PASSWORD = S_Base.sBase.pSYS_USER.GetModel(USER_CODE);
            if(USER_PASSWORD.USER_PASSWORD != USER_PASSWORD_OLD)
            {
                bResult = false;
                sResult = "旧密码错误";
                return bResult;
            }

            USER_PASSWORD.USER_PASSWORD = USER_PASSWORD_NEW;
            S_Base.sBase.pSYS_USER.Update(USER_PASSWORD);
            return bResult;
        }
        
        /// <summary>
        /// 获得编码列表
        /// </summary>
        /// <param name="ITEM_CODE">编码</param>
        public DataTable ITEM_LIST_GetDictionary(string ITEM_CODE)
        {
            using (DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT ITEM_LIST_CODE AS value,ITEM_LIST_NAME AS name FROM V_SYS_ITEM WHERE ITEM_CODE='{0}' and ITEM_LIST_FLAG = '1' order by item_list_order", ITEM_CODE)))
            {
                return dt;
            }

            //return null;
        }

        /// <summary>
        /// 根据ITEM_CODE获得列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_ITEM_LIST> ITEM_LIST_GetList_ITEM_CODE(string ITEM_CODE)
        {
            return S_Base.sBase.pSYS_ITEM_LIST.GetListItemCode(ITEM_CODE);
        }
        
        /// <summary>
        /// 根据菜单编码获得菜单实例
        /// </summary>
        public SiaSun.LMS.Model.SYS_MENU MENU_GetModel(int MENU_ID)
        {
            return S_Base.sBase.pSYS_MENU.GetModel(MENU_ID);
        }         

        /// <summary>
        /// 菜单-获取列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList()
        {
            IList<SiaSun.LMS.Model.SYS_MENU> lsSYS_MENU =null;

            lsSYS_MENU = S_Base.sBase.pSYS_MENU.GetList_MENU_PARAMETER();

            return lsSYS_MENU;
        }

        /// <summary>
        /// 菜单-获取列表
        /// </summary>
        /// <param name="ROLE_ID">角色编号</param>
        /// <param name="bSelect">是否选定</param>
        public IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList_ROLE_Select(int ROLE_ID, bool bSelect)
        {
            IList<SiaSun.LMS.Model.SYS_MENU> lsSYS_MENU =null;

            if (bSelect)
            {
                lsSYS_MENU = S_Base.sBase.pSYS_MENU.GetList_ROLE_ID(ROLE_ID);
            }
            else
            {
                lsSYS_MENU = S_Base.sBase.pSYS_MENU.GetList_ROLE_ID_NO(ROLE_ID);
            }

            return lsSYS_MENU;
        }
        
        /// <summary>
        /// 获得关系实例
        /// </summary>
        public Model.SYS_RELATION RELATION_GetModel(string RELATION_CODE)
        {
            return S_Base.sBase.pSYS_RELATION.GetModelRelationCode(RELATION_CODE);
        }
        
        /// <summary>
        /// 获得关系列表
        /// </summary>
        public IList<Model.SYS_RELATION_LIST> RELATION_LIST_GetList_ID1(int RELATION_ID, int RELATION_ID1)
        {
            return S_Base.sBase.pSYS_RELATION_LIST.GetListRelationIDRelaionID1(RELATION_ID, RELATION_ID1);
        }

        /// <summary>
        /// 添加关系
        /// </summary>
        public bool RELATION_LIST_Add(string RELATION_CODE, int RELATION_ID1, int[] List_RELATION_ID2, out string Result)
        {
            Result = string.Empty;
            SiaSun.LMS.Model.SYS_RELATION mSYS_RELATION = S_Base.sBase.pSYS_RELATION.GetModelRelationCode(RELATION_CODE);
            if (mSYS_RELATION == null)
            {
                Result = S_Base.sBase.sSystem.MessageConverter_GetKeyValue("Relation_CheckRelationExists", string.Format("RelationCode-{0}", RELATION_CODE));
                return false;
            }

            //获得选定的列表
            IList<SiaSun.LMS.Model.SYS_RELATION_LIST> list_RELATION_LIST = S_Base.sBase.pSYS_RELATION_LIST.GetListRelationIDRelaionID1(mSYS_RELATION.RELATION_ID, RELATION_ID1);

            //删除取消选定的关系
            foreach (SiaSun.LMS.Model.SYS_RELATION_LIST mSYS_RELATION_LIST in list_RELATION_LIST)
            {
                if (mSYS_RELATION_LIST.RELATION_ID1 == 0 && mSYS_RELATION_LIST.RELATION_ID2 == 0)
                    continue;
                if (!List_RELATION_ID2.Contains(mSYS_RELATION_LIST.RELATION_ID2))
                {
                    S_Base.sBase.pSYS_RELATION_LIST.Delete(mSYS_RELATION_LIST.RELATION_LIST_ID);
                }
            }

            //添加新的关系
            for (int i = 0; i < List_RELATION_ID2.Length; i++)
            {
                //检查是否重复
                SiaSun.LMS.Model.SYS_RELATION_LIST mSYS_RELATION_LIST = S_Base.sBase.pSYS_RELATION_LIST.GetModelRelationIDRelationID1RelationID2(mSYS_RELATION.RELATION_ID, RELATION_ID1, List_RELATION_ID2[i]);
                if (mSYS_RELATION_LIST == null)
                {
                    mSYS_RELATION_LIST = new SiaSun.LMS.Model.SYS_RELATION_LIST();
                    mSYS_RELATION_LIST.RELATION_ID = mSYS_RELATION.RELATION_ID;
                    mSYS_RELATION_LIST.RELATION_ID1 = RELATION_ID1;
                    mSYS_RELATION_LIST.RELATION_ID2 = List_RELATION_ID2[i];
                    mSYS_RELATION_LIST.RELATION_LIST_FLAG = 1;
                    S_Base.sBase.pSYS_RELATION_LIST.Add(mSYS_RELATION_LIST);
                }
            }

            return true;
        }

        /// <summary>
        /// 获得所有映射列表
        /// </summary>
        public IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList()
        {
            return S_Base.sBase.pSYS_TABLE_CONVERTER.GetList();
        }

        /// <summary>
        /// 根据表名获得所有映射列表
        /// </summary>
        public IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList_ConverterCode(string TABLE_CONVERTER_CODE)
        {
            return S_Base.sBase.pSYS_TABLE_CONVERTER.GetList_ConverterCode(TABLE_CONVERTER_CODE);
        }

        /// <summary>
        /// 根据CONVERTER_ID获得所有列表
        /// </summary>
        /// <returns></returns>
        public IList<Model.SYS_TABLE_CONVERTER_LIST> TABLE_CONVERTER_LIST_GetList_ConverterID(int TABLE_CONVERTER_ID)
        {
            return S_Base.sBase.pSYS_TABLE_CONVERTER_LIST.GetList_ConverterID(TABLE_CONVERTER_ID);
        }

        /// <summary>
        /// 根据数据导入模板导入数据
        /// </summary>
        public DataSet SYS_TABLE_CONVERTER_Import(string TABLE_CONVERTER_CODE, DataTable tableImport, out string strResult)
        {
            DataSet dsImport = new DataSet();
            strResult = string.Empty;

            //判断导入数据是否空
            if (tableImport.Rows.Count > 0)
            {
                try
                {
                    //获得转换列表
                    IList<Model.SYS_TABLE_CONVERTER> listTABLE_CONVERTER = S_Base.sBase.pSYS_TABLE_CONVERTER.GetList_ConverterCode(TABLE_CONVERTER_CODE);
                    if (listTABLE_CONVERTER.Count == 0)
                    {
                        strResult = string.Format("TABLE_CONVERTER_CODE={0} is not exists in SYS_TABLE_CONVERTER.", TABLE_CONVERTER_CODE);
                        return dsImport;
                    }

                    //获得转换信息实例
                    Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER = listTABLE_CONVERTER[0];
                    //获得转换信息列表
                    IList<Model.SYS_TABLE_CONVERTER_LIST> listTABLE_CONVERTER_LIST = S_Base.sBase.pSYS_TABLE_CONVERTER_LIST.GetList_ConverterID(mTABLE_CONVERTER.TABLE_CONVERTER_ID);
                    if (listTABLE_CONVERTER_LIST.Count == 0)
                    {
                        strResult = string.Format("There is no data in SYS_TABLE_CONVERTER_LIST.");
                        return dsImport;
                    }

                    //设置表名并添加到数据集中
                    DataTable tableParent = new DataTable(mTABLE_CONVERTER.PARENT_TABLE);
                    DataTable tableChild = new DataTable(mTABLE_CONVERTER.CHILD_TABLE);
                    dsImport.Tables.AddRange(new DataTable[] { tableParent, tableChild });

                    //遍历所有转换列表，构建父级表和子级表的结构
                    foreach (Model.SYS_TABLE_CONVERTER_LIST mTABLE_CONVERTER_LIST in listTABLE_CONVERTER_LIST)
                    {
                        //创建新列并设置列属性
                        DataColumn col = new DataColumn(mTABLE_CONVERTER_LIST.COLUMN_NAME);
                        col.Unique = mTABLE_CONVERTER_LIST.UNIQUE_FLAG == "1";
                        col.AllowDBNull = mTABLE_CONVERTER_LIST.ISNULL_FLAG == "1";

                        //获得父级表结构
                        if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && mTABLE_CONVERTER.PARENT_TABLE == mTABLE_CONVERTER_LIST.TABLE_NAME)
                        {
                            if (!tableParent.Columns.Contains(mTABLE_CONVERTER_LIST.COLUMN_NAME))
                            {
                                tableParent.Columns.Add(col);
                            }
                        }

                        //获得子级表结构  
                        if (!string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE) && mTABLE_CONVERTER.CHILD_TABLE == mTABLE_CONVERTER_LIST.TABLE_NAME)
                        {
                            if (!tableChild.Columns.Contains(mTABLE_CONVERTER_LIST.COLUMN_NAME))
                            {
                                tableChild.Columns.Add(col);
                            }
                        }
                    }

                    //遍历导入数据信息，向父级表和子级表中添加数据，当前只支持父级表中存在一条数据
                    //如果只有父级表，没有子级表
                    if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                    {
                        //导入父级表数据
                        TABLE_CONVERTER_ImportRow(false, tableImport, mTABLE_CONVERTER.PARENT_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableParent);
                    }

                    //如果存在父级表和子级表
                    if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && !string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                    {
                        //导入父级表--父级表中仅有一条记录
                        TABLE_CONVERTER_ImportRow(true, tableImport, mTABLE_CONVERTER.PARENT_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableParent);
                        //导入子级表
                        TABLE_CONVERTER_ImportRow(false, tableImport, mTABLE_CONVERTER.CHILD_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableChild);
                    }
                }
                catch (Exception ex)
                {
                    strResult = ex.Message;
                }
            }

            return dsImport;
        }

        /// <summary>
        /// 导入数据表
        /// </summary>
        private void TABLE_CONVERTER_ImportRow(bool IsOnlyOne, DataTable tableImport, string TABLE_NAME, Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, IList<Model.SYS_TABLE_CONVERTER_LIST> listTABLE_CONVERTER_LIST, ref DataTable tableAdd)
        {
            try
            {
                //获得拆分列信息
                //using (DataTable tableSplitProperty = S_Base.sBase.pSYS_SPLIT_PROPERTY.GetTable_TYPE_KEY(mTABLE_CONVERTER.SPLIT_PROPERTY_TYPE, mTABLE_CONVERTER.SPLIT_PROPERTY_KEY))
                //{
                    //遍历导入数据信息，向父级表和子级表中添加数据，当前只支持父级表中存在一条数据
                    foreach (DataRow rowImport in tableImport.Rows)
                    {
                        if (IsOnlyOne && tableAdd.Rows.Count > 0)
                            break;

                        bool boolResult = true;
                        DataRow rowAdd = tableAdd.NewRow();

                        //组合拆分列属性值
                        //if (!string.IsNullOrEmpty(mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN) && tableAdd.Columns.Contains(mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN))
                        //{
                        //    if (tableSplitProperty.Rows.Count > 0)
                        //    {
                        //        string strSplitPropertyValue = null;
                        //        IDictionary<string, string> dicSplitPropertyCodeValue = new Dictionary<string, string>();
                        //        //遍历拆分属性
                        //        foreach (DataRow rowSplit in tableSplitProperty.Rows)
                        //        {
                        //            if (rowSplit.IsNull("SYS_PROPERTY_NAME") || string.IsNullOrEmpty(rowSplit["SYS_PROPERTY_NAME"].ToString()) || rowSplit.IsNull("SYS_PROPERTY_CODE") || string.IsNullOrEmpty(rowSplit["SYS_PROPERTY_CODE"].ToString()))
                        //                continue;
                        //            //判断导入列是否存在
                        //            if (tableImport.Columns.Contains(rowSplit["SYS_PROPERTY_NAME"].ToString()))
                        //            {
                        //                dicSplitPropertyCodeValue.Add(rowSplit["SYS_PROPERTY_CODE"].ToString(), rowImport[rowSplit["SYS_PROPERTY_NAME"].ToString()].ToString());
                        //            }
                        //        }
                        //        //获得组合属性
                        //        strSplitPropertyValue = base.SYS_SPLIT_PROPERTY_GetPropertyValue(mTABLE_CONVERTER.SPLIT_PROPERTY_TYPE, mTABLE_CONVERTER.SPLIT_PROPERTY_KEY, dicSplitPropertyCodeValue);
                        //        rowAdd[mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN] = strSplitPropertyValue;
                        //    }
                        //}

                        //非拆分组合列
                        foreach (DataColumn colImport in tableImport.Columns)
                        {
                            //判断该列是否存在映射
                            if (listTABLE_CONVERTER_LIST.Count(r => r.CONVERT_COLUMN_NAME == colImport.ColumnName) > 0)
                            {
                                //获得映射列表实例
                                Model.SYS_TABLE_CONVERTER_LIST mTABLE_CONVERTER_LIST = listTABLE_CONVERTER_LIST.First(r => r.CONVERT_COLUMN_NAME == colImport.ColumnName);

                                //添加表数据
                                if (mTABLE_CONVERTER_LIST.TABLE_NAME == TABLE_NAME)
                                {
                                    //校验是否唯一值
                                    if (mTABLE_CONVERTER_LIST.UNIQUE_FLAG == "1")
                                    {
                                        //获得条件字符串
                                        string strExistsWhere = string.Format("{0}='{1}'", mTABLE_CONVERTER_LIST.COLUMN_NAME, rowImport[colImport].ToString());
                                        //判断数据集和数据库表中的值是否重复
                                        if (tableAdd.Select(strExistsWhere).Length > 0 /*|| base.Exist(TABLE_NAME, strExistsWhere)*/)
                                        {
                                            boolResult = false;
                                            break;
                                        }
                                    }

                                    //校验是否空值
                                    if (mTABLE_CONVERTER_LIST.ISNULL_FLAG == "0")
                                    {
                                        //判断值是否空
                                        if (rowImport.IsNull(colImport) || string.IsNullOrEmpty(rowImport[colImport].ToString()))
                                        {
                                            boolResult = false;
                                            break;
                                        }
                                    }

                                    //行数据赋值
                                    rowAdd[mTABLE_CONVERTER_LIST.COLUMN_NAME] = rowImport[colImport];
                                }
                            }
                        }

                        //判断执行结果
                        if (boolResult)
                        {
                            tableAdd.Rows.Add(rowAdd);
                        }
                    }
                //}
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 提交保存导入数据
        /// </summary>
        public int TABLE_CONVERTER_Save(Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, DataSet dsImport, out string strResult)
        {
            int intAffect = 0;
            strResult = string.Empty;

            //判断数据集合
            if (dsImport.Tables.Count > 0)
            {
                //如果只有父级表，没有子级表
                if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                {
                    //判断是否有记录
                    if (dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE].Rows.Count > 0)
                    {
                        //提交父级表
                        intAffect = S_Base.sBase.sDatabase.Save(dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE], mTABLE_CONVERTER.PARENT_TABLE);
                    }
                }

                //如果存在父级表和子级表
                if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && !string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                {
                    //父级表
                    using (DataTable tableParent = dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE])
                    {
                        //判断是否有记录
                        if (tableParent.Rows.Count > 0)
                        {
                            //提交父级表
                            intAffect = S_Base.sBase.sDatabase.Save(tableParent, mTABLE_CONVERTER.PARENT_TABLE);
                            if (intAffect > 0)
                            {
                                //子级表
                                using (DataTable tableChild = dsImport.Tables[mTABLE_CONVERTER.CHILD_TABLE])
                                {
                                    //判断子表是否存在数据
                                    if (tableChild.Rows.Count > 0)
                                    {
                                        //获得关键字段值
                                        object objKeyValue = tableParent.Rows[0][mTABLE_CONVERTER.PARENT_KEY];

                                        //设置子级表值
                                        foreach (DataRow rowChild in dsImport.Tables[mTABLE_CONVERTER.CHILD_TABLE].Rows)
                                        {
                                            rowChild[mTABLE_CONVERTER.CHILD_FOREIGN_KEY] = objKeyValue;
                                        }

                                        //提交子级表数据
                                        intAffect = S_Base.sBase.sDatabase.Save(tableChild, mTABLE_CONVERTER.CHILD_TABLE);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return intAffect;
        }

        #endregion [ROLE RELATION]


        #region [FLOW]

        /// <summary>获取流程参数
        /// 获取流程参数
        /// </summary>
        /// <param name="FLOW_TYPE_CODE">流程类型</param>
        /// <returns></returns>
        public IList<SiaSun.LMS.Model.FLOW_PARA> FlowGetParameters(string FLOW_TYPE_CODE)
        {
            return S_Base.sBase.pFLOW_PARA.GetListFlowTypeCode(FLOW_TYPE_CODE);
        }

        /// <summary>获取计划动作
        /// 获取计划动作
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <returns></returns>
        public DataTable PlanGetAction(string PLAN_ID)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT * FROM V_FLOW_PLAN_ACTION WHERE PLAN_ID = {0}", PLAN_ID));

            return dt;
        }

        /// <summary>获取计划动作
        /// 获取计划动作
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="ACTION_DEFAULT">默认动作</param>
        /// <returns></returns>
        public DataTable PlanGetAction(string PLAN_ID, string FLOW_ACTION_DEFAULT)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT * FROM V_FLOW_PLAN_ACTION WHERE FLOW_ACTION_DEFAULT ='{1}' AND PLAN_ID = {0}", PLAN_ID, FLOW_ACTION_DEFAULT));

            return dt;
        }

        /// <summary>获取任务动作
        /// 获取任务动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <returns></returns>
        public DataTable ManageGetAction(string MANAGE_ID)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT * FROM V_FLOW_MANAGE_ACTION WHERE MANAGE_ID = {0}", MANAGE_ID));

            return dt;
        }

        /// <summary>获取任务动作
        /// 获取任务动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <param name="ACTION_CODE">动作编码</param>
        /// <returns></returns>
        public DataTable ManageGetAction(string MANAGE_ID, string ACTION_CODE)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT * FROM V_FLOW_MANAGE_ACTION WHERE MANAGE_ID = {0} AND ACTION_CODE='{1}'", MANAGE_ID, ACTION_CODE));

            return dt;
        }

        /// <summary>获取控制动作
        /// 获取控制动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <returns></returns>
        public DataTable ControlGetAction(string MANAGE_ID)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("SELECT * FROM V_FLOW_CONTROL_ACTION WHERE MANAGE_ID = {0}", MANAGE_ID));

            return dt;
        }

        #endregion [FLOW]


        #region [GOODS]

        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsID(int GOODS_ID)
        {
            return S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_ID);
        }

        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCode(string GOODS_CODE)
        {
            return S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_CODE);
        }

        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCodeContract(string GOODS_CODE, string CONTRACT)
        {
            return S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_CODE, CONTRACT);
        }

        public SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassCode(string GOODS_CLASS_CODE)
        {
            return S_Base.sBase.pGOODS_CLASS.GetModel(GOODS_CLASS_CODE);
        }
        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassID(int GOODS_CLASS_ID)
        {
            return S_Base.sBase.pGOODS_CLASS.GetModel(GOODS_CLASS_ID);
        }

        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public IList<SiaSun.LMS.Model.GOODS_MAIN> GoodsGetListGoodsClassID(int GOODS_CLASS_ID)
        {
            return S_Base.sBase.pGOODS_MAIN.GetListGoodsClassID(GOODS_CLASS_ID);
        }

        public IList<SiaSun.LMS.Model.GOODS_PROPERTY> GoodsPropertyGetListGoodsTypeID(int GOODS_TYPE_ID)
        {
            return S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(GOODS_TYPE_ID);
        }


        public SiaSun.LMS.Model.GOODS_TEMPLATE GoodsTemplateGetModel(int TEMPLATE_ID)
        {
            return S_Base.sBase.pGOODS_TEMPLATE.GetModel(TEMPLATE_ID);
        }

        public IList<SiaSun.LMS.Model.GOODS_TEMPLATE> GoodsTemplateGetList(int GOODS_ID)
        {
            return S_Base.sBase.pGOODS_TEMPLATE.GetList(GOODS_ID);
        }

        public IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> GoodsTemplateListGetList(int TEMPLATE_ID)
        {
            return S_Base.sBase.pGOODS_TEMPLATE_LIST.GetList(TEMPLATE_ID);
        }


        #endregion [GOODS]


        #region [CELL]

        /// <summary>获得库房
        /// 获得库房
        /// </summary>
        /// <param name="WAREHOUSE_CODE">库房编码</param>
        /// <returns>返回值</returns>
        public DataTable WAREHOUSE_GetList(int USER_ID, string WAREHOUSE_TYPE)
        {
            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format(@"SELECT WAREHOUSE_ID,WAREHOUSE_NAME FROM WH_WAREHOUSE 
                                                        WHERE {0} {1}",
                                                        USER_ID >= 0 ? "1=1" : string.Format(@"(WAREHOUSE_ID IN(SELECT RELATION_ID2 
                                                        FROM V_SYS_RELATION WHERE RELATION_CODE='SYS_USER-WH_WAREHOUSE'
                                                        AND RELATION_ID1= {0}))", USER_ID),
                                                        WAREHOUSE_TYPE == string.Empty ? string.Empty : string.Format("AND WAREHOUSE_TYPE = '{0}'", WAREHOUSE_TYPE)));

            return dt;
        }

        /// <summary>获得库区
        /// 获得库区
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_TYPE">库区类型</param>
        /// <returns>返回值</returns>
        public DataTable AREA_GetList(int WAREHOUSE_ID, string AREA_TYPE)
        {
            string sSQL = "SELECT AREA_ID,AREA_CODE,AREA_NAME,AREA_TYPE FROM WH_AREA WHERE 1=1 {0} {1}";

            string areaTypeSql = string.IsNullOrEmpty(AREA_TYPE) ? string.Empty : string.Format(" AND AREA_TYPE = '{0}'", AREA_TYPE);

            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format(sSQL,
                //(0 != WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                areaTypeSql));

            return dt;
        }

        /// <summary>获得排
        /// 获得排
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_ID">库区编号</param>
        /// <param name="LOGIC_ID">存储区编号</param>
        /// <param name="CELL_TYPE">货位类型</param>
        /// <param name="CELL_INOUT">出入库方向</param>
        /// <returns></returns>
        public DataTable CELL_Z_GetList(int WAREHOUSE_ID)
        {
            string sSQL = "SELECT * FROM V_WH_CELL_Z WHERE 1=1 {0} ORDER BY CELL_Z";

            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format(sSQL,
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString())
                ));

            return dt;
        }

        /// <summary>获得排
        /// 获得排
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_ID">库区编号</param>
        /// <param name="LOGIC_ID">存储区编号</param>
        /// <param name="CELL_TYPE">货位类型</param>
        /// <param name="CELL_INOUT">出入库方向</param>
        /// <returns></returns>
        public DataTable CELL_Z_GetList_AREA(int WAREHOUSE_ID, int AREA_ID)
        {
            string sSQL = "SELECT * FROM V_WH_CELL_Z WHERE 1=1 {0} {1} ORDER BY CELL_Z";

            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format(sSQL,
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                (0 == AREA_ID) ? string.Empty : string.Format(" AND AREA_ID = {0}", AREA_ID.ToString())
                ));

            return dt;
        }

        public IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z(int WAREHOUSE_ID, string CELL_Z)
        {
            return S_Base.sBase.pWH_CELL.CELL_GetList_Z(WAREHOUSE_ID, CELL_Z);
        }

        public IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z_ByArea(int AREA_ID, string CELL_Z)
        {
            return S_Base.sBase.pWH_CELL.CELL_GetList(AREA_ID.ToString(), CELL_Z);
        }

        public bool CellInit()
        {
            bool bResult = true;

            //直接return，防止误操作
            //return bResult;

            try
            {
                IList<SiaSun.LMS.Model.WH_DESCRIPTION> lsWH_DESCRIPTION = S_Base.sBase.pWH_DESCRIPTION.GetList();

                foreach (SiaSun.LMS.Model.WH_DESCRIPTION mWH_DESCRIPTION in lsWH_DESCRIPTION.Where(r => r.DESCRIPTION_FLAG == "1").OrderBy(r => r.DESCRIPTION_ID))
                {
                    if (mWH_DESCRIPTION.CELL_TYPE == SiaSun.LMS.Enum.CELL_TYPE.Cell.ToString())
                    {
                        CellCreate(mWH_DESCRIPTION.WAREHOUSE_ID,
                                                  mWH_DESCRIPTION.AREA_ID,
                                                  mWH_DESCRIPTION.LOGIC_ID,
                                                  mWH_DESCRIPTION.START_Z,
                                                  mWH_DESCRIPTION.END_Z,
                                                  mWH_DESCRIPTION.START_X,
                                                  mWH_DESCRIPTION.END_X,
                                                  mWH_DESCRIPTION.START_Y,
                                                  mWH_DESCRIPTION.END_Y,
                                                  mWH_DESCRIPTION.DEVICE_CODE,
                                                  mWH_DESCRIPTION.LANE_WAY,
                                                  mWH_DESCRIPTION.SHELF_TYPE,
                                                  mWH_DESCRIPTION.SHELF_NEIGHBOUR,
                                                  mWH_DESCRIPTION.CELL_MODEL,
                                                  mWH_DESCRIPTION.CELL_LOGICAL_NAME,
                                                  mWH_DESCRIPTION.CELL_INOUT,
                                                  mWH_DESCRIPTION.CELL_TYPE,
                                                  mWH_DESCRIPTION.CELL_STORAGE_TYPE,
                                                  mWH_DESCRIPTION.CELL_FORK_TYPE,
                                                  Convert.ToInt32(mWH_DESCRIPTION.CELL_FORK_COUNT),
                                                  mWH_DESCRIPTION.CELL_WIDTH,
                                                  mWH_DESCRIPTION.CELL_HEIGHT
                                                  );
                    }
                    else
                    {
                        StationCreate(
                                         mWH_DESCRIPTION.WAREHOUSE_ID,
                                         mWH_DESCRIPTION.AREA_ID,
                                         mWH_DESCRIPTION.LOGIC_ID,
                                         mWH_DESCRIPTION.DEVICE_CODE,
                                         mWH_DESCRIPTION.DEVICE_NAME,
                                         mWH_DESCRIPTION.LANE_WAY,
                                         mWH_DESCRIPTION.SHELF_TYPE,
                                         mWH_DESCRIPTION.SHELF_NEIGHBOUR,
                                         mWH_DESCRIPTION.CELL_MODEL,
                                         mWH_DESCRIPTION.CELL_LOGICAL_NAME,
                                         mWH_DESCRIPTION.CELL_INOUT,
                                         mWH_DESCRIPTION.CELL_TYPE,
                                         mWH_DESCRIPTION.CELL_STORAGE_TYPE,
                                         mWH_DESCRIPTION.CELL_FORK_TYPE
                                         );
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
            }


            return bResult;
        }

        /// <summary>
        /// 新建货位
        /// </summary>
        private void CellCreate(int WAREHOUSE_ID, int AREA_ID, int LOGIC_ID, int z_begin, int z_end, int x_begin, int x_end, int y_begin, int y_end, string DEVICE_CODE, string LANE_WAY, string SHELF_TYPE, string SHELF_NEIGHBOUR, string CELL_MODEL, string CELL_LOGICAL_NAME, string CELL_INOUT, string CELL_TYPE, string CELL_STORAGE_TYPE, string CELL_FORK_TYPE, int CELL_FORK_COUNT, int CELL_WIDTH, int CELL_HEIGHT)
        {
            SiaSun.LMS.Model.WH_CELL mST_CELL = new SiaSun.LMS.Model.WH_CELL();

            for (int z = z_begin; z <= z_end; z++)
            {
                for (int x = x_begin; x <= x_end; x++)
                {
                    for (int y = y_begin; y <= y_end; y++)
                    {
                        mST_CELL.WAREHOUSE_ID = WAREHOUSE_ID;
                        mST_CELL.AREA_ID = AREA_ID;
                        mST_CELL.LOGIC_ID = LOGIC_ID;
                        mST_CELL.CELL_CODE = z.ToString().PadLeft(2, '0') + "-" + x.ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');
                        mST_CELL.CELL_NAME = z.ToString().PadLeft(2, '0') + "排" + x.ToString().PadLeft(2, '0') + "列" + y.ToString().PadLeft(2, '0') + "层";
                        mST_CELL.LANE_WAY = LANE_WAY;
                        mST_CELL.DEVICE_CODE = DEVICE_CODE;
                        mST_CELL.SHELF_TYPE = SHELF_TYPE;
                        mST_CELL.SHELF_NEIGHBOUR = SHELF_NEIGHBOUR;
                        mST_CELL.CELL_MODEL = CELL_MODEL;
                        mST_CELL.CELL_LOGICAL_NAME = CELL_LOGICAL_NAME;
                        mST_CELL.CELL_INOUT = CELL_INOUT;
                        mST_CELL.CELL_TYPE = CELL_TYPE;
                        mST_CELL.CELL_STORAGE_TYPE = CELL_STORAGE_TYPE;
                        mST_CELL.CELL_FORK_TYPE = CELL_FORK_TYPE;
                        mST_CELL.CELL_Z = z;
                        mST_CELL.CELL_X = x;
                        mST_CELL.CELL_Y = y;
                        mST_CELL.CELL_STATUS = SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString();
                        mST_CELL.RUN_STATUS = SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString();
                        mST_CELL.CELL_WIDTH = CELL_WIDTH;
                        mST_CELL.CELL_HEIGHT = CELL_HEIGHT;
                        mST_CELL.CELL_FLAG = "1";

                        //mST_CELL.CELL_GROUP = z.ToString().PadLeft(2, '0') + "-" + (x % CELL_FORK_COUNT == 0 ? (x / CELL_FORK_COUNT) : (x / CELL_FORK_COUNT) + 1).ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');

                        S_Base.sBase.pWH_CELL.Add(mST_CELL);
                    }
                }
            }
        }

        /// <summary>
        /// 新建站台
        /// </summary>
        private void StationCreate(int WAREHOUSE_ID, int AREA_ID, int LOGIC_ID, string DEVICE_CODE, string DEVICE_NAME, string LANE_WAY, string SHELF_TYPE, string SHELF_NEIGHBOUR, string CELL_MODEL, string CELL_LOGICAL_NAME, string CELL_INOUT, string CELL_TYPE, string CELL_STORAGE_TYPE, string CELL_FORK_TYPE)
        {
            SiaSun.LMS.Model.WH_CELL mST_CELL = new SiaSun.LMS.Model.WH_CELL();

            mST_CELL.WAREHOUSE_ID = WAREHOUSE_ID;
            mST_CELL.AREA_ID = AREA_ID;
            mST_CELL.LOGIC_ID = LOGIC_ID;
            mST_CELL.CELL_CODE = DEVICE_CODE;
            mST_CELL.CELL_NAME = DEVICE_NAME;
            mST_CELL.LANE_WAY = LANE_WAY;
            mST_CELL.DEVICE_CODE = DEVICE_CODE;
            mST_CELL.SHELF_TYPE = SHELF_TYPE;
            mST_CELL.SHELF_NEIGHBOUR = SHELF_NEIGHBOUR;
            mST_CELL.CELL_MODEL = CELL_MODEL;
            mST_CELL.CELL_LOGICAL_NAME = CELL_LOGICAL_NAME;
            mST_CELL.CELL_INOUT = CELL_INOUT;
            mST_CELL.CELL_TYPE = CELL_TYPE;
            mST_CELL.CELL_STORAGE_TYPE = CELL_STORAGE_TYPE;
            mST_CELL.CELL_FORK_TYPE = CELL_FORK_TYPE;
            mST_CELL.CELL_Z = 0;
            mST_CELL.CELL_X = 0;
            mST_CELL.CELL_Y = 0;
            mST_CELL.CELL_STATUS = SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString();
            mST_CELL.RUN_STATUS = SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString();
            mST_CELL.CELL_FLAG = "1";

            S_Base.sBase.pWH_CELL.Add(mST_CELL);
        }



        #endregion [CELL]


        /// <summary>
        /// 执行指定类型的指定方法
        /// </summary>
        public bool DynamicInvoke(string classType, string methodName, out string message, params object[] methodParams)
        {
            message = string.Empty;

            string classFullName = $"SiaSun.LMS.Implement.{classType}";
            Assembly assembly = base.GetType().Assembly;
            object otargetObject = assembly.CreateInstance(classFullName);
            Type type = assembly.GetType(classFullName);

            object[] combineParams = new object[methodParams.Length + 1];
            methodParams.CopyTo(combineParams, 0);
            combineParams[methodParams.Length] = message;

            //注意：所有参数必须是string类型传入
            bool result = (bool)type.InvokeMember(methodName, BindingFlags.InvokeMethod | BindingFlags.Default, null, otargetObject, combineParams);
            //bool result = (bool)type.InvokeMember(methodName, BindingFlags.InvokeMethod , null, otargetObject, combineParams);

            message = combineParams.Last().ToString();
            return result;
        }

        /// <summary>
        /// 生成系统日志
        /// </summary>
        public void CreateLog(string clientIp, LogLevel level, string logClass, string method, LogType type, string logOperator, string message, Exception exception)
        {
            try
            {
                message = message.Replace("\n", "").Replace(" ", "");

                int offsetBase = 39000;
                int offsetTimes = message.Length / offsetBase;
                offsetTimes = message.Length % offsetBase == 0 ? offsetTimes - 1 : offsetTimes;

                bool multiPart = offsetTimes > 0 ? true : false;

                while (offsetTimes >= 0)
                {
                    int offset = offsetBase * offsetTimes;

                    string messagePart = message.Substring(offset, message.Length - offset > offsetBase ? offsetBase : message.Length - offset).TrimEnd();
                    if (multiPart)
                    {
                        messagePart = string.Format("Part.{0}-{1}", offsetTimes + 1, messagePart);
                    }

                    string sqlString = string.Format(@"insert into SYS_LOG (LOG_ID, LOG_DATE, LOG_THREAD, LOG_LEVEL, LOG_CLASS, LOG_METHOD, LOG_LOGGER, LOG_TYPE, LOG_OPERATOR, LOG_MESSAGE, LOG_EXCEPTION) 
                                                       values (SYS_LOG_SEQ.nextval, '{0}' , '{1}' , '{2}' , '{3}' , '{4}' , '{5}' , '{6}' , '{7}' , '{8}' , '{9}')",
                                                       DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                                       clientIp,
                                                       level.ToString(),
                                                       logClass,
                                                       method,
                                                       "Custom",
                                                       type.ToString(),
                                                       logOperator,
                                                       message,
                                                       exception == null ? "" : exception.ToString());

                    int refCount = S_Base.sBase.sDatabase.ExecuteNonQuery(sqlString);

                    if (refCount < 1)
                    {
                        S_Base.sBase.Log.WarnFormat("自定义写入日志失败_类[{0}]_方法[{1}]", logClass, method);
                    }

                    offsetTimes--;
                }
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error(string.Format("自定义日志方法发生异常_类[{0}]_方法[{1}]", logClass, method), ex);
            }
        }

        /// <summary>
        /// 查找系统参数，预设默认值
        /// </summary>
        public string GetSysParameter(string paraKey, string paraDefault)
        {
            string result = string.Empty;
            if (!GetSysParameter(paraKey, out result))
            {
                result = paraDefault;
            }
            return result;
        }

        /// <summary>
        /// 根据关键字获得信息描述
        /// </summary>
        public string MessageConverter_GetKeyValue(string Key, params object[] Param)
        {
            string strMessage = null;
            try
            {
                string strLanguge = SiaSun.LMS.Common.AppSettings.GetValue("Language");
                if (xmlDocMessage == null)
                {
                    xmlDocMessage = MessageConverter_GetXmlDoc();
                }

                System.Xml.XmlNode xmlNode = xmlDocMessage.SelectSingleNode(string.Format("MessageDictionary/Message[@Key='{0}']", Key));
                if (xmlNode != null)
                {
                    //获得消息值
                    strMessage = string.Format(xmlNode.Attributes[strLanguge].Value.ToString(), (Param == null || Param.Length == 0 ? new object[] { string.Empty } : Param));
                }
            }
            catch
            {
                strMessage = Key;
            }
            return string.IsNullOrEmpty(strMessage) ? Key : strMessage;
        }

        /// <summary>
        /// 复制GOODS_PROPERTY
        /// 2020-04-01 10:56
        /// </summary>
        public bool CloneGoodsProperty(int goodsId, object target, object source, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                int goodsClassId = 0;

                if (goodsId > 0)
                {
                    Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                    if (mGOODS_MAIN == null)
                    {
                        result = false;
                        message = string.Format("未找到物料信息_物料ID[{0}]", goodsId);
                        return result;
                    }
                    goodsClassId = mGOODS_MAIN.GOODS_CLASS_ID;
                }
                else
                {
                    goodsClassId = 1;
                }

                Model.GOODS_CLASS mGOODS_CLASS = S_Base.sBase.pGOODS_CLASS.GetModel(goodsClassId);
                if (mGOODS_CLASS == null)
                {
                    result = false;
                    message = string.Format("未找到物料类别_物料ID[{0}]", goodsId);
                    return result;
                }

                PropertyInfo[] sourcePropertyArray = source.GetType().GetProperties();
                PropertyInfo[] targetPropertyArray = target.GetType().GetProperties();

                IList<Model.GOODS_PROPERTY> lsGOODS_PROPERTY = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);
                foreach (var mGOODS_PROPERTY in lsGOODS_PROPERTY)
                {
                    var sourceProperty = sourcePropertyArray.FirstOrDefault(r => r.Name.ToLower() == mGOODS_PROPERTY.GOODS_PROPERTY_FIELD.ToLower());
                    if (sourceProperty == null)
                    {
                        sourceProperty = sourcePropertyArray.FirstOrDefault(r => r.Name.ToLower() == mGOODS_PROPERTY.GOODS_PROPERTY_CODE.ToLower());
                    }
                    var targetProperty = targetPropertyArray.FirstOrDefault(r => r.Name.ToLower() == mGOODS_PROPERTY.GOODS_PROPERTY_FIELD.ToLower() || r.Name.ToLower() == mGOODS_PROPERTY.GOODS_PROPERTY_CODE.ToLower());

                    if (sourceProperty != null && targetProperty != null)
                    {
                        var valueString = sourceProperty.GetValue(source, null) != null ? sourceProperty.GetValue(source, null).ToString() : string.Empty;
                        if (!string.IsNullOrEmpty(valueString))
                        {
                            targetProperty.SetValue(target, valueString, null);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"复制物料属性时异常_信息[{ex.Message}]";
            }

            return result;
        }

        /// <summary>
        /// 根据物料属性的编码获取在数据库中存储的字段名称
        /// 2020-05-08 09:44:23
        /// </summary>
        /// <param name="goodsId"></param>
        /// <param name="propertyCode"></param>
        /// <returns></returns>
        internal string GetPropertyFieldColumnName(int goodsId, string propertyCode)
        {
            int goodsClassId = 0;

            if (goodsId > 0)
            {
                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                if (goodsMain == null)
                {
                    return null;
                }
                goodsClassId = goodsMain.GOODS_CLASS_ID;
            }
            else
            {
                goodsClassId = 1;
            }

            Model.GOODS_CLASS goodsClass = S_Base.sBase.pGOODS_CLASS.GetModel(goodsClassId);
            if (goodsClass == null)
            {
                return null;
            }

            IList<Model.GOODS_PROPERTY> lsGOODS_PROPERTY = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(goodsClass.GOODS_TYPE_ID);
            if (lsGOODS_PROPERTY != null && lsGOODS_PROPERTY.Count > 0)
            {
                var target = lsGOODS_PROPERTY.FirstOrDefault(r => r.GOODS_PROPERTY_CODE == propertyCode);
                if(target !=null)
                {
                    return target.GOODS_PROPERTY_FIELD;
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 根据物料属性编码获取属性值 2020-07-24 13:38:51
        /// </summary>
        internal string GetPropertyValue(int goodsId, object source, string propertyCode)
        {
            var goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
            if (goodsMain == null)
            {
                return null;
            }
            var propertyColumnName = this.GetPropertyFieldColumnName(goodsId, propertyCode);
            if (string.IsNullOrEmpty(propertyColumnName))
            {
                return null;
            }
            var sourcePropertyArray = source.GetType().GetProperties();
            if (sourcePropertyArray == null || sourcePropertyArray.Count() < 1)
            {
                return null;
            }
            var sourceProperty = sourcePropertyArray.FirstOrDefault(r => r.Name.ToLower() == propertyColumnName.ToLower());
            if (sourceProperty == null)
            {
                return null;
            }

            var result = sourceProperty.GetValue(source, null);
            if(result != null)
            {
                return result.ToString();
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 根据物料属性编码设置属性值 2020-12-01 20:50:10
        /// </summary>
        internal T SetPropertyValue<T>(int goodsId, T target, string propertyCode, string propertyValue, out string message) where T:class
        {
            message = string.Empty;

            try
            {
                var goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                if (goodsMain == null)
                {
                    message = $"未找到ID为[{goodsId}]的物料信息";
                    return null;
                }
                var propertyColumnName = this.GetPropertyFieldColumnName(goodsId, propertyCode);
                if (string.IsNullOrEmpty(propertyColumnName))
                {
                    message = $"未找到物料ID[{goodsId}]的属性列名[{propertyCode}]";
                    return null;
                }
                var targetPropertyArray = target.GetType().GetProperties();
                if (targetPropertyArray == null || targetPropertyArray.Count() < 1)
                {
                    message = $"目标对象[{target.ToString()}]的反射属性列表为空";
                    return null;
                }
                var targetProperty = targetPropertyArray.FirstOrDefault(r => r.Name.ToLower() == propertyColumnName.ToLower());
                if (targetProperty == null)
                {
                    message = $"未找到目标对象[{target.ToString()}]的[{propertyColumnName}]属性";
                    return null;
                }

                targetProperty.SetValue(target, propertyValue, null);
            }
            catch(Exception ex)
            {
                target = null;
                message = $"异常_信息[{ex.Message}]";
            }

            return target;
        }

        /// <summary>
        /// 比较两个对象各个属性的是否相等，相等返回true否则false
        /// 2021-01-11 14:58:03
        /// </summary>
        /// <param name="goodsId"></param>
        /// <param name="lSource"></param>
        /// <param name="rSource"></param>
        /// <returns></returns>
        internal bool ComparePropertyValue(int goodsId, object lSource, object rSource,string skipList = "")
        {
            int goodsClassId = 0;

            if (goodsId > 0)
            {
                Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                if (mGOODS_MAIN == null)
                {
                    return false;
                }
                goodsClassId = mGOODS_MAIN.GOODS_CLASS_ID;
            }
            else
            {
                goodsClassId = 1;
            }

            Model.GOODS_CLASS mGOODS_CLASS = S_Base.sBase.pGOODS_CLASS.GetModel(goodsClassId);
            if (mGOODS_CLASS == null)
            {
                return false;
            }

            PropertyInfo[] lPropertyArray = lSource.GetType().GetProperties();
            PropertyInfo[] rPropertyArray = rSource.GetType().GetProperties();

            IList<Model.GOODS_PROPERTY> goodsPropertys = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);
            foreach (var goodsProperty in goodsPropertys)
            {
                if (skipList.Contains(goodsProperty.GOODS_PROPERTY_FIELD) || skipList.Contains(goodsProperty.GOODS_PROPERTY_CODE))
                {
                    continue;
                }

                var lProperty = lPropertyArray.FirstOrDefault(r => r.Name.ToLower() == goodsProperty.GOODS_PROPERTY_FIELD.ToLower());
                var rProperty = rPropertyArray.FirstOrDefault(r => r.Name.ToLower() == goodsProperty.GOODS_PROPERTY_FIELD.ToLower());

                if ((lProperty != null && rProperty == null) || (lProperty == null && rProperty != null))
                {
                    return false;
                }

                if (lProperty != null && rProperty != null)
                {
                    var lValue = lProperty.GetValue(lSource, null);
                    var rValue = rProperty.GetValue(rSource, null);

                    if ((lValue == null && rValue != null) || (lValue != null && rValue == null) ||
                        ((lValue != null && rValue != null) && lValue.ToString() != rValue.ToString()))
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 返回动态类型的类的实例
        /// </summary>
        /// <param name="classType"></param>
        /// <returns></returns>
        internal dynamic DynamicInvoke(string classType)
        {
            string classFullName = $"SiaSun.LMS.Implement.{classType}";
            Assembly assembly = this.GetType().Assembly;
            dynamic targetObject = assembly.CreateInstance(classFullName);
            return targetObject;
        }

        /// <summary>
        /// 获取系统参数
        /// </summary>
        private Dictionary<string, string> GetSysParameter()
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();

            DataTable dt = S_Base.sBase.sDatabase.GetList(string.Format("select * from SYS_PARAMETER where PARAMETER_FLAG='1'"));
            foreach (DataRow dr in dt.Rows)
            {
                dicResult.Add(dr["PARAMETER_KEY"].ToString(), dr["PARAMETER_VALUE"].ToString());
            }

            if (dicResult == null || dicResult.Count == 0)
            {
                return null;
            }
            else
            {
                return dicResult;
            }
        }

        /// <summary>
        /// 根据编码查找系统参数
        /// </summary>
        public bool GetSysParameter(string paraKey, out string paraValue)
        {
            bool bResult = true;
            paraValue = string.Empty;

            try
            {
                DataTable dtPara = S_Base.sBase.sDatabase.GetList(string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY='{0}' and PARAMETER_FLAG='1'", paraKey));
                if (dtPara != null && dtPara.Rows.Count == 1)
                {
                    paraValue = dtPara.Rows[0]["PARAMETER_VALUE"].ToString();
                }
                else
                {
                    bResult = false;
                    paraValue = string.Format("S_SystemService.GetSysParameter:未能获取数据_key[{0}]", paraKey);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                paraValue = string.Format("S_SystemService.GetSysParameter:获取系统参数异常_信息[{0}]", ex.Message);
            }

            return bResult;
        }

        /// <summary>
        /// 根据编码查找系统参数
        /// </summary>
        private bool GetSysParameterUpdateTime(string paraKey, out string paraUpdateItem)
        {
            bool bResult = true;
            paraUpdateItem = string.Empty;

            try
            {
                DataTable dtPara = S_Base.sBase.sDatabase.GetList(string.Format("select UPDATE_TIME from SYS_PARAMETER where PARAMETER_KEY='{0}'", paraKey));
                if (dtPara != null && dtPara.Rows.Count == 1)
                {
                    paraUpdateItem = dtPara.Rows[0]["UPDATE_TIME"].ToString();
                }
                else
                {
                    bResult = false;
                    paraUpdateItem = string.Format("S_SystemService.GetSysParameterUpdateTime:未能获取数据_key[{0}]", paraKey);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                paraUpdateItem = string.Format("S_SystemService.GetSysParameterUpdateTime:获取系统参数更新时间异常_信息[{0}]", ex.Message);
            }

            return bResult;
        }

        /// <summary>
        /// 根据编码更新系统参数
        /// </summary>
        private bool UpdateSysParameter(string paraKey, string paraValue)
        {
            try
            {
                if (string.IsNullOrEmpty(paraKey) || string.IsNullOrEmpty(paraValue))
                {
                    return false;
                }

                string updateSql = string.Format("update SYS_PARAMETER set PARAMETER_VALUE = '{0}' ,UPDATE_TIME = '{2}' where PARAMETER_KEY = '{1}' ", paraValue, paraKey, Common.StringUtil.GetDateTime());

                int refCount = S_Base.sBase.sDatabase.ExecuteNonQuery(updateSql);

                return refCount > 0;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// 根据关键字获得信息描述
        /// </summary>
        private XmlDocument MessageConverter_GetXmlDoc()
        {
            string strMessageXmlFile = AppDomain.CurrentDomain.BaseDirectory + string.Format(@"Files\MessageDictionary.xml");
            return new Common.XmlFiles(strMessageXmlFile);

        }

        /// <summary>
        /// 数据库物料属性动态赋值
        /// </summary>
        internal bool GoodsPropertySetValue<T>(int GOODS_ID, T objTarget, object objSource, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_ID);
            if (mGOODS_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未找到物料信息_物料ID[{0}]", GOODS_ID);
                return bResult;
            }

            SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = S_Base.sBase.pGOODS_CLASS.GetModel(mGOODS_MAIN.GOODS_CLASS_ID);

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);

            string GOODS_PROPERTY_CODE = string.Empty;

            string GOODS_PROPERTY_SOURCE = string.Empty;

            string GOODS_PROPERTY_VALUE = string.Empty;

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                try
                {
                    GOODS_PROPERTY_CODE = mGOODS_PROPERTY.GOODS_PROPERTY_FIELD;

                    GOODS_PROPERTY_SOURCE = mGOODS_PROPERTY.GOODS_PROPERTY_CODE;

                    if (string.IsNullOrEmpty(GOODS_PROPERTY_CODE))
                    {
                        continue;
                    }

                    PropertyInfo[] propertys_source = objSource.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys_source)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE, StringComparison.CurrentCultureIgnoreCase))
                        {
                            GOODS_PROPERTY_VALUE = pi.GetValue(objSource, null) == null ? string.Empty : pi.GetValue(objSource, null).ToString();
                        }
                    }

                    if (string.IsNullOrEmpty(GOODS_PROPERTY_VALUE))
                    {
                        continue;
                    }
                    PropertyInfo[] propertys_in = objTarget.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys_in)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE, StringComparison.CurrentCultureIgnoreCase))
                        {
                            pi.SetValue(objTarget, GOODS_PROPERTY_VALUE, null);
                        }
                    }
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.StackTrace;
                }
            }

            return bResult;
        }

        /// <summary>
        /// 获取物料属性字典
        /// </summary>
        internal bool GoodsPropertyGetHashtable(int GOODS_ID, object obj, out Hashtable htResult, out string sResult)
        {
            bool bResult = true;
            htResult = null;
            sResult = string.Empty;

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_ID);

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY =
                S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(S_Base.sBase.pGOODS_CLASS.GetModel(mGOODS_MAIN.GOODS_CLASS_ID).GOODS_TYPE_ID);

            string GOODS_PROPERTY_KEY = string.Empty;
            string GOODS_PROPERTY_VALUE = string.Empty;

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                try
                {
                    GOODS_PROPERTY_KEY = mGOODS_PROPERTY.GOODS_PROPERTY_FIELD;

                    if (string.IsNullOrEmpty(GOODS_PROPERTY_KEY))
                    {
                        continue;
                    }

                    PropertyInfo[] propertys = obj.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_KEY, StringComparison.CurrentCultureIgnoreCase))
                        {
                            object objGOODS_PROPERTY_VALUE = pi.GetValue(obj, null);

                            if (null == objGOODS_PROPERTY_VALUE)
                            {
                                break;
                            }

                            GOODS_PROPERTY_VALUE = objGOODS_PROPERTY_VALUE.ToString();

                            if (htResult == null)
                            {
                                htResult = new Hashtable();
                            }

                            htResult.Add(GOODS_PROPERTY_KEY, GOODS_PROPERTY_VALUE);
                        }
                    }
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.Message + "||" + ex.StackTrace;
                }
                finally
                {
                    // bResult = bResult && htResult != null;
                }
            }

            return bResult;
        }

        /// <summary>
        /// 获得属性SQL语句
        /// </summary>
        internal string GetGoodsPropertySql(Hashtable htGOODS_PROPERTY)
        {
            string sGOODS_PROPERTY = string.Empty;

            if (null != htGOODS_PROPERTY)
            {
                foreach (DictionaryEntry deGOODS_PROPERTY in htGOODS_PROPERTY)
                {
                    if (null == deGOODS_PROPERTY.Value)
                    {
                        continue;
                    }

                    if (string.IsNullOrEmpty(deGOODS_PROPERTY.Value.ToString()))
                    {
                        sGOODS_PROPERTY += string.Format(" AND ({0} is null or {0}='') ",
                        deGOODS_PROPERTY.Key.ToString(),
                        deGOODS_PROPERTY.Value.ToString());

                        continue;
                    }

                    sGOODS_PROPERTY += string.Format(" AND {0} = '{1}' ",
                        deGOODS_PROPERTY.Key.ToString(),
                        Common.StringUtil.SpecialCharFilter(deGOODS_PROPERTY.Value.ToString()));
                }
            }
            return sGOODS_PROPERTY;
        }

        /// <summary>
        /// 根据源生成物料属性查询where子句中的条件 2020-04-30 10:48:36
        /// </summary>
        internal string GetGoodsPropertySql(int goodsId, object source, Enum.MatchMode matchMode = MatchMode.IgnoreNull ,string skipList="")
        {
            StringBuilder result = new StringBuilder();
            int goodsClassId = 0;

            if (goodsId > 0)
            {
                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                if (goodsMain == null)
                {
                    return null;
                }
                goodsClassId = goodsMain.GOODS_CLASS_ID;
            }
            else
            {
                goodsClassId = 1;
            }

            Model.GOODS_CLASS goodsClass = S_Base.sBase.pGOODS_CLASS.GetModel(goodsClassId);
            if (goodsClass == null)
            {
                return null;
            }

            PropertyInfo[] sourcePropertyArray = source.GetType().GetProperties();

            IList<Model.GOODS_PROPERTY> goodsPropertyList = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(goodsClass.GOODS_TYPE_ID);
            foreach (var goodsProperty in goodsPropertyList)
            {
                if (skipList.Contains(goodsProperty.GOODS_PROPERTY_FIELD) || skipList.Contains(goodsProperty.GOODS_PROPERTY_CODE))
                {
                    continue;
                }

                var sourceProperty = sourcePropertyArray.FirstOrDefault(r => r.Name.ToLower() == goodsProperty.GOODS_PROPERTY_FIELD.ToLower()|| r.Name.ToLower() == goodsProperty.GOODS_PROPERTY_CODE.ToLower());
                if (sourceProperty == null || sourceProperty.GetValue(source, null) == null || string.IsNullOrEmpty(sourceProperty.GetValue(source, null).ToString()))
                {
                    if ((matchMode == Enum.MatchMode.FullMatch) ||
                        (matchMode == Enum.MatchMode.ConsiderKey && goodsProperty.GOODS_PROPERTY_KEYFLAG == Enum.FLAG.P.ToString("d")))
                    {
                        result.Append($" and ({goodsProperty.GOODS_PROPERTY_FIELD} is null or {goodsProperty.GOODS_PROPERTY_FIELD} = '') ");
                    }
                }
                else
                {
                    result.Append($" and ({goodsProperty.GOODS_PROPERTY_FIELD} = '{sourceProperty.GetValue(source, null)}')");
                }

            }

            return result.ToString();
        }

        /// <summary>
        /// 根据配置的标记字段名，得到跳过不考虑的属性，使用“|”分割，与GetGoodsPropertySql方法配合使用
        /// 2020-12-31 09:27:23
        /// </summary>
        /// <param name="goodsId"></param>
        /// <param name="keyColumnName"></param>
        /// <returns></returns>
        internal string GetGoodsPropertySkipList(int goodsId, string keyColumnName)
        {
            StringBuilder result = new StringBuilder();
            int goodsClassId = 0;

            if (goodsId > 0)
            {
                Model.GOODS_MAIN goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(goodsId);
                if (goodsMain == null)
                {
                    return null;
                }
                goodsClassId = goodsMain.GOODS_CLASS_ID;
            }
            else
            {
                goodsClassId = 1;
            }

            Model.GOODS_CLASS goodsClass = S_Base.sBase.pGOODS_CLASS.GetModel(goodsClassId);
            if (goodsClass == null)
            {
                return null;
            }

            IList<Model.GOODS_PROPERTY> goodsPropertyList = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(goodsClass.GOODS_TYPE_ID);

            foreach (var item in goodsPropertyList)
            {
                var property = item.GetType().GetProperty(keyColumnName);
                if (property != null)
                {
                    var propertyValue = property.GetValue(item, null);

                    if(propertyValue ==null || propertyValue.ToString() != Enum.FLAG.P.ToString("d"))
                    {
                        result.Append($"{item.GOODS_PROPERTY_CODE}|");
                    }
                }
            }
            return result.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        internal bool GoodsPropertyCheck(int GOODS_ID, Object mObj, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(GOODS_ID);

            SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = S_Base.sBase.pGOODS_CLASS.GetModel(mGOODS_MAIN.GOODS_CLASS_ID);

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY = S_Base.sBase.pGOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);

            string GOODS_PROPERTY_CODE = string.Empty;

            string GOODS_PROPERTY_VALUE = string.Empty;

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                try
                {
                    GOODS_PROPERTY_CODE = mGOODS_PROPERTY.GOODS_PROPERTY_CODE;

                    PropertyInfo[] propertys = mObj.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE))
                        {
                            GOODS_PROPERTY_VALUE = pi.GetValue(mObj, null).ToString();
                        }
                    }

                    //是自定义属性
                    if ("1".Equals(mGOODS_PROPERTY.GOODS_PROPERTY_VALID) && (string.IsNullOrEmpty(GOODS_PROPERTY_VALUE) || GOODS_PROPERTY_VALUE.Equals("%")))
                    {
                        bResult = false;

                        sResult = string.Format("{0}为必填项", mGOODS_PROPERTY.GOODS_PROPERTY_NAME);

                        return bResult;
                    }
                }
                catch (Exception ex)
                {
                    sResult += ex.Message;
                }
            }

            return bResult;
        }

        /// <summary>
        /// 生成库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        internal bool StorageCreate(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

                if (null == mSTORAGE_MAIN)
                {
                    mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();

                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.MANAGE_TYPE_CODE.Equals("ManageIn") ? mMANAGE_MAIN.START_CELL_ID : mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;

                    mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                    mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                    mSTORAGE_MAIN.STOCK_WEIGHT = mMANAGE_MAIN.STOCK_WEIGHT;  //2020-06-16 15:53:53

                    S_Base.sBase.pSTORAGE_MAIN.Add(mSTORAGE_MAIN);
                }
                else
                {
                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    S_Base.sBase.pSTORAGE_MAIN.Update(mSTORAGE_MAIN);
                }


                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);


                    if (mSTORAGE_LIST != null)
                    {
                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY == 0)

                            S_Base.sBase.pSTORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);
                        else

                            S_Base.sBase.pSTORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {

                        mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                        mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                        mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                        mSTORAGE_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                        bResult = S_Base.sBase.sSystem.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                        if (!bResult)
                        {
                            return bResult;
                        }

                        mSTORAGE_LIST.GOODS_PROPERTY1 = mMANAGE_LIST.GOODS_PROPERTY1;
                        mSTORAGE_LIST.GOODS_PROPERTY2 = mMANAGE_LIST.GOODS_PROPERTY2;
                        mSTORAGE_LIST.GOODS_PROPERTY3 = mMANAGE_LIST.GOODS_PROPERTY3;
                        mSTORAGE_LIST.GOODS_PROPERTY4 = mMANAGE_LIST.GOODS_PROPERTY4;
                        mSTORAGE_LIST.GOODS_PROPERTY5 = mMANAGE_LIST.GOODS_PROPERTY5;
                        mSTORAGE_LIST.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                        mSTORAGE_LIST.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                        mSTORAGE_LIST.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                        mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                        S_Base.sBase.pSTORAGE_LIST.Add(mSTORAGE_LIST);
                    }

                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN_EXIST = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

                if (mSTORAGE_MAIN_EXIST != null)
                {
                    if (S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_EXIST.STORAGE_ID).Count == 0)
                    {
                        S_Base.sBase.pSTORAGE_MAIN.Delete(mSTORAGE_MAIN_EXIST.STORAGE_ID);
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 删除库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        internal bool StorageDelete(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                return bResult;
            }


            IList<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);

            SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = null;

            SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST = null;

            try
            {
                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        S_Base.sBase.pSTORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        S_Base.sBase.pSTORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);
                        //wdz add 2018-04-29
                        S_Base.sBase.pSTORAGE_DETAIL.DeleteStorageListId(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (S_Base.sBase.pSTORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    S_Base.sBase.sManage.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }

                                S_Base.sBase.pSTORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                            }
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 库存转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        internal bool StorageMove(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = S_Base.sBase.pMANAGE_LIST.GetListManageID(MANAGE_ID);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }


                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        S_Base.sBase.pSTORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        S_Base.sBase.pSTORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (S_Base.sBase.pSTORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    S_Base.sBase.sManage.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }

                                S_Base.sBase.pSTORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                            }

                        }
                    }

                    mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModelCellIDStockBarcode(mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE);

                    if (null == mSTORAGE_MAIN)
                    {
                        mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();
                        mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;
                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;
                        mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;
                        mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                        mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;
                        mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                        S_Base.sBase.pSTORAGE_MAIN.Add(mSTORAGE_MAIN);
                    }
                    else
                    {
                        //xcjt add 2017-01-04
                        if (!string.IsNullOrEmpty(mMANAGE_MAIN.CELL_MODEL))
                        {
                            mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                        }
                        if (!string.IsNullOrEmpty(mMANAGE_MAIN.FULL_FLAG))
                        {
                            mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;
                        }

                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                        S_Base.sBase.pSTORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }


                    mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                    mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                    mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                    mSTORAGE_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                    bResult = S_Base.sBase.sSystem.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    mSTORAGE_LIST.GOODS_PROPERTY1 = mMANAGE_LIST.GOODS_PROPERTY1;
                    mSTORAGE_LIST.GOODS_PROPERTY2 = mMANAGE_LIST.GOODS_PROPERTY2;
                    mSTORAGE_LIST.GOODS_PROPERTY3 = mMANAGE_LIST.GOODS_PROPERTY3;
                    mSTORAGE_LIST.GOODS_PROPERTY4 = mMANAGE_LIST.GOODS_PROPERTY4;
                    mSTORAGE_LIST.GOODS_PROPERTY5 = mMANAGE_LIST.GOODS_PROPERTY5;
                    mSTORAGE_LIST.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                    mSTORAGE_LIST.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                    mSTORAGE_LIST.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                    //wdz comment 2018-02-06
                    //mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                    S_Base.sBase.pSTORAGE_LIST.Add(mSTORAGE_LIST);
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 库存转移-整托盘位置转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        internal bool StorageMove(string stockBarcode, int manageStockWeight, int startCellId, int endCellId, bool replaceStockbarcode, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                Model.STORAGE_MAIN mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if (mSTORAGE_MAIN == null)
                {
                    result = false;
                    message = string.Format("未找到库存_条码[{0}]", stockBarcode);
                    return result;
                }
                IList<Model.STORAGE_LIST> lsSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count < 1)
                {
                    result = false;
                    message = string.Format("未找到库存列表_条码[{0}]", stockBarcode);
                    return result;
                }

                var storageCell = S_Base.sBase.pWH_CELL.GetModel(mSTORAGE_MAIN.CELL_ID);
                if(storageCell == null)
                {
                    result = false;
                    message = string.Format("未找到库存的货位信息_条码[{0}]_货位ID[{1}]", stockBarcode, mSTORAGE_MAIN.CELL_ID);
                    return result;
                }
                var startCell = S_Base.sBase.pWH_CELL.GetModel(startCellId);
                if (startCell == null)
                {
                    result = false;
                    message = string.Format("未找任务起点的货位信息_货位ID[{0}]", startCellId);
                    return result;
                }
                var startArea = S_Base.sBase.pWH_AREA.GetModel(startCell.AREA_ID);
                if (startArea == null)
                {
                    result = false;
                    message = string.Format("未找任务起点的库区信息_货位编码[{0}]", startCell.CELL_CODE);
                    return result;
                }
                var endCell = S_Base.sBase.pWH_CELL.GetModel(endCellId);
                if (endCell == null)
                {
                    result = false;
                    message = string.Format("未找任务终点的货位信息_货位ID[{0}]", endCellId);
                    return result;
                }
                var endArea = S_Base.sBase.pWH_AREA.GetModel(endCell.AREA_ID);
                if(endArea == null)
                {
                    result = false;
                    message = string.Format("未找任务终点的库区信息_货位编码[{0}]", endCell.CELL_CODE);
                    return result;
                }
                //if (storageCell.AREA_ID != startCell.AREA_ID)
                //{
                //    result = false;
                //    message = string.Format("库存所在货位区域[{0}]与传入起始货位区域[{1}]不符_条码[{2}]", storageCell.AREA_ID, startCell.AREA_ID, stockBarcode);
                //    return result;
                //}

                //目标货位如果存在库存，则将MAIN 表合并
                string updateTime = Common.StringUtil.GetDateTime();
                Model.STORAGE_MAIN storageMainExist = S_Base.sBase.pSTORAGE_MAIN.GetModelCellID(endCellId);
                //2020-09-28 13:21:20 暂时不合并
                if(storageMainExist == null || true)
                {
                    //2021-04-04 23:00:15 是否需要替换托盘条码，用于成品货位-平库的移库任务，将物料条码变为托盘条码，每一个LIST生成一个新MAIN
                    if (replaceStockbarcode 
                        && startArea.AREA_CODE ==Enum.AreaCode.Product.ToString() 
                        && endArea.AREA_CODE.Contains(Enum.AreaCode.ZCQ.ToString()))
                    {
                        foreach (var storageList in lsSTORAGE_LIST)
                        {
                            int refCount = S_Base.sBase.sDatabase.ExecuteNonQuery($"delete from STORAGE_MAIN where STORAGE_ID = {mSTORAGE_MAIN.STORAGE_ID}");
                            if (refCount > 0)
                            {
                                S_Base.sBase.Log.Info($"成品库移库到平库区删除源条码对应的STORAGE_MAIN_信息[{Common.JsonHelper.Serializer(mSTORAGE_MAIN)}]");
                            }
                            var newStorageMain = new Model.STORAGE_MAIN()
                            {
                                FULL_FLAG = mSTORAGE_MAIN.FULL_FLAG,
                                GOODS_TEMPLATE_ID = mSTORAGE_MAIN.GOODS_TEMPLATE_ID,
                                STOCK_BARCODE = storageList.BOX_BARCODE,
                                STORAGE_ID = 0,
                                STORAGE_REMARK = "成品库移库到平库区重新生成",
                                CELL_ID = endCell.CELL_ID,
                                CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL,
                                IS_EXCEPTION = mSTORAGE_MAIN.IS_EXCEPTION,
                                KITBOX_UP_COMPLETE = mSTORAGE_MAIN.STOCK_BARCODE,
                                SPLIT_SOURCE_CELL_ID = mSTORAGE_MAIN.CELL_ID,
                                STOCK_WEIGHT = 0
                            };

                            S_Base.sBase.pSTORAGE_MAIN.Add(newStorageMain);

                            storageList.STORAGE_ID = newStorageMain.STORAGE_ID;
                            S_Base.sBase.pSTORAGE_LIST.Update(storageList);
                        }
                    }
                    else
                    {
                        mSTORAGE_MAIN.STOCK_WEIGHT = manageStockWeight;
                        mSTORAGE_MAIN.CELL_ID = endCellId;
                        S_Base.sBase.pSTORAGE_MAIN.Update(mSTORAGE_MAIN);

                        foreach (var item in lsSTORAGE_LIST)
                        {
                            item.UPDATE_TIME = updateTime;
                            S_Base.sBase.pSTORAGE_LIST.Update(item);
                        }
                    }
                }
                else
                {
                    S_Base.sBase.pSTORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);

                    foreach (var item in lsSTORAGE_LIST)
                    {
                        item.STORAGE_ID = storageMainExist.STORAGE_ID;
                        item.UPDATE_TIME = updateTime;
                        S_Base.sBase.pSTORAGE_LIST.Update(item);
                    }
                }

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("移动库存异常_信息[{0}]", ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 将查询库存的代码抽取出来，返回可用库存
        /// 2020-09-17 16:50:01
        /// </summary>
        internal DataTable GetEnableStorage(int goodsId ,string goodsPropertySql, string outCellCode, bool checkRoute=true, string orderString="")
        {
            string querySql = $@"
                select * from (
                    select CELL_ID ,AREA_CODE, STOCK_BARCODE ,SHELF_TYPE ,sum(LANE_WAY_QUANTITY_UNLOCK) as LANE_WAY_QUANTITY_UNLOCK,sum(MANAGE_OUT_QUANTITY) as MANAGE_OUT_QUANTITY,min(DATE_WEIGHT) as DATE_WEIGHT from(
                        select 
                            CELL_ID ,AREA_CODE, STOCK_BARCODE ,SHELF_TYPE, 
                            nvl((select sum(STORAGE_LIST_QUANTITY_UNLOCK) from V_STORAGE_LIST a where a.LANE_WAY=t.LANE_WAY),0) as LANE_WAY_QUANTITY_UNLOCK,
                            nvl((select count(0) from MANAGE_MAIN left join WH_CELL on MANAGE_MAIN.START_CELL_ID = WH_CELL.CELL_ID where WH_CELL.DEVICE_CODE= t.DEVICE_CODE),0) as MANAGE_OUT_QUANTITY,
                            (to_date(substr(ENTRY_TIME,0,10),'yyyy-MM-dd')-to_date('2020-06-01','yyyy-MM-dd')) as DATE_WEIGHT
                        from V_STORAGE_LIST t
                        where GOODS_ID = {goodsId} {goodsPropertySql}
                                and CELL_TYPE = 'Cell' and CELL_STATUS = 'Full' and RUN_STATUS = 'Enable'
                                and {(checkRoute?$"t.DEVICE_CODE in (select START_DEVICE from IO_CONTROL_ROUTE where END_DEVICE='{outCellCode}' and CONTROL_ROUTE_STATUS=1 and CONTROL_ROUTE_MANAGE=1) " :"1=1")})
                    group by CELL_ID ,AREA_CODE, STOCK_BARCODE,SHELF_TYPE)
                where LANE_WAY_QUANTITY_UNLOCK > 0
                order by {orderString} MANAGE_OUT_QUANTITY ,SHELF_TYPE desc,DATE_WEIGHT ,LANE_WAY_QUANTITY_UNLOCK desc";

            return S_Base.sBase.sDatabase.GetList(querySql);
        }

        /// <summary>
        /// 客户端添加日志
        /// </summary>
        public void CLog(string userName, string pcIp, string formName, string Message)
        {
            S_Base.sBase.ClientLog.InfoFormat("用户[{0}]_计算机[{1}]_界面[{2}]_信息[{3}]", userName, pcIp, formName, Message);
        }
    }
}
