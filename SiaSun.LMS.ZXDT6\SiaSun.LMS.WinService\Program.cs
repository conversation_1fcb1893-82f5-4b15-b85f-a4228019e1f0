﻿//#define DEPLOY
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Reflection;
using Quartz;
using Quartz.Impl;


namespace SiaSun.LMS.WinService
{
    static class Program
    {
        public static string _HouseUrl = string.Empty;
        public static string _InterfaceUrl = string.Empty;
        public static log4net.ILog hostLog = log4net.LogManager.GetLogger("WinServiceLog");
        public static Implement.S_Base BaseService = Implement.S_Base.sBase;

        static ISchedulerFactory schedulerFactory = new StdSchedulerFactory();
        static IScheduler scheduler;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        static void Main()
        {
#if DEPLOY
            ServiceBase[] ServicesToRun;
            
            ServicesToRun = new ServiceBase[] 
			{ 
				new WMSService() 
			};
            ServiceBase.Run(ServicesToRun);
#else 

            try
            {
                Program._HouseUrl = Common.StringUtil.GetConfig("SiasunUrl");
                Program._InterfaceUrl = Common.StringUtil.GetConfig("InterfaceUrl");

                ServiceHostGroup.StartAllConfiguredServices("House",_HouseUrl);
                ServiceHostGroup.StartAllConfiguredServices("All", _InterfaceUrl);

                scheduler = schedulerFactory.GetScheduler();
                scheduler.Start();

                while (true)
                {
                    System.Threading.Thread.Sleep(2000);
                }
            }
            catch (Exception ex)
            {
                hostLog.Fatal("系统异常", ex);
            }
#endif
        }
    }
}
