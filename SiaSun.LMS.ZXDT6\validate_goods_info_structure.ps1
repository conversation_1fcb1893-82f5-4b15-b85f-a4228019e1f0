# Validate GoodsInfoSync structure
Write-Host "Validating GoodsInfoSync refactored structure..." -ForegroundColor Green

# Read the refactored file
$content = Get-Content -Path "SiaSun.LMS.Implement\Interface\WMS\GoodsInfoSync.cs" -Raw

# Check for key structural elements
$checks = @(
    @{ Name = "InputParam class with complete structure"; Pattern = "class InputParam" },
    @{ Name = "daLeiId field"; Pattern = "public string daLeiId" },
    @{ Name = "goodsCode field"; Pattern = "public string goodsCode" },
    @{ Name = "goodsStatus field"; Pattern = "public int goodsStatus" },
    @{ Name = "UnitInformationEntityDTO nested class"; Pattern = "class UnitInformationEntityDTO" },
    @{ Name = "BrandVO nested class"; Pattern = "class BrandVO" },
    @{ Name = "ConversionDetailVO nested class"; Pattern = "class ConversionDetailVO" },
    @{ Name = "OutputParam with standardized structure"; Pattern = "class OutputParam" },
    @{ Name = "code field in OutputParam"; Pattern = "public int code" },
    @{ Name = "msg field in OutputParam"; Pattern = "public string msg" },
    @{ Name = "traceId field in OutputParam"; Pattern = "public string traceId" },
    @{ Name = "IntefaceMethod override"; Pattern = "internal override string IntefaceMethod" },
    @{ Name = "JSON deserialization"; Pattern = "Common.JsonHelper.Deserialize" },
    @{ Name = "Required field validation"; Pattern = "接口入参必填项" },
    @{ Name = "FormatResponse method"; Pattern = "private string FormatResponse" },
    @{ Name = "BuildGoodsRemark method"; Pattern = "private string BuildGoodsRemark" },
    @{ Name = "ProcessUnitInformation method"; Pattern = "private void ProcessUnitInformation" },
    @{ Name = "ProcessBrandInformation method"; Pattern = "private void ProcessBrandInformation" }
)

$passedChecks = 0
$totalChecks = $checks.Count

foreach ($check in $checks) {
    if ($content -match [regex]::Escape($check.Pattern)) {
        Write-Host "✓ $($check.Name)" -ForegroundColor Green
        $passedChecks++
    } else {
        Write-Host "✗ $($check.Name)" -ForegroundColor Red
    }
}

Write-Host "`nValidation Summary:" -ForegroundColor Cyan
Write-Host "Passed: $passedChecks/$totalChecks checks" -ForegroundColor $(if ($passedChecks -eq $totalChecks) { "Green" } else { "Yellow" })

if ($passedChecks -eq $totalChecks) {
    Write-Host "All structural validations passed! ✓" -ForegroundColor Green
} else {
    Write-Host "Some validations failed. Please review the implementation." -ForegroundColor Yellow
}

# Check for specific field mappings from the interface documentation
Write-Host "`nChecking interface documentation field mappings:" -ForegroundColor Cyan
$fieldMappings = @(
    "daLeiId", "daLeiName", "xiaoLeiId", "xiaoLeiName", "daZuId", "daZuName", 
    "xiaoZuId", "xiaoZuName", "goodsStatus", "code", "parentId", "parentName", 
    "name", "goodsType", "goodsAttribute", "unitId", "unitName", "goodsVersion", 
    "goodsClass", "goodsCode", "suggestedAmount", "isLaborInsuranceMaterials", 
    "ignoreParent", "fixedAssetFlag", "professionalAssetFlag", "unitInformationEntityDTO", 
    "brandVOs", "isControlledByProductDate", "createDate", "createUser", "createName", 
    "updateDate", "updateUser", "updateName", "id", "status", "billCode"
)

$mappedFields = 0
foreach ($field in $fieldMappings) {
    if ($content -match "public .* $field \{") {
        $mappedFields++
    }
}

Write-Host "Mapped fields: $mappedFields/$($fieldMappings.Count)" -ForegroundColor $(if ($mappedFields -eq $fieldMappings.Count) { "Green" } else { "Yellow" })

Write-Host "`nValidation completed." -ForegroundColor Green