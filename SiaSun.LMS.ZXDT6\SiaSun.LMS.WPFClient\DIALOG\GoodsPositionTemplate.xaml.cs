﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Drawing;
using System.IO;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// GoodsPositionTemplate.xaml 的交互逻辑
    /// </summary>
    public partial class GoodsPositionTemplate : Window
    {
        private string _strGoodsCode = string.Empty;

        public GoodsPositionTemplate(string parmCode)
        {
            InitializeComponent();

            this._strGoodsCode = parmCode;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.Title = string.Format("模具{0}的摆放模板",this._strGoodsCode);

            //wdz alter 2017-08-16
            //Bitmap bmp = MainApp.I_SystemService.GetGoodsPositionTemplateImage(string.Format("{0}{1}.jpg", MainApp._ImageUrl, this._strGoodsCode));
            Bitmap bmp = null;

            if (bmp != null)
            {
                MemoryStream ms = new MemoryStream();
                bmp.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                BitmapImage img = new BitmapImage();
                img.BeginInit();
                img.StreamSource = ms;
                img.EndInit();
                this.imgPositionTemplate.Source = img;
            }
            else
            {
                this.Close();
            }
        }
    }
}
