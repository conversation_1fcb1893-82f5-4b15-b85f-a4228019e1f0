﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     
 *       日期：     2020/4/17
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// INTERFACE_QUEUE 
    /// </summary>
    [Serializable]
    public class INTERFACE_QUEUE
    {
        public INTERFACE_QUEUE()
        {

        }

        private System.Decimal _queue_id;
        private string _target_system;
        private string _interface_name;
        private string _interface_type;
        private string _invoke_type;
        private System.Decimal _plan_id;
        private string _plan_code;
        private System.Decimal _manage_id;
        private string _stock_barcode;
        private string _param_in;
        private string _param_out;
        private string _write_datetime;
        private string _handle_datetime;
        private System.Decimal _handle_flag;
        private string _error_describe;
        private string _queue_remark;
        private string _backup_filed1;
        private string _backup_filed2;
        private string _backup_filed3;
        private string _backup_filed4;
        private string _backup_filed5;

        ///<sumary>
        /// ID
        ///</sumary>
        [DataMember]
        public System.Decimal QUEUE_ID
        {
            get { return _queue_id; }
            set { _queue_id = value; }
        }
        ///<sumary>
        /// 目标系统
        ///</sumary>
        [DataMember]
        public string TARGET_SYSTEM
        {
            get { return _target_system; }
            set { _target_system = value; }
        }
        ///<sumary>
        /// 接口名
        ///</sumary>
        [DataMember]
        public string INTERFACE_NAME
        {
            get { return _interface_name; }
            set { _interface_name = value; }
        }
        ///<sumary>
        /// 接口类型
        ///</sumary>
        [DataMember]
        public string INTERFACE_TYPE
        {
            get { return _interface_type; }
            set { _interface_type = value; }
        }
        ///<sumary>
        /// 调用类型
        ///</sumary>
        [DataMember]
        public string INVOKE_TYPE
        {
            get { return _invoke_type; }
            set { _invoke_type = value; }
        }
        ///<sumary>
        /// 计划ID
        ///</sumary>
        [DataMember]
        public System.Decimal PLAN_ID
        {
            get { return _plan_id; }
            set { _plan_id = value; }
        }
        ///<sumary>
        /// 计划单号
        ///</sumary>
        [DataMember]
        public string PLAN_CODE
        {
            get { return _plan_code; }
            set { _plan_code = value; }
        }
        ///<sumary>
        /// 任务ID
        ///</sumary>
        [DataMember]
        public System.Decimal MANAGE_ID
        {
            get { return _manage_id; }
            set { _manage_id = value; }
        }
        ///<sumary>
        /// 托盘条码
        ///</sumary>
        [DataMember]
        public string STOCK_BARCODE
        {
            get { return _stock_barcode; }
            set { _stock_barcode = value; }
        }
        ///<sumary>
        /// 入参
        ///</sumary>
        [DataMember]
        public string PARAM_IN
        {
            get { return _param_in; }
            set { _param_in = value; }
        }
        ///<sumary>
        /// 出参
        ///</sumary>
        [DataMember]
        public string PARAM_OUT
        {
            get { return _param_out; }
            set { _param_out = value; }
        }
        ///<sumary>
        /// 写入时间
        ///</sumary>
        [DataMember]
        public string WRITE_DATETIME
        {
            get { return _write_datetime; }
            set { _write_datetime = value; }
        }
        ///<sumary>
        /// 处理时间
        ///</sumary>
        [DataMember]
        public string HANDLE_DATETIME
        {
            get { return _handle_datetime; }
            set { _handle_datetime = value; }
        }
        ///<sumary>
        /// 处理标记
        ///</sumary>
        [DataMember]
        public System.Decimal HANDLE_FLAG
        {
            get { return _handle_flag; }
            set { _handle_flag = value; }
        }
        ///<sumary>
        /// 错误信息
        ///</sumary>
        [DataMember]
        public string ERROR_DESCRIBE
        {
            get { return _error_describe; }
            set { _error_describe = value; }
        }
        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string QUEUE_REMARK
        {
            get { return _queue_remark; }
            set { _queue_remark = value; }
        }
        ///<sumary>
        /// 预留字段1
        ///</sumary>
        [DataMember]
        public string BACKUP_FILED1
        {
            get { return _backup_filed1; }
            set { _backup_filed1 = value; }
        }
        ///<sumary>
        /// 预留字段2
        ///</sumary>
        [DataMember]
        public string BACKUP_FILED2
        {
            get { return _backup_filed2; }
            set { _backup_filed2 = value; }
        }
        ///<sumary>
        /// 预留字段3
        ///</sumary>
        [DataMember]
        public string BACKUP_FILED3
        {
            get { return _backup_filed3; }
            set { _backup_filed3 = value; }
        }
        ///<sumary>
        /// 预留字段4
        ///</sumary>
        [DataMember]
        public string BACKUP_FILED4
        {
            get { return _backup_filed4; }
            set { _backup_filed4 = value; }
        }
        ///<sumary>
        /// 预留字段5
        ///</sumary>
        [DataMember]
        public string BACKUP_FILED5
        {
            get { return _backup_filed5; }
            set { _backup_filed5 = value; }
        }
    }
}
