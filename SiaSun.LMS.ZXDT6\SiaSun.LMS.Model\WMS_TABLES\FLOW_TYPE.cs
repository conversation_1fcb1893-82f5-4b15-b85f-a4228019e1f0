﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// FLOW_TYPE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class FLOW_TYPE
	{
		public FLOW_TYPE()
		{
			
		}
		
		private int _flow_type_id;
		private string _flow_type_code;
		private string _flow_type_name;
		private string _flow_type_remark;
		private int _flow_type_order;
		private string _flow_type_flag;
		
		///<sumary>
		/// 流程类型编号
        ///</sumary>
        [DataMember]
		public int FLOW_TYPE_ID
		{
			get{return _flow_type_id;}
			set{_flow_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string FLOW_TYPE_CODE
		{
			get{return _flow_type_code;}
			set{_flow_type_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string FLOW_TYPE_NAME
		{
			get{return _flow_type_name;}
			set{_flow_type_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string FLOW_TYPE_REMARK
		{
			get{return _flow_type_remark;}
			set{_flow_type_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int FLOW_TYPE_ORDER
		{
			get{return _flow_type_order;}
			set{_flow_type_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string FLOW_TYPE_FLAG
		{
			get{return _flow_type_flag;}
			set{_flow_type_flag = value;}
		}
	}
}
