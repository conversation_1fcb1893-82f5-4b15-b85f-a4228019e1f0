﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_RELATION" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_RELATION" type="SiaSun.LMS.Model.SYS_RELATION, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_RELATION">
			<result property="RELATION_ID" column="relation_id" />
			<result property="RELATION_CODE" column="relation_code" />
			<result property="RELATION_NAME" column="relation_name" />
			<result property="RELATION_ID1" column="relation_id1" />
			<result property="RELATION_ID2" column="relation_id2" />
			<result property="RELATION_REMARK" column="relation_remark" />
			<result property="RELATON_NAME1" column="relaton_name1" />
			<result property="RELATON_NAME2" column="relaton_name2" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_RELATION_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  relation_id,
				  relation_code,
				  relation_name,
				  relation_id1,
				  relation_id2,
				  relation_remark,
				  relaton_name1,
				  relaton_name2
			From SYS_RELATION
		</select>
		
		<select id="SYS_RELATION_SELECT_BY_RELATION_CODE" parameterClass="string" extends = "SYS_RELATION_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					relation_code=#RELATION_CODE# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="SYS_RELATION_INSERT" parameterClass="SYS_RELATION">
      Insert Into SYS_RELATION (
      relation_id,
      relation_code,
      relation_name,
      relation_id1,
      relation_id2,
      relation_remark,
      relaton_name1,
      relaton_name2
      )Values(
      #RELATION_ID#,
      #RELATION_CODE#,
      #RELATION_NAME#,
      #RELATION_ID1#,
      #RELATION_ID2#,
      #RELATION_REMARK#,
      #RELATON_NAME1#,
      #RELATON_NAME2#
      )
      <!--<selectKey  resultClass="int" type="post" property="RELATION_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_RELATION_UPDATE" parameterClass="SYS_RELATION">
      Update SYS_RELATION Set
      <!--relation_id=#RELATION_ID#,-->
      relation_code=#RELATION_CODE#,
      relation_name=#RELATION_NAME#,
      relation_id1=#RELATION_ID1#,
      relation_id2=#RELATION_ID2#,
      relation_remark=#RELATION_REMARK#,
      relaton_name1=#RELATON_NAME1#,
      relaton_name2=#RELATON_NAME2#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					relation_id=#RELATION_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_RELATION_DELETE" parameterClass="int">
			Delete From SYS_RELATION
			<dynamic prepend="WHERE">
				<isParameterPresent>
					relation_id=#RELATION_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>