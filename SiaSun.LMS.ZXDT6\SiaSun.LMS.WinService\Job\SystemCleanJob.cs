﻿using System;
using System.Data;
using System.Linq;
using System.Text;
using Quartz;

namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class SystemCleanJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                Program.hostLog.Debug("进入SystemCleanJob");

                string strDel = string.Empty;
                int refCount = 0;

                //清除sys_log
                string sSysLogClearTh = string.Empty;
                int iSysLogClearTh = 0;
                if (Program.BaseService.sSystem.GetSysParameter("SysLogClearThreshold", out sSysLogClearTh) &&
                    int.TryParse(sSysLogClearTh, out iSysLogClearTh) && iSysLogClearTh > 0)
                {
                    //SYS_LOG1
                    strDel = string.Format(@"delete from SYS_LOG1 where LOG_DATE<'{0}'", System.DateTime.Now.AddMonths(-iSysLogClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = Program.BaseService.sDatabase.ExecuteNonQuery(strDel);
                    if (refCount > 0)
                    {
                        Program.hostLog.InfoFormat("清理[{1}]个月前的SYS_LOG1日志信息_删除[{0}]条", refCount, sSysLogClearTh);
                    }

                    //IO_CONTROL_APPLY_HIS
                    strDel = string.Format(@"delete from IO_CONTROL_APPLY_HIS where CREATE_TIME<'{0}'", System.DateTime.Now.AddMonths(-iSysLogClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = Program.BaseService.sDatabase.ExecuteNonQuery(strDel);
                    if (refCount > 0)
                    {
                        Program.hostLog.InfoFormat("清理[{1}]个月前的IO_CONTROL_APPLY_HIS申请记录信息_删除[{0}]条", refCount, sSysLogClearTh);
                    }

                }

                //清除记录信息
                string sRecordClearTh = string.Empty;
                int iRecordClearTh = 0;
                if (Program.BaseService.sSystem.GetSysParameter("RecordClearThreshold", out sRecordClearTh) &&
                    int.TryParse(sRecordClearTh, out iRecordClearTh) && iRecordClearTh > 0)
                {
                    //清除RECORD_DETAIL
                    strDel = string.Format(@"delete from RECORD_DETAIL where RECORD_DETAIL.RECORD_LIST_ID in (select RECORD_LIST.RECORD_LIST_ID from RECORD_LIST where RECORD_LIST.RECORD_ID in (select RECORD_MAIN.RECORD_ID from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}'))", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = Program.BaseService.sDatabase.ExecuteNonQuery(strDel);
                    if (refCount > 0)
                    {
                        Program.hostLog.InfoFormat("清除[{1}]个月前的RECORD_DETAIL信息_删除[{0}]条", refCount, iRecordClearTh);
                    }
                    //清除RECORD_LIST
                    strDel = string.Format(@"delete from RECORD_LIST where RECORD_LIST.RECORD_ID in (select RECORD_MAIN.RECORD_ID from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}')", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = Program.BaseService.sDatabase.ExecuteNonQuery(strDel);
                    if (refCount > 0)
                    {
                        Program.hostLog.InfoFormat("清除[{1}]个月前的RECORD_LIST信息_删除[{0}]条", refCount, iRecordClearTh);
                    }

                    //清除RECORD_MAIN
                    strDel = string.Format(@"delete from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}'", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = Program.BaseService.sDatabase.ExecuteNonQuery(strDel);
                    if (refCount > 0)
                    {
                        Program.hostLog.InfoFormat("清除[{1}]个月前的RECORD_MAIN信息_删除[{0}]条", refCount, iRecordClearTh);
                    }
                }
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("清除日志记录信息时发生异常", ex);
            }
            finally
            {
                Program.hostLog.Debug("离开SystemCleanJob");
            }
        }
    }
}
