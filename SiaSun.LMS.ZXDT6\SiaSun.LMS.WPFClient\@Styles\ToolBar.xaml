<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style
        x:Key="{x:Static ToolBar.ButtonStyleKey}"
        BasedOn="{x:Null}"
        TargetType="{x:<PERSON> Button}">
        <Setter Property="FocusVisualStyle" Value="{DynamicResource NuclearButtonFocusVisual}" />
        <Setter Property="Foreground" Value="#FF042271" />
        <Setter Property="FontSize" Value="10" />
        <Setter Property="MinHeight" Value="18" />
        <Setter Property="MinWidth" Value="50" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Template" Value="{DynamicResource ButtonTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.CheckBoxStyleKey}" TargetType="{x:Type CheckBox}">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource CheckBoxFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource CheckBoxTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.RadioButtonStyleKey}" TargetType="{x:Type RadioButton}">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource RadioButtonFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource RadioButtonTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.ComboBoxStyleKey}" TargetType="{x:Type ComboBox}">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="6,2,25,2" />

        <Setter Property="Template" Value="{DynamicResource ComboBoxTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.TextBoxStyleKey}" TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource WhiteColor}" />
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                    <GradientStop Color="#FFABAEB3" />
                    <GradientStop Offset="1" Color="#FFE2E8EE" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Template" Value="{DynamicResource TextBoxTemplate}" />
    </Style>

    <Style x:Key="ToolBarHorizontalOverflowButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="{StaticResource ToolBarToggleButtonHorizontalBackground}" />
        <Setter Property="MinHeight" Value="0" />
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="BackgroundOver"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="BackgroundOver_Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.65" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="BackgroundOver"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="BackgroundOver_Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="FocusVisualElement"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border
                            x:Name="Bd"
                            Background="{DynamicResource NormalBrush}"
                            CornerRadius="0,0,0,0"
                            SnapsToDevicePixels="true" />
                        <Border
                            x:Name="BackgroundOver"
                            Background="{DynamicResource MouseOverBrush}"
                            BorderBrush="{DynamicResource MouseOverBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="1.75"
                            Opacity="0" />
                        <Border
                            x:Name="BackgroundOver_Highlight"
                            Margin="0,0,0,0"
                            BorderBrush="{DynamicResource MouseOverHighlightBrush}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="1"
                            Opacity="0" />
                        <Canvas
                            Width="6"
                            Height="7"
                            Margin="7,2,2,2"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            SnapsToDevicePixels="true">
                            <Path Data="M 1 1.5 L 6 1.5" Stroke="White" />
                            <Path Data="M 0 0.5 L 5 0.5" Stroke="{TemplateBinding Foreground}" />
                            <Path Data="M 0.5 4 L 6.5 4 L 3.5 7 Z" Fill="White" />
                            <Path Data="M -0.5 3 L 5.5 3 L 2.5 6 Z" Fill="{TemplateBinding Foreground}" />
                        </Canvas>
                        <Border
                            x:Name="FocusVisualElement"
                            Margin="-1"
                            BorderBrush="#FFE99862"
                            BorderThickness="1"
                            CornerRadius="2.75"
                            IsHitTestVisible="false"
                            Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="FocusedOn_BeginStoryboard" Storyboard="{StaticResource FocusedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource ToolBarGripper}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ToolBarThumbStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="Transparent"
                        CornerRadius="0,0,0,0"
                        SnapsToDevicePixels="True">
                        <Rectangle>
                            <Rectangle.Fill>
                                <DrawingBrush
                                    TileMode="Tile"
                                    Viewbox="0,0,4,4"
                                    ViewboxUnits="Absolute"
                                    Viewport="0,0,4,4"
                                    ViewportUnits="Absolute">
                                    <DrawingBrush.Drawing>
                                        <DrawingGroup>
                                            <GeometryDrawing Brush="White" Geometry="M 1 1 L 1 3 L 3 3 L 3 1 z" />
                                            <GeometryDrawing Brush="{StaticResource ToolBarGripper}" Geometry="M 0 0 L 0 2 L 2 2 L 2 0 z" />
                                        </DrawingGroup>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Cursor" Value="SizeAll" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ToolBarVerticalOverflowButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="{StaticResource ToolBarToggleButtonVerticalBackground}" />
        <Setter Property="MinHeight" Value="0" />
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border
                        x:Name="Bd"
                        Background="{TemplateBinding Background}"
                        CornerRadius="0,0,3,3"
                        SnapsToDevicePixels="true">
                        <Canvas
                            Width="7"
                            Height="6"
                            Margin="2,7,2,2"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            SnapsToDevicePixels="true">
                            <Path Data="M 1.5 1 L 1.5 6" Stroke="White" />
                            <Path Data="M 0.5 0 L 0.5 5" Stroke="{TemplateBinding Foreground}" />
                            <Path Data="M 3.5 0.5 L 7 3.5 L 4 6.5 Z" Fill="White" />
                            <Path Data="M 3 -0.5 L 6 2.5 L 3 5.5 Z" Fill="{TemplateBinding Foreground}" />
                        </Canvas>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ToolBarButtonHover}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ToolBarButtonHover}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource ToolBarGripper}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ToolBar}">
        <Setter Property="Background" Value="{StaticResource ToolBarHorizontalBackground}" />
        <Setter Property="BorderBrush" Value="#FFB1703C" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToolBar}">
                    <Grid
                        x:Name="Grid"
                        Margin="3,1,1,1"
                        SnapsToDevicePixels="true">
                        <Grid x:Name="OverflowGrid" HorizontalAlignment="Right">
                            <ToggleButton
                                x:Name="OverflowButton"
                                ClickMode="Press"
                                FocusVisualStyle="{x:Null}"
                                IsChecked="{Binding Path=IsOverflowOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                IsEnabled="{TemplateBinding HasOverflowItems}"
                                Style="{StaticResource ToolBarHorizontalOverflowButtonStyle}">
                                <ToggleButton.Background>
                                    <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                        <GradientStop Offset="0" Color="#FFFFFFFF" />
                                        <GradientStop Offset="0.521" Color="#FF8AAEDA" />
                                        <GradientStop Offset="0.194" Color="#FFC6D6EC" />
                                        <GradientStop Offset="0.811" Color="#FFB4C9E5" />
                                        <GradientStop Offset="0.507" Color="#FFB7C8E0" />
                                        <GradientStop Offset="1" Color="#FFD1DEF0" />
                                    </LinearGradientBrush>
                                </ToggleButton.Background>
                            </ToggleButton>
                            <Popup
                                x:Name="OverflowPopup"
                                AllowsTransparency="true"
                                Focusable="false"
                                IsOpen="{Binding Path=IsOverflowOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                Placement="Bottom"
                                PopupAnimation="{DynamicResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}"
                                StaysOpen="False">
                                <Border x:Name="Shdw">
                                    <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="1,1,1,1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0.056,0.5" EndPoint="1.204,0.5">
                                                <GradientStop Offset="0" Color="#FFFFFFFF" />
                                                <GradientStop Offset="1" Color="#FFD4D7DB" />
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <ToolBarOverflowPanel
                                            x:Name="PART_ToolBarOverflowPanel"
                                            Margin="2"
                                            FocusVisualStyle="{x:Null}"
                                            Focusable="true"
                                            KeyboardNavigation.DirectionalNavigation="Cycle"
                                            KeyboardNavigation.TabNavigation="Cycle"
                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                            WrapWidth="200" />
                                    </Border>
                                </Border>
                            </Popup>
                        </Grid>
                        <Border
                            x:Name="MainPanelBorder"
                            Margin="0,0,11,0"
                            Padding="{TemplateBinding Padding}"
                            Background="{DynamicResource NormalBrush}"
                            BorderBrush="{DynamicResource NormalBorderBrush}"
                            BorderThickness="1"
                            CornerRadius="0,0,0,0">
                            <Grid>

                                <DockPanel KeyboardNavigation.TabIndex="1" KeyboardNavigation.TabNavigation="Local">
                                    <Thumb
                                        x:Name="ToolBarThumb"
                                        Width="10"
                                        Margin="-3,-1,0,0"
                                        Padding="6,5,1,6"
                                        Style="{StaticResource ToolBarThumbStyle}" />
                                    <ContentPresenter
                                        x:Name="ToolBarHeader"
                                        Margin="4,0,4,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        ContentSource="Header"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    <ToolBarPanel
                                        x:Name="PART_ToolBarPanel"
                                        Margin="0,1,2,2"
                                        Background="{DynamicResource NormalBrush}"
                                        IsItemsHost="true"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                </DockPanel>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsOverflowOpen" Value="true">
                            <Setter TargetName="ToolBarThumb" Property="IsEnabled" Value="false" />
                        </Trigger>
                        <Trigger Property="Header" Value="{x:Null}">
                            <Setter TargetName="ToolBarHeader" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="ToolBarTray.IsLocked" Value="true">
                            <Setter TargetName="ToolBarThumb" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger SourceName="OverflowPopup" Property="HasDropShadow" Value="true">
                            <Setter TargetName="Shdw" Property="Margin" Value="0,0,5,5" />
                            <Setter TargetName="Shdw" Property="SnapsToDevicePixels" Value="true" />
                            <Setter TargetName="Shdw" Property="Background" Value="#71000000" />
                        </Trigger>
                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter TargetName="Grid" Property="Margin" Value="1,3,1,1" />
                            <Setter TargetName="OverflowButton" Property="Style" Value="{StaticResource ToolBarVerticalOverflowButtonStyle}" />
                            <Setter TargetName="ToolBarThumb" Property="Height" Value="10" />
                            <Setter TargetName="ToolBarThumb" Property="Width" Value="Auto" />
                            <Setter TargetName="ToolBarThumb" Property="Margin" Value="-1,-3,0,0" />
                            <Setter TargetName="ToolBarThumb" Property="Padding" Value="5,6,6,1" />
                            <Setter TargetName="ToolBarHeader" Property="Margin" Value="0,0,0,4" />
                            <Setter TargetName="PART_ToolBarPanel" Property="Margin" Value="1,0,2,2" />
                            <Setter TargetName="ToolBarThumb" Property="DockPanel.Dock" Value="Top" />
                            <Setter TargetName="ToolBarHeader" Property="DockPanel.Dock" Value="Top" />
                            <Setter TargetName="OverflowGrid" Property="HorizontalAlignment" Value="Stretch" />
                            <Setter TargetName="OverflowGrid" Property="VerticalAlignment" Value="Bottom" />
                            <Setter TargetName="OverflowPopup" Property="Placement" Value="Right" />
                            <Setter TargetName="MainPanelBorder" Property="Margin" Value="0,0,0,11" />
                            <Setter Property="Background" Value="{StaticResource ToolBarVerticalBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="#FF000000" />
    </Style>

</ResourceDictionary>