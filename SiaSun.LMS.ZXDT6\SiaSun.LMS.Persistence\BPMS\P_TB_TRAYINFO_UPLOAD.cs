﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     FST
 *       日期：     2018/10/18
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// TB_TRAYINFO_UPLOAD
    /// </summary>
    public class P_TB_TRAYINFO_UPLOAD : P_Base_Bpms
    {
        public P_TB_TRAYINFO_UPLOAD()
        {
            //
            // TODO: 此处添加TB_TRAYINFO_UPLOAD的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<TB_TRAYINFO_UPLOAD> GetList()
        {
            return ExecuteQueryForList<TB_TRAYINFO_UPLOAD>("TB_TRAYINFO_UPLOAD_SELECT", null);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("TB_TRAYINFO_UPLOAD_SELECT", null);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public TB_TRAYINFO_UPLOAD GetModel(System.Int64 DATA_INDEX)
        {
            return ExecuteQueryForObject<TB_TRAYINFO_UPLOAD>("TB_TRAYINFO_UPLOAD_SELECT_BY_ID", DATA_INDEX);
        }
    }
}
