﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
    /// LED_LIST
	/// </summary>
    public class P_LED_LIST : P_Base_House
	{
        public P_LED_LIST()
		{
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<LED_LIST> GetList()
        {
            return this.ExecuteQueryForList<LED_LIST>("LED_LIST_SELECT", null);
        }

        /// <summary>
        /// 得到列表,根据LED_ID
        /// </summary>
        public IList<LED_LIST> GetList(int LED_ID)
        {
            return this.ExecuteQueryForList<LED_LIST>("LED_LIST_SELECT_BY_LED_ID", LED_ID);
        }

        /// <summary>
        /// 得到明细,根据LED_ID,LINE_NO
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public LED_LIST GetModel(int LED_ID, int LINE_NO)
        {
            Hashtable ht = new Hashtable();
            ht.Add("LED_ID", LED_ID);
            ht.Add("LINE_NO", LINE_NO);
            return this.ExecuteQueryForObject<LED_LIST>("LED_LIST_SELECT_BY_LED_ID_LINE_NO", ht);
        }


        /// <summary>
        /// 得到明细,根据LED_LIST_ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public LED_LIST GetModel(int LED_LIST_ID)
        {
            return this.ExecuteQueryForObject<LED_LIST>("LED_LIST_SELECT_BY_ID", LED_LIST_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
        public void Add(LED_LIST led_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("LED_LIST");
                led_list.LED_LIST_ID = id;
            }           

            this.ExecuteInsert("LED_LIST_INSERT", led_list);
		}

		/// <summary>
		/// 修改
		/// </summary>
        public void Update(LED_LIST led_list)
		{
            this.ExecuteUpdate("LED_LIST_UPDATE", led_list);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
        public void Delete(int LED_LIST_ID)
		{
            this.ExecuteDelete("LED_LIST_DELETE", LED_LIST_ID);
		}
		

	}
}
