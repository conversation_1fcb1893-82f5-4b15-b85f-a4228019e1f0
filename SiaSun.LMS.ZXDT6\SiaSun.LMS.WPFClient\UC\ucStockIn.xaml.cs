﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucStockTaskInput.xaml 的交互逻辑
    /// </summary>
    public partial class ucStockIn : UserControl
    {
        int intGoodsClassID = 0;

        int intGoodsTypeID = 0;

        /// <summary>
        /// 托盘类型选择事件
        /// </summary>
        public delegate void U_StockTypeValueChangedHandler();

        //托盘类型选择事件
        public event U_StockTypeValueChangedHandler U_StockTypeChanged;


        /// <summary>
        /// 托盘类型
        /// </summary>
        public int U_StockType
        {

            get { return Convert.ToInt32( this.cmbStockType.SelectedValue.ToString()); }

        }

        public int U_StockQuantity
        {
            get
            {
                if (this.rbSingle.IsChecked.Value || this.rbSingleContinue.IsChecked.Value )
                {
                    return 1;
                }

                if (this.rbMultiple.IsChecked.Value)
                {
                    return this.lvStockBarcode.Items.Count;
                }

                return 0;
            }
        }

        public IList<string> U_lsStockBarcodes
        {
            get
            {
                List<string> lsStockBarcodes = new List<string>();

                if (this.rbSingle.IsChecked.Value)
                {
                    lsStockBarcodes.Add(this.txtBeginStockBarCode.Text.TrimEnd());
                }

                if (this.rbSingleContinue.IsChecked.Value)
                {
                    lsStockBarcodes = this.getBarCodeList(this.txtBeginStockBarCode.Text.TrimEnd(), this.txtEndStockBarCode.Text.TrimEnd());
                }

                if (this.rbMultiple.IsChecked.Value)
                {
                    List<string> temp = new List<string>();

                    foreach (ListViewItem lvi in this.lvStockBarcode.Items)
                    {
                        temp.Add(lvi.Content.ToString());
                    }

                    temp.Sort();

                    string sStockBarcode = string.Empty;

                    foreach (string strStockBarcode in temp)
                    {
                        sStockBarcode += strStockBarcode + "|";
                    }

                    sStockBarcode = sStockBarcode.TrimEnd('|');

                    lsStockBarcodes.Add(sStockBarcode);
                }

                return lsStockBarcodes;
            }
        }

        /// <summary>
        /// 物料类别
        /// </summary>
        public int U_GoodsClassID
        {
            get { return intGoodsClassID; }
            set { intGoodsClassID = value; }
        }

        public int U_GoodsTypeID
        {
            get { return intGoodsTypeID; }
            set { intGoodsTypeID = value; }
        }

        public int U_GoodsID
        {

            get
            {
                if (this.cmbStockType.SelectedValue != null)
                    return Convert.ToInt32(this.cmbStockType.SelectedValue);
                else
                    return 0;
            }
        }

        public ucStockIn()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化数据控件
        /// </summary>
        public void U_InitControl()
        {
            //初始化数据
            cmbStockType.DisplayMemberPath = "GOODS_NAME";
            cmbStockType.SelectedValuePath = "GOODS_ID";
            cmbStockType.ItemsSource = MainApp.I_SystemService.GoodsGetListGoodsClassID(intGoodsClassID);

            this.cmbStockType.SelectionChanged += new SelectionChangedEventHandler(cmbStockType_SelectionChanged);

            this.rbSingle.IsChecked = true;

            if (this.cmbStockType.Items.Count == 1)
            {
                this.cmbStockType.SelectedIndex = 0;
            }
        }

        public void U_Refresh()
        {
            this.txtBeginStockBarCode.Text = string.Empty;

            this.txtEndStockBarCode.Text = string.Empty;

            this.lvStockBarcode.Items.Clear();
        }

        void cmbStockType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.U_StockTypeChanged != null)
                U_StockTypeChanged();

        }

        private List<string> getBarCodeList(string startCode, string endCode)
        {
            List<string> lstResult = new List<string>();

            if (startCode.Trim().Length != endCode.Trim().Length)
            {
                return lstResult;
            }

            int intStartNum = -1, intEndNum = -1;
            string strStartCode = "-", strEndCode = "-";
            char[] codeStartArray = startCode.ToCharArray();
            char[] codeEndArray = endCode.ToCharArray();

            for (int i = codeStartArray.Length - 1; i >= 0; i--)
            {
                if (!(codeStartArray[i] >= '0' && codeStartArray[i] <= '9' && codeEndArray[i] >= '0' && codeEndArray[i] <= '9'))
                {
                    if (startCode.Length - i <= 5)
                    {
                        intStartNum = Convert.ToInt32(startCode.Substring(i + 1, startCode.Length - (i + 1)));
                        intEndNum = Convert.ToInt32(endCode.Substring(i + 1, endCode.Length - (i + 1)));
                        strStartCode = startCode.Substring(0, i + 1);
                        strEndCode = endCode.Substring(0, i + 1);
                    }
                    else
                    {
                        intStartNum = Convert.ToInt32(startCode.Substring(startCode.Length - 5, 5));
                        intEndNum = Convert.ToInt32(endCode.Substring(endCode.Length - 5, 5));
                        strStartCode = startCode.Substring(0, startCode.Length - 5);
                        strEndCode = endCode.Substring(0, startCode.Length - 5);
                    }

                    break;
                }
                else if (i == 0)
                {
                    int numLength = startCode.Length;
                    if (numLength > 5)
                    {
                        numLength = 5;
                    }
                    intStartNum = Convert.ToInt32(startCode.Substring(startCode.Length - numLength, numLength));
                    intEndNum = Convert.ToInt32(endCode.Substring(endCode.Length - numLength, numLength));
                    strStartCode = startCode.Substring(0, startCode.Length - numLength);
                    strEndCode = endCode.Substring(0, startCode.Length - numLength);
                }
            }
            if (intStartNum == -1 || intEndNum == -1)
            {
                strStartCode = startCode;
                strEndCode = endCode;
            }
            if (!strStartCode.Equals(strEndCode))
            {
                lstResult.Add(startCode);
                lstResult.Add(endCode);
                return lstResult;
            }

            for (int i = intStartNum; i <= intEndNum; i++)
            {
                int tempLength = startCode.Length - (strStartCode + i.ToString()).Length;
                string strZeroNum = "";
                if (tempLength > 0)
                {
                    for (int j = 0; j < tempLength; j++)
                    {
                        strZeroNum += "0";
                    }
                }
                else
                {
                    strZeroNum = "";
                }

                lstResult.Add(strStartCode + strZeroNum + i.ToString());
            }

            return lstResult;
        }

        /// <summary>
        /// 初始化控件并获得数据
        /// </summary>
        public bool U_CheckValidate(out string Message)
        {
            //校验数据是否合法
            Message = string.Empty;


            if (cmbStockType.SelectedValue == null)
            {
                Message = "请选择类型！";
                return false;
            }

            if (!this.rbSingle.IsChecked.Value && !this.rbMultiple.IsChecked.Value && !this.rbSingleContinue.IsChecked.Value)
            {
                Message = "请选择入库方式！";
                return false;
            }

            if (this.rbSingle.IsChecked.Value)
            {
                if (!Common.RegexValid.IsValidate(this.txtBeginStockBarCode.Text, MainApp.I_SystemService.GetSysParameter("BoxBarcodeValidRegex", "")))
                {
                    Message = "请检查条码是否合法！";
                    txtBeginStockBarCode.Focus();
                    txtBeginStockBarCode.SelectAll();
                    return false;
                }           
            }

            if (this.rbSingleContinue.IsChecked.Value)
            {
                if (!Common.RegexValid.IsValidate(this.txtBeginStockBarCode.Text, MainApp.I_SystemService.GetSysParameter("BoxBarcodeValidRegex", "")))
                {
                    Message = "请检查条码是否合法！";
                    txtBeginStockBarCode.Focus();
                    txtBeginStockBarCode.SelectAll();
                    return false;
                }

                if (this.txtEndStockBarCode.Text.TrimEnd() != string.Empty && !Common.RegexValid.IsValidate(this.txtBeginStockBarCode.Text, MainApp.I_SystemService.GetSysParameter("BoxBarcodeValidRegex", "")))
                {
                    Message = "请检查条码是否合法！";
                    txtEndStockBarCode.Focus();
                    txtEndStockBarCode.SelectAll();
                    return false;
                }
            }

            if (this.rbMultiple.IsChecked.Value)
            {
                if (this.lvStockBarcode.Items.Count == 0)
                {
                    Message = "请输入托盘组条码！";
                    return false;
                }
            }


            return true;
        }

        public void Add(string STOCK_BARCODE)
        {
            if (string.IsNullOrEmpty(STOCK_BARCODE))
            {
                return;
            }

            if (!Common.RegexValid.IsValidate(STOCK_BARCODE, MainApp.I_SystemService.GetSysParameter("BoxBarcodeValidRegex", "")))
            {
                return ;
            }

            ListViewItem lvi = new ListViewItem();
            
            lvi.Content = STOCK_BARCODE;
            if (this.lvStockBarcode.Items.Contains(lvi))
            {
                return;
            }

            lvStockBarcode.Items.Add(lvi);


        }

        //回车确认
        private void WrapPanel_KeyDown(object sender, KeyEventArgs e)
        {
            
            if (e.Key == Key.Enter)
            {
                TextBox textBox = e.OriginalSource as TextBox;
                if (textBox != null)
                {
                    switch (textBox.Name)
                    {
                        case "txtBeginStockBarCode":
                            //设置焦点  
                            this.Add(this.txtBeginStockBarCode.Text);
                            break;
                    }
                }
            }
        }

        private void rbSingle_Checked(object sender, RoutedEventArgs e)
        {
            if (this.rbSingle.IsChecked.Value)
            {
                this.tbkStockBarcode.Text = "箱条码";
                this.lvStockBarcode.Visibility = System.Windows.Visibility.Hidden;

                this.panelEndStockBarCode.Visibility = System.Windows.Visibility.Hidden;
            }
            if (this.rbSingleContinue.IsChecked.Value)
            {
                this.tbkStockBarcode.Text = "起始条码";
                this.lvStockBarcode.Visibility = System.Windows.Visibility.Hidden;

                this.panelEndStockBarCode.Visibility = System.Windows.Visibility.Visible;
            }
            if (this.rbMultiple.IsChecked.Value)
            {
                this.lvStockBarcode.Visibility = System.Windows.Visibility.Visible;

                this.panelEndStockBarCode.Visibility = System.Windows.Visibility.Collapsed;
            }
        }

    }
}
