﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="LCD_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="LCD_LIST" type="SiaSun.LMS.Model.LCD_LIST, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SELECTRESULT" class="LCD_LIST">
      <result property="LCD_LIST_ID" column="lcd_list_id" />
      <result property="LCD_ID" column="lcd_id" />
      <result property="DISPLAY_TYPE" column="display_type" />
      <result property="DISPLAY_FLAG" column="display_flag" />
      <result property="WRITE_TIME" column="write_time" />
      <result property="LIFE_TIME" column="life_time" />
      <result property="HANDLE_FLAG" column="handle_flag" />
      <result property="DISPLAY_INFO1" column="display_info1" />
      <result property="DISPLAY_INFO2" column="display_info2" />
      <result property="DISPLAY_INFO3" column="display_info3" />
      <result property="DISPLAY_INFO4" column="display_info4" />
      <result property="DISPLAY_INFO5" column="display_info5" />
      <result property="DISPLAY_INFO6" column="display_info6" />
      <result property="DISPLAY_INFO7" column="display_info7" />
      <result property="DISPLAY_INFO8" column="display_info8" />
      <result property="DISPLAY_INFO9" column="display_info9" />
      <result property="DISPLAY_INFO10" column="display_info10" />
      <result property="DISPLAY_INFO11" column="display_info11" />
      <result property="DISPLAY_INFO12" column="display_info12" />
      <result property="DISPLAY_INFO13" column="display_info13" />
      <result property="DISPLAY_INFO14" column="display_info14" />
      <result property="DISPLAY_INFO15" column="display_info15" />
      <result property="DISPLAY_INFO16" column="display_info16" />
      <result property="DISPLAY_INFO17" column="display_info17" />
      <result property="DISPLAY_INFO18" column="display_info18" />
      <result property="DISPLAY_INFO19" column="display_info19" />
      <result property="DISPLAY_INFO20" column="display_info20" />
      <result property="BACKUP_FIELD1" column="backup_field1" />
      <result property="BACKUP_FIELD2" column="backup_field2" />
      <result property="BACKUP_FIELD3" column="backup_field3" />
      <result property="BACKUP_FIELD4" column="backup_field4" />
      <result property="BACKUP_FIELD5" column="backup_field5" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="LCD_LIST_SELECT" parameterClass="int" resultMap="SELECTRESULT">
      Select
      lcd_list_id,
      lcd_id,
      display_type,
      display_flag,
      write_time,
      life_time,
      handle_flag,
      display_info1,
      display_info2,
      display_info3,
      display_info4,
      display_info5,
      display_info6,
      display_info7,
      display_info8,
      display_info9,
      display_info10,
      display_info11,
      display_info12,
      display_info13,
      display_info14,
      display_info15,
      display_info16,
      display_info17,
      display_info18,
      display_info19,
      display_info20,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      From LCD_LIST
    </select>

    <select id="LCD_LIST_SELECT_BY_ID" parameterClass="int" extends = "LCD_LIST_SELECT" resultMap="SELECTRESULT">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          lcd_list_id=#LCD_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>



    <insert id="LCD_LIST_INSERT" parameterClass="LCD_LIST">
      Insert Into LCD_LIST (
      lcd_list_id,
      lcd_id,
      display_type,
      display_flag,
      write_time,
      life_time,
      handle_flag,
      display_info1,
      display_info2,
      display_info3,
      display_info4,
      display_info5,
      display_info6,
      display_info7,
      display_info8,
      display_info9,
      display_info10,
      display_info11,
      display_info12,
      display_info13,
      display_info14,
      display_info15,
      display_info16,
      display_info17,
      display_info18,
      display_info19,
      display_info20,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      )Values(
      #LCD_LIST_ID#,
      #LCD_ID#,
      #DISPLAY_TYPE#,
      #DISPLAY_FLAG#,
      #WRITE_TIME#,
      #LIFE_TIME#,
      #HANDLE_FLAG#,
      #DISPLAY_INFO1#,
      #DISPLAY_INFO2#,
      #DISPLAY_INFO3#,
      #DISPLAY_INFO4#,
      #DISPLAY_INFO5#,
      #DISPLAY_INFO6#,
      #DISPLAY_INFO7#,
      #DISPLAY_INFO8#,
      #DISPLAY_INFO9#,
      #DISPLAY_INFO10#,
      #DISPLAY_INFO11#,
      #DISPLAY_INFO12#,
      #DISPLAY_INFO13#,
      #DISPLAY_INFO14#,
      #DISPLAY_INFO15#,
      #DISPLAY_INFO16#,
      #DISPLAY_INFO17#,
      #DISPLAY_INFO18#,
      #DISPLAY_INFO19#,
      #DISPLAY_INFO20#,
      #BACKUP_FIELD1#,
      #BACKUP_FIELD2#,
      #BACKUP_FIELD3#,
      #BACKUP_FIELD4#,
      #BACKUP_FIELD5#
      )
      <!--<selectKey  resultClass="int" type="post" property="LCD_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="LCD_LIST_UPDATE" parameterClass="LCD_LIST">
      Update LCD_LIST Set
      <!--lcd_list_id=#LCD_LIST_ID#,-->
      lcd_id=#LCD_ID#,
      display_type=#DISPLAY_TYPE#,
      display_flag=#DISPLAY_FLAG#,
      write_time=#WRITE_TIME#,
      life_time=#LIFE_TIME#,
      handle_flag=#HANDLE_FLAG#,
      display_info1=#DISPLAY_INFO1#,
      display_info2=#DISPLAY_INFO2#,
      display_info3=#DISPLAY_INFO3#,
      display_info4=#DISPLAY_INFO4#,
      display_info5=#DISPLAY_INFO5#,
      display_info6=#DISPLAY_INFO6#,
      display_info7=#DISPLAY_INFO7#,
      display_info8=#DISPLAY_INFO8#,
      display_info9=#DISPLAY_INFO9#,
      display_info10=#DISPLAY_INFO10#,
      display_info11=#DISPLAY_INFO11#,
      display_info12=#DISPLAY_INFO12#,
      display_info13=#DISPLAY_INFO13#,
      display_info14=#DISPLAY_INFO14#,
      display_info15=#DISPLAY_INFO15#,
      display_info16=#DISPLAY_INFO16#,
      display_info17=#DISPLAY_INFO17#,
      display_info18=#DISPLAY_INFO18#,
      display_info19=#DISPLAY_INFO19#,
      display_info20=#DISPLAY_INFO20#,
      backup_field1=#BACKUP_FIELD1#,
      backup_field2=#BACKUP_FIELD2#,
      backup_field3=#BACKUP_FIELD3#,
      backup_field4=#BACKUP_FIELD4#,
      backup_field5=#BACKUP_FIELD5#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          lcd_list_id=#LCD_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="LCD_LIST_DELETE" parameterClass="int">
      Delete From LCD_LIST
      <dynamic prepend="WHERE">
        <isParameterPresent>
          lcd_list_id=#LCD_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>