﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.LOG_QUERY"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="LOG_QUERY" Height="222" Width="430" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <GroupBox Grid.Row="0" Grid.ColumnSpan="3" Header="选择打开指定日志文件" >
             <Border  >
                <WrapPanel>
                    <TextBlock Text="文件名：" VerticalAlignment="Center" Margin="5"></TextBlock>
                    <TextBox Name="txtFilePath" MinWidth="200" Height="23" Margin="5" IsEnabled="False"></TextBox>
                    <Button Name="btnOpenFile" Content="打开" Margin="5" Width="50" Click="btnOpenFile_Click"></Button>
                </WrapPanel>
            </Border>           
        </GroupBox>

        <uc:ucTreeView x:Name="tvwNode" Grid.Row="1" Grid.Column="0" MinWidth="150" ></uc:ucTreeView >
        
        <GridSplitter  Grid.Row="1" Grid.Column="1" Width="2" VerticalAlignment="Stretch"></GridSplitter>

        <GroupBox Name="grpLog" Grid.Column="2" Grid.Row="1" Header="日志信息" Tag=" {0}-日志信息" >
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <Border Grid.Row="0" Grid.ColumnSpan="3" CornerRadius="2" Background="PaleGoldenrod">
                    <WrapPanel Margin="5" ButtonBase.Click="WrapPanel_Click">
                        <TextBlock Text="时间："  VerticalAlignment="Center"></TextBlock>
                        <TextBox Name="txtTime" MinWidth="100"></TextBox>
                        <Button Name="btnQuery" Content="查询"  Width="50" Margin="5,0,0,0"></Button>
                        <Button Name="btnRefresh" Content="刷新"  Width="50" Margin="5,0,0,0"></Button>
                        <Button Name="btnExport" Content="导出Excel"   Margin="5,0,0,0"></Button>
                    </WrapPanel>
                </Border>
                <uc:DataGridTemplate x:Name="gridLog" Grid.Row="1">
                    <uc:DataGridTemplate.Columns>
                        <DataGridTextColumn Header="时间" Binding="{Binding Path=TimeStamp}"></DataGridTextColumn>
                        <DataGridTextColumn Header="消息" Binding="{Binding Path=Message}"></DataGridTextColumn>
                    </uc:DataGridTemplate.Columns>
                </uc:DataGridTemplate>
            </Grid>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
