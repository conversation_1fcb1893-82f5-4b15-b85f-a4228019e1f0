﻿using System;
using System.Collections.Generic;

using System.Text;

namespace SiaSun.LMS.Enum
{
    /// <summary>
    /// 正反
    /// </summary>
    public enum FLAG
    {
        P = 1,
        N = 0
    }

    /// <summary>
    /// 计划类型分组
    /// </summary>
    public enum PLAN_TYPE_GROUP
    {
        /// <summary>
        /// 仓储类
        /// </summary>
        StoreGroup = 1,
        /// <summary>
        /// 工位类
        /// </summary>
        WorkStationGroup = 2,
        /// <summary>
        /// 生产装配类
        /// </summary>
        ProduceGroup = 3
    }

    /// <summary>
    /// 计划类别
    /// </summary>
    public enum PLAN_TYPE_CODE
    {
        PlanCommonOut = 0,
        PlanCarrierOut = 1,
        PlanIn = 2,
        /// <summary>
        /// 入库红冲（出库）
        /// </summary>
        PlanReversalOut = 3,
        /// <summary>
        /// 出库红冲（入库）
        /// </summary>
        PlanReversalIn = 4,
        /// <summary>
        /// 盘点下架
        /// </summary>
        PlanCommonDown = 5,
    }

    /// <summary>
    /// 计划状态
    /// </summary>
    public enum PLAN_STATUS
    {
        Waiting,
        Executing, 
        Complete,
        WaitConfirm,

        Pause,//iWMS暂停
        Cancel,//iWMS取消
    }

    /// <summary>
    /// 工艺流程任务类型
    /// </summary>
    public enum PLAN_INOUT
    {
        /// <summary>
        /// 入库
        /// </summary>
        In = 1,
        /// <summary>
        /// 出库
        /// </summary>
        Out = 2,
        /// <summary>
        /// 移库
        /// </summary>
        Move = 3,
        /// <summary>
        /// 拣选，回流
        /// </summary>
        Sort = 4
    }

    /// <summary>
    /// 管理任务类型
    /// </summary>
    public enum MANAGE_TYPE
    {
        ManageIn,
        ManageOut,
        ManageUp,
        ManageDown,
        ManageMove,
        ManagePick,
        ManageTransport,
        ManageMoveLaneway,
        ManageAdjust,
        ManageAdjustIn,
        ManageAdjustOut
    }

    /// <summary>
    /// 管理任务出入类型
    /// </summary>
    public enum MANAGE_TYPE_INOUT
    {
        In=1,
        Out=2,
        Move=3,
        Transport=4
    }

    /// <summary>
    /// 任务状态
    /// </summary>
    public enum MANAGE_STATUS
    {
        Waiting,
        Picking,
        Picked,
        Executing,
        WaitPick,//托盘流通装拆托
        WaitConfirm,
        Complete,
        ExceptionComplete,
        Error,
        Cancel,
        Pause,//iWMS暂停
    }

    /// <summary>
    /// 控制任务状态
    /// </summary>
    public enum CONTROL_STATUS
    {
        /// <summary>
        /// 等待
        /// </summary>
        Wait = 0,
        /// <summary>
        /// iWMS暂停
        /// </summary>
        Pause = 5,
        /// <summary>
        /// 调度已经获取任务
        /// </summary>
        Control_Readed = 7,
        /// <summary>
        /// 开始运行
        /// </summary>
        Runing = 10,
        /// <summary>
        /// 堆垛机运行
        /// </summary>
        DeviceRuning = 11,
        /// <summary>
        /// AGV运行
        /// </summary>
        AGVRuning = 12,
        /// <summary>
        /// 调度申请改道
        /// </summary>
        LterRouteApply = 30,
        /// <summary>
        /// 管理答复改道申请
        /// </summary>
        LterRouteReplay = 40,
        /// <summary>
        /// 异常完成
        /// </summary>
        TaskAbend = 990,
        /// <summary>
        /// 任务被删除
        /// </summary>
        TaskDelete = 900,
        /// <summary>
        /// 堆垛机的取空处理
        /// </summary>
        EmptyOutPut = 980,
        /// <summary>
        /// 堆垛机的送货重需要改路径处理
        /// </summary>
        RepeatInput = 970,
        /// <summary>
        /// 任务完成
        /// </summary>
        Finish = 999,
        /// <summary>
        /// WebService调用临时任务
        /// </summary>
        TempStatus = -7
    }

    /// <summary>
    /// 指令申请状态
    /// </summary>
    public enum CONTROL_APPLY_STATUS
    {
        /// <summary>
        /// 等待
        /// </summary>
        Waiting = 0,
        /// <summary>
        /// 读取
        /// </summary>
        Read = 1,
        /// <summary>
        /// 完成
        /// </summary>
        Finish = 2,
        /// <summary>
        /// 异常
        /// </summary>
        Error = 3
    }

    /// <summary>
    /// 
    /// </summary>
    public enum CONTROL_TYPE
    {
        In = 1,
        Out = 2,
        Move = 3,
        MoveStation = 4,
    }

    /// <summary>
    /// 库区类型
    /// </summary>
    public enum AREA_TYPE
    {
        LiKu,
        XuNiKu
    }

    /// <summary>
    /// 库区分组 2020-07-06 09:44:55
    /// </summary>
    public enum AREA_GROUP
    {
        /// <summary>
        /// 自动设备存取
        /// </summary>
        Auto = 1,
        /// <summary>
        /// 人工存取
        /// </summary>
        Manual = 2
    }

    /// <summary>
    /// 货位类型
    /// </summary>
    public enum CELL_TYPE
    {
        /// <summary>
        /// 货位
        /// </summary>
        Cell,
        /// <summary>
        /// 站台/输送台
        /// </summary>
        Station,
        /// <summary>
        /// 盐水箱
        /// </summary>
        Tank
    }

    /// <summary>
    /// 货位存储类型
    /// </summary>
    public enum CELL_STORAGE_TYPE
    {
        /// <summary>
        /// 存储单托盘
        /// </summary>
        Single,
        /// <summary>
        /// 存储多托盘
        /// </summary>
        Multiple
    }

    /// <summary>
    /// 货架类型
    /// </summary>
    public enum SHELF_TYPE
    {
        /// <summary>
        /// 单伸
        /// </summary>
        Single = 1,

        /// <summary>
        /// 双伸内测
        /// </summary>
        DoubleIn = 2,

        /// <summary>
        /// 双伸外测
        /// </summary>
        DoubleOut = 3
    }

    /// <summary>
    /// 货位类别
    /// </summary>
    public enum CELL_FORK_TYPE
    {
        /// <summary>
        /// 普通
        /// </summary>
        Normal,
        /// <summary>
        /// 双伸
        /// </summary>
        Double,
        /// <summary>
        /// 双叉
        /// </summary>
        Multi
    }

    /// <summary>
    /// 货位任务类型
    /// </summary>
    public enum CELL_INOUT
    {
        /// <summary>
        /// 入
        /// </summary>
        In = 1,
        /// <summary>
        /// 出
        /// </summary>
        Out = 2,
        /// <summary>
        /// 可入可出
        /// </summary>
        InOut = 3
    }

    /// <summary>
    /// 货位存储状态
    /// </summary>
    public enum CELL_STATUS
    {
        /// <summary>
        /// 满货
        /// </summary>
        Full,
        /// <summary>
        /// 有货
        /// </summary>
        Have,
        /// <summary>
        /// 无货
        /// </summary>
        Nohave,
        /// <summary>
        /// 空托盘
        /// </summary>
        Pallet,
        /// <summary>
        /// 异常货位
        /// </summary>
        Exception,
        /// <summary>
        /// 禁用
        /// </summary>
        Forbiden,
        /// <summary>
        /// 拆除
        /// </summary>
        Remove,
    }

    /// <summary>
    /// 货位运行状态
    /// </summary>
    public enum RUN_STATUS
    {
        /// <summary>
        /// 禁用
        /// </summary>
        Disable,
        /// <summary>
        /// 待用
        /// </summary>
        Enable,
        /// <summary>
        /// 运行
        /// </summary>
        Run,
        /// <summary>
        /// 选定
        /// </summary>
        Selected,
    }

    /// <summary>
    /// 任务执行的设备类型
    /// </summary>
    public enum DEVICE_TYPE
    {
        /// <summary>
        /// 系统设备
        /// </summary>
        system,
        /// <summary>
        /// 自动设备
        /// </summary>
        auto,
        /// <summary>
        /// AGV设备
        /// </summary>
        agv
    }

    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试
        /// </summary>
        DEBUG,

        /// <summary>
        /// 信息
        /// </summary>
        INFO,

        /// <summary>
        /// 告警
        /// </summary>
        WARN,

        /// <summary>
        /// 错误
        /// </summary>
        ERROR,

        /// <summary>
        /// 毁灭
        /// </summary>
        FATAL,

    }

    /// <summary>
    /// 日志类型
    /// </summary>
    public enum LogType
    {
        /// <summary>
        /// 单纯记录
        /// </summary>
        record,

        /// <summary>
        /// 声光告警提示
        /// </summary>
        alert
    }

    /// <summary>
    /// 组垛形式
    /// </summary>
    public enum CellModel
    {
        /// <summary>
        /// 托盘
        /// </summary>
        Stock,

        /// <summary>
        /// 托盘垛
        /// </summary>
        /// 
        Stack,

        /// <summary>
        /// 双垛
        /// </summary>
        DoubleStack

    }

    /// <summary>
    /// 入库前选定巷道优先考虑的变量
    /// </summary>
    public enum SelectLanewayOrderType
    {
        /// <summary>
        /// 优先选择货位剩余多的
        /// </summary>
        CellFirst,

        /// <summary>
        /// 优先选择入库任务少的巷道
        /// </summary>
        ManageFirst,

        /// <summary>
        /// 优先选择库存少的巷道
        /// </summary>
        StorageFirst,

        /// <summary>
        /// 优先给穿梭车任务配对
        /// </summary>
        PairFirst
    }

    /// <summary>
    /// 系统名称
    /// </summary>
    public enum SystemName
    {
        SSWMS,
        iWMS
    }

    /// <summary>
    /// 区域编码
    /// </summary>
    public enum AreaCode
    {
        Product,        //成品
        Battery,        //素电
        ZCQ             //暂存区
    }

    /// <summary>
    /// 一些匹配的关键字
    /// </summary>
    public enum KeyWords
    {
        //分配货位
        AllotCell,
        //出库计划执行时根据PLAN_LIST 中配置的出库站台下任务
        ByList,
        Success,
        Fail,
        emptyPallet,
        AGVExceptStation,

        扫码申请,
        外层自动倒库,
        管理申请,
        接口下达,
        自动补空托盘
    }

    /// <summary>
    /// 库房的ID，方便识别
    /// </summary>
    public enum WarehouseID
    {
        成品库ID = 1,
        分拣库ID = 2,
        双伸库ID = 3
    }

    /// <summary>
    /// 库区的ID，方便识别
    /// </summary>
    public enum AreaID
    {
        立库 = 1,
        素电立库区 = 2,
        成品站台区 = 5,
        素电站台区 = 7,
    }

    /// <summary>
    /// 根据属性值生成sql语句的模式
    /// </summary>
    public enum MatchMode
    {
        /// <summary>
        /// 空值随意匹配
        /// </summary>
        IgnoreNull,

        /// <summary>
        /// 关键字段空值完全匹配（传入空则寻找属性为空的库存）
        /// </summary>
        ConsiderKey,

        /// <summary>
        /// 所有字段完全匹配
        /// </summary>
        FullMatch
    }

    /// <summary>
    /// LCD显示类型
    /// </summary>
    public enum DISPLAY_TYPE
    {
        /// <summary>
        /// 根据标记，标记>0显示
        /// </summary>
        ByFlag,

        /// <summary>
        /// 根据存活时间
        /// </summary>
        ByLifeTime
    }

}
