﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========================BureauBlue Skin==================  -->
    <Color x:Key="BlackColor">#FF000000</Color>
    <Color x:Key="WhiteColor">#FFFFFFFF</Color>
    <SolidColorBrush x:Key="OutsideFontColor" Color="#FF000000" />
    <SolidColorBrush x:Key="NormalHighlightBrush" Color="#FFFFFFFF" />
    <SolidColorBrush x:Key="ControlBorderBrush" Color="#FFB1703C" />
    <SolidColorBrush x:Key="GlyphBrush" Color="#FF527DB5" />
    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#FFF" />
    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="#888" />
    <SolidColorBrush x:Key="DisabledBackgroundBrush" Color="#A5FFFFFF" />
    <SolidColorBrush x:Key="DisabledBorderBrush" Color="#66FFFFFF" />
    <SolidColorBrush x:Key="FocusBrush" Color="#FFE99862" />

    <LinearGradientBrush x:Key="ControlBackgroundBrush" StartPoint="0.056,0.5" EndPoint="1.204,0.5">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="1" Color="#FFD4D7DB" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="NormalBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="0.521" Color="#FF8AAEDA" />
        <GradientStop Offset="0.194" Color="#FFC6D6EC" />
        <GradientStop Offset="0.811" Color="#FFB4C9E5" />
        <GradientStop Offset="0.507" Color="#FFB7C8E0" />
        <GradientStop Offset="1" Color="#FFD1DEF0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="NormalBorderBrush" StartPoint="0.5,1" EndPoint="0.5,0">
        <GradientStop Color="#FF84B2D4" />
        <GradientStop Offset="1" Color="#FFADC7DE" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="MouseOverBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="0.318" Color="#FFFEF3B5" />
        <GradientStop Offset="0.488" Color="#FFFFEB70" />
        <GradientStop Offset="0.502" Color="#FFFFD02E" />
        <GradientStop Offset="0.834" Color="#FFFFD932" />
        <GradientStop Offset="1" Color="#FFFFF48B" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="MouseOverBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFEEE8CF" />
        <GradientStop Offset="0.536" Color="#FFC4AF8C" />
        <GradientStop Offset="1" Color="#FFDCD1BF" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="MouseOverHighlightBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFFFFFFB" />
        <GradientStop Offset="1" Color="#FFFEF3B5" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="PressedBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFC3BCAE" />
        <GradientStop Offset="0.046" Color="#FFFDCE9D" />
        <GradientStop Offset="0.452" Color="#FFFFA35B" />
        <GradientStop Offset="0.461" Color="#FFFF8A2C" />
        <GradientStop Offset="0.724" Color="#FFFF9F30" />
        <GradientStop Offset="1" Color="#FFFFC472" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="PressedBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FF8E8165" />
        <GradientStop Offset="1" Color="#FFC3BCAE" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="PressedHighlightBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.665" Color="#FFFFFFFF" />
        <GradientStop Offset="0" Color="#FFC3BCAE" />
    </LinearGradientBrush>

    <!--  CheckBox Brushes  -->
    <SolidColorBrush x:Key="CheckBoxBackgroundBrush" Color="#FFF4F4F4" />
    <SolidColorBrush x:Key="CheckBoxBorderBrush" Color="#FF868686" />
    <SolidColorBrush x:Key="CheckBoxInnerBoxBackgroundBrush" Color="#FFCACFD5" />
    <LinearGradientBrush x:Key="CheckBoxInnerBoxBorderBrush" StartPoint="0.915,0.92" EndPoint="-0.007,-0.012">
        <GradientStop Color="#FFE4E5E9" />
        <GradientStop Offset="1" Color="#FFA2ACB9" />
    </LinearGradientBrush>
    <SolidColorBrush x:Key="CheckBoxBackgroundFillBrush" Color="#FFDEEAFA" />
    <SolidColorBrush x:Key="CheckBoxMouseOverBrush" Color="#FFFCE7AF" />
    <LinearGradientBrush x:Key="CheckBoxPressBorderBrush" StartPoint="0.886,0.808" EndPoint="0.055,0.119">
        <GradientStop Color="#FFF4D9BE" />
        <GradientStop Offset="1" Color="#FFF28A27" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="CheckBoxInnerBoxGradientBrush" StartPoint="0.238,0.228" EndPoint="0.752,0.749">
        <GradientStop Offset="0.254" Color="#00F6F6F6" />
        <GradientStop Offset="0.54" Color="#53F8F8F8" />
        <GradientStop Offset="0.996" Color="#BFFFFFFF" />
    </LinearGradientBrush>

    <!--  RadioButton Brushes  -->
    <SolidColorBrush x:Key="RadioButtonCheckIconBorderBrush" Color="#FFDA8229" />
    <SolidColorBrush x:Key="RadioButtonBackgroundBrush" Color="#FFEDEDEE" />
    <SolidColorBrush x:Key="RadioButtonBorderBrush" Color="#FF597AA5" />
    <LinearGradientBrush x:Key="RadioButtonInnerCircleBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Color="#FFC8CDD2" />
        <GradientStop Offset="0.531" Color="#FFF2F2F2" />
        <GradientStop Offset="1" Color="#FFF5F5F5" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonInnerCircleBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.004" Color="#FFB3B8BD" />
        <GradientStop Offset="1" Color="#FFE0E0E0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonMouseOverBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Color="#FFE3DBA9" />
        <GradientStop Offset="0.531" Color="#FFFEF5DD" />
        <GradientStop Offset="1" Color="#FFFEF5DD" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonMouseOverBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.004" Color="#FFE3CF87" />
        <GradientStop Offset="1" Color="#FFFCF0D3" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonPressBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Color="#FFC8CDD2" />
        <GradientStop Offset="0.531" Color="#FFF2F2F2" />
        <GradientStop Offset="1" Color="#FFF5F5F5" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonPressBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.004" Color="#FFAC773F" />
        <GradientStop Offset="0.987" Color="#FFC8B5A3" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonCheckIconBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.013" Color="#FFFDDC8B" />
        <GradientStop Offset="0.188" Color="#FFFDDC8B" />
        <GradientStop Offset="0.491" Color="#FFF9952F" />
        <GradientStop Offset="1" Color="#FFF9954A" />
    </LinearGradientBrush>

    <!--  ScrollBar RepeatButtonBrushes  -->
    <SolidColorBrush x:Key="ScrollBarThumbPressedBorderBrush" Color="#FF17498A" />
    <SolidColorBrush x:Key="ScrollBarThumbMouseOverBorderBrush" Color="#FF3C6EB0" />
    <SolidColorBrush x:Key="ScrollBarThumbBorderBrush" Color="#FF606F94" />
    <SolidColorBrush x:Key="ScrollBarRepeatButtonBorderBrush" Color="#FF8C97A5" />
    <LinearGradientBrush x:Key="ScrollBarRepeatButtonBrush" StartPoint="0.5,1" EndPoint="0.5,0">
        <GradientStop Offset="0.5" Color="#FFF1F6FE" />
        <GradientStop Offset="0.513" Color="#FFC7D9F1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarRepeatButtonPressedBrush" StartPoint="0.5,1" EndPoint="0.5,0">
        <GradientStop Offset="0.5" Color="#FFF1F6FE" />
        <GradientStop Offset="0.513" Color="#FFD1D6DD" />
    </LinearGradientBrush>
    <SolidColorBrush x:Key="ScrollBarRepeatButtonPressedBorderBrush" Color="#FF19598A" />

    <!--  ScrollBar ThumbBrushes  -->
    <LinearGradientBrush x:Key="ScrollBarThumbBrush" StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
        <GradientStop Offset="0" Color="#FFD1DBE6" />
        <GradientStop Offset="0.5" Color="#FFD1DAE4" />
        <GradientStop Offset="0.513" Color="#FFE6E9F0" />
        <GradientStop Offset="1" Color="#FFE8E9E9" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarThumbMouseOverBrush" StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
        <GradientStop Offset="0" Color="#FFB4D1F7" />
        <GradientStop Offset="0.5" Color="#FFAACBF6" />
        <GradientStop Offset="0.513" Color="#FFCADFFA" />
        <GradientStop Offset="1" Color="#FFBED0E8" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarThumbPressedBrush" StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
        <GradientStop Offset="0" Color="#FFB4D1F7" />
        <GradientStop Offset="0.5" Color="#FF6EA6F0" />
        <GradientStop Offset="0.513" Color="#FFA4C7F6" />
        <GradientStop Offset="1" Color="#FF9CBBE5" />
    </LinearGradientBrush>

    <!--  ListItem Brushes  -->
    <LinearGradientBrush x:Key="ListItemSelectedBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.046" Color="#FFFFFFFF" />
        <GradientStop Offset="0.194" Color="#FFD7E0EA" />
        <GradientStop Offset="0.507" Color="#FFBCC5D5" />
        <GradientStop Offset="0.521" Color="#FFA4ADBB" />
        <GradientStop Offset="0.811" Color="#FFBAC1CF" />
        <GradientStop Offset="0.982" Color="#FFE3E4E6" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ListItemSelectedBorderBrush" StartPoint="0.5,1" EndPoint="0.5,0">
        <GradientStop Color="#FF8B8B8B" />
        <GradientStop Offset="1" Color="#FFADADAD" />
    </LinearGradientBrush>

    <!--  ProgressBar Brushes  -->
    <LinearGradientBrush x:Key="ProgressBarIndicatorBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="#FFC6D6EC" />
        <GradientStop Offset="0.502" Color="#FFBDD6FF" />
        <GradientStop Offset="0.522" Color="#FF71A7FD" />
        <GradientStop Offset="0.763" Color="#FF94BDFD" />
        <GradientStop Offset="1" Color="#FFA9CAFF" />
    </LinearGradientBrush>

    <!--  SliderBrushes  -->
    <SolidColorBrush x:Key="SliderThumbBorderBrush" Color="#FF496FA2" />
    <SolidColorBrush x:Key="SliderThumbBrush" Color="#FFC1C1C1" />
    <LinearGradientBrush x:Key="SliderBackgroundBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.493" Color="#FF324D70" />
        <GradientStop Offset="1" Color="#FF6A8BB6" />
    </LinearGradientBrush>

    <!--  TextControlsBrushes  -->
    <LinearGradientBrush x:Key="TextControlBorderBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Color="#FFABAEB3" />
        <GradientStop Offset="1" Color="#FFE2E8EE" />
    </LinearGradientBrush>

    <!--  ToolBar Brushes  -->
    <SolidColorBrush x:Key="ToolBarButtonHover" Color="#FF125E7C" />
    <SolidColorBrush x:Key="ToolBarGripper" Color="#C6C3C6" />
    <SolidColorBrush x:Key="ToolBarSubMenuBackground" Color="#FFFDFDFD" />
    <SolidColorBrush x:Key="ToolBarMenuBorder" Color="#FFFFFFFF" />
    <LinearGradientBrush x:Key="ToolBarHorizontalBackground" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="#FFFFFF" />
        <GradientStop Offset="0.5" Color="#FFFBFF" />
        <GradientStop Offset="1" Color="#F7F7F7" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarToggleButtonHorizontalBackground" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="#ECECEC" />
        <GradientStop Offset="0.5" Color="#DDDDDD" />
        <GradientStop Offset="1" Color="#A0A0A0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarToggleButtonVerticalBackground" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Offset="0" Color="#ECECEC" />
        <GradientStop Offset="0.5" Color="#DDDDDD" />
        <GradientStop Offset="1" Color="#A0A0A0" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarVerticalBackground" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Offset="0" Color="#FFFFFF" />
        <GradientStop Offset="0.5" Color="#FFFBFF" />
        <GradientStop Offset="1" Color="#F7F7F7" />
    </LinearGradientBrush>

    <BorderGapMaskConverter x:Key="BorderGapMaskConverter" />

    <Style x:Key="NuclearButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle
                            Margin="2"
                            Stroke="#60000000"
                            StrokeDashArray="1 2"
                            StrokeThickness="1" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  ======================Effect===================================  -->
    <DropShadowBitmapEffect
        x:Key="PopupDropShadow"
        ShadowDepth="1.5"
        Softness="0.15" />

    <!--  =========================Customer Color==========================  -->
    <Color x:Key="ControlLightColor">White</Color>
    <Color x:Key="ControlMediumColor">#FF7381F9</Color>

    <Color x:Key="BorderLightColor">#FFCCCCCC</Color>
    <Color x:Key="BorderMediumColor">#FF888888</Color>

    <!--  ======================Customer Brush=============================  -->
    <LinearGradientBrush x:Key="BorderBackground" StartPoint="0,0" EndPoint="1,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="AliceBlue" />
                <GradientStop Offset="0.6" Color="WhiteSmoke" />
                <GradientStop Offset="1.0" Color="AliceBlue" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ControlNormalBackGround" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0.0" Color="White" />
        <GradientStop Offset=".7" Color="Lavender" />
        <GradientStop Offset="1.0" Color="LightSteelBlue" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ControlMouseOverBackGround" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0.0" Color="CornflowerBlue" />
        <GradientStop Offset=".7" Color="Lavender" />
        <GradientStop Offset="1.0" Color="White" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridColumnHeader" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="0.521" Color="#FF8AAEDA" />
        <GradientStop Offset="0.194" Color="#FFC6D6EC" />
        <GradientStop Offset="0.811" Color="#FFB4C9E5" />
        <GradientStop Offset="0.507" Color="#FFB7C8E0" />
        <GradientStop Offset="1" Color="#FFD1DEF0" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRowHeader" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="#FFFFFFFF" />
        <GradientStop Offset="0.521" Color="#FF8AAEDA" />
        <GradientStop Offset="0.194" Color="#FFC6D6EC" />
        <GradientStop Offset="0.811" Color="#FFB4C9E5" />
        <GradientStop Offset="0.507" Color="#FFB7C8E0" />
        <GradientStop Offset="1" Color="#FFD1DEF0" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRow" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="Snow" />
        <GradientStop Offset="0.5" Color="LightBlue" />
        <GradientStop Offset="1" Color="Snow" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRowMouseOver" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="White" />
        <GradientStop Offset="1" Color="#FFE2F1FF" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRowSelected" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="LightBlue" />
        <GradientStop Offset="1" Color="Lavender" />
    </LinearGradientBrush>

</ResourceDictionary>