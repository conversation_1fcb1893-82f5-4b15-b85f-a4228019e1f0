# ========================================
# .NET / C# / Visual Studio 通用 .gitignore
# 适用于：.NET Core, .NET 5/6/7/8, .NET Framework
# ========================================

# 用户级文件（系统自动生成，无需版本控制）
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# 构建输出
[Bb]in/
[Oo]bj/
[Bb]uild/
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]inaries/

# Visual Studio 工作区缓存
.vs/
*.VC.db
*.VC.VC.opendb

# ReSharper / Rider
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user
.idea/

# Visual Studio Code
.vscode/
*.code-workspace

# NuGet 包缓存
*.nupkg
*.snupkg
**/packages/*
# 保留 packages.config 本身，但忽略包内容
!**/packages/build/
!**/packages/repositories.config
.nuget/
project.lock.json
project.fragment.lock.json
artifacts/

# MSTest 测试结果
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# 代码覆盖率
*.coverage
*.coveragexml
*.codecover

# 临时文件
*.tmp
*.temp
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# ClickOnce 部署
publish/
*.publishproj
*.pubxml
*.azurePubxml

# Windows 镜像缓存
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Mac
.DS_Store
.AppleDouble
.LSOverride

# 其他工具
*.bak
*.cache
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.vspscc
*.vssscc
*.psess
*.vsp
*.vspx
*.sap

# Azure DevOps Pipelines
.vsts-ci.yml
.vsts-ci.yaml

# Docker
**/docker-compose.override.yml
**/docker-compose.*.yml
!**/docker-compose.yml
.dockerignore

# 证书/密钥
*.pfx
*.snk
*.key
*.pem
secrets.json
appsettings.Development.json
appsettings.*.json
!appsettings.json

# 本地工具缓存
.dotnet/
.tools/