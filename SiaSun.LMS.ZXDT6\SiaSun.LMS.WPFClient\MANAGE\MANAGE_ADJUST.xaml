﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_ADJUST"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="MANAGE_ADJUST"
    Width="500"
    Height="470"
    Loaded="DocumentContent_Loaded">
    <GroupBox Grid.Column="2" Header="库存调整">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <uc:ucSplitPropertyGridTab x:Name="gridStorageList" Grid.Row="1" />
            <WrapPanel
                Grid.Row="2"
                Margin="3"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                ButtonBase.Click="WrapPanel_Click"
                Orientation="Horizontal">
                <uc:ucManagePosition x:Name="ucManagePosition" Grid.Row="0" />
                <Button
                    Name="btnConfirm"
                    Width="60"
                    Margin="0,0,10,0">
                    保存
                </Button>
                <Button
                    Name="btnRefresh"
                    Width="60"
                    Margin="0,0,20,0">
                    刷新
                </Button>
                <Label Content="扫描[托盘条码]点击[刷新]，在准备调整的行前打勾并修改[实际数量]，点击[保存]完成操作" />
            </WrapPanel>
        </Grid>
    </GroupBox>

</ad:DocumentContent>
