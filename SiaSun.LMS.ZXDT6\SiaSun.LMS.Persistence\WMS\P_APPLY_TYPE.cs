﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     siasun
 *       日期：     2014/11/3
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// APPLY_TYPE
    /// </summary>
    public class P_APPLY_TYPE : P_Base_House
    {
        public P_APPLY_TYPE()
        {
            //
            // TODO: 此处添加APPLY_TYPE的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<APPLY_TYPE> GetList()
        {
            return ExecuteQueryForList<APPLY_TYPE>("APPLY_TYPE_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(APPLY_TYPE apply_type)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("APPLY_TYPE");
                apply_type.APPLY_ID = id;
            }

            return ExecuteInsert("APPLY_TYPE_INSERT", apply_type);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public int Update(APPLY_TYPE apply_type)
        {
            return ExecuteUpdate("APPLY_TYPE_UPDATE", apply_type);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public APPLY_TYPE GetModel(System.Int32 APPLY_ID)
        {
            return ExecuteQueryForObject<APPLY_TYPE>("APPLY_TYPE_SELECT_BY_ID", APPLY_ID);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public APPLY_TYPE GetModelDeviceCode(string DEVICE_CODE)
        {
            return ExecuteQueryForObject<APPLY_TYPE>("APPLY_TYPE_SELECT_BY_DEVICE_CODE", DEVICE_CODE);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public APPLY_TYPE GetModelApplyTypeCode(string APPLY_TYPE_CODE)
        {
            return ExecuteQueryForObject<APPLY_TYPE>("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", APPLY_TYPE_CODE);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 APPLY_ID)
        {
            return ExecuteDelete("APPLY_TYPE_DELETE", APPLY_ID);
        }


    }
}
