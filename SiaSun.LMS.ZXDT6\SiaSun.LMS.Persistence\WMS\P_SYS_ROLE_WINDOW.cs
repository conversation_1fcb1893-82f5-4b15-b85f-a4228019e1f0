﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_ROLE_WINDOW
	/// </summary>
	public class P_SYS_ROLE_WINDOW : P_Base_House
	{
		public P_SYS_ROLE_WINDOW ()
		{
			//
			// TODO: 此处添加SYS_ROLE_WINDOW的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_ROLE_WINDOW> GetList()
		{
			return ExecuteQueryForList<SYS_ROLE_WINDOW>("SYS_ROLE_WINDOW_SELECT",null);
		}




        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_ROLE_WINDOW> GetListRoleIDMenuID(int ROLE_ID,int MENU_ID)
        {
            Hashtable ht = new Hashtable();

            ht.Add("ROLE_ID", ROLE_ID);

            ht.Add("MENU_ID", MENU_ID);


            return ExecuteQueryForList<SYS_ROLE_WINDOW>("SYS_ROLE_WINDOW_SELECT_BY_ROLE_ID_MENU_ID", ht);
        }


        /// <summary>
        /// 得到列表
        /// </summary>
        public SYS_ROLE_WINDOW GetModelRoleIDMenuIDControlName(int ROLE_ID, int MENU_ID, string CONTROL_NAME)
        {
            Hashtable ht = new Hashtable();

            ht.Add("ROLE_ID", ROLE_ID);

            ht.Add("MENU_ID", MENU_ID);

            ht.Add("CONTROL_NAME", CONTROL_NAME);

            return ExecuteQueryForObject<SYS_ROLE_WINDOW>("SYS_ROLE_WINDOW_SELECT_BY_ROLE_ID_MENU_ID_CONTROL_NAME", ht);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_ROLE_WINDOW sys_role_window)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_ROLE_WINDOW");
                sys_role_window.ROLE_WINDOW_ID = id;
            }

            return ExecuteInsert("SYS_ROLE_WINDOW_INSERT",sys_role_window);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_ROLE_WINDOW sys_role_window)
		{
			return ExecuteUpdate("SYS_ROLE_WINDOW_UPDATE",sys_role_window);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_ROLE_WINDOW GetModel(System.Int32 ROLE_WINDOW_ID)
		{
			return ExecuteQueryForObject<SYS_ROLE_WINDOW>("SYS_ROLE_WINDOW_SELECT_BY_ID",ROLE_WINDOW_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 ROLE_WINDOW_ID)
		{
			return ExecuteDelete("SYS_ROLE_WINDOW_DELETE",ROLE_WINDOW_ID);
		}
		

	}
}
