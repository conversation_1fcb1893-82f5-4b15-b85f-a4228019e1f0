﻿# 物资中央库对接接口文档
## 物资基本信息
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|daLeiId|String|"daLeiId\_e85c34d04b27"|大类 ID|
|一级|daLeiName|String|"daLeiName\_01b96bf3c8c5"|大类名称|
|一级|xiaoLeiId|String|"xiaoLeiId\_e5d9bbad3cc9"|小类 ID|
|一级|xiaoLeiName|String|"xiaoLeiName\_ae1734f7ddbf"|小类名称|
|一级|daZuId|String|"daZuId\_3cf08a781017"|大组 ID|
|一级|daZuName|String|"daZuName\_6f180568c10a"|大组名称|
|一级|xiaoZuId|String|"xiaoZuId\_f2e3083ff1c4"|小组 ID|
|一级|xiaoZuName|String|"xiaoZuName\_30fbfbd69798"|小组名称|
|一级|goodsStatus|Integer|0|物资状态 1:启用 0:停用|
|一级|code|String|"code\_dc5d1c457c0c"|编码|
|一级|parentId|String|"parentId\_3d8e1e1ba6e5"|父级|
|一级|parentName|String|"parentName\_0516851761a8"|父级名称|
|一级|name|String|"name\_09c505e2f869"|名称|
|一级|goodsType|String|"goodsType\_653e86e9380f"|物资类型|
|一级|goodsAttribute|String|"goodsAttribute\_a607c1f9aa87"|物资属性|
|一级|unitId|String|"unitId\_e8027160a20e"|计量单位ID|
|一级|unitName|String|"unitName\_e02ebfc9fefd"|计量单位名称|
|一级|goodsVersion|String|"goodsVersion\_f5e0374a63ae"|规格型号|
|一级|goodsClass|String|"goodsClass\_f4cd40be48c1"|物资分类|
|一级|goodsCode|String|"goodsCode\_4ec16355b4b5"|<p>物资编码</p><p></p>|
|一级|suggestedAmount|BigDecimal|0|建议金额|
|一级|isLaborInsuranceMaterials|Integer|0|是否劳保物资（0 - 否，1 - 是）|
|一级|ignoreParent|Boolean|FALSE|忽略上级|
|一级|fixedAssetFlag|String|"fixedAssetFlag\_fc1864ba9d65"|是否固定资产 0 否 1是|
|一级|professionalAssetFlag|String|"professionalAssetFlag\_1bfc61f404c2"|是否专业资产 0否 1是|
|一级|unitInformationEntityDTO|Object|-|单位信息|
|二级|unitInformationEntityDTO.goodsId|String|"goodsId\_b08a408a6250"|*物资管理ID*|
|二级|unitInformationEntityDTO.conversionDetailVOs|Array|-|换算比率|
|三级|conversionDetailVOs.conversionRatio|String|"conversionRatio\_72a4ac9e47d0"|换算比率|
|三级|conversionDetailVOs.conversionUnit|String|"conversionUnit\_b07c6dc5a26b"|换算单位|
|二级|unitInformationEntityDTO.grossWeight|Integer|0|单品毛重|
|二级|unitInformationEntityDTO.netWeight|Integer|0|单品净重|
|二级|unitInformationEntityDTO.unitHeight|Integer|0|基本单位高(厘米)|
|二级|unitInformationEntityDTO.unitWide|Integer|0|基本单位宽(厘米)|
|二级|unitInformationEntityDTO.unitLength|Integer|0|基本单位长(厘米)|
|二级|unitInformationEntityDTO.unit|String|"unit\_8bd554241164"|基础单位|
|二级|unitInformationEntityDTO.createDate|String|"2025-07-16 15:49:17"|创建时间|
|二级|unitInformationEntityDTO.createUser|String|"createUser\_493e2e79ed6a"|创建人 ID|
|二级|unitInformationEntityDTO.createName|String|"createName\_775dba06e83b"|创建人名称|
|二级|unitInformationEntityDTO.updateDate|String|"2025-07-16 15:49:17"|更新时间|
|二级|unitInformationEntityDTO.updateUser|String|"updateUser\_8041457708b2"|更新人 ID|
|二级|unitInformationEntityDTO.updateName|String|"updateName\_bbbe4c0cc647"|更新人名称|
|二级|unitInformationEntityDTO.id|String|"id\_763202be376a"|ID|
|二级|unitInformationEntityDTO.status|Integer|0|状态|
|二级|unitInformationEntityDTO.billCode|String|"billCode\_c51c252c436d"|单据编码|
|一级|brandVOs|Array|-|品牌信息|
|二级|brandVOs.brandName|String|"brandName\_ca06fedf3877"|品牌|
|二级|brandVOs.purchasesNum|String|"purchasesNum\_5e84aa115356"|采购次数|
|二级|brandVOs.initialPutawayTime|String|"2025-07-16 15:49:17"|初始入库时间|
|二级|brandVOs.inboundQuantity|Integer|0|入库数量|
|二级|brandVOs.remark|String|"remark\_4bfcd425780a"|备注|
|一级|isControlledByProductDate|String|"isControlledByProductDate\_9e3479e80518"|<p>是否根据生产日期管控  定资产 0 否 1是</p><p>）</p>|
|一级|createDate|String|"2025-07-16 15:49:17"|创建时间（yyyy-MM-dd HH:mm:ss）|
|一级|createUser|String|"createUser\_020e09331b23"|创建人 ID|
|一级|createName|String|"createName\_f77c0e88dce6"|创建人名称|
|一级|updateDate|String|"2025-07-16 15:49:17"|更新时间（yyyy-MM-dd HH:mm:ss）|
|一级|updateUser|String|"updateUser\_5803939104b5"|更新人 ID|
|一级|updateName|String|"updateName\_a42ef4c2bb30"|更新人名称|
|一级|id|String|"id\_cd78e51bb9b7"|ID（唯一标识）|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_d0d03b9746a7"|单据编码|

### Request信息示例
{

`  `"daLeiId": "daLeiId\_e85c34d04b27",

`  `"daLeiName": "daLeiName\_01b96bf3c8c5",

`  `"xiaoLeiId": "xiaoLeiId\_e5d9bbad3cc9",

`  `"xiaoLeiName": "xiaoLeiName\_ae1734f7ddbf",

`  `"daZuId": "daZuId\_3cf08a781017",

`  `"daZuName": "daZuName\_6f180568c10a",

`  `"xiaoZuId": "xiaoZuId\_f2e3083ff1c4",

`  `"xiaoZuName": "xiaoZuName\_30fbfbd69798",

`  `"goodsStatus": 0,

`  `"code": "code\_dc5d1c457c0c",

`  `"parentId": "parentId\_3d8e1e1ba6e5",

`  `"parentName": "parentName\_0516851761a8",

`  `"name": "name\_09c505e2f869",

`  `"goodsType": "goodsType\_653e86e9380f",

`  `"goodsAttribute": "goodsAttribute\_a607c1f9aa87",

`  `"unitId": "unitId\_e8027160a20e",

`  `"unitName": "unitName\_e02ebfc9fefd",

`  `"goodsVersion": "goodsVersion\_f5e0374a63ae",

`  `"goodsClass": "goodsClass\_f4cd40be48c1",

`  `"goodsCode": "goodsCode\_4ec16355b4b5",

`  `"suggestedAmount": 0.00,

`  `"isLaborInsuranceMaterials": 0,

`  `"ignoreParent": false,

`  `"sourceType": "sourceType\_b842e4ad08a0",

`  `"entityModelId": "entityModelId\_68eef0ed5482",

`  `"fixedAssetFlag": "fixedAssetFlag\_fc1864ba9d65",

`  `"professionalAssetFlag": "professionalAssetFlag\_1bfc61f404c2",

`  `"unitInformationEntityDTO": {

`    `"goodsId": "goodsId\_b08a408a6250",

`    `"conversionDetailVOs": [

`      `{

`        `"conversionRatio": "conversionRatio\_72a4ac9e47d0",

`        `"conversionUnit": "conversionUnit\_b07c6dc5a26b"

`      `}

`    `],

`    `"grossWeight": 0,

`    `"netWeight": 0,

`    `"unitHeight": 0,

`    `"unitWide": 0,

`    `"unitLength": 0,

`    `"unit": "unit\_8bd554241164",

`    `"sourceType": "sourceType\_cdfb40234436",

`    `"entityModelId": "entityModelId\_acfd2f7245ad",

`    `"createDate": "2025-07-16 15:49:17",

`    `"createUser": "createUser\_493e2e79ed6a",

`    `"createName": "createName\_775dba06e83b",

`    `"updateDate": "2025-07-16 15:49:17",

`    `"updateUser": "updateUser\_8041457708b2",

`    `"updateName": "updateName\_bbbe4c0cc647",

`    `"id": "id\_763202be376a",

`    `"status": 0,

`    `"billCode": "billCode\_c51c252c436d"

`  `},

`  `"brandVOs": [

`    `{

`      `"brandName": "brandName\_ca06fedf3877",

`      `"purchasesNum": "purchasesNum\_5e84aa115356",

`      `"initialPutawayTime": "2025-07-16 15:49:17",

`      `"inboundQuantity": 0,

`      `"remark": "remark\_4bfcd425780a"

`    `}

`  `],

`  `"isControlledByProductDate": "isControlledByProductDate\_9e3479e80518",

`  `"createDate": "2025-07-16 15:49:17",

`  `"createUser": "createUser\_020e09331b23",

`  `"createName": "createName\_f77c0e88dce6",

`  `"updateDate": "2025-07-16 15:49:17",

`  `"updateUser": "updateUser\_5803939104b5",

`  `"updateName": "updateName\_a42ef4c2bb30",

`  `"id": "id\_cd78e51bb9b7",

`  `"status": 0,

`  `"billCode": "billCode\_d0d03b9746a7"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 计量单位
### 接口信息
测试地址：

正式地址：

服务名称：接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|measuringUnitStatus|String|"measuringUnitStatus\_68fb194c9f65"|计量单位状态|
|一级|measuringUnitCode|String|"measuringUnitCode\_941d0953f7b8"|计量单位编码|
|一级|code|String|"code\_81edd02cee01"|编码|
|一级|parentId|String|"parentId\_10b6be92121c"|父级 ID|
|一级|name|String|"name\_08ff6f616d5b"|名称|
|一级|ignoreParent|Boolean|FALSE|忽略上级|
|一级|createDate|String|"2025-07-16 16:45:08"|创建时间|
|一级|createUser|String|"createUser\_f726e85da156"|创建人|
|一级|createName|String|"createName\_f38e27b86d6f"|创建人姓名|
|一级|updateDate|String|"2025-07-16 16:45:08"|更新时间|
|一级|updateUser|String|"updateUser\_9883a85a35ad"|更新人|
|一级|updateName|String|"updateName\_6af8709d9f21"|更新人姓名|
|一级|id|String|"id\_6d92197a53a7"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_34afd2d7965c"|单据编码|

### Request信息示例
{

`  `"measuringUnitStatus": "measuringUnitStatus\_9c9ccd2de1da",

`  `"measuringUnitCode": "measuringUnitCode\_7e761420a544",

`  `"code": "code\_9107b8504dae",

`  `"parentId": "parentId\_e5982edb4583",

`  `"name": "name\_62691684d5fa",

`  `"ignoreParent": false,

`  `"createDate": "2025-07-16 16:48:18",

`  `"createUser": "createUser\_1c43794770b9",

`  `"createName": "createName\_53692366e154",

`  `"updateDate": "2025-07-16 16:48:18",

`  `"updateUser": "updateUser\_2d7317c7714a",

`  `"updateName": "updateName\_91726c9f4019",

`  `"id": "id\_768f8804d4b1",

`  `"status": 0,

`  `"billCode": "billCode\_8dbb966c514e"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 仓库
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|isHaveMaterials|Integer|0|*是否存在物资*|
|一级|contactId|String|"contactId\_197a7bc1b543"|联系人 ID|
|一级|contactName|String|"contactName\_0069b0fb98ba"|联系人姓名|
|一级|gsId|String|"gsId\_f560f5ebabdc"|公司 ID|
|一级|gsName|String|"gsName\_9c1398953ca6"|公司名称|
|一级|bmId|String|"bmId\_121c222fd4ab"|部门 ID|
|一级|bmName|String|"bmName\_b0edfaf8fa7d"|部门名称|
|一级|teamName|String|"teamName\_2dd1718ecfd2"|仓库所属组织名称|
|一级|warehouseStatus|String|"warehouseStatus\_809a59a6b56e"|仓库状态 启用:ENABLED 停用:DISABLED|
|一级|remark|String|"remark\_f51418d6a5be"|备注|
|一级|warehouseLevel|String|"warehouseLevel\_e777d1f3686d"|等级|
|一级|describe|String|"describe\_92176034eb91"|描述|
|一级|isDeliveryAddress|String|"isDeliveryAddress\_751dd8c27681"|为送货地址|
|一级|isVirtualWarehouse|String|"isVirtualWarehouse\_cd3f3376236d"|虚拟库房|
|一级|physicalAddress|String|"physicalAddress\_5f4a67ac7bec"|对应物理库房|
|一级|stationPlace|String|"stationPlace\_ce7caff68421"|空间位置|
|一级|roomPlace|String|"roomPlace\_efc84904380b"|房间/位置|
|一级|floor|String|"floor\_fe0fbb84d5f3"|层/区域|
|一级|teamId|String|"teamId\_0715216f8bd4"|仓库所属组织|
|一级|warehouseArea|BigDecimal|0|仓库面积|
|一级|warehouseOutRoomArea|BigDecimal|0|室外面积|
|一级|trainStation|String|"trainStation\_f6e5eca8cfef"|车站/地点|
|一级|belongingPlace|String|"belongingPlace\_60b76881ac24"|归属地区|
|一级|warehouseRoomArea|BigDecimal|0|室内面积|
|一级|phone|String|"phone\_8d9c46e929c1"|联系方式|
|一级|addressType|String|"addressType\_a7975db8d256"|位置类型|
|一级|warehouseChargeId|String|"warehouseChargeId\_635c5ec959b2"|仓库负责人 ID|
|一级|warehouseChargeName|String|"warehouseChargeName\_1d1a14053f3f"|仓库负责人姓名|
|一级|warehouseAddress|String|"warehouseAddress\_ed7c4d886ee9"|仓库位置|
|一级|warehouseType|String|"warehouseType\_288152b70439"|仓库类型|
|一级|lineId|String|"lineId\_5a7c792be751"|线路 ID|
|一级|lineName|String|"lineName\_9dfd404d710f"|线路名称|
|一级|mangeUserId|String|"mangeUserId\_125374939c24"|仓库主管|
|一级|name|String|"name\_b3be27f77157"|仓库名称|
|一级|code|String|"code\_a2c3d29046ce"|仓库编码|
|一级|warehouseShelfRelList|Array|-|库房货架关联表|
|二级|warehouseShelfRelList[0].shelfCode|String|"shelfCode\_7e3faf976ebc"|*货架位编码*|
|二级|warehouseShelfRelList[0].warehouseId|String|"warehouseId\_691145e9716d"|库房管理id|
|二级|warehouseShelfRelList[0].qrCode|String|"qrCode\_479e96126966"|二维码|
|二级|warehouseShelfRelList[0].shelfStatus|Integer|0|货架状态|
|二级|warehouseShelfRelList[0].describe|String|"describe\_95c5907e3625"|描述|
|二级|warehouseShelfRelList[0].shelfName|String|"shelfName\_231f0becf3de"|货架名称|
|二级|warehouseShelfRelList[0].shelfId|String|"shelfId\_fd360fbb0d01"|货架 ID|
|一级|createDate|String|"2025-07-16 16:52:38"|创建时间|
|一级|createUser|String|"createUser\_73e475815d1e"|创建人|
|一级|createName|String|"createName\_49d861680df7"|创建人姓名|
|一级|updateDate|String|"2025-07-16 16:52:38"|更新时间|
|一级|updateUser|String|"updateUser\_4f33aa66575c"|更新人|
|一级|updateName|String|"updateName\_fcfee537de48"|更新人姓名|
|一级|id|String|"id\_83602bd91caf"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_b7a3a540b9db"|单据编号|

### Request信息示例
{

`  `"isHaveMaterials": 0,

`  `"contactId": "contactId\_449a0cc8a6ce",

`  `"contactName": "contactName\_bf188b84076f",

`  `"gsId": "gsId\_750723eb6d67",

`  `"gsName": "gsName\_7845f26a8ac5",

`  `"bmId": "bmId\_28fa2df24239",

`  `"bmName": "bmName\_95b7fc9148eb",

`  `"teamName": "teamName\_d027a87fc901",

`  `"warehouseStatus": "warehouseStatus\_28da9981303e",

`  `"remark": "remark\_7d41ed6d9bc5",

`  `"warehouseLevel": "warehouseLevel\_c11c98349513",

`  `"describe": "describe\_086c42ce5170",

`  `"isDeliveryAddress": "isDeliveryAddress\_a0f5208df8d7",

`  `"isVirtualWarehouse": "isVirtualWarehouse\_c50cb8c328cd",

`  `"physicalAddress": "physicalAddress\_3c50e3fc3086",

`  `"stationPlace": "stationPlace\_01644bb35381",

`  `"roomPlace": "roomPlace\_9762ceac7aa1",

`  `"floor": "floor\_349ed2a7acd9",

`  `"teamId": "teamId\_f58be609b9c6",

`  `"warehouseArea": 0.00,

`  `"warehouseOutRoomArea": 0.00,

`  `"trainStation": "trainStation\_ca38ed5f1537",

`  `"belongingPlace": "belongingPlace\_b6038d8ab384",

`  `"warehouseRoomArea": 0.00,

`  `"phone": "phone\_8df80c2b4a4c",

`  `"addressType": "addressType\_1320819df76d",

`  `"warehouseChargeId": "warehouseChargeId\_0a48743208df",

`  `"warehouseChargeName": "warehouseChargeName\_6b01fb0cf153",

`  `"warehouseAddress": "warehouseAddress\_efc8ff9c1053",

`  `"warehouseType": "warehouseType\_cf501e50d4c9",

`  `"lineId": "lineId\_b296e02971ab",

`  `"lineName": "lineName\_f944780b11af",

`  `"mangeUserId": "mangeUserId\_ffe573bb6cd6",

`  `"name": "name\_094b87668d3b",

`  `"code": "code\_0ee9b0e8715f",

`  `"warehouseShelfRelList": [

`    `{

`      `"shelfCode": "shelfCode\_14f3fe973ee3",

`      `"warehouseId": "warehouseId\_ad9c272e9cf1",

`      `"qrCode": "qrCode\_1098cb1980a8",

`      `"shelfStatus": 0,

`      `"describe": "describe\_9407c0728471",

`      `"shelfName": "shelfName\_2ed2b6c84ae3",

`      `"shelfId": "shelfId\_f9a7d56ac47e"

`    `}

`  `],

`  `"createDate": "2025-07-16 17:25:02",

`  `"createUser": "createUser\_3d6e4e5cf2ba",

`  `"createName": "createName\_8a914a3b3565",

`  `"updateDate": "2025-07-16 17:25:02",

`  `"updateUser": "updateUser\_63d12f2e4b68",

`  `"updateName": "updateName\_3c8e4bec1c81",

`  `"id": "id\_662d99894993",

`  `"status": 0,

`  `"billCode": "billCode\_75740a68e96f"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}
## 组织架构
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|orgLevel|Integer|0|机构级别|
|一级|orgCode|String|"orgCode\_0af060071f39"|机构编码|
|一级|orgName|String|"orgName\_a240fdeb8472"|机构名称|
|一级|parentOrgCode|String|"parentOrgCode\_5578efd91739"|上级机构编码|
|一级|oldOrgCode|String|"oldOrgCode\_1df3f23be540"|旧机构编码|
|一级|orgStatus|Integer|0|*状态*|
|一级|isDelete|Integer|0|*是否删除*|
|一级|orgType|String|"orgType\_e0776ce6a719"|组织类型|
|一级|personInCharge|String|"personInCharge\_6ae752773ecc"|*组织负责人*|
|一级|type|Integer|0|*类型 0 内部 1 外部*|
|一级|code|String|"code\_22174ef3fcb4"|编码|
|一级|createDate|String|"2025-07-16 17:32:12"|创建时间|
|一级|updateDate|String|"2025-07-16 17:32:12"|更新时间|
|一级|id|String|"id\_0b380337212d"|ID|

### Request信息示例
{

`  `"orgLevel": 0,

`  `"orgCode": "orgCode\_75ff23e01fab",

`  `"orgName": "orgName\_350f1f11f122",

`  `"parentOrgCode": "parentOrgCode\_d69c12eca96b",

`  `"oldOrgCode": "oldOrgCode\_6a66fe8de6e4",

`  `"orgStatus": 0,

`  `"isDelete": 0,

`  `"orgType": "orgType\_ed284c49194e",

`  `"personInCharge": "personInCharge\_ad957374385c",

`  `"type": 0,

`  `"code": "code\_648565b7b595",

`  `"createDate": "2025-07-16 17:33:45",

`  `"updateDate": "2025-07-16 17:33:45",

`  `"id": "id\_0d80ce1c3dc3"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}
## 货架位
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|isHaveMaterials|Integer|0|是否有物料|
|一级|belongingPlace|String|"belongingPlace\_9c833c74df75"|所属地点|
|一级|gsId|String|"gsId\_e49379c254a3"|公司 ID|
|一级|gsName|String|"gsName\_8874b6979ef1"|公司名称|
|一级|lineId|String|"lineId\_eff1a6501a9a"|线路 ID|
|一级|lineName|String|"lineName\_e88bcbc36561"|线路名称|
|一级|remark|String|"remark\_7862b42e018f"|备注|
|一级|addressType|String|"addressType\_6a6eedc375fc"|地址类型|
|一级|hasGoods|Boolean|FALSE|是否有货物|
|一级|warehouseCharge|String|"warehouseCharge\_84a880e2c4fe"|仓库负责人 ID|
|一级|warehouseChargeName|String|"warehouseChargeName\_eafbb736e19c"|仓库负责人姓名|
|一级|warehouseAddress|String|"warehouseAddress\_4972a3f130f7"|仓库地址|
|一级|warehouseType|String|"warehouseType\_1d2031263c53"|仓库类型|
|一级|qrCode|String|"qrCode\_8b8344c40eb2"|二维码|
|一级|shelfStatus|Integer|0|货架状态|
|一级|describe|String|"describe\_f013e9edd725"|描述|
|一级|warehouseCode|String|"warehouseCode\_989eea826a9c"|仓库编码|
|一级|warehouseName|String|"warehouseName\_f13a3da79bd6"|仓库名称|
|一级|warehouseId|String|"warehouseId\_d213d3b02eea"|仓库 ID|
|一级|name|String|"name\_8386b583dff1"|名称|
|一级|code|String|"code\_04fc92e0fad9"|编码|
|一级|sourceType|String|"sourceType\_7d996efcabe1"|来源类型|
|一级|entityModelId|String|"entityModelId\_09255fd88d92"|实体模型 ID|
|一级|processDefinitionId|String|"processDefinitionId\_d258e9425af9"|流程定义 ID|
|一级|processInstanceId|String|"processInstanceId\_e45409350930"|流程实例 ID|
|一级|processInstanceKey|String|"processInstanceKey\_29d72e8eaf38"|流程实例键|
|一级|nextProcessUserList|Object|{}|下一流程用户列表|
|一级|attachmentFileName|String|"attachmentFileName\_018bbddf8723"|附件文件名|
|一级|attachmentFileUrl|String|"attachmentFileUrl\_2caf54cb7906"|附件文件 URL|
|一级|createDate|String|"2025-07-16 17:35:45"|创建时间|
|一级|createUser|String|"createUser\_98b30f94f9cb"|创建人|
|一级|createName|String|"createName\_e8946bee0185"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:35:45"|更新时间|
|一级|updateUser|String|"updateUser\_b0867f1cb51c"|更新人|
|一级|updateName|String|"updateName\_3ec213015f58"|更新人姓名|
|一级|id|String|"id\_db768f351a11"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_b00512735873"|单据编码|

### Request信息示例
{

`  `"isHaveMaterials": 0,

`  `"belongingPlace": "belongingPlace\_9c833c74df75",

`  `"gsId": "gsId\_e49379c254a3",

`  `"gsName": "gsName\_8874b6979ef1",

`  `"lineId": "lineId\_eff1a6501a9a",

`  `"lineName": "lineName\_e88bcbc36561",

`  `"remark": "remark\_7862b42e018f",

`  `"addressType": "addressType\_6a6eedc375fc",

`  `"hasGoods": false,

`  `"warehouseCharge": "warehouseCharge\_84a880e2c4fe",

`  `"warehouseChargeName": "warehouseChargeName\_eafbb736e19c",

`  `"warehouseAddress": "warehouseAddress\_4972a3f130f7",

`  `"warehouseType": "warehouseType\_1d2031263c53",

`  `"qrCode": "qrCode\_8b8344c40eb2",

`  `"shelfStatus": 0,

`  `"describe": "describe\_f013e9edd725",

`  `"warehouseCode": "warehouseCode\_989eea826a9c",

`  `"warehouseName": "warehouseName\_f13a3da79bd6",

`  `"warehouseId": "warehouseId\_d213d3b02eea",

`  `"name": "name\_8386b583dff1",

`  `"code": "code\_04fc92e0fad9",

`  `"sourceType": "sourceType\_7d996efcabe1",

`  `"entityModelId": "entityModelId\_09255fd88d92",

`  `"processDefinitionId": "processDefinitionId\_d258e9425af9",

`  `"processInstanceId": "processInstanceId\_e45409350930",

`  `"processInstanceKey": "processInstanceKey\_29d72e8eaf38",

`  `"nextProcessUserList": {},

`  `"attachmentFileName": "attachmentFileName\_018bbddf8723",

`  `"attachmentFileUrl": "attachmentFileUrl\_2caf54cb7906",

`  `"createDate": "2025-07-16 17:35:45",

`  `"createUser": "createUser\_98b30f94f9cb",

`  `"createName": "createName\_e8946bee0185",

`  `"updateDate": "2025-07-16 17:35:45",

`  `"updateUser": "updateUser\_b0867f1cb51c",

`  `"updateName": "updateName\_3ec213015f58",

`  `"id": "id\_db768f351a11",

`  `"status": 0,

`  `"billCode": "billCode\_b00512735873"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}
## 入库单
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|stockTakeResultId|String|"stockTakeResultId\_8e6a369008f9"|盘点结果 ID|
|一级|stockTakeResultCode|String|"stockTakeResultCode\_da6e57e5f86d"|盘点结果编码|
|一级|stockTakeResultName|String|"stockTakeResultName\_4846e48b1a82"|盘点结果名称|
|一级|isRedStorage|Integer|0|是否红冲入库|
|一级|money|BigDecimal|0|不含税金额|
|一级|taxMoney|BigDecimal|0|含税金额|
|一级|storageStatus|String|"storageStatus\_26f0e9ed7067"|入库状态|
|一级|storageGoodsSource|String|"storageGoodsSource\_438f0b0711a6"|来源类型(1采购/2移交/3其他/4材料间领料/5*盘盈入库*)|
|一级|storageDate|String|"2025-07-16"|入库时间|
|一级|warehouseId|String|"warehouseId\_977dbba4f97c"|入库仓库ID|
|一级|warehouseName|String|"warehouseName\_20a2502ce3e3"|入库仓库名称|
|一级|storageCode|String|"storageCode\_0183da2fe292"|入库编码|
|一级|storageName|String|"storageName\_42d29550ebec"|入库名称|
|一级|contractId|String|"contractId\_12f9ed550c4e"|合同 ID|
|一级|contractCode|String|"contractCode\_ffc7354d8561"|合同编码|
|一级|contractName|String|"contractName\_f5d7fb16cb78"|合同名称|
|一级|supplierId|String|"supplierId\_f7e932c1c46d"|供应商 ID|
|一级|supplier|String|"supplier\_4ed3640cff04"|供应商名称|
|一级|supplierPhone|String|"supplierPhone\_2637651a32f6"|供应商电话|
|一级|buyOrderId|String|"buyOrderId\_18f5d15b5a40"|采购订单 ID|
|一级|buyOrderCode|String|"buyOrderCode\_88b38e047db1"|采购订单编码|
|一级|buyOrderName|String|"buyOrderName\_c95b28541f5a"|采购订单名称|
|一级|buyOrderOperatorId|String|"buyOrderOperatorId\_9311e3472e4d"|采购经办人ID|
|一级|buyOrderOperator|String|"buyOrderOperator\_6d65b1f56b1f"|采购经办人名称|
|一级|secondParty|String|"secondParty\_ab6fbe41cfdb"|乙方名称|
|一级|secondPartyId|String|"secondPartyId\_1c60f95253e0"|乙方 ID|
|一级|receiveId|String|"receiveId\_336aad1d630c"|收货单 ID|
|一级|receiveCode|String|"receiveCode\_053deabb0ee1"|收货单编码|
|一级|receiveName|String|"receiveName\_6d8f7b4fa9b2"|收货单名称|
|一级|receiveUserId|String|"receiveUserId\_6615213c7d15"|收货人 ID|
|一级|receiveUser|String|"receiveUser\_86f90db6f394"|收货人名称|
|一级|deliveryId|String|"deliveryId\_d991f6a6d0ea"|送货单 ID|
|一级|deliveryCode|String|"deliveryCode\_9d01c4c590b4"|送货单编码|
|一级|deliveryName|String|"deliveryName\_cf3f6c84f321"|送货单名称|
|一级|checkedId|String|"checkedId\_1aa1041e712d"|检验单ID|
|一级|checkedCode|String|"checkedCode\_bfbdff30881d"|检验单编号|
|一级|checkedName|String|"checkedName\_fe1ec02de295"|检验单名称|
|一级|isPass|Integer|0|财务是否审核|
|一级|storageMoneyTax|BigDecimal|0|入库金额（含税）|
|一级|initId|String|"initId\_42ec40c4778f"|初始ID|
|一级|storageInfoList|Array|-|入库明细列表|
|二级|storageInfoList[0].contractCode|String|"contractCode\_fd69b0084b90"|合同编码|
|二级|storageInfoList[0].orderIndex|String|"orderIndex\_8f8d1e7ff60d"|订单序号|
|二级|storageInfoList[0].redStorageAllNum|Integer|0|已红冲数量|
|二级|storageInfoList[0].isControlledByProductDate|String|"isControlledByProductDate\_faf163f84545"|是否根据生产日期管控  定资产 0 否 1是|
|二级|storageInfoList[0].deliveryWarehouseId|String|"deliveryWarehouseId\_4cbe1a775ff5"|送货仓库 ID|
|二级|storageInfoList[0].deliveryWarehouse|String|"deliveryWarehouse\_24adc81d2ac8"|送货仓库名称|
|二级|storageInfoList[0].mgDeptName|String|"mgDeptName\_85973cf42263"|管理部门名称|
|二级|storageInfoList[0].mgDeptId|String|"mgDeptId\_49a04a5f155c"|管理部门 ID|
|二级|storageInfoList[0].storageId|String|"storageId\_023da939f3c6"|入库单 ID|
|二级|storageInfoList[0].checkedInfoId|String|"checkedInfoId\_f6d87253b29a"|*检验单明细*id|
|二级|storageInfoList[0].initId|String|"initId\_8449adcd3e94"|初始化 ID|
|二级|storageInfoList[0].goodsId|String|"goodsId\_621e071cb29f"|物资ID|
|二级|storageInfoList[0].goodsCode|String|"goodsCode\_2f80b7d641e0"|物资编码|
|二级|storageInfoList[0].goodsName|String|"goodsName\_522b9afc11c1"|物资名称|
|二级|storageInfoList[0].goodsType|String|"goodsType\_baa92db6c993"|物资类型|
|二级|storageInfoList[0].goodsSource|String|"goodsSource\_3cf631081c0f"|物资来源(1采购/2移交/3其他/4材料间领料/5*盘盈入库*)|
|二级|storageInfoList[0].brand|String|"brand\_9b02c6307289"|品牌|
|二级|storageInfoList[0].goodsVersion|String|"goodsVersion\_9b69e3ed9dd8"|规格型号|
|二级|storageInfoList[0].goodsAttribute|String|"goodsAttribute\_9f4064f19356"|物资属性|
|二级|storageInfoList[0].needGoodsNum|Integer|0|需求数量|
|二级|storageInfoList[0].contractGoodsNum|Integer|0|合同数量|
|二级|storageInfoList[0].addGoodsNum|Integer|0|累计入库数量|
|二级|storageInfoList[0].storageNum|Integer|0|本次入库数量|
|二级|storageInfoList[0].unitId|String|"unitId\_d4a50c2eb0cf"|单位 ID|
|二级|storageInfoList[0].unitName|String|"unitName\_b01917a1a3b0"|单位名称|
|二级|storageInfoList[0].warehouseId|String|"warehouseId\_4dea61dc9a13"|仓库 ID|
|二级|storageInfoList[0].warehouseName|String|"warehouseName\_259f8b708557"|仓库名称|
|二级|storageInfoList[0].shelfId|String|"shelfId\_6d70cab76df9"|货架 ID|
|二级|storageInfoList[0].shelfName|String|"shelfName\_bd42d2c780fb"|货架名称|
|二级|storageInfoList[0].batch|String|"batch\_333ebc81f898"|批次|
|二级|storageInfoList[0].taxPrice|BigDecimal|0|含税单价|
|二级|storageInfoList[0].taxMoney|BigDecimal|0|含税金额|
|二级|storageInfoList[0].tax|BigDecimal|0|税率|
|二级|storageInfoList[0].price|BigDecimal|0|不含税单价|
|二级|storageInfoList[0].money|BigDecimal|0|不含税金额|
|二级|storageInfoList[0].orgId|String|"orgId\_99c6e53f44ef"|所属公司ID|
|二级|storageInfoList[0].orgName|String|"orgName\_1328333b3b26"|所属公司|
|二级|storageInfoList[0].deptId|String|"deptId\_6e52b06bccb6"|所属部门ID|
|二级|storageInfoList[0].deptName|String|"deptName\_215edff51de7"|所属部门|
|二级|storageInfoList[0].bzId|String|"bzId\_e992e62917c1"|班组 ID|
|二级|storageInfoList[0].bzName|String|"bzName\_4bf3deecb9d2"|班组名称|
|二级|storageInfoList[0].gbId|String|"gbId\_3c4cb374c63e"|工班 ID|
|二级|storageInfoList[0].gbName|String|"gbName\_3ed789d71b65"|工班名称|
|二级|storageInfoList[0].manageDeptId|String|"manageDeptId\_38d33774a206"|管理部门 ID|
|二级|storageInfoList[0].manageDeptName|String|"manageDeptName\_566984c7b9b6"|管理部门名称|
|二级|storageInfoList[0].lineId|String|"lineId\_8af4ff137d30"|线路 ID|
|二级|storageInfoList[0].lineName|String|"lineName\_8dc098ecf085"|线路名称|
|二级|storageInfoList[0].gkDeptId|String|"gkDeptId\_81d1e0cc26d3"|归口部门 ID|
|二级|storageInfoList[0].gkDeptName|String|"gkDeptName\_bb940939e303"|归口部门名称|
|二级|storageInfoList[0].localSend|String|"localSend\_88eddb3b5deb"|现场直送|
|二级|storageInfoList[0].remark|String|"remark\_33f100b30be6"|备注|
|二级|storageInfoList[0].createDate|String|"2025-07-16 17:40:09"|创建时间|
|二级|storageInfoList[0].createUser|String|"createUser\_0d02da3e22a7"|创建人|
|二级|storageInfoList[0].createName|String|"createName\_14b88cd585d3"|创建人姓名|
|二级|storageInfoList[0].updateDate|String|"2025-07-16 17:40:09"|更新时间|
|二级|storageInfoList[0].updateUser|String|"updateUser\_2181e8c78055"|更新人|
|二级|storageInfoList[0].updateName|String|"updateName\_0065282a39ee"|更新人姓名|
|二级|storageInfoList[0].id|String|"id\_4596845e99aa"|ID|
|二级|storageInfoList[0].status|Integer|0|状态|
|二级|storageInfoList[0].billCode|String|"billCode\_3bda9a9c3416"|单据编码|
|一级|createDate|String|"2025-07-16 17:40:09"|创建时间|
|一级|createUser|String|"createUser\_c84c945f5b3d"|创建人|
|一级|createName|String|"createName\_ba8be75d73f8"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:40:09"|更新时间|
|一级|updateUser|String|"updateUser\_05e4faa56119"|更新人|
|一级|updateName|String|"updateName\_78f7bcdeeeac"|更新人姓名|
|一级|id|String|"id\_8b18324f30ef"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_84f65e9ef9d4"|单据编码|

### Request信息示例
{

`  `"stockTakeResultId": "stockTakeResultId\_8e6a369008f9",

`  `"stockTakeResultCode": "stockTakeResultCode\_da6e57e5f86d",

`  `"stockTakeResultName": "stockTakeResultName\_4846e48b1a82",

`  `"warehouseForeman": "warehouseForeman\_83ed654f79dd",

`  `"warehouseForemanDate": "2025-07-16 17:40:09",

`  `"exportBillCode": "exportBillCode\_67ad199fd9a6",

`  `"exportCheckedCode": "exportCheckedCode\_a9fb78c61020",

`  `"isRedStorage": 0,

`  `"money": 0.00,

`  `"taxMoney": 0.00,

`  `"storageStatus": "storageStatus\_26f0e9ed7067",

`  `"storageGoodsSource": "storageGoodsSource\_438f0b0711a6",

`  `"storageDate": "2025-07-16",

`  `"warehouseId": "warehouseId\_977dbba4f97c",

`  `"warehouseName": "warehouseName\_20a2502ce3e3",

`  `"storageCode": "storageCode\_0183da2fe292",

`  `"storageName": "storageName\_42d29550ebec",

`  `"contractId": "contractId\_12f9ed550c4e",

`  `"contractCode": "contractCode\_ffc7354d8561",

`  `"contractName": "contractName\_f5d7fb16cb78",

`  `"supplierId": "supplierId\_f7e932c1c46d",

`  `"supplier": "supplier\_4ed3640cff04",

`  `"supplierPhone": "supplierPhone\_2637651a32f6",

`  `"buyOrderId": "buyOrderId\_18f5d15b5a40",

`  `"buyOrderCode": "buyOrderCode\_88b38e047db1",

`  `"buyOrderName": "buyOrderName\_c95b28541f5a",

`  `"buyOrderOperatorId": "buyOrderOperatorId\_9311e3472e4d",

`  `"buyOrderOperator": "buyOrderOperator\_6d65b1f56b1f",

`  `"secondParty": "secondParty\_ab6fbe41cfdb",

`  `"secondPartyId": "secondPartyId\_1c60f95253e0",

`  `"receiveId": "receiveId\_336aad1d630c",

`  `"receiveCode": "receiveCode\_053deabb0ee1",

`  `"receiveName": "receiveName\_6d8f7b4fa9b2",

`  `"receiveUserId": "receiveUserId\_6615213c7d15",

`  `"receiveUser": "receiveUser\_86f90db6f394",

`  `"deliveryId": "deliveryId\_d991f6a6d0ea",

`  `"deliveryCode": "deliveryCode\_9d01c4c590b4",

`  `"deliveryName": "deliveryName\_cf3f6c84f321",

`  `"checkedId": "checkedId\_1aa1041e712d",

`  `"checkedCode": "checkedCode\_bfbdff30881d",

`  `"checkedName": "checkedName\_fe1ec02de295",

`  `"isPass": 0,

`  `"storageMoneyTax": 0.00,

`  `"initId": "initId\_42ec40c4778f",

`  `"storageInfoList": [

`    `{

`      `"contractCode": "contractCode\_fd69b0084b90",

`      `"orderIndex": "orderIndex\_8f8d1e7ff60d",

`      `"redStorageAllNum": 0,

`      `"isControlledByProductDate": "isControlledByProductDate\_faf163f84545",

`      `"deliveryWarehouseId": "deliveryWarehouseId\_4cbe1a775ff5",

`      `"deliveryWarehouse": "deliveryWarehouse\_24adc81d2ac8",

`      `"mgDeptName": "mgDeptName\_85973cf42263",

`      `"mgDeptId": "mgDeptId\_49a04a5f155c",

`      `"storageId": "storageId\_023da939f3c6",

`      `"checkedInfoId": "checkedInfoId\_f6d87253b29a",

`      `"initId": "initId\_8449adcd3e94",

`      `"goodsId": "goodsId\_621e071cb29f",

`      `"goodsCode": "goodsCode\_2f80b7d641e0",

`      `"goodsName": "goodsName\_522b9afc11c1",

`      `"goodsType": "goodsType\_baa92db6c993",

`      `"goodsSource": "goodsSource\_3cf631081c0f",

`      `"brand": "brand\_9b02c6307289",

`      `"goodsVersion": "goodsVersion\_9b69e3ed9dd8",

`      `"goodsAttribute": "goodsAttribute\_9f4064f19356",

`      `"needGoodsNum": 0,

`      `"contractGoodsNum": 0,

`      `"addGoodsNum": 0,

`      `"storageNum": 0,

`      `"unitId": "unitId\_d4a50c2eb0cf",

`      `"unitName": "unitName\_b01917a1a3b0",

`      `"warehouseId": "warehouseId\_4dea61dc9a13",

`      `"warehouseName": "warehouseName\_259f8b708557",

`      `"shelfId": "shelfId\_6d70cab76df9",

`      `"shelfName": "shelfName\_bd42d2c780fb",

`      `"batch": "batch\_333ebc81f898",

`      `"taxPrice": 0.00,

`      `"taxMoney": 0.00,

`      `"tax": 0.00,

`      `"price": 0.00,

`      `"money": 0.00,

`      `"orgId": "orgId\_99c6e53f44ef",

`      `"orgName": "orgName\_1328333b3b26",

`      `"deptId": "deptId\_6e52b06bccb6",

`      `"deptName": "deptName\_215edff51de7",

`      `"bzId": "bzId\_e992e62917c1",

`      `"bzName": "bzName\_4bf3deecb9d2",

`      `"gbId": "gbId\_3c4cb374c63e",

`      `"gbName": "gbName\_3ed789d71b65",

`      `"manageDeptId": "manageDeptId\_38d33774a206",

`      `"manageDeptName": "manageDeptName\_566984c7b9b6",

`      `"lineId": "lineId\_8af4ff137d30",

`      `"lineName": "lineName\_8dc098ecf085",

`      `"gkDeptId": "gkDeptId\_81d1e0cc26d3",

`      `"gkDeptName": "gkDeptName\_bb940939e303",

`      `"localSend": "localSend\_88eddb3b5deb",

`      `"remark": "remark\_33f100b30be6",

`      `"createDate": "2025-07-16 17:40:09",

`      `"createUser": "createUser\_0d02da3e22a7",

`      `"createName": "createName\_14b88cd585d3",

`      `"updateDate": "2025-07-16 17:40:09",

`      `"updateUser": "updateUser\_2181e8c78055",

`      `"updateName": "updateName\_0065282a39ee",

`      `"id": "id\_4596845e99aa",

`      `"status": 0,

`      `"billCode": "billCode\_3bda9a9c3416"

`    `}

`  `],

`  `"createDate": "2025-07-16 17:40:09",

`  `"createUser": "createUser\_c84c945f5b3d",

`  `"createName": "createName\_ba8be75d73f8",

`  `"updateDate": "2025-07-16 17:40:09",

`  `"updateUser": "updateUser\_05e4faa56119",

`  `"updateName": "updateName\_78f7bcdeeeac",

`  `"id": "id\_8b18324f30ef",

`  `"status": 0,

`  `"billCode": "billCode\_84f65e9ef9d4"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 入库红冲单
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|redStorageType|Integer|0|红冲类型：1 冲销；2 退货|
|一级|initId|String|"initId\_1c86a9c4dee7"|初始化 ID|
|一级|buyOrderCode|String|"buyOrderCode\_35a46061689c"|采购订单编码|
|一级|contractCode|String|"contractCode\_ca57364608f7"|合同编码|
|一级|reason|String|"reason\_f2d3d94ce7bc"|红冲原因|
|一级|warehouseName|String|"warehouseName\_d72458c7a2a6"|仓库名称|
|一级|warehouseId|String|"warehouseId\_8d81aeb3b87d"|仓库 ID|
|一级|storageDate|String|"2025-07-16 17:46:00"|入库时间|
|一级|sourceUserName|String|"sourceUserName\_4ee52f61a685"|来源联系人名称|
|一级|sourceUserId|String|"sourceUserId\_8d355bb7859d"|来源联系人 ID|
|一级|sourceUnitName|String|"sourceUnitName\_c82125e69bc8"|来源单位名称|
|一级|sourceUnitId|String|"sourceUnitId\_42065721ef06"|来源单位 ID|
|一级|operatorName|String|"operatorName\_063850af7e5c"|操作人名称|
|一级|operatorId|String|"operatorId\_44b52de2ee15"|红冲经办人|
|一级|storageOperatorName|String|"storageOperatorName\_01a6525214a6"|入库经办人名称|
|一级|storageOperatorId|String|"storageOperatorId\_87a4bf6208c2"|入库经办人|
|一级|storageCode|String|"storageCode\_889a168272bd"|来源单号（入库单）|
|一级|storageName|String|"storageName\_39d760bf36b7"|来源单名称（入库单）|
|一级|storageGoodsSource|String|"storageGoodsSource\_e5cd5054754e"|来源类型|
|一级|money|BigDecimal|0|本次入库不含税金额|
|一级|taxMoney|BigDecimal|0|本次红冲含税金额|
|一级|redStorageName|String|"redStorageName\_03f37159360f"|入库红冲单名称|
|一级|redStorageDetailList|Array|-|入库红冲单详情|
|二级|redStorageDetailList[0].reason|String|"reason\_c319eebe50ea"|红冲原因|
|二级|redStorageDetailList[0].lineId|String|"lineId\_b00f33b98d0d"|线路 ID|
|二级|redStorageDetailList[0].lineName|String|"lineName\_b54d678a1875"|线路名称|
|二级|redStorageDetailList[0].orderIndex|String|"orderIndex\_76eb10b901e6"|序号|
|二级|redStorageDetailList[0].initId|String|"initId\_f9091c012c27"|初始化 ID|
|二级|redStorageDetailList[0].tax|BigDecimal|0|税率|
|二级|redStorageDetailList[0].goodsType|String|"goodsType\_4cad8d0f9c53"|物资类型|
|二级|redStorageDetailList[0].goodsVersion|String|"goodsVersion\_166456a0f671"|规格型号|
|二级|redStorageDetailList[0].redStorageId|String|"redStorageId\_774c62813822"|入库红冲单id|
|二级|redStorageDetailList[0].remark|String|"remark\_91a8a80adeb7"|备注|
|二级|redStorageDetailList[0].localSend|String|"localSend\_79d5fbc5de42"|现场直送|
|二级|redStorageDetailList[0].gkDeptName|String|"gkDeptName\_5bf1aad8ef5e"|归口部门名称|
|二级|redStorageDetailList[0].gkDeptId|String|"gkDeptId\_780ef4f13ea0"|归口部门 ID|
|二级|redStorageDetailList[0].manageDeptName|String|"manageDeptName\_b5fa868f3f92"|管理部门名称|
|二级|redStorageDetailList[0].manageDeptId|String|"manageDeptId\_3c8d1d02f493"|管理部门 ID|
|二级|redStorageDetailList[0].batch|String|"batch\_a30f760ad607"|批次|
|二级|redStorageDetailList[0].gbName|String|"gbName\_960c6e2da626"|工班名称|
|二级|redStorageDetailList[0].gbId|String|"gbId\_045d2615802e"|工班 ID|
|二级|redStorageDetailList[0].bzName|String|"bzName\_c2827e0f394f"|班组名称|
|二级|redStorageDetailList[0].bzId|String|"bzId\_dc2c37ece89d"|班组 ID|
|二级|redStorageDetailList[0].deptName|String|"deptName\_0df3b8987cc2"|所属部门名称|
|二级|redStorageDetailList[0].deptId|String|"deptId\_725476b662d3"|所属部门 ID|
|二级|redStorageDetailList[0].orgName|String|"orgName\_c51f2cef97e4"|所属公司|
|二级|redStorageDetailList[0].orgId|String|"orgId\_6dac965fceff"|所属公司ID|
|二级|redStorageDetailList[0].zbCycle|String|"zbCycle\_4055e46f2bcc"|质保周期|
|二级|redStorageDetailList[0].produceDate|String|"2025-07-16"|生产日期|
|二级|redStorageDetailList[0].shelfName|String|"shelfName\_a10ee183e372"|货架名称|
|二级|redStorageDetailList[0].shelfId|String|"shelfId\_8807aafea879"|货架 ID|
|二级|redStorageDetailList[0].warehouseName|String|"warehouseName\_20673f4850a9"|仓库名称|
|二级|redStorageDetailList[0].warehouseId|String|"warehouseId\_c4786b746252"|仓库 ID|
|二级|redStorageDetailList[0].taxAllPrice|BigDecimal|0|含税总额|
|二级|redStorageDetailList[0].taxPrice|BigDecimal|0|含税单价|
|二级|redStorageDetailList[0].brand|String|"brand\_27340b168723"|品牌|
|二级|redStorageDetailList[0].unitName|String|"unitName\_2b45d3aff434"|单位名称|
|二级|redStorageDetailList[0].unitId|String|"unitId\_8a90c35ee849"|单位 ID|
|二级|redStorageDetailList[0].redStorageNum|Integer|0|本次红冲数量|
|二级|redStorageDetailList[0].storageNum|Integer|0|入库数量|
|二级|redStorageDetailList[0].goodsName|String|"goodsName\_08a66b797063"|物资名称|
|二级|redStorageDetailList[0].goodsCode|String|"goodsCode\_053edcc2f3b0"|物资编码|
|二级|redStorageDetailList[0].goodsId|String|"goodsId\_20e906977b06"|物资 ID|
|二级|redStorageDetailList[0].storageType|String|"storageType\_69ab4444e9d2"|入库类型|
|二级|redStorageDetailList[0].createDate|String|"2025-07-16 17:46:00"|创建时间|
|二级|redStorageDetailList[0].createUser|String|"createUser\_175cd30e49dd"|创建人|
|二级|redStorageDetailList[0].createName|String|"createName\_e4c4761f20d1"|创建人姓名|
|二级|redStorageDetailList[0].updateDate|String|"2025-07-16 17:46:00"|更新时间|
|二级|redStorageDetailList[0].updateUser|String|"updateUser\_74421ebcaf11"|更新人|
|二级|redStorageDetailList[0].updateName|String|"updateName\_db003c4a491e"|更新人姓名|
|二级|redStorageDetailList[0].id|String|"id\_a1a91cefe658"|ID|
|二级|redStorageDetailList[0].status|Integer|0|状态|
|二级|redStorageDetailList[0].billCode|String|"billCode\_8dfbd8d62e47"|单据编码|
|一级|createDate|String|"2025-07-16 17:46:00"|创建时间|
|一级|createUser|String|"createUser\_0474ac3c4499"|创建人|
|一级|createName|String|"createName\_eeaa1b78b3e5"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:46:00"|更新时间|
|一级|updateUser|String|"updateUser\_a1ba51a15018"|更新人|
|一级|updateName|String|"updateName\_4d9095e28fd7"|更新人姓名|
|一级|id|String|"id\_68f8449fc9cd"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_8dd09d22d90d"|单据编码|

### Request信息示例
{

`  `"redStorageType": 0,

`  `"inGoodsCodeExport": "inGoodsCodeExport\_88a747a8a202",

`  `"materialManagementEngineer": "materialManagementEngineer\_508bdac2067d",

`  `"materialManagementEngineerDate": "2025-07-16 17:46:00",

`  `"exportBillCode": "exportBillCode\_4ee33cb8dfdc",

`  `"initId": "initId\_1c86a9c4dee7",

`  `"buyOrderCode": "buyOrderCode\_35a46061689c",

`  `"contractCode": "contractCode\_ca57364608f7",

`  `"reason": "reason\_f2d3d94ce7bc",

`  `"warehouseName": "warehouseName\_d72458c7a2a6",

`  `"warehouseId": "warehouseId\_8d81aeb3b87d",

`  `"storageDate": "2025-07-16 17:46:00",

`  `"sourceUserName": "sourceUserName\_4ee52f61a685",

`  `"sourceUserId": "sourceUserId\_8d355bb7859d",

`  `"sourceUnitName": "sourceUnitName\_c82125e69bc8",

`  `"sourceUnitId": "sourceUnitId\_42065721ef06",

`  `"operatorName": "operatorName\_063850af7e5c",

`  `"operatorId": "operatorId\_44b52de2ee15",

`  `"storageOperatorName": "storageOperatorName\_01a6525214a6",

`  `"storageOperatorId": "storageOperatorId\_87a4bf6208c2",

`  `"storageCode": "storageCode\_889a168272bd",

`  `"storageName": "storageName\_39d760bf36b7",

`  `"storageGoodsSource": "storageGoodsSource\_e5cd5054754e",

`  `"money": 0.00,

`  `"taxMoney": 0.00,

`  `"redStorageName": "redStorageName\_03f37159360f",

`  `"redStorageDetailList": [

`    `{

`      `"reason": "reason\_c319eebe50ea",

`      `"lineId": "lineId\_b00f33b98d0d",

`      `"lineName": "lineName\_b54d678a1875",

`      `"orderIndex": "orderIndex\_76eb10b901e6",

`      `"initId": "initId\_f9091c012c27",

`      `"tax": 0.00,

`      `"goodsType": "goodsType\_4cad8d0f9c53",

`      `"goodsVersion": "goodsVersion\_166456a0f671",

`      `"redStorageId": "redStorageId\_774c62813822",

`      `"remark": "remark\_91a8a80adeb7",

`      `"localSend": "localSend\_79d5fbc5de42",

`      `"gkDeptName": "gkDeptName\_5bf1aad8ef5e",

`      `"gkDeptId": "gkDeptId\_780ef4f13ea0",

`      `"manageDeptName": "manageDeptName\_b5fa868f3f92",

`      `"manageDeptId": "manageDeptId\_3c8d1d02f493",

`      `"batch": "batch\_a30f760ad607",

`      `"gbName": "gbName\_960c6e2da626",

`      `"gbId": "gbId\_045d2615802e",

`      `"bzName": "bzName\_c2827e0f394f",

`      `"bzId": "bzId\_dc2c37ece89d",

`      `"deptName": "deptName\_0df3b8987cc2",

`      `"deptId": "deptId\_725476b662d3",

`      `"orgName": "orgName\_c51f2cef97e4",

`      `"orgId": "orgId\_6dac965fceff",

`      `"zbCycle": "zbCycle\_4055e46f2bcc",

`      `"produceDate": "2025-07-16",

`      `"shelfName": "shelfName\_a10ee183e372",

`      `"shelfId": "shelfId\_8807aafea879",

`      `"warehouseName": "warehouseName\_20673f4850a9",

`      `"warehouseId": "warehouseId\_c4786b746252",

`      `"taxAllPrice": 0.00,

`      `"taxPrice": 0.00,

`      `"brand": "brand\_27340b168723",

`      `"unitName": "unitName\_2b45d3aff434",

`      `"unitId": "unitId\_8a90c35ee849",

`      `"redStorageNum": 0,

`      `"storageNum": 0,

`      `"goodsName": "goodsName\_08a66b797063",

`      `"goodsCode": "goodsCode\_053edcc2f3b0",

`      `"goodsId": "goodsId\_20e906977b06",

`      `"storageType": "storageType\_69ab4444e9d2",

`      `"sourceType": "sourceType\_f01efcae383f",

`      `"entityModelId": "entityModelId\_735f5c4c39d4",

`      `"processDefinitionId": "processDefinitionId\_33377c61003b",

`      `"processInstanceId": "processInstanceId\_44d66a079ee5",

`      `"processInstanceKey": "processInstanceKey\_ba43775b66f5",

`      `"nextProcessUserList": {},

`      `"attachmentFileName": "attachmentFileName\_f66b4f4ba955",

`      `"attachmentFileUrl": "attachmentFileUrl\_3a044a6bacc3",

`      `"createDate": "2025-07-16 17:46:00",

`      `"createUser": "createUser\_175cd30e49dd",

`      `"createName": "createName\_e4c4761f20d1",

`      `"updateDate": "2025-07-16 17:46:00",

`      `"updateUser": "updateUser\_74421ebcaf11",

`      `"updateName": "updateName\_db003c4a491e",

`      `"id": "id\_a1a91cefe658",

`      `"status": 0,

`      `"billCode": "billCode\_8dfbd8d62e47"

`    `}

`  `],

`  `"sourceType": "sourceType\_0ed1dfc1a1af",

`  `"entityModelId": "entityModelId\_3a0e4e386b04",

`  `"processDefinitionId": "processDefinitionId\_5af86cdf7790",

`  `"processInstanceId": "processInstanceId\_177b8b16e572",

`  `"processInstanceKey": "processInstanceKey\_581694c0e776",

`  `"nextProcessUserList": {},

`  `"attachmentFileName": "attachmentFileName\_ed1e09239c97",

`  `"attachmentFileUrl": "attachmentFileUrl\_82ef0a548a81",

`  `"createDate": "2025-07-16 17:46:00",

`  `"createUser": "createUser\_0474ac3c4499",

`  `"createName": "createName\_eeaa1b78b3e5",

`  `"updateDate": "2025-07-16 17:46:00",

`  `"updateUser": "updateUser\_a1ba51a15018",

`  `"updateName": "updateName\_4d9095e28fd7",

`  `"id": "id\_68f8449fc9cd",

`  `"status": 0,

`  `"billCode": "billCode\_8dd09d22d90d"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 出库单
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|deliverAddress|String|"deliverAddress\_4425b9dcddb8"|配送地点|
|一级|isDeliver|Integer|0|是否配送|
|一级|outboundType|Integer|0|出库类型（1领料出库  2借用出库  3销售出库）|
|一级|applyType|Integer|0|申领类型（1总库领料  2材料间领料）|
|一级|isRedOut|Integer|0|是否红冲入库|
|一级|chargeUser|String|"chargeUser\_0018b7e0b3fa"|仓库负责人|
|一级|chargeUserId|String|"chargeUserId\_473a2c5fd83e"|仓库负责人Id|
|一级|deliverPhone|String|"deliverPhone\_81c037a1fdbd"|配送联系方式|
|一级|deliverPeople|String|"deliverPeople\_b0b3226c00d3"|配送联系人|
|一级|deliverTime|String|"2025-07-16 17:49:10"|配送时间|
|一级|pickingType|Integer|0|领料类型（1材料间  2实际消耗）|
|一级|materialRoomWarehouseId|String|"materialRoomWarehouseId\_b5231fdddbc6"|材料间仓库ID|
|一级|materialRoomWarehouseName|String|"materialRoomWarehouseName\_aa2d523722db"|材料间仓库名称|
|一级|isPass|Integer|0|财务是否审核|
|一级|outGoodsName|String|"outGoodsName\_f7afbbfa261c"|出库名称|
|一级|operatorId|String|"operatorId\_4734f30d322e"|经办人id|
|一级|operatorName|String|"operatorName\_0b9e1fec53f2"|经办人姓名|
|一级|operatTime|String|"2025-07-16"|经办时间|
|一级|describe|String|"describe\_a150ae0677af"|描述|
|一级|closeDate|String|"2025-07-16"|关闭日期|
|一级|applyUserName|String|"applyUserName\_3cc6490cc471"|申请人姓名|
|一级|applyUserId|String|"applyUserId\_dd255fa5a98a"|申请人 ID|
|一级|applyDeptName|String|"applyDeptName\_49e5489785b8"|申请单位名称|
|一级|applyDeptId|String|"applyDeptId\_465073085f2e"|申请单位 ID|
|一级|workCode|String|"workCode\_6c0985676513"|工单号|
|一级|applyDate|String|"2025-07-16"|申请日期|
|一级|applyCode|String|"applyCode\_05f44c63a29e"|领料单号|
|一级|outGoodsInfoList|Array|-|出库单详情|
|二级|outGoodsInfoList[0].remark|String|"remark\_d1e8d1ce2704"|备注|
|二级|outGoodsInfoList[0].applyUserName|String|"applyUserName\_5959e58d3489"|申请人姓名|
|二级|outGoodsInfoList[0].orderIndex|String|"orderIndex\_e75dd1c18d23"|订单序号|
|二级|outGoodsInfoList[0].applyGoodsId|String|"applyGoodsId\_de20654a10ed"|申领料申请管理id|
|二级|outGoodsInfoList[0].applyGoodsInfoId|String|"applyGoodsInfoId\_6a25568ff904"|领料明细id|
|二级|outGoodsInfoList[0].canUseNum|Integer|0|可申请数量|
|二级|outGoodsInfoList[0].redOutAllNum|Integer|0|已红冲数量|
|二级|outGoodsInfoList[0].inventoryId|String|"inventoryId\_c7409da503d5"|库存 ID|
|二级|outGoodsInfoList[0].materialRoomWarehouseId|String|"materialRoomWarehouseId\_ce94bd4ae9f0"|材料间仓库ID|
|二级|outGoodsInfoList[0].materialRoomWarehouseName|String|"materialRoomWarehouseName\_9a1dbd654c9d"|材料间仓库名称|
|二级|outGoodsInfoList[0].outGoodsId|String|"outGoodsId\_0f4a06a972f5"|出库单id|
|二级|outGoodsInfoList[0].isNeedWordOrder|Integer|0|需工单消耗|
|二级|outGoodsInfoList[0].repairProcess|String|"repairProcess\_314995fc1b44"|修程|
|二级|outGoodsInfoList[0].major|String|"major\_aa1f4d385ddd"|专业|
|二级|outGoodsInfoList[0].forWay|String|"forWay\_956e4b70c0c1"|用于线路|
|二级|outGoodsInfoList[0].forWayId|String|"forWayId\_6158330aa5ce"|用于线路ID|
|二级|outGoodsInfoList[0].projectInfo|String|"projectInfo\_4905f30eafcd"|项目信息|
|二级|outGoodsInfoList[0].useForBuffer|String|"useForBuffer\_cce1da62545e"|用途补充说明|
|二级|outGoodsInfoList[0].useFor|String|"useFor\_4063f3107c20"|用途|
|二级|outGoodsInfoList[0].goodsNum|Integer|0|数量|
|二级|outGoodsInfoList[0].mgDeptName|String|"mgDeptName\_47fda85095dd"|管理部门名称|
|二级|outGoodsInfoList[0].mgDeptId|String|"mgDeptId\_3ec712e82625"|管理部门 ID|
|二级|outGoodsInfoList[0].warehouseWay|String|"warehouseWay\_8695b0029d63"|仓库线路|
|二级|outGoodsInfoList[0].warehouseWayId|String|"warehouseWayId\_eaa8e440195e"|仓库线路id|
|二级|outGoodsInfoList[0].shelfName|String|"shelfName\_67b1d3d4e252"|货架名称|
|二级|outGoodsInfoList[0].shelfId|String|"shelfId\_e9902afe9338"|货架 ID|
|二级|outGoodsInfoList[0].warehouseName|String|"warehouseName\_cffe40af0f55"|仓库名称|
|二级|outGoodsInfoList[0].warehouseId|String|"warehouseId\_dcfccac56417"|仓库 ID|
|二级|outGoodsInfoList[0].goodsType|String|"goodsType\_34b03500cfa5"|物资类型|
|二级|outGoodsInfoList[0].goodsAttribute|String|"goodsAttribute\_eeea516117b6"|物资属性|
|二级|outGoodsInfoList[0].brand|String|"brand\_119683851f86"|品牌|
|二级|outGoodsInfoList[0].unitId|String|"unitId\_8bc754efb1a0"|库存单位 ID|
|二级|outGoodsInfoList[0].unitName|String|"unitName\_4d7cc992afbe"|库存单位名称|
|二级|outGoodsInfoList[0].goodsVersion|String|"goodsVersion\_86a8f31a7994"|规格型号|
|二级|outGoodsInfoList[0].goodsName|String|"goodsName\_c87004a545d7"|物资名称|
|二级|outGoodsInfoList[0].goodsCode|String|"goodsCode\_6b823b9cc88f"|物资编码|
|二级|outGoodsInfoList[0].goodsId|String|"goodsId\_b9671ed5a8fb"|物资ID|
|二级|outGoodsInfoList[0].createDate|String|"2025-07-16 17:49:10"|创建时间|
|二级|outGoodsInfoList[0].createUser|String|"createUser\_ab737c6879af"|创建人|
|二级|outGoodsInfoList[0].createName|String|"createName\_c50d9f7d2c0e"|创建人姓名|
|二级|outGoodsInfoList[0].updateDate|String|"2025-07-16 17:49:10"|更新时间|
|二级|outGoodsInfoList[0].updateUser|String|"updateUser\_f923eef1f5ba"|更新人|
|二级|outGoodsInfoList[0].updateName|String|"updateName\_43df603d199f"|更新人姓名|
|二级|outGoodsInfoList[0].id|String|"id\_0a3fafb215d5"|ID|
|二级|outGoodsInfoList[0].status|Integer|0|状态|
|二级|outGoodsInfoList[0].billCode|String|"billCode\_abe21f3c6956"|单据编码|
|一级|createDate|String|"2025-07-16 17:49:10"|创建时间|
|一级|createUser|String|"createUser\_4dea48d74f43"|创建人|
|一级|createName|String|"createName\_4929043d362d"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:49:10"|更新时间|
|一级|updateUser|String|"updateUser\_845f869a2fd4"|更新人|
|一级|updateName|String|"updateName\_d1fe10317ef6"|更新人姓名|
|一级|id|String|"id\_0a0a5fb2cfca"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_09d7e1eb9a7b"|单据编码|

### Request信息示例
{

`  `"deliverAddress": "deliverAddress\_4425b9dcddb8",

`  `"isDeliver": 0,

`  `"exportBillCode": "exportBillCode\_392cb72accc8",

`  `"outboundType": 0,

`  `"applyType": 0,

`  `"isRedOut": 0,

`  `"chargeUser": "chargeUser\_0018b7e0b3fa",

`  `"chargeUserId": "chargeUserId\_473a2c5fd83e",

`  `"deliverPhone": "deliverPhone\_81c037a1fdbd",

`  `"deliverPeople": "deliverPeople\_b0b3226c00d3",

`  `"deliverTime": "2025-07-16 17:49:10",

`  `"pickingType": 0,

`  `"materialRoomWarehouseId": "materialRoomWarehouseId\_b5231fdddbc6",

`  `"materialRoomWarehouseName": "materialRoomWarehouseName\_aa2d523722db",

`  `"isPass": 0,

`  `"outGoodsName": "outGoodsName\_f7afbbfa261c",

`  `"operatorId": "operatorId\_4734f30d322e",

`  `"operatorName": "operatorName\_0b9e1fec53f2",

`  `"operatTime": "2025-07-16",

`  `"describe": "describe\_a150ae0677af",

`  `"closeDate": "2025-07-16",

`  `"applyUserName": "applyUserName\_3cc6490cc471",

`  `"applyUserId": "applyUserId\_dd255fa5a98a",

`  `"applyDeptName": "applyDeptName\_49e5489785b8",

`  `"applyDeptId": "applyDeptId\_465073085f2e",

`  `"workCode": "workCode\_6c0985676513",

`  `"applyDate": "2025-07-16",

`  `"applyCode": "applyCode\_05f44c63a29e",

`  `"outGoodsInfoList": [

`    `{

`      `"remark": "remark\_d1e8d1ce2704",

`      `"applyUserName": "applyUserName\_5959e58d3489",

`      `"orderIndex": "orderIndex\_e75dd1c18d23",

`      `"applyGoodsId": "applyGoodsId\_de20654a10ed",

`      `"applyGoodsInfoId": "applyGoodsInfoId\_6a25568ff904",

`      `"canUseNum": 0,

`      `"redOutAllNum": 0,

`      `"inventoryId": "inventoryId\_c7409da503d5",

`      `"materialRoomWarehouseId": "materialRoomWarehouseId\_ce94bd4ae9f0",

`      `"materialRoomWarehouseName": "materialRoomWarehouseName\_9a1dbd654c9d",

`      `"outGoodsId": "outGoodsId\_0f4a06a972f5",

`      `"isNeedWordOrder": 0,

`      `"repairProcess": "repairProcess\_314995fc1b44",

`      `"major": "major\_aa1f4d385ddd",

`      `"forWay": "forWay\_956e4b70c0c1",

`      `"forWayId": "forWayId\_6158330aa5ce",

`      `"projectInfo": "projectInfo\_4905f30eafcd",

`      `"useForBuffer": "useForBuffer\_cce1da62545e",

`      `"useFor": "useFor\_4063f3107c20",

`      `"goodsNum": 0,

`      `"mgDeptName": "mgDeptName\_47fda85095dd",

`      `"mgDeptId": "mgDeptId\_3ec712e82625",

`      `"warehouseWay": "warehouseWay\_8695b0029d63",

`      `"warehouseWayId": "warehouseWayId\_eaa8e440195e",

`      `"shelfName": "shelfName\_67b1d3d4e252",

`      `"shelfId": "shelfId\_e9902afe9338",

`      `"warehouseName": "warehouseName\_cffe40af0f55",

`      `"warehouseId": "warehouseId\_dcfccac56417",

`      `"goodsType": "goodsType\_34b03500cfa5",

`      `"goodsAttribute": "goodsAttribute\_eeea516117b6",

`      `"brand": "brand\_119683851f86",

`      `"unitId": "unitId\_8bc754efb1a0",

`      `"unitName": "unitName\_4d7cc992afbe",

`      `"goodsVersion": "goodsVersion\_86a8f31a7994",

`      `"goodsName": "goodsName\_c87004a545d7",

`      `"goodsCode": "goodsCode\_6b823b9cc88f",

`      `"goodsId": "goodsId\_b9671ed5a8fb",

`      `"sourceType": "sourceType\_c6529ad9421d",

`      `"entityModelId": "entityModelId\_813df2a896eb",

`      `"processDefinitionId": "processDefinitionId\_dca7c830757f",

`      `"processInstanceId": "processInstanceId\_e7e335af441d",

`      `"processInstanceKey": "processInstanceKey\_d5e67007e315",

`      `"nextProcessUserList": {},

`      `"attachmentFileName": "attachmentFileName\_bd217619d4f7",

`      `"attachmentFileUrl": "attachmentFileUrl\_2a4663126b59",

`      `"createDate": "2025-07-16 17:49:10",

`      `"createUser": "createUser\_ab737c6879af",

`      `"createName": "createName\_c50d9f7d2c0e",

`      `"updateDate": "2025-07-16 17:49:10",

`      `"updateUser": "updateUser\_f923eef1f5ba",

`      `"updateName": "updateName\_43df603d199f",

`      `"id": "id\_0a3fafb215d5",

`      `"status": 0,

`      `"billCode": "billCode\_abe21f3c6956"

`    `}

`  `],

`  `"sourceType": "sourceType\_ae3022340a98",

`  `"entityModelId": "entityModelId\_cf3572df83fc",

`  `"processDefinitionId": "processDefinitionId\_11a8b6aecb0b",

`  `"processInstanceId": "processInstanceId\_f60ae4ebbb35",

`  `"processInstanceKey": "processInstanceKey\_50bb871b5519",

`  `"nextProcessUserList": {},

`  `"attachmentFileName": "attachmentFileName\_2badf8508802",

`  `"attachmentFileUrl": "attachmentFileUrl\_4ecca6cc5376",

`  `"createDate": "2025-07-16 17:49:10",

`  `"createUser": "createUser\_4dea48d74f43",

`  `"createName": "createName\_4929043d362d",

`  `"updateDate": "2025-07-16 17:49:10",

`  `"updateUser": "updateUser\_845f869a2fd4",

`  `"updateName": "updateName\_d1fe10317ef6",

`  `"id": "id\_0a0a5fb2cfca",

`  `"status": 0,

`  `"billCode": "billCode\_09d7e1eb9a7b"

}

### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 总库领料/材料间领料
### 接口信息
测试地址：

正式地址

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|materialRoomWarehouseId|String|"materialRoomWarehouseId\_3f92ff5c3e15"|材料间仓库ID|
|一级|materialRoomWarehouseName|String|"materialRoomWarehouseName\_17415a696f93"|材料间仓库名称|
|一级|pickingType|Integer|0|领料类型（1材料间  2实际消耗）|
|一级|isToMaterialRoom|Integer|0|是否到材料间|
|一级|deliverTime|String|"2025-07-16 17:53:12"|配送时间|
|一级|name|String|"name\_72707d36aa74"|领料名称|
|一级|described|String|"described\_dcae16caab1f"|描述|
|一级|closeDate|String|"2025-07-16"|申请单关闭时间|
|一级|workOrderCode|String|"workOrderCode\_a607121bc120"|工单编号（关联工单信息）|
|一级|workOrderId|String|"workOrderId\_166d0094f04c"|工单ID|
|一级|applyType|Integer|0|申领类型（1总库领料  2材料间领料）|
|一级|applyStatus|Integer|0|领料申请状态（1申请中  2申请同意  3申请拒绝）|
|一级|changeDate|String|"2025-07-16"|变更日期|
|一级|applyDate|String|"2025-07-16"|申领日期|
|一级|deliverStatus|Integer|0|配送状态（0未配送  2 配送中  3配送完成）|
|一级|deliverPhone|String|"deliverPhone\_98c9a080f72a"|配送联系方式|
|一级|deliverPeople|String|"deliverPeople\_7d6682cb3c33"|配送联系人|
|一级|deliverAddress|String|"deliverAddress\_b2d937b18ccf"|配送地点|
|一级|isDeliver|Integer|0|是否配送|
|一级|warehouse\_MANAGE|String|"warehouse\_MANAGE\_cbd13447c4f2"|仓库主管|
|一级|manageDeptId|String|"manageDeptId\_05c936c67acd"|管理单位ID|
|一级|manageDeptName|String|"manageDeptName\_a82a3506ffef"|管理单位名称|
|一级|applyDeptId|String|"applyDeptId\_0f994f917636"|申请单位ID|
|一级|applyDeptName|String|"applyDeptName\_eeeccd0d32a9"|申请单位名称|
|一级|applyUserId|String|"applyUserId\_b36912e33a05"|申请人 ID|
|一级|applyUserName|String|"applyUserName\_b811bdfd503c"|申请人姓名|
|一级|applyGoodsCode|String|"applyGoodsCode\_714e61948951"|领料编号|
|一级|applyGoodsInfoList|Array|-|领料明细|
|二级|applyGoodsInfoList[0].remark|String|"remark\_d390daf571ee"|备注|
|二级|applyGoodsInfoList[0].orderIndex|String|"orderIndex\_679bc490ee5a"|序号|
|二级|applyGoodsInfoList[0].canUseNum|Integer|0|可领用数量|
|二级|applyGoodsInfoList[0].mgDept|String|"mgDept\_eafbf43ebecd"|管理部门|
|二级|applyGoodsInfoList[0].mgDeptId|String|"mgDeptId\_e18c623dc2d5"|管理部门 ID|
|二级|applyGoodsInfoList[0].materialRoomWarehouseId|String|"materialRoomWarehouseId\_27647f9b382f"|材料间仓库ID|
|二级|applyGoodsInfoList[0].materialRoomWarehouseName|String|"materialRoomWarehouseName\_5f64f15bb4e7"|材料间仓库名称|
|二级|applyGoodsInfoList[0].goodsSource|String|"goodsSource\_724d0d5477d6"|物资来源(采购/移交/其他)|
|二级|applyGoodsInfoList[0].gkDeptName|String|"gkDeptName\_01926e042ce8"|归口部门名称|
|二级|applyGoodsInfoList[0].gkDeptId|String|"gkDeptId\_dc3f3130cd01"|归口部门 ID|
|二级|applyGoodsInfoList[0].shelfCode|String|"shelfCode\_69c977b0797e"|货架编码|
|二级|applyGoodsInfoList[0].shelfName|String|"shelfName\_5f838bd56e2d"|货架名称|
|二级|applyGoodsInfoList[0].applyGoodsId|String|"applyGoodsId\_67b549fb93db"|领料申请管理id|
|二级|applyGoodsInfoList[0].describe|String|"describe\_b101699770a6"|描述|
|二级|applyGoodsInfoList[0].workOrderId|String|"workOrderId\_c6afb21f108b"|工单 ID|
|二级|applyGoodsInfoList[0].isCollect|String|"isCollect\_8aa1dbfdd17e"|是否收藏|
|二级|applyGoodsInfoList[0].repairProcess|String|"repairProcess\_56d1e99677b6"|工程|
|二级|applyGoodsInfoList[0].isNeedWordOrder|String|"isNeedWordOrder\_1d4800145b6e"|是否需要工单消耗|
|二级|applyGoodsInfoList[0].major|String|"major\_07c729627651"|专业|
|二级|applyGoodsInfoList[0].projectInfo|String|"projectInfo\_ec6bd5c15d0b"|项目信息|
|二级|applyGoodsInfoList[0].goodsVersion|String|"goodsVersion\_f220feca500d"|规格型号|
|二级|applyGoodsInfoList[0].useForBuffer|String|"useForBuffer\_6bf4063a6fec"|用途补充说明|
|二级|applyGoodsInfoList[0].useFor|String|"useFor\_126d22b8869d"|用途|
|二级|applyGoodsInfoList[0].num|Integer|0|申领数量|
|二级|applyGoodsInfoList[0].lineId|String|"lineId\_abad357b5692"|来源线路|
|二级|applyGoodsInfoList[0].lineName|String|"lineName\_9b534f477963"|线路名称|
|二级|applyGoodsInfoList[0].forLineName|String|"forLineName\_d9671eff4137"|用于线路|
|二级|applyGoodsInfoList[0].forLineId|String|"forLineId\_7e4955c40777"|用于线路ID|
|二级|applyGoodsInfoList[0].buyType|String|"buyType\_0d86462bda4a"|自购/移交|
|二级|applyGoodsInfoList[0].shelfId|String|"shelfId\_f354a4e6cfa9"|货架位|
|二级|applyGoodsInfoList[0].warehouseId|String|"warehouseId\_e8845deb72ca"|仓库 ID|
|二级|applyGoodsInfoList[0].warehouseName|String|"warehouseName\_4188d22634fe"|仓库名称|
|二级|applyGoodsInfoList[0].unitId|String|"unitId\_d435d3f65f75"|订单单位ID|
|二级|applyGoodsInfoList[0].unitName|String|"unitName\_68dd2b49a31f"|订单单位名称|
|二级|applyGoodsInfoList[0].brand|String|"brand\_b031add0786d"|品牌|
|二级|applyGoodsInfoList[0].goodsAttribute|String|"goodsAttribute\_e4363813316b"|物资属性|
|二级|applyGoodsInfoList[0].goodsType|String|"goodsType\_d1d01df96b3a"|物资类型|
|二级|applyGoodsInfoList[0].goodsName|String|"goodsName\_e41b51a917bb"|物资名称|
|二级|applyGoodsInfoList[0].goodsCode|String|"goodsCode\_eef2f1122ffa"|物资编码|
|二级|applyGoodsInfoList[0].inventoryId|String|"inventoryId\_5e2f60528c30"|库存 ID|
|二级|applyGoodsInfoList[0].goodsId|String|"goodsId\_7ed5c5cb3afe"|物资Id|
|二级|applyGoodsInfoList[0].storageInfoId|String|"storageInfoId\_5eec8a03d494"|入库明细ID|
|二级|applyGoodsInfoList[0].actualNum|Integer|0|实际数量|
|二级|applyGoodsInfoList[0].createDate|String|"2025-07-16 17:53:12"|创建时间|
|二级|applyGoodsInfoList[0].createUser|String|"createUser\_26d8a1a4b639"|创建人|
|二级|applyGoodsInfoList[0].createName|String|"createName\_2aa4838d064b"|创建人姓名|
|二级|applyGoodsInfoList[0].updateDate|String|"2025-07-16 17:53:12"|更新时间|
|二级|applyGoodsInfoList[0].updateUser|String|"updateUser\_86513f48387c"|更新人|
|二级|applyGoodsInfoList[0].updateName|String|"updateName\_5f311fbfe4fa"|更新人姓名|
|二级|applyGoodsInfoList[0].id|String|"id\_95182c3e1413"|ID|
|二级|applyGoodsInfoList[0].status|Integer|0|状态|
|二级|applyGoodsInfoList[0].billCode|String|"billCode\_d9e247ea43e7"|单据编码|
|一级|createDate|String|"2025-07-16 17:53:12"|创建时间|
|一级|createUser|String|"createUser\_a3ea025a216e"|创建人|
|一级|createName|String|"createName\_75c56a3635c7"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:53:12"|更新时间|
|一级|updateUser|String|"updateUser\_2f33e5163879"|更新人|
|一级|updateName|String|"updateName\_4c5dfb1892a9"|更新人姓名|
|一级|id|String|"id\_a5de5fa256ac"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_fdd00bc310ba"|单据编码|

### Request信息示例
{

`  `"warehouseForeman": "warehouseForeman\_978aaab65e0b",

`  `"warehouseForemanDate": "2025-07-16 17:53:12",

`  `"exportBillCode": "exportBillCode\_868857d1c940",

`  `"materialRoomWarehouseId": "materialRoomWarehouseId\_3f92ff5c3e15",

`  `"materialRoomWarehouseName": "materialRoomWarehouseName\_17415a696f93",

`  `"pickingType": 0,

`  `"isToMaterialRoom": 0,

`  `"deliverTime": "2025-07-16 17:53:12",

`  `"name": "name\_72707d36aa74",

`  `"described": "described\_dcae16caab1f",

`  `"closeDate": "2025-07-16",

`  `"workOrderCode": "workOrderCode\_a607121bc120",

`  `"workOrderId": "workOrderId\_166d0094f04c",

`  `"applyType": 0,

`  `"applyStatus": 0,

`  `"changeDate": "2025-07-16",

`  `"applyDate": "2025-07-16",

`  `"deliverStatus": 0,

`  `"deliverPhone": "deliverPhone\_98c9a080f72a",

`  `"deliverPeople": "deliverPeople\_7d6682cb3c33",

`  `"deliverAddress": "deliverAddress\_b2d937b18ccf",

`  `"isDeliver": 0,

`  `"warehouse\_MANAGE": "warehouse\_MANAGE\_cbd13447c4f2",

`  `"manageDeptId": "manageDeptId\_05c936c67acd",

`  `"manageDeptName": "manageDeptName\_a82a3506ffef",

`  `"applyDeptId": "applyDeptId\_0f994f917636",

`  `"applyDeptName": "applyDeptName\_eeeccd0d32a9",

`  `"applyUserId": "applyUserId\_b36912e33a05",

`  `"applyUserName": "applyUserName\_b811bdfd503c",

`  `"applyGoodsCode": "applyGoodsCode\_714e61948951",

`  `"applyGoodsInfoList": [

`    `{

`      `"remark": "remark\_d390daf571ee",

`      `"orderIndex": "orderIndex\_679bc490ee5a",

`      `"canUseNum": 0,

`      `"mgDept": "mgDept\_eafbf43ebecd",

`      `"mgDeptId": "mgDeptId\_e18c623dc2d5",

`      `"materialRoomWarehouseId": "materialRoomWarehouseId\_27647f9b382f",

`      `"materialRoomWarehouseName": "materialRoomWarehouseName\_5f64f15bb4e7",

`      `"goodsSource": "goodsSource\_724d0d5477d6",

`      `"gkDeptName": "gkDeptName\_01926e042ce8",

`      `"gkDeptId": "gkDeptId\_dc3f3130cd01",

`      `"shelfCode": "shelfCode\_69c977b0797e",

`      `"shelfName": "shelfName\_5f838bd56e2d",

`      `"applyGoodsId": "applyGoodsId\_67b549fb93db",

`      `"describe": "describe\_b101699770a6",

`      `"workOrderId": "workOrderId\_c6afb21f108b",

`      `"isCollect": "isCollect\_8aa1dbfdd17e",

`      `"repairProcess": "repairProcess\_56d1e99677b6",

`      `"isNeedWordOrder": "isNeedWordOrder\_1d4800145b6e",

`      `"major": "major\_07c729627651",

`      `"projectInfo": "projectInfo\_ec6bd5c15d0b",

`      `"goodsVersion": "goodsVersion\_f220feca500d",

`      `"useForBuffer": "useForBuffer\_6bf4063a6fec",

`      `"useFor": "useFor\_126d22b8869d",

`      `"num": 0,

`      `"lineId": "lineId\_abad357b5692",

`      `"lineName": "lineName\_9b534f477963",

`      `"forLineName": "forLineName\_d9671eff4137",

`      `"forLineId": "forLineId\_7e4955c40777",

`      `"buyType": "buyType\_0d86462bda4a",

`      `"shelfId": "shelfId\_f354a4e6cfa9",

`      `"warehouseId": "warehouseId\_e8845deb72ca",

`      `"warehouseName": "warehouseName\_4188d22634fe",

`      `"unitId": "unitId\_d435d3f65f75",

`      `"unitName": "unitName\_68dd2b49a31f",

`      `"brand": "brand\_b031add0786d",

`      `"goodsAttribute": "goodsAttribute\_e4363813316b",

`      `"goodsType": "goodsType\_d1d01df96b3a",

`      `"goodsName": "goodsName\_e41b51a917bb",

`      `"goodsCode": "goodsCode\_eef2f1122ffa",

`      `"inventoryId": "inventoryId\_5e2f60528c30",

`      `"goodsId": "goodsId\_7ed5c5cb3afe",

`      `"storageInfoId": "storageInfoId\_5eec8a03d494",

`      `"actualNum": 0, 

`      `"createDate": "2025-07-16 17:53:12",

`      `"createUser": "createUser\_26d8a1a4b639",

`      `"createName": "createName\_2aa4838d064b",

`      `"updateDate": "2025-07-16 17:53:12",

`      `"updateUser": "updateUser\_86513f48387c",

`      `"updateName": "updateName\_5f311fbfe4fa",

`      `"id": "id\_95182c3e1413",

`      `"status": 0,

`      `"billCode": "billCode\_d9e247ea43e7"

`    `}

`  `],

`  `"createDate": "2025-07-16 17:53:12",

`  `"createUser": "createUser\_a3ea025a216e",

`  `"createName": "createName\_75c56a3635c7",

`  `"updateDate": "2025-07-16 17:53:12",

`  `"updateUser": "updateUser\_2f33e5163879",

`  `"updateName": "updateName\_4c5dfb1892a9",

`  `"id": "id\_a5de5fa256ac",

`  `"status": 0,

`  `"billCode": "billCode\_fdd00bc310ba"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 出库红冲单
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|outGoodsDate|String|"2025-07-16"|出库红冲时间|
|一级|reason|String|"reason\_7141f2100a6b"|红冲原因|
|一级|warehouseName|String|"warehouseName\_d8d0c85a2165"|红冲仓库名称|
|一级|warehouseId|String|"warehouseId\_e42cfa7d79b1"|红冲仓库 ID|
|一级|operatorName|String|"operatorName\_f342f686bf55"|经办人姓名|
|一级|operatorId|String|"operatorId\_e049b8f83bd1"|经办人ID|
|一级|outOperatorName|String|"outOperatorName\_e4edf5d2074b"|出库经办人姓名|
|一级|outOperatorId|String|"outOperatorId\_6d98ab38e620"|出库经办人ID|
|一级|outGoodsCode|String|"outGoodsCode\_4f9103254281"|出库编号|
|一级|outGoodsName|String|"outGoodsName\_a0018739530c"|出库名称|
|一级|money|BigDecimal|0|本次红冲不含税金额|
|一级|taxMoney|BigDecimal|0|本次红冲含税金额|
|一级|redOutName|String|"redOutName\_129185b0d2dc"|出库红冲名称|
|一级|redOutDetailList|Array|-|红冲出库|
|二级|redOutDetailList[0].remark|String|"remark\_751f6635e605"|备注|
|二级|redOutDetailList[0].goodsId|String|"goodsId\_ea1b47c48750"|物资ID|
|二级|redOutDetailList[0].forWay|String|"forWay\_babc6baa3c5b"|用于线路|
|二级|redOutDetailList[0].forWayId|String|"forWayId\_fb22f169be6c"|用于线路ID|
|二级|redOutDetailList[0].reason|String|"reason\_be1188c714b3"|红冲原因|
|二级|redOutDetailList[0].orderIndex|String|"orderIndex\_2624527eebf2"|序号|
|二级|redOutDetailList[0].goodsNum|Integer|0|出库数量|
|二级|redOutDetailList[0].tax|BigDecimal|0|税率|
|二级|redOutDetailList[0].redOutId|String|"redOutId\_33ad829f6f1c"|红冲出库单id|
|二级|redOutDetailList[0].outGoodsInfoId|String|"outGoodsInfoId\_221e3f9081db"|出库单详情id|
|二级|redOutDetailList[0].localSend|String|"localSend\_8a507bef55ef"|现场直送|
|二级|redOutDetailList[0].gkDeptName|String|"gkDeptName\_e51ab4b32418"|归口部门名称|
|二级|redOutDetailList[0].gkDeptId|String|"gkDeptId\_611229c39d55"|归口部门 ID|
|二级|redOutDetailList[0].manageDeptName|String|"manageDeptName\_7b2091edca69"|管理单位名称|
|二级|redOutDetailList[0].manageDeptId|String|"manageDeptId\_eb3b1312e2a3"|管理单位ID|
|二级|redOutDetailList[0].batch|String|"batch\_8b080c5ae005"|批次|
|二级|redOutDetailList[0].gbName|String|"gbName\_156ac88e7079"|工班名称|
|二级|redOutDetailList[0].gbId|String|"gbId\_b2e88bf52327"|工班 ID|
|二级|redOutDetailList[0].bzName|String|"bzName\_fe9db5edd44a"|班组名称|
|二级|redOutDetailList[0].bzId|String|"bzId\_c4db5ccc3d50"|班组 ID|
|二级|redOutDetailList[0].deptName|String|"deptName\_91a0110f4f9b"|所属部门|
|二级|redOutDetailList[0].deptId|String|"deptId\_d0d4f005a284"|所属部门ID|
|二级|redOutDetailList[0].orgName|String|"orgName\_2bfaa05dc95f"|所属公司|
|二级|redOutDetailList[0].orgId|String|"orgId\_3f439ab2a3e7"|所属公司ID|
|二级|redOutDetailList[0].zbCycle|String|"zbCycle\_e0f0942e9de8"|质保周期|
|二级|redOutDetailList[0].produceDate|String|"2025-07-16"|生产日期|
|二级|redOutDetailList[0].shelfName|String|"shelfName\_b6a3e3e170a1"|货架名称|
|二级|redOutDetailList[0].shelfId|String|"shelfId\_6d8044cef3ab"|货架 ID|
|二级|redOutDetailList[0].warehouseName|String|"warehouseName\_1805395c6e31"|仓库名称|
|二级|redOutDetailList[0].warehouseId|String|"warehouseId\_a5e7163032e6"|仓库 ID|
|二级|redOutDetailList[0].taxAllPrice|BigDecimal|0|含税总额|
|二级|redOutDetailList[0].taxPrice|BigDecimal|0|含税单价|
|二级|redOutDetailList[0].brand|String|"brand\_a746d6a9446a"|品牌|
|二级|redOutDetailList[0].unitId|String|"unitId\_5bf777f857a5"|单位 ID|
|二级|redOutDetailList[0].unitName|String|"unitName\_843c1005ebf7"|单位名称|
|二级|redOutDetailList[0].outStorageNum|Integer|0|本次红冲数量|
|二级|redOutDetailList[0].goodsVersion|String|"goodsVersion\_ab03730475b4"|规格型号|
|二级|redOutDetailList[0].goodsType|String|"goodsType\_b6fe22d400c5"|物资类型|
|二级|redOutDetailList[0].goodsName|String|"goodsName\_df2e4d35ad43"|物资名称|
|二级|redOutDetailList[0].goodsCode|String|"goodsCode\_f8c59bd947c1"|物资编码|
|二级|redOutDetailList[0].outStorageType|String|"outStorageType\_c4955b12044d"|出库类型|
|二级|redOutDetailList[0].createDate|String|"2025-07-16 17:56:28"|创建时间|
|二级|redOutDetailList[0].createUser|String|"createUser\_71d17b2f7702"|创建人|
|二级|redOutDetailList[0].createName|String|"createName\_42baa50086fc"|创建人姓名|
|二级|redOutDetailList[0].updateDate|String|"2025-07-16 17:56:28"|更新时间|
|二级|redOutDetailList[0].updateUser|String|"updateUser\_19cb229d6df1"|更新人|
|二级|redOutDetailList[0].updateName|String|"updateName\_ed2516548485"|更新人姓名|
|二级|redOutDetailList[0].id|String|"id\_6015d6dd3a4e"|ID|
|二级|redOutDetailList[0].status|Integer|0|状态|
|二级|redOutDetailList[0].billCode|String|"billCode\_eb0c08510425"|单据编码|
|一级|createDate|String|"2025-07-16 17:56:28"|创建时间|
|一级|createUser|String|"createUser\_64514c8bb24d"|创建人|
|一级|createName|String|"createName\_e68eb5475e9f"|创建人姓名|
|一级|updateDate|String|"2025-07-16 17:56:28"|更新时间|
|一级|updateUser|String|"updateUser\_7aafb9c33036"|更新人|
|一级|updateName|String|"updateName\_cb76233cd7d1"|更新人姓名|
|一级|id|String|"id\_6f9498914064"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_e4b6f7b8742b"|　|

### Request信息示例
{

`  `"outGoodsCodeExport": "outGoodsCodeExport\_bcd1adea17b3",

`  `"warehouseForeman": "warehouseForeman\_0ccfc703c0a2",

`  `"warehouseForemanDate": "2025-07-16 17:56:28",

`  `"exportBillCode": "exportBillCode\_b16385140867",

`  `"outGoodsDate": "2025-07-16",

`  `"reason": "reason\_7141f2100a6b",

`  `"warehouseName": "warehouseName\_d8d0c85a2165",

`  `"warehouseId": "warehouseId\_e42cfa7d79b1",

`  `"operatorName": "operatorName\_f342f686bf55",

`  `"operatorId": "operatorId\_e049b8f83bd1",

`  `"outOperatorName": "outOperatorName\_e4edf5d2074b",

`  `"outOperatorId": "outOperatorId\_6d98ab38e620",

`  `"outGoodsCode": "outGoodsCode\_4f9103254281",

`  `"outGoodsName": "outGoodsName\_a0018739530c",

`  `"money": 0.00,

`  `"taxMoney": 0.00,

`  `"redOutName": "redOutName\_129185b0d2dc",

`  `"redOutDetailList": [

`    `{

`      `"remark": "remark\_751f6635e605",

`      `"goodsId": "goodsId\_ea1b47c48750",

`      `"forWay": "forWay\_babc6baa3c5b",

`      `"forWayId": "forWayId\_fb22f169be6c",

`      `"reason": "reason\_be1188c714b3",

`      `"orderIndex": "orderIndex\_2624527eebf2",

`      `"goodsNum": 0,

`      `"tax": 0.00,

`      `"redOutId": "redOutId\_33ad829f6f1c",

`      `"outGoodsInfoId": "outGoodsInfoId\_221e3f9081db",

`      `"localSend": "localSend\_8a507bef55ef",

`      `"gkDeptName": "gkDeptName\_e51ab4b32418",

`      `"gkDeptId": "gkDeptId\_611229c39d55",

`      `"manageDeptName": "manageDeptName\_7b2091edca69",

`      `"manageDeptId": "manageDeptId\_eb3b1312e2a3",

`      `"batch": "batch\_8b080c5ae005",

`      `"gbName": "gbName\_156ac88e7079",

`      `"gbId": "gbId\_b2e88bf52327",

`      `"bzName": "bzName\_fe9db5edd44a",

`      `"bzId": "bzId\_c4db5ccc3d50",

`      `"deptName": "deptName\_91a0110f4f9b",

`      `"deptId": "deptId\_d0d4f005a284",

`      `"orgName": "orgName\_2bfaa05dc95f",

`      `"orgId": "orgId\_3f439ab2a3e7",

`      `"zbCycle": "zbCycle\_e0f0942e9de8",

`      `"produceDate": "2025-07-16",

`      `"shelfName": "shelfName\_b6a3e3e170a1",

`      `"shelfId": "shelfId\_6d8044cef3ab",

`      `"warehouseName": "warehouseName\_1805395c6e31",

`      `"warehouseId": "warehouseId\_a5e7163032e6",

`      `"taxAllPrice": 0.00,

`      `"taxPrice": 0.00,

`      `"brand": "brand\_a746d6a9446a",

`      `"unitId": "unitId\_5bf777f857a5",

`      `"unitName": "unitName\_843c1005ebf7",

`      `"outStorageNum": 0,

`      `"goodsVersion": "goodsVersion\_ab03730475b4",

`      `"goodsType": "goodsType\_b6fe22d400c5",

`      `"goodsName": "goodsName\_df2e4d35ad43",

`      `"goodsCode": "goodsCode\_f8c59bd947c1",

`      `"outStorageType": "outStorageType\_c4955b12044d",

`      `"createDate": "2025-07-16 17:56:28",

`      `"createUser": "createUser\_71d17b2f7702",

`      `"createName": "createName\_42baa50086fc",

`      `"updateDate": "2025-07-16 17:56:28",

`      `"updateUser": "updateUser\_19cb229d6df1",

`      `"updateName": "updateName\_ed2516548485",

`      `"id": "id\_6015d6dd3a4e",

`      `"status": 0,

`      `"billCode": "billCode\_eb0c08510425"

`    `}

`  `],

`  `"createDate": "2025-07-16 17:56:28",

`  `"createUser": "createUser\_64514c8bb24d",

`  `"createName": "createName\_e68eb5475e9f",

`  `"updateDate": "2025-07-16 17:56:28",

`  `"updateUser": "updateUser\_7aafb9c33036",

`  `"updateName": "updateName\_cb76233cd7d1",

`  `"id": "id\_6f9498914064",

`  `"status": 0,

`  `"billCode": "billCode\_e4b6f7b8742b"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 盘点计划表
### 接口信息
测试地址：

正式地址：

接口提供方：立体仓

请求方式：post	
### Request信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|stockTakeName|String|"stockTakeName\_90cc5c8c2b2f"|盘点计划名称|
|一级|stockTakeCode|String|"stockTakeCode\_5d11a76e5e6e"|盘点计划编码|
|一级|description|String|"description\_8e9d9f2abff8"|描述|
|一级|stockTakeType|String|"stockTakeType\_aa3e14460c33"|盘点类型|
|一级|stockTakeWay|String|"stockTakeWay\_4ab867e36490"|盘点方式|
|一级|stockTakeStartDate|String|"2025-07-17"|盘点开始日期|
|一级|stockTakeEndDate|String|"2025-07-17"|盘点结束日期|
|一级|unitId|String|"unitId\_14a8a21a1f24"|单位 ID|
|一级|unitName|String|"unitName\_a1109768071e"|单位名称|
|一级|operatorId|String|"operatorId\_8abcf1d25b16"|经办人 ID|
|一级|operatorName|String|"operatorName\_92fc478f6d06"|经办人姓名|
|一级|stockTakeManageId|String|"stockTakeManageId\_d0d7b062378b"|负责人 ID|
|一级|stockTakeManageName|String|"stockTakeManageName\_ebcf1a02f2af"|负责人姓名|
|一级|stockTakeWarehouseList|Array|-|盘点仓库列表|
|二级|stockTakeWarehouseList[0].stockTakeId|String|"stockTakeId\_be71606a64f2"|盘点计划 ID|
|二级|stockTakeWarehouseList[0].warehouseId|String|"warehouseId\_6f3c11be0303"|仓库 ID (关联仓库)|
|二级|stockTakeWarehouseList[0].warehouseCode|String|"warehouseCode\_ef07a5877a93"|仓库编码|
|二级|stockTakeWarehouseList[0].warehouseName|String|"warehouseName\_c28896a97e70"|仓库名称|
|二级|stockTakeWarehouseList[0].highGoods|String|"highGoods\_c6905b047401"|只盘点高价值物资|
|二级|stockTakeWarehouseList[0].stockTakeRatio|String|"stockTakeRatio\_54b608de2541"|*比例(抽盘时使用)*|
|二级|stockTakeWarehouseList[0].shelfId|String|"shelfId\_9ecdb64e710b"|货架 ID|
|二级|stockTakeWarehouseList[0].shelfName|String|"shelfName\_746b924ee34c"|货架名称|
|二级|stockTakeWarehouseList[0].createDate|String|"2025-07-17 09:04:01"|创建时间|
|二级|stockTakeWarehouseList[0].createUser|String|"createUser\_92c36bf9ba38"|创建人|
|二级|stockTakeWarehouseList[0].createName|String|"createName\_b9aaf3dc35d0"|创建人姓名|
|二级|stockTakeWarehouseList[0].updateDate|String|"2025-07-17 09:04:01"|更新时间|
|二级|stockTakeWarehouseList[0].updateUser|String|"updateUser\_1df93220a38d"|更新人|
|二级|stockTakeWarehouseList[0].updateName|String|"updateName\_d3b6fcd942ab"|更新人姓名|
|二级|stockTakeWarehouseList[0].id|String|"id\_80c2e631b0a0"|ID|
|二级|stockTakeWarehouseList[0].status|Integer|0|状态|
|二级|stockTakeWarehouseList[0].billCode|String|"billCode\_65f0be97061e"|单据编码|
|一级|stockTakeGoodsList|Array|-|计划盘点物资|
|二级|stockTakeGoodsList[0].inventoryId|String|"inventoryId\_37cbb399de0f"|库存 ID|
|二级|stockTakeGoodsList[0].stockTakeId|String|"stockTakeId\_afba08394369"|盘点计划 ID|
|二级|stockTakeGoodsList[0].goodsId|String|"goodsId\_3ba541ec500c"|物资 ID|
|二级|stockTakeGoodsList[0].goodsCode|String|"goodsCode\_f93cadf9ed3c"|物资编码|
|二级|stockTakeGoodsList[0].goodsName|String|"goodsName\_2dc8e252dc7c"|物资名称|
|二级|stockTakeGoodsList[0].goodsVersion|String|"goodsVersion\_c01e73e28a42"|规格型号|
|二级|stockTakeGoodsList[0].storageNum|Integer|0|库存数量|
|二级|stockTakeGoodsList[0].unitId|String|"unitId\_43e957ca7f2f"|单位 ID|
|二级|stockTakeGoodsList[0].unitName|String|"unitName\_758649c3b12d"|单位名称|
|二级|stockTakeGoodsList[0].brand|String|"brand\_520d210847e2"|品牌|
|二级|stockTakeGoodsList[0].warehouseId|String|"warehouseId\_f8e2584615d3"|仓库 ID|
|二级|stockTakeGoodsList[0].warehouseName|String|"warehouseName\_df3d3237104f"|仓库名称|
|二级|stockTakeGoodsList[0].warehouseCode|String|"warehouseCode\_17a10f1804ff"|仓库编码|
|二级|stockTakeGoodsList[0].shelfId|String|"shelfId\_f281b7c989cd"|货架 ID|
|二级|stockTakeGoodsList[0].shelfName|String|"shelfName\_e0e294f94379"|货架名称|
|二级|stockTakeGoodsList[0].shelfCode|String|"shelfCode\_5446fe9bd57c"|货架编码|
|二级|stockTakeGoodsList[0].stockTakeUserId|String|"stockTakeUserId\_b4dcf14bea6b"|盘点人 ID|
|二级|stockTakeGoodsList[0].stockTakeUserName|String|"stockTakeUserName\_d0733c844ac1"|盘点人姓名|
|二级|stockTakeGoodsList[0].stockTakePlanStartDate|String|"2025-07-17"|盘点开始日期|
|二级|stockTakeGoodsList[0].stockTakePlanEndDate|String|"2025-07-17"|盘点结束日期|
|二级|stockTakeGoodsList[0].createDate|String|"2025-07-17 09:04:01"|创建时间|
|二级|stockTakeGoodsList[0].createUser|String|"createUser\_80034b043c49"|创建人|
|二级|stockTakeGoodsList[0].createName|String|"createName\_70a2d3b40104"|创建人姓名|
|二级|stockTakeGoodsList[0].updateDate|String|"2025-07-17 09:04:01"|更新时间|
|二级|stockTakeGoodsList[0].updateUser|String|"updateUser\_7029390c8040"|更新人|
|二级|stockTakeGoodsList[0].updateName|String|"updateName\_f1789e4b5ad9"|更新人姓名|
|二级|stockTakeGoodsList[0].id|String|"id\_97ac9cf980a1"|ID|
|二级|stockTakeGoodsList[0].status|Integer|0|状态|
|二级|stockTakeGoodsList[0].billCode|String|"billCode\_26a4cc5b4b53"|单据编码|
|一级|stockTakeMangeDeptId|String|"stockTakeMangeDeptId\_38c364e4b925"|责任单位 ID|
|一级|stockTakeMangeDeptName|String|"stockTakeMangeDeptName\_9d1083db4cb0"|责任单位名称|
|一级|createDate|String|"2025-07-17 09:04:01"|创建时间|
|一级|createUser|String|"createUser\_2b626ba2ae79"|创建人|
|一级|createName|String|"createName\_5120f62594b2"|创建人姓名|
|一级|updateDate|String|"2025-07-17 09:04:01"|更新时间|
|一级|updateUser|String|"updateUser\_aea0fa5b7957"|更新人|
|一级|updateName|String|"updateName\_7a4dcea7d7ff"|更新人姓名|
|一级|id|String|"id\_45af294b75c7"|ID|
|一级|status|Integer|0|状态|
|一级|billCode|String|"billCode\_2b7e53ed548e"|单据编码|

### Request信息示例
{

`  `"stockTakeName": "stockTakeName\_90cc5c8c2b2f",

`  `"stockTakeCode": "stockTakeCode\_5d11a76e5e6e",

`  `"description": "description\_8e9d9f2abff8",

`  `"stockTakeType": "stockTakeType\_aa3e14460c33",

`  `"stockTakeWay": "stockTakeWay\_4ab867e36490",

`  `"stockTakeStartDate": "2025-07-17",

`  `"stockTakeEndDate": "2025-07-17",

`  `"unitId": "unitId\_14a8a21a1f24",

`  `"unitName": "unitName\_a1109768071e",

`  `"operatorId": "operatorId\_8abcf1d25b16",

`  `"operatorName": "operatorName\_92fc478f6d06",

`  `"stockTakeManageId": "stockTakeManageId\_d0d7b062378b",

`  `"stockTakeManageName": "stockTakeManageName\_ebcf1a02f2af",

`  `"stockTakeWarehouseList": [

`    `{

`      `"stockTakeId": "stockTakeId\_be71606a64f2",

`      `"warehouseId": "warehouseId\_6f3c11be0303",

`      `"warehouseCode": "warehouseCode\_ef07a5877a93",

`      `"warehouseName": "warehouseName\_c28896a97e70",

`      `"highGoods": "highGoods\_c6905b047401",

`      `"stockTakeRatio": "stockTakeRatio\_54b608de2541",

`      `"shelfId": "shelfId\_9ecdb64e710b",

`      `"shelfName": "shelfName\_746b924ee34c",

`      `"createDate": "2025-07-17 09:04:01",

`      `"createUser": "createUser\_92c36bf9ba38",

`      `"createName": "createName\_b9aaf3dc35d0",

`      `"updateDate": "2025-07-17 09:04:01",

`      `"updateUser": "updateUser\_1df93220a38d",

`      `"updateName": "updateName\_d3b6fcd942ab",

`      `"id": "id\_80c2e631b0a0",

`      `"status": 0,

`      `"billCode": "billCode\_65f0be97061e"

`    `}

`  `],

`  `"stockTakeGoodsList": [

`    `{

`      `"inventoryId": "inventoryId\_37cbb399de0f",

`      `"stockTakeId": "stockTakeId\_afba08394369",

`      `"goodsId": "goodsId\_3ba541ec500c",

`      `"goodsCode": "goodsCode\_f93cadf9ed3c",

`      `"goodsName": "goodsName\_2dc8e252dc7c",

`      `"goodsVersion": "goodsVersion\_c01e73e28a42",

`      `"storageNum": 0,

`      `"unitId": "unitId\_43e957ca7f2f",

`      `"unitName": "unitName\_758649c3b12d",

`      `"brand": "brand\_520d210847e2",

`      `"warehouseId": "warehouseId\_f8e2584615d3",

`      `"warehouseName": "warehouseName\_df3d3237104f",

`      `"warehouseCode": "warehouseCode\_17a10f1804ff",

`      `"shelfId": "shelfId\_f281b7c989cd",

`      `"shelfName": "shelfName\_e0e294f94379",

`      `"shelfCode": "shelfCode\_5446fe9bd57c",

`      `"stockTakeUserId": "stockTakeUserId\_b4dcf14bea6b",

`      `"stockTakeUserName": "stockTakeUserName\_d0733c844ac1",

`      `"stockTakePlanStartDate": "2025-07-17",

`      `"stockTakePlanEndDate": "2025-07-17",

`      `"createDate": "2025-07-17 09:04:01",

`      `"createUser": "createUser\_80034b043c49",

`      `"createName": "createName\_70a2d3b40104",

`      `"updateDate": "2025-07-17 09:04:01",

`      `"updateUser": "updateUser\_7029390c8040",

`      `"updateName": "updateName\_f1789e4b5ad9",

`      `"id": "id\_97ac9cf980a1",

`      `"status": 0,

`      `"billCode": "billCode\_26a4cc5b4b53"

`    `}

`  `],

`  `"stockTakeMangeDeptId": "stockTakeMangeDeptId\_38c364e4b925",

`  `"stockTakeMangeDeptName": "stockTakeMangeDeptName\_9d1083db4cb0",

`  `"createDate": "2025-07-17 09:04:01",

`  `"createUser": "createUser\_2b626ba2ae79",

`  `"createName": "createName\_5120f62594b2",

`  `"updateDate": "2025-07-17 09:04:01",

`  `"updateUser": "updateUser\_aea0fa5b7957",

`  `"updateName": "updateName\_7a4dcea7d7ff",

`  `"id": "id\_45af294b75c7",

`  `"status": 0,

`  `"billCode": "billCode\_2b7e53ed548e"

}
### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 盘点结果
### 注册（需提前联系物资系统管理员申请注册许可证）
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：WebService	
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:regist></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{"systemIdentify":"ZYK"}</paramsJSONStr></p><p>`      `</ser:regist></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统唯一标识|systemIdentify|字符串|默认：ZYK|

**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:registResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{</p><p>"data":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZnA8edAy1OiuYeRk5CSYJIEpljD12MKM5o8JkBOhf+xDEw7YU2AhhRBTXkjyU04msgyOu8WCiXnx8Wr15ZzV7A1BT5ymQOQgzsJUVQCj0FGtp9IqTdHcV2CQPY7d2/YWcYylcMk54COpikUY5w3gzFGlGqIJLOBTT/mboYn3aTwIDAQAB",</p><p>"message":"成功",</p><p>"status":200}</String></p><p>`        `</ns2:registResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### <a name="_toc153360498"></a>获取凭证
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:applyToken></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`  `"interfaceIdentify": "InventoryResultCallback</p><p>",</p><p>`  `"secrit": "第一步返回data的值",</p><p>`  `"systemIdentify": "ZYK",</p><p>`  `"tokenExpired": "凭证过期时间请选填1或2或3 （1：60s 2:300s 3:600s）"</p><p>}</paramsJSONStr></p><p>`      `</ser:applyToken></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|访问接口|interfaceIdentify|字符串|默认：stockTakeResultCallback|
|密钥|secrit|字符串||
|系统标识|systemIdentify|字符串|默认：ZYK|
|凭证过期时间|tokenExpired|字符串||
**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:getCertificateResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{"data":"J3WDMrwrmZdNmiLSZfcmBVHEyUGdVKBkGJsm2fZifYP/U5ihJnhhuF9JB6kyfGrn+M93oF8iz3CuD1lg9sp606OroL/D6PWBGxek2pMehGmjcy79b4LWwwJNNVd2TVYnEi7w2NOUUaG19B5oe9vZ/ODewJE0yehiNnLVkIcELy0=","message":"成功","status":200}</String></p><p>`        `</ns2:getCertificateResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### <a name="_toc153360499"></a>访问接口
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header></p><p>`      `<ser:certificate>第二步返回结果data里的值</ser:certificate></p><p>`   `</soapenv:Header></p><p>`   `<soapenv:Body></p><p>`      `<ser:accessInterface></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`         `"systemIdentify":"ZYK",</p><p>"json":"具体访问接口的json入参,请看备注"</p><p></paramsJSONStr></p><p>`      `</ser:accessInterface></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统标识|systemIdentify|字符串|默认：ZYK|
|访问接口|certificate|字符串|默认：|
|明细id|interfaceIdentify|字符串|stockTakeResultCallback|
|参数JSON|json|浮点|明细|
#### json信息描述

|**层级**|**参数名**||**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|一级|stockTakeId||String|"stockTakeName\_6ce231873ef4"|盘点计划id|
|一级|stockTakeName||String|"stockTakeName\_6ce231873ef4"|盘点计划名称|
|一级|stockTakeCode||String|"stockTakeCode\_b2ace708ea50"|盘点计划单编号|
|一级|stockTakeResultGoodsList||Array|-|盘点结果-物资|
|二级|stockTakeResultGoodsList[0].inventoryId||String|"inventoryId\_629bf927bb38"|库存 ID|
|二级|stockTakeResultGoodsList[0].stockTakeTaskId||String|"stockTakeTaskId\_8ab9d6afeac2"|盘点任务 ID|
|二级|stockTakeResultGoodsList[0].stockTakeTaskName||String|"stockTakeTaskName\_44d04c27fa8e"|盘点任务名称|
|二级|stockTakeResultGoodsList[0].stockTakeTaskCode||String|"stockTakeTaskCode\_2548e42c78d9"|盘点任务编码|
|二级|stockTakeResultGoodsList[0].stockTakeResultId||String|"stockTakeResultId\_80f6b27adb7b"|盘点结果 ID|
|二级|stockTakeResultGoodsList[0].goodsId||String|"goodsId\_37244d06d88c"|物资 ID|
|二级|stockTakeResultGoodsList[0].goodsCode||String|"goodsCode\_ad4ac94c9329"|物资编码|
|二级|stockTakeResultGoodsList[0].goodsName||String|"goodsName\_231acc1b6fa2"|物资名称|
|二级|stockTakeResultGoodsList[0].goodsVersion||String|"goodsVersion\_3ebde6609f39"|规格型号|
|二级|stockTakeResultGoodsList[0].storageNum||Integer|0|库存数量|
|二级|stockTakeResultGoodsList[0].lockNum||Integer|0|锁定数量|
|二级|stockTakeResultGoodsList[0].stockTakeNum||Integer|0|盘点数量|
|二级|stockTakeResultGoodsList[0].stockTakeOverNum||Integer|0|盘盈数量|
|二级|stockTakeResultGoodsList[0].stockTakeFloorNum||Integer|0|盘亏数量|
|二级|stockTakeResultGoodsList[0].unitId||String|"unitId\_2eb652f773b3"|单位 ID|
|二级|stockTakeResultGoodsList[0].unitName||String|"unitName\_e09e2050b928"|单位名称|
|二级|stockTakeResultGoodsList[0].brand||String|"brand\_3a30c2dbb7cb"|品牌|
|二级|stockTakeResultGoodsList[0].warehouseId||String|"warehouseId\_b12b2a1baf12"|仓库 ID|
|二级|stockTakeResultGoodsList[0].warehouseName||String|"warehouseName\_2f9b6c2db497"|仓库名称|
|二级|stockTakeResultGoodsList[0].warehouseCode||String|"warehouseCode\_9aedaf3073a1"|仓库编码|
|二级|stockTakeResultGoodsList[0].shelfId||String|"shelfId\_2463f5508538"|货架 ID|
|二级|stockTakeResultGoodsList[0].shelfName||String|"shelfName\_04565bed9ea3"|货架名称|
|二级|stockTakeResultGoodsList[0].shelfCode||String|"shelfCode\_c200e57f7ebf"|货架编码|
|二级|stockTakeResultGoodsList[0].stockTakeUserId||String|"stockTakeUserId\_e1fbdba750ba"|盘点人 ID|
|二级|stockTakeResultGoodsList[0].stockTakeUserName||String|"stockTakeUserName\_2220a5f95245"|盘点人姓名|
|二级|stockTakeResultGoodsList[0].stockTakePlanStartDate||String|"2025-07-17"|盘点开始日期|
|二级|stockTakeResultGoodsList[0].stockTakePlanEndDate||String|"2025-07-17"|盘点结束日期|
|二级|stockTakeResultGoodsList[0].createDate||String|"2025-07-17 09:17:01"|创建时间|
|二级|stockTakeResultGoodsList[0].createUser||String|"createUser\_c1d1fd289d32"|创建人|
|二级|stockTakeResultGoodsList[0].createName||String|"createName\_8232eb9f1ab9"|创建人姓名|
|二级|stockTakeResultGoodsList[0].updateDate||String|"2025-07-17 09:17:01"|更新时间|
|二级|stockTakeResultGoodsList[0].updateUser||String|"updateUser\_f8fc17d0c003"|更新人|
|二级|stockTakeResultGoodsList[0].updateName||String|"updateName\_6b6cad3cdd36"|更新人姓名|
|二级|stockTakeResultGoodsList[0].id||String|"id\_9eb68d15f8ed"|ID|
|二级|stockTakeResultGoodsList[0].status||Integer|0|状态|
|二级|stockTakeResultGoodsList[0].billCode||String|"billCode\_9d0be9ea1a52"|单据编码|

#### json信息示例
{

" stockTakeId ": "stockTakeId\_6ce231873ef4",

`  `"stockTakeName": "stockTakeName\_6ce231873ef4",

`  `"stockTakeCode": "stockTakeCode\_b2ace708ea50",

`  `"stockTakeResultGoodsList": [

`    `{

`      `"inventoryId": "inventoryId\_629bf927bb38",

`      `"stockTakeTaskId": "stockTakeTaskId\_8ab9d6afeac2",

`      `"stockTakeTaskName": "stockTakeTaskName\_44d04c27fa8e",

`      `"stockTakeTaskCode": "stockTakeTaskCode\_2548e42c78d9",

`      `"stockTakeResultId": "stockTakeResultId\_80f6b27adb7b",

`      `"goodsId": "goodsId\_37244d06d88c",

`      `"goodsCode": "goodsCode\_ad4ac94c9329",

`      `"goodsName": "goodsName\_231acc1b6fa2",

`      `"goodsVersion": "goodsVersion\_3ebde6609f39",

`      `"storageNum": 0,

`      `"lockNum": 0,

`      `"stockTakeNum": 0,

`      `"stockTakeOverNum": 0,

`      `"stockTakeFloorNum": 0,

`      `"unitId": "unitId\_2eb652f773b3",

`      `"unitName": "unitName\_e09e2050b928",

`      `"brand": "brand\_3a30c2dbb7cb",

`      `"warehouseId": "warehouseId\_b12b2a1baf12",

`      `"warehouseName": "warehouseName\_2f9b6c2db497",

`      `"warehouseCode": "warehouseCode\_9aedaf3073a1",

`      `"shelfId": "shelfId\_2463f5508538",

`      `"shelfName": "shelfName\_04565bed9ea3",

`      `"shelfCode": "shelfCode\_c200e57f7ebf",

`      `"stockTakeUserId": "stockTakeUserId\_e1fbdba750ba",

`      `"stockTakeUserName": "stockTakeUserName\_2220a5f95245",

`      `"stockTakePlanStartDate": "2025-07-17",

`      `"stockTakePlanEndDate": "2025-07-17",

`      `"createDate": "2025-07-17 09:17:01",

`      `"createUser": "createUser\_c1d1fd289d32",

`      `"createName": "createName\_8232eb9f1ab9",

`      `"updateDate": "2025-07-17 09:17:01",

`      `"updateUser": "updateUser\_f8fc17d0c003",

`      `"updateName": "updateName\_6b6cad3cdd36",

`      `"id": "id\_9eb68d15f8ed",

`      `"status": 0,

`      `"billCode": "billCode\_9d0be9ea1a52"

`    `}

`  `]

}
#### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

#### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 入库单完成上报
### 注册（需提前联系物资系统管理员申请注册许可证）
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：WebService	
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:regist></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{"systemIdentify":"ZYK"}</paramsJSONStr></p><p>`      `</ser:regist></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统唯一标识|systemIdentify|字符串|默认：ZYK|

**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:registResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{</p><p>"data":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZnA8edAy1OiuYeRk5CSYJIEpljD12MKM5o8JkBOhf+xDEw7YU2AhhRBTXkjyU04msgyOu8WCiXnx8Wr15ZzV7A1BT5ymQOQgzsJUVQCj0FGtp9IqTdHcV2CQPY7d2/YWcYylcMk54COpikUY5w3gzFGlGqIJLOBTT/mboYn3aTwIDAQAB",</p><p>"message":"成功",</p><p>"status":200}</String></p><p>`        `</ns2:registResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 获取凭证
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:applyToken></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`  `"interfaceIdentify": "InventoryResultCallback</p><p>",</p><p>`  `"secrit": "第一步返回data的值",</p><p>`  `"systemIdentify": "ZYK",</p><p>`  `"tokenExpired": "凭证过期时间请选填1或2或3 （1：60s 2:300s 3:600s）"</p><p>}</paramsJSONStr></p><p>`      `</ser:applyToken></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|访问接口|interfaceIdentify|字符串|storageCallback|
|密钥|secrit|字符串||
|系统标识|systemIdentify|字符串|默认：ZYK|
|凭证过期时间|tokenExpired|字符串||
**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:getCertificateResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{"data":"J3WDMrwrmZdNmiLSZfcmBVHEyUGdVKBkGJsm2fZifYP/U5ihJnhhuF9JB6kyfGrn+M93oF8iz3CuD1lg9sp606OroL/D6PWBGxek2pMehGmjcy79b4LWwwJNNVd2TVYnEi7w2NOUUaG19B5oe9vZ/ODewJE0yehiNnLVkIcELy0=","message":"成功","status":200}</String></p><p>`        `</ns2:getCertificateResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 访问接口
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header></p><p>`      `<ser:certificate>第二步返回结果data里的值</ser:certificate></p><p>`   `</soapenv:Header></p><p>`   `<soapenv:Body></p><p>`      `<ser:accessInterface></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`         `"systemIdentify":"ZYK",</p><p>"json":"具体访问接口的json入参,请看备注"</p><p></paramsJSONStr></p><p>`      `</ser:accessInterface></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统标识|systemIdentify|字符串|默认：ZYK|
|访问接口|certificate|字符串|默认：|
|明细id|interfaceIdentify|字符串|storageCallback|
|参数JSON|json|浮点|明细|
#### json信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|storageNum|Integer|0|本次入库数量|
|一级|warehouseCode|String|"warehouseId\_4dea61dc9a13"|仓库 code|
|一级|shelfCode|String|"shelfId\_6d70cab76df9"|货架 code|
|一级|oId|String|"id\_4596845e99aa"|入库明细原id ID|
|一级|lId|String|"id\_4596845e99aa"|立体仓系统：入库明细id|
|一级|storageType|Integer|入库类型|28:入库单，73:出库红冲单，75:归还单|

#### json信息示例
[

`    `{ 

`      `"storageNum": 0, 

`      `"warehouseCode": "warehouseCode\_9aedaf3073a1",

`      `"shelfCode": "shelfCode\_c200e57f7ebf",

`      `" oId": "id\_9eb68d15f8ed",

" lId": "id\_9eb68d15f8ed",

" storageType": 28

},

`    `{ 

`      `"storageNum": 0, 

`      `"warehouseCode": "warehouseCode\_9aedaf3073a1",

`      `"shelfCode": "shelfCode\_c200e57f7ebf",

`      `" oId": "id\_9eb68d15f8ed",

" lId": "id\_9eb68d15f8ed",

" storageType": 28

}

]
#### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

#### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}

## 出库单完成上报
### 注册（需提前联系物资系统管理员申请注册许可证）
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：WebService	
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:regist></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{"systemIdentify":"ZYK"}</paramsJSONStr></p><p>`      `</ser:regist></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统唯一标识|systemIdentify|字符串|默认：ZYK|

**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:registResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{</p><p>"data":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZnA8edAy1OiuYeRk5CSYJIEpljD12MKM5o8JkBOhf+xDEw7YU2AhhRBTXkjyU04msgyOu8WCiXnx8Wr15ZzV7A1BT5ymQOQgzsJUVQCj0FGtp9IqTdHcV2CQPY7d2/YWcYylcMk54COpikUY5w3gzFGlGqIJLOBTT/mboYn3aTwIDAQAB",</p><p>"message":"成功",</p><p>"status":200}</String></p><p>`        `</ns2:registResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 获取凭证
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:applyToken></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`  `"interfaceIdentify": "InventoryResultCallback</p><p>",</p><p>`  `"secrit": "第一步返回data的值",</p><p>`  `"systemIdentify": "ZYK",</p><p>`  `"tokenExpired": "凭证过期时间请选填1或2或3 （1：60s 2:300s 3:600s）"</p><p>}</paramsJSONStr></p><p>`      `</ser:applyToken></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|访问接口|interfaceIdentify|字符串|outGoodsCallback|
|密钥|secrit|字符串||
|系统标识|systemIdentify|字符串|默认：ZYK|
|凭证过期时间|tokenExpired|字符串||
**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:getCertificateResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{"data":"J3WDMrwrmZdNmiLSZfcmBVHEyUGdVKBkGJsm2fZifYP/U5ihJnhhuF9JB6kyfGrn+M93oF8iz3CuD1lg9sp606OroL/D6PWBGxek2pMehGmjcy79b4LWwwJNNVd2TVYnEi7w2NOUUaG19B5oe9vZ/ODewJE0yehiNnLVkIcELy0=","message":"成功","status":200}</String></p><p>`        `</ns2:getCertificateResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 访问接口
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header></p><p>`      `<ser:certificate>第二步返回结果data里的值</ser:certificate></p><p>`   `</soapenv:Header></p><p>`   `<soapenv:Body></p><p>`      `<ser:accessInterface></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`         `"systemIdentify":"ZYK",</p><p>"json":"具体访问接口的json入参,请看备注"</p><p></paramsJSONStr></p><p>`      `</ser:accessInterface></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统标识|systemIdentify|字符串|默认：ZYK|
|访问接口|certificate|字符串|默认：|
|明细id|interfaceIdentify|字符串|outGoodsCallback|
|参数JSON|json|浮点|明细|
#### json信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|二级|goodsNum|Integer|0|数量|
|二级|warehouseCode|String|"shelfName\_67b1d3d4e252"|仓库编码|
|二级|shelfCode|String|"shelfId\_e9902afe9338"|货架编码|
|二级|oId|String|"warehouseName\_cffe40af0f55"|出库明细原id ID|
|二级|lId|String|"warehouseId\_dcfccac56417"|立体仓系统：出库明细id|
|二级|outboundType|Integer|63|出库类型：63:出库单，72:入库红冲单，74:借用|

#### json信息示例
[

`    `{ 

`      `" goodsNum": 0, 

`      `"warehouseCode": "warehouseCode\_9aedaf3073a1",

`      `"shelfCode": "shelfCode\_c200e57f7ebf",

`      `" oId": "id\_9eb68d15f8ed",

" lId": "id\_9eb68d15f8ed",

" outboundType": 63,

},

{ 

`      `" goodsNum": 0, 

`      `"warehouseCode": "warehouseCode\_9aedaf3073a1",

`      `"shelfCode": "shelfCode\_c200e57f7ebf",

`      `" oId": "id\_9eb68d15f8ed",

" lId": "id\_9eb68d15f8ed",

" outboundType": 72,

},

]
#### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

#### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}
## 移库
### 注册（需提前联系物资系统管理员申请注册许可证）
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：WebService	
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:regist></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{"systemIdentify":"ZYK"}</paramsJSONStr></p><p>`      `</ser:regist></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统唯一标识|systemIdentify|字符串|默认：ZYK|

**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:registResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{</p><p>"data":"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZnA8edAy1OiuYeRk5CSYJIEpljD12MKM5o8JkBOhf+xDEw7YU2AhhRBTXkjyU04msgyOu8WCiXnx8Wr15ZzV7A1BT5ymQOQgzsJUVQCj0FGtp9IqTdHcV2CQPY7d2/YWcYylcMk54COpikUY5w3gzFGlGqIJLOBTT/mboYn3aTwIDAQAB",</p><p>"message":"成功",</p><p>"status":200}</String></p><p>`        `</ns2:registResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 获取凭证
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header/></p><p>`   `<soapenv:Body></p><p>`      `<ser:applyToken></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`  `"interfaceIdentify": "InventoryResultCallback</p><p>",</p><p>`  `"secrit": "第一步返回data的值",</p><p>`  `"systemIdentify": "ZYK",</p><p>`  `"tokenExpired": "凭证过期时间请选填1或2或3 （1：60s 2:300s 3:600s）"</p><p>}</paramsJSONStr></p><p>`      `</ser:applyToken></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|访问接口|interfaceIdentify|字符串|warehouseRemove|
|密钥|secrit|字符串||
|系统标识|systemIdentify|字符串|默认：ZYK|
|凭证过期时间|tokenExpired|字符串||
**Response结构**

|**Element**|
| - |
|<p><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"></p><p>`    `<soap:Body></p><p>`        `<ns2:getCertificateResponse xmlns:ns2="http://service.ws.admin.wuxicloud.com/"></p><p>`            `<String>{"data":"J3WDMrwrmZdNmiLSZfcmBVHEyUGdVKBkGJsm2fZifYP/U5ihJnhhuF9JB6kyfGrn+M93oF8iz3CuD1lg9sp606OroL/D6PWBGxek2pMehGmjcy79b4LWwwJNNVd2TVYnEi7w2NOUUaG19B5oe9vZ/ODewJE0yehiNnLVkIcELy0=","message":"成功","status":200}</String></p><p>`        `</ns2:getCertificateResponse></p><p>`    `</soap:Body></p><p></soap:Envelope></p>|

#### Response信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|状态|Status|整型|200:成功|
|错误信息|message|字符串||
|返回实体|Data|字符串||

### 访问接口
测试地址：http:// 10.92.234.42:9008/api/ws/AuthService?wsdl

正式地址：http:// 10.92.233.34:9008/api/ws/AuthService?wsdl

服务名称：物资系统

请求方式：post
#### Request结构

|**Element**|
| - |
|<p><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.ws.admin.wuxicloud.com/"></p><p>`   `<soapenv:Header></p><p>`      `<ser:certificate>第二步返回结果data里的值</ser:certificate></p><p>`   `</soapenv:Header></p><p>`   `<soapenv:Body></p><p>`      `<ser:accessInterface></p><p>`         `<!--Optional:--></p><p>`         `<paramsJSONStr>{</p><p>`         `"systemIdentify":"ZYK",</p><p>"json":"具体访问接口的json入参,请看备注"</p><p></paramsJSONStr></p><p>`      `</ser:accessInterface></p><p>`   `</soapenv:Body></p><p></soapenv:Envelope></p>|

#### Request信息描述

|**属性名称**|**代码**|**类型**|**备注**|
| :-: | :-: | :-: | :-: |
|系统标识|systemIdentify|字符串|默认：ZYK|
|访问接口|certificate|字符串|默认：|
|明细id|interfaceIdentify|字符串|warehouseRemove|
|参数JSON|json|浮点|明细|
#### json信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|targetShelfCode|String|0|目标货架位编号|
|一级|targetShelfName|String|"shelfName\_67b1d3d4e252"|目标货架位名称|
|一级|targetShelfId|String|"shelfId\_e9902afe9338"|目标货位id|
|一级|targetWarehouseCode|String|"warehouseName\_cffe40af0f55"|目标库房编号|
|一级|targetWarehouseName|String|"warehouseId\_dcfccac56417"|目标库房名称|
|一级|targetWarehouseId|String|63|目标库房|
|一级|removeNum|Integer||移库数量|
|一级|shelfName|String||原货架位名称|
|一级|shelfCode|String||原货架位编号|
|一级|shelfId|String||原货位id|
|一级|warehouseCode|String||原库房编号|
|一级|warehouseName|String||原库房名称|
|一级|warehouseId|String||原库房|
|一级|goodsCode|String||物资编码|

#### json信息示例
[

`   `{ 

`  `"targetShelfCode": "string", // 目标货架位编号

`  `"targetShelfName": "string", // 目标货架位名称

`  `"targetShelfId": "string", // 目标货位id

`  `"targetWarehouseCode": "string", // 目标库房编号

`  `"targetWarehouseName": "string", // 目标库房名称

`  `"targetWarehouseId": "string", // 目标库房

`  `"removeNum ": "integer", // 移库数量

`  `"shelfName": "string", // 原货架位名称

`  `"shelfCode": "string", // 原货架位编号

`  `"shelfId": "string", // 原货位id

`  `"warehouseCode": "string", // 原库房编号

`  `"warehouseName": "string", // 原库房名称

`  `"warehouseId": "string", // 原库房

`  `"goodsCode": "string" // 物资编码

}

]
#### Response信息描述

|**层级**|**参数名**|**数据类型**|**示例值**|**说明**|
| :-: | :-: | :-: | :-: | :-: |
|一级|code|Integer|0|响应状态码：200，成功；1，失败；2，入参错误|
|一级|msg|String|"msg\_8d651c4f2a2d"|响应消息|
|一级|traceId|String|"traceId\_2141b8d613d0"|跟踪 ID（用于排查问题）|

#### Response信息示例
{

`  `"code": 0,

`  `"msg": "msg\_60299ecfb046",

`  `"traceId": "traceId\_9d61457be7b5"

}
##

