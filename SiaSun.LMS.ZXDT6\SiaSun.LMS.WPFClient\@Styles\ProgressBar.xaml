<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type ProgressBar}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ProgressBar}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="IndeterminateOn" RepeatBehavior="Forever">
                            <DoubleAnimationUsingKeyFrames
                                RepeatBehavior="Forever"
                                Storyboard.TargetName="IndeterminateGradientFill"
                                Storyboard.TargetProperty="(Shape.Fill).(Brush.Transform).(TransformGroup.Children)[0].X">
                                <SplineDoubleKeyFrame KeyTime="0" Value="0" />
                                <SplineDoubleKeyFrame KeyTime="00:00:.5" Value="20" />
                            </DoubleAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="IndeterminateRoot"
                                Storyboard.TargetProperty="(UIElement.Visibility)"
                                Duration="00:00:00.0010000">
                                <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Visible}" />
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border
                            x:Name="PART_Track"
                            BorderThickness="1"
                            CornerRadius="3"
                            Opacity="0.825">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                    <GradientStop Color="#FFFFFFFF" />
                                    <GradientStop Offset="0.327" Color="#FFD8D8D8" />
                                    <GradientStop Offset="0.488" Color="#FFDADADA" />
                                    <GradientStop Offset="0.539" Color="#FFBEBEBE" />
                                    <GradientStop Offset="0.77" Color="#FFD6D6D6" />
                                    <GradientStop Offset="1" Color="#FFFFFFFF" />
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                    <GradientStop Offset="0" Color="#FFBBBBBB" />
                                    <GradientStop Offset="1" Color="#FF7E7E7E" />
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                        </Border>

                        <Rectangle
                            x:Name="PART_Indicator"
                            Margin="1"
                            HorizontalAlignment="Left"
                            Fill="{DynamicResource ProgressBarIndicatorBrush}"
                            Opacity="0.83"
                            RadiusX="1.5"
                            RadiusY="1.5" />
                        <Grid x:Name="IndeterminateRoot" Visibility="Collapsed">
                            <Rectangle
                                x:Name="IndeterminateSolidFill"
                                Width="Auto"
                                Margin="1"
                                Fill="#FF6EA4FD"
                                RadiusX="2"
                                RadiusY="2" />
                            <Rectangle
                                x:Name="ProgressBarRootGradient"
                                Margin="1"
                                Panel.ZIndex="1"
                                RadiusX="1.5"
                                RadiusY="1.5">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                        <GradientStop Offset="0.046" Color="#F6BCD5FF" />
                                        <GradientStop Offset="0.18" Color="#96D4E4FF" />
                                        <GradientStop Offset="0.512" Color="#4FFFFFFF" />
                                        <GradientStop Offset="0.521" Color="#00D6D6D6" />
                                        <GradientStop Offset="1" Color="#BABCD5FF" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <Rectangle
                                x:Name="IndeterminateGradientFill"
                                Margin="1"
                                Opacity="0.7"
                                RadiusX="2"
                                RadiusY="2"
                                StrokeThickness="1">
                                <Rectangle.Fill>
                                    <LinearGradientBrush MappingMode="Absolute" SpreadMethod="Repeat" StartPoint="20,1" EndPoint="0,1">
                                        <LinearGradientBrush.Transform>
                                            <TransformGroup>
                                                <TranslateTransform X="0" />
                                                <SkewTransform AngleX="-10" />
                                            </TransformGroup>
                                        </LinearGradientBrush.Transform>
                                        <GradientStop Offset="0.088" Color="#FFBCD5FF" />
                                        <GradientStop Offset="0.475" Color="#006EA4FD" />
                                        <GradientStop Offset="0.899" Color="#FFBCD5FF" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                        </Grid>
                        <Border
                            x:Name="DisabledVisualElement"
                            Background="#A5FFFFFF"
                            BorderBrush="#66FFFFFF"
                            BorderThickness="1"
                            IsHitTestVisible="false"
                            Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="DisabledVisualElement" Property="Opacity" Value="1" />
                        </Trigger>
                        <Trigger Property="IsIndeterminate" Value="True">
                            <Trigger.ExitActions>
                                <StopStoryboard BeginStoryboardName="IndeterminateOn_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="IndeterminateOn_BeginStoryboard" Storyboard="{StaticResource IndeterminateOn}" />
                            </Trigger.EnterActions>
                            <Setter TargetName="PART_Track" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="PART_Indicator" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>