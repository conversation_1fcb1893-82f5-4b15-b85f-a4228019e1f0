﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="APPLY_TYPE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="APPLY_TYPE" type="SiaSun.LMS.Model.APPLY_TYPE, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="APPLY_TYPE">
      <result property="APPLY_ID" column="apply_id" />
      <result property="APPLY_TYPE_CODE" column="apply_type_code" />
      <result property="APPLY_TYPE_NAME" column="apply_type_name" />
      <result property="APPLY_TYPE_CLASS" column="apply_type_class" />
      <result property="APPLY_TYPE_ORDER" column="apply_type_order" />
      <result property="APPLY_TYPE_FLAG" column="apply_type_flag" />
      <result property="APPLY_TYPE_PROPERTY" column="apply_type_property" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="APPLY_TYPE_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      apply_id,
      apply_type_code,
      apply_type_name,
      apply_type_class,
      apply_type_order,
      apply_type_flag,
      apply_type_property
      From APPLY_TYPE
    </select>

    <select id="APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE" parameterClass="String" extends = "APPLY_TYPE_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          apply_type_code=#APPLY_TYPE_CODE# and APPLY_TYPE_FLAG=1
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="APPLY_TYPE_SELECT_BY_DEVICE_CODE" parameterClass="String" extends = "APPLY_TYPE_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          apply_type_device like '%'||#DEVICE_CODE#||'%' and APPLY_TYPE_FLAG=1
        </isParameterPresent>
      </dynamic>
    </select>


    <insert id="APPLY_TYPE_INSERT" parameterClass="APPLY_TYPE">
      Insert Into APPLY_TYPE (
      apply_id,
      apply_type_code,
      apply_type_name,
      apply_type_class,
      apply_type_order,
      apply_type_flag,
      apply_type_property
      )Values(
      #APPLY_ID#,
      #APPLY_TYPE_CODE#,
      #APPLY_TYPE_NAME#,
      #APPLY_TYPE_CLASS#,
      #APPLY_TYPE_ORDER#,
      #APPLY_TYPE_FLAG#,
      #APPLY_TYPE_PROPERTY#
      )
      <!--<selectKey  resultClass="int" type="post" property="APPLY_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="APPLY_TYPE_UPDATE" parameterClass="APPLY_TYPE">
      Update APPLY_TYPE Set
      <!--apply_id=#APPLY_ID#,-->
      apply_type_code=#APPLY_TYPE_CODE#,
      apply_type_name=#APPLY_TYPE_NAME#,
      apply_type_class=#APPLY_TYPE_CLASS#,
      apply_type_order=#APPLY_TYPE_ORDER#,
      apply_type_flag=#APPLY_TYPE_FLAG#,
      apply_type_property=#APPLY_TYPE_PROPERTY#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          apply_id=#APPLY_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="APPLY_TYPE_DELETE" parameterClass="int">
      Delete From APPLY_TYPE
      <dynamic prepend="WHERE">
        <isParameterPresent>
          apply_id=#APPLY_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>