using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 入库单完成上报回调接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(applyToken) -> 携带凭证调用 accessInterface(InventoryResultCallback)
    /// </summary>
    public class InboundReceiptCompleteCallback : InterfaceBase
    {
        // 文档中的通用响应结构
        class AuthResponse
        {
            public int status { get; set; }
            public string message { get; set; }
            public string data { get; set; }
        }

        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        /// <summary>
        /// 入库单完成上报明细项
        /// </summary>
        public class InboundReceiptCompleteItem
        {
            /// <summary>
            /// 本次入库数量
            /// </summary>
            public int storageNum { get; set; }

            /// <summary>
            /// 仓库编码
            /// </summary>
            public string warehouseCode { get; set; }

            /// <summary>
            /// 货架编码
            /// </summary>
            public string shelfCode { get; set; }

            /// <summary>
            /// 入库明细原ID
            /// </summary>
            public string oId { get; set; }

            /// <summary>
            /// 立体仓系统：入库明细ID
            /// </summary>
            public string lId { get; set; }

            /// <summary>
            /// 入库类型：28:入库单，73:出库红冲单，75:归还单
            /// </summary>
            public int storageType { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统入库单完成上报回调。
        /// </summary>
        /// <param name="inboundReceiptCompleteItems">入库单完成明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(List<InboundReceiptCompleteItem> inboundReceiptCompleteItems, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (inboundReceiptCompleteItems == null || inboundReceiptCompleteItems.Count == 0)
                {
                    result = false;
                    message = "参数错误：inboundReceiptCompleteItems 不能为空";
                    return result;
                }

                // 验证每个明细项的必要字段
                foreach (var item in inboundReceiptCompleteItems)
                {
                    if (string.IsNullOrEmpty(item.warehouseCode) || string.IsNullOrEmpty(item.shelfCode) ||
                        string.IsNullOrEmpty(item.oId) || string.IsNullOrEmpty(item.lId))
                    {
                        result = false;
                        message = "参数错误：明细项中的warehouseCode、shelfCode、oId、lId不能为空";
                        return result;
                    }

                    if (item.storageType != 28 && item.storageType != 73 && item.storageType != 75)
                    {
                        result = false;
                        message = "参数错误：storageType必须为28(入库单)、73(出库红冲单)或75(归还单)";
                        return result;
                    }
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }

                var registResp = TryParse<AuthResponse>(registOut, out string parseErr1);
                if (registResp == null || registResp.status != 200 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.message)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "InventoryResultCallback",
                    secrit = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("applyToken", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用applyToken失败：{applyTokenOut}";
                    return result;
                }

                var applyResp = TryParse<AuthResponse>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.status != 200 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"applyToken返回无效：{(applyResp == null ? parseErr2 : applyResp.message)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(InventoryResultCallback)
                string payloadJson = Common.JsonHelper.Serializer(inboundReceiptCompleteItems);

                // 按文档要求，accessInterface 的 paramsJSONStr 结构
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    json = payloadJson
                });

                // 由于accessInterface需要在SOAP Header中携带certificate，
                // 这里直接构造SOAP并发送
                if (!PostAccessInterfaceWithCertificate("accessInterface", certificate, paramsJSONStr, out string accessRespJson, out string httpErr))
                {
                    result = false;
                    message = $"调用accessInterface失败：{httpErr}";
                    return result;
                }

                // 按文档，该接口通常返回形如 {"code":0,"msg":"...","traceId":"..."}
                var accessResp = TryParse<OutputParam>(accessRespJson, out string parseErr3);
                if (accessResp == null || (accessResp.code != 0 && accessResp.code != 1 && accessResp.code != 2))
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                if (accessResp.code != 0)
                {
                    result = false;
                    message = $"入库单完成上报失败：{accessResp.msg}";
                    return result;
                }

                message = "入库单完成上报成功";
                S_Base.sBase.Log.Info($"InboundReceiptCompleteCallback成功_明细数[{inboundReceiptCompleteItems.Count}]_traceId[{accessResp.traceId}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"InboundReceiptCompleteCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 重载方法：支持单个入库单明细上报
        /// </summary>
        /// <param name="storageNum">本次入库数量</param>
        /// <param name="warehouseCode">仓库编码</param>
        /// <param name="shelfCode">货架编码</param>
        /// <param name="oId">入库明细原ID</param>
        /// <param name="lId">立体仓系统入库明细ID</param>
        /// <param name="storageType">入库类型</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(int storageNum, string warehouseCode, string shelfCode, 
            string oId, string lId, int storageType, out string message)
        {
            var items = new List<InboundReceiptCompleteItem>
            {
                new InboundReceiptCompleteItem
                {
                    storageNum = storageNum,
                    warehouseCode = warehouseCode,
                    shelfCode = shelfCode,
                    oId = oId,
                    lId = lId,
                    storageType = storageType
                }
            };

            return IntefaceMethod(items, out message);
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }

        private bool PostAccessInterfaceWithCertificate(string methodName, string certificate, string paramsJSONStr, out string responseJson, out string error)
        {
            responseJson = string.Empty;
            error = null;
            try
            {
                // 使用InterfaceBase中配置的ExternalServiceUrl
                string serviceUrl = GetExternalServiceUrl();
                if (string.IsNullOrEmpty(serviceUrl))
                {
                    error = "未配置ExternalServiceUrl";
                    return false;
                }

                // 构建SOAP Envelope，根据文档示例
                var sb = new StringBuilder();
                sb.Append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ws.admin.wuxicloud.com/\">");
                sb.Append("<soapenv:Header>");
                sb.Append("<ser:certificate>").Append(System.Security.SecurityElement.Escape(certificate)).Append("</ser:certificate>");
                sb.Append("</soapenv:Header>");
                sb.Append("<soapenv:Body>");
                sb.Append("<ser:").Append(methodName).Append(">");
                sb.Append("<paramsJSONStr>").Append(System.Security.SecurityElement.Escape(paramsJSONStr)).Append("</paramsJSONStr>");
                sb.Append("</ser:").Append(methodName).Append(">");
                sb.Append("</soapenv:Body>");
                sb.Append("</soapenv:Envelope>");

                var req = (HttpWebRequest)WebRequest.Create(serviceUrl);
                req.Method = "POST";
                req.ContentType = "text/xml; charset=utf-8";
                req.Headers.Add("SOAPAction", "\"http://service.ws.admin.wuxicloud.com/" + methodName + "\"");

                byte[] data = Encoding.UTF8.GetBytes(sb.ToString());
                using (var stream = req.GetRequestStream())
                {
                    stream.Write(data, 0, data.Length);
                }

                using (var resp = (HttpWebResponse)req.GetResponse())
                using (var reader = new System.IO.StreamReader(resp.GetResponseStream(), Encoding.UTF8))
                {
                    string respText = reader.ReadToEnd();
                    // 提取Body中的<String>JSON</String>
                    var xml = new XmlDocument();
                    xml.LoadXml(respText);
                    var node = xml.SelectSingleNode("//String");
                    if (node != null)
                    {
                        responseJson = node.InnerText;
                    }
                    else
                    {
                        // 兜底：直接返回原文
                        responseJson = respText;
                    }
                }

                return true;
            }
            catch (WebException wex)
            {
                error = wex.Message;
                try
                {
                    using (var resp = (HttpWebResponse)wex.Response)
                    using (var reader = new System.IO.StreamReader(resp.GetResponseStream()))
                    {
                        error += " | " + reader.ReadToEnd();
                    }
                }
                catch { }
                return false;
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return false;
            }
        }

        private string GetExternalServiceUrl()
        {
            // InterfaceBase中externalServiceUrl是private静态，这里从配置读取同名键
            return SiaSun.LMS.Common.StringUtil.GetConfig("ExternalServiceUrl");
        }
    }
}
