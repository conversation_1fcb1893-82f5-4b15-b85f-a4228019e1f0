﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="WH_LOGIC" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="WH_LOGIC" type="SiaSun.LMS.Model.WH_LOGIC, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="WH_LOGIC">
			<result property="LOGIC_ID" column="logic_id" />
			<result property="WAREHOUSE_ID" column="warehouse_id" />
			<result property="LOGIC_TYPE" column="logic_type" />
			<result property="LOGIC_CODE" column="logic_code" />
			<result property="LOGIC_NAME" column="logic_name" />
			<result property="LOGIC_ORDER" column="logic_order" />
			<result property="LOGIC_FLAG" column="logic_flag" />
			<result property="LOGIC_REMARK" column="logic_remark" />
			<result property="LOGIC_GROUP" column="logic_group" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="WH_LOGIC_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  logic_id,
				  warehouse_id,
				  logic_type,
				  logic_code,
				  logic_name,
				  logic_order,
				  logic_flag,
				  logic_remark,
				  logic_group
			From WH_LOGIC
		</select>
		
		<select id="WH_LOGIC_SELECT_BY_ID" parameterClass="int" extends = "WH_LOGIC_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					logic_id=#LOGIC_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="WH_LOGIC_INSERT" parameterClass="WH_LOGIC">
      Insert Into WH_LOGIC (
      logic_id,
      warehouse_id,
      logic_type,
      logic_code,
      logic_name,
      logic_order,
      logic_flag,
      logic_remark,
      logic_group
      )Values(
      #LOGIC_ID#,
      #WAREHOUSE_ID#,
      #LOGIC_TYPE#,
      #LOGIC_CODE#,
      #LOGIC_NAME#,
      #LOGIC_ORDER#,
      #LOGIC_FLAG#,
      #LOGIC_REMARK#,
      #LOGIC_GROUP#
      )
      <!--<selectKey  resultClass="int" type="post" property="LOGIC_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="WH_LOGIC_UPDATE" parameterClass="WH_LOGIC">
      Update WH_LOGIC Set
      <!--logic_id=#LOGIC_ID#,-->
      warehouse_id=#WAREHOUSE_ID#,
      logic_type=#LOGIC_TYPE#,
      logic_code=#LOGIC_CODE#,
      logic_name=#LOGIC_NAME#,
      logic_order=#LOGIC_ORDER#,
      logic_flag=#LOGIC_FLAG#,
      logic_remark=#LOGIC_REMARK#,
      logic_group=#LOGIC_GROUP#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					logic_id=#LOGIC_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="WH_LOGIC_DELETE" parameterClass="int">
			Delete From WH_LOGIC
			<dynamic prepend="WHERE">
				<isParameterPresent>
					logic_id=#LOGIC_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>