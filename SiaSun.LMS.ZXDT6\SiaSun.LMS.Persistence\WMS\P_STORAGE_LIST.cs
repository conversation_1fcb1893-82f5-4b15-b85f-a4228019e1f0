﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// STORAGE_LIST
	/// </summary>
	public class P_STORAGE_LIST : P_Base_House
	{
		public P_STORAGE_LIST ()
		{
			//
			// TODO: 此处添加STORAGE_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_LIST> GetList()
		{
			return ExecuteQueryForList<STORAGE_LIST>("STORAGE_LIST_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LIST> GetListStorageID(int STORAGE_ID)
        {
            return this.ExecuteQueryForList<STORAGE_LIST>("STORAGE_LIST_SELECT_BY_STORAGE_ID", STORAGE_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LIST> GetList(string GOODS_PROPERTY)
        {            
            return this.ExecuteQueryForList<STORAGE_LIST>("STORAGE_LIST_SELECT_BY_GOODS_PROPERTY", GOODS_PROPERTY);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(STORAGE_LIST storage_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("STORAGE_LIST");
                storage_list.STORAGE_LIST_ID = id;
            }

            return ExecuteInsert("STORAGE_LIST_INSERT",storage_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(STORAGE_LIST storage_list)
		{
			return ExecuteUpdate("STORAGE_LIST_UPDATE",storage_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public STORAGE_LIST GetModel(System.Int32 STORAGE_LIST_ID)
		{
			return ExecuteQueryForObject<STORAGE_LIST>("STORAGE_LIST_SELECT_BY_ID",STORAGE_LIST_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public STORAGE_LIST GetModel(System.String BOX_BARCODE)
        {
            return ExecuteQueryForObject<STORAGE_LIST>("STORAGE_LIST_SELECT_BOX_BARCODE", BOX_BARCODE);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 STORAGE_LIST_ID)
		{
			return ExecuteDelete("STORAGE_LIST_DELETE",STORAGE_LIST_ID);
		}
		

	}
}
