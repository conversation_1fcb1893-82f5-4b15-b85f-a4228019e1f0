<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:simpleType name="LogLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DEBUG" />
      <xs:enumeration value="INFO" />
      <xs:enumeration value="WARN" />
      <xs:enumeration value="ERROR" />
      <xs:enumeration value="FATAL" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="LogLevel" nillable="true" type="tns:LogLevel" />
  <xs:simpleType name="LogType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="record" />
      <xs:enumeration value="alert" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="LogType" nillable="true" type="tns:LogType" />
</xs:schema>