﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.GOODS.GOODS_EDIT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="GOODS_EDIT" Height="300" Width="556" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.Resources>
            <ContextMenu x:Key="menuGoodsClass" MenuItem.Click="ContextMenu_Click">
                <MenuItem Name="menuItemImportGoods" Header="导入物料"></MenuItem>
                <MenuItem Name="menuItemUpdate" Header="刷新"></MenuItem>
            </ContextMenu>
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <uc:ucTreeView Margin="1" x:Name="tvwGoodsClass" Grid.Column="0" ContextMenu="{StaticResource menuGoodsClass}" ></uc:ucTreeView>
        <GroupBox Name="grpGoods" Grid.Column="1" Header="物料列表" Tag=" {0}-物料列表" Margin="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>

                <uc:ucQuickQuery x:Name="ucQueryGoods" Margin="1" Grid.Row="0" ></uc:ucQuickQuery>
                <uc:ucCommonDataGrid x:Name="gridGoods" Margin="1" Grid.Row="1"></uc:ucCommonDataGrid>
            </Grid>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
