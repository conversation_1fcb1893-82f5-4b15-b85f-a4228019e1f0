﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="1.aspx.cs" Inherits="_1" EnableEventValidation="false"%>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
    <head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <title></title>
        <link rel="stylesheet" href="/layui/css/layui.css" />
        <link rel="stylesheet" href="/main.css" />
        <script src="/jquery-3.4.1.min.js"></script>
        <script src="/layui/layui.all.js"></script>
        <script src="/main.js"></script>
    </head>
    <body>
        <form id="form1" runat="server" autocomplete="off">
            <div class="layui-form-item">
              <asp:Label runat="server" Text="查询条件" CssClass="layui-form-label" for="tbSearch"></asp:Label>
              <asp:TextBox runat="server" ID="tbSearch" CssClass="layui-input input-width" />
              <asp:Button runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="bSearch_Click" />
              <asp:Button runat="server" Text="查询" CssClass="layui-input-inline layui-btn layui-btn-normal search-button-width" UseSubmitBehavior="false" OnClick="bSearch_Click" />
            </div>
            <div class="layui-form-item" style="padding:0 15px;height:220px;overflow:hidden;overflow-y:auto;">
              <asp:GridView runat="server" ID="gvGoods" AutoGenerateColumns="False" style="width:100%;"
                ShowHeaderWhenEmpty="False" CellPadding="4" ForeColor="#333333" GridLines="None"
                OnRowDataBound="GoodsDataBound" OnSelectedIndexChanged="gvGoods_SelectedIndexChanged">
                    <AlternatingRowStyle BackColor="White" />
                    <Columns>
                      <asp:TemplateField>
                        <HeaderStyle CssClass="t-value1 d-none" />
                        <ItemStyle CssClass="t-value1 d-none" />
                        <HeaderTemplate>物资ID</HeaderTemplate>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value1" Text='<%#Eval("value1")%>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                      <asp:TemplateField>
                        <HeaderStyle CssClass="t-value2" />
                        <ItemStyle CssClass="t-value2" />
                        <HeaderTemplate>材料编码</HeaderTemplate>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value2" Text='<%#Eval("value2")%>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                      <asp:TemplateField>
                        <HeaderStyle CssClass="t-value3" />
                        <ItemStyle CssClass="t-value3" />
                        <HeaderTemplate>物资名称</HeaderTemplate>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value3" Text='<%#Eval("value3")%>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                    </Columns>
                    <EditRowStyle BackColor="#2461BF" />
                    <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                    <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                    <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
                    <RowStyle BackColor="#EFF3FB" />
                    <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
                    <SortedAscendingCellStyle BackColor="#F5F7FB" />
                    <SortedAscendingHeaderStyle BackColor="#6D95E1" />
                    <SortedDescendingCellStyle BackColor="#E9EBEF" />
                    <SortedDescendingHeaderStyle BackColor="#4870BE" />
                </asp:GridView>
            </div>
            <div class="layui-form-item" style="margin-top:10px">
                <asp:Label runat="server" Text="绑定站台" CssClass="layui-form-label"></asp:Label>
                <asp:DropDownList runat="server" Width="150" >
                    <asp:ListItem Text="第一站台"></asp:ListItem>
                    <asp:ListItem Text="第二站台"></asp:ListItem>
                    <asp:ListItem Text="第三站台"></asp:ListItem>
                    <asp:ListItem Text="第四站台"></asp:ListItem>
                </asp:DropDownList>
            </div>
            <div class="layui-form-item">
                <asp:Label runat="server" Text="托盘条码" CssClass="layui-form-label" for="tbStockBarcode"></asp:Label>
                <asp:TextBox ID="tbStockBarcode" runat="server"></asp:TextBox>
            </div>
            <div class="layui-form-item" style="padding:0 15px;min-height:220px;">
                <asp:GridView ID="gvInStorage" runat="server" AutoGenerateColumns="False" style="width:100%;"
                    ShowHeaderWhenEmpty="False" CellPadding="4" ForeColor="#333333" GridLines="None"
                    OnRowDataBind="GoodsDataUnbind" OnSelectedIndexChanged="gvGoods_SelectedIndexDelete"
                    OnRowDeleting="GoodsRowDelete">
                    <AlternatingRowStyle BackColor="White" />
                    <Columns>
                      <asp:TemplateField HeaderText="物资ID">
                        <HeaderStyle CssClass="t-value1 d-none" />
                        <ItemStyle CssClass="t-value1 d-none"/>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value1" Text='<%#Eval("value1") %>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                      <asp:TemplateField HeaderText="材料编码">
                        <HeaderStyle CssClass="t-value2" />
                        <ItemStyle CssClass="t-value2"/>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value2" Text='<%#Eval("value2") %>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                      <asp:TemplateField HeaderText="物资名称">
                        <HeaderStyle CssClass="t-value3" />
                        <ItemStyle CssClass="t-value3"/>
                        <ItemTemplate>
                          <asp:Label runat="server" ID="value3" Text='<%#Eval("value3") %>' />
                        </ItemTemplate>
                      </asp:TemplateField>
                      <asp:TemplateField HeaderText="数量" >
                        <HeaderStyle CssClass="t-value4" />
                        <ItemStyle CssClass="t-value4"/>
                        <ItemTemplate>
                          <asp:TextBox runat="server" ID="value4" CssClass="quantity" Text='<%#Eval("value4") %>' onclick="select();" onFocus="select();" />
                        </ItemTemplate>
                      </asp:TemplateField>
                    </Columns>
                    <EditRowStyle BackColor="#2461BF" />
                    <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                    <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
                    <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
                    <RowStyle BackColor="#EFF3FB" />
                    <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
                    <SortedAscendingCellStyle BackColor="#F5F7FB" />
                    <SortedAscendingHeaderStyle BackColor="#6D95E1" />
                    <SortedDescendingCellStyle BackColor="#E9EBEF" />
                    <SortedDescendingHeaderStyle BackColor="#4870BE" />
                </asp:GridView>
            </div>
            <div class="layui-form-item">
                <input type="button" class="layui-input-inline layui-btn layui-btn-normal button-width" value="入库" wms-confirm="确认入库" />
                <asp:Button runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="bSave_Click" />
                <asp:Button runat="server" Text="刷新" CssClass="layui-input-inline layui-btn layui-btn-normal button-width" UseSubmitBehavior="false" OnClick="bRefresh_Click" />
            </div>
        </form>
    </body>
</html>
