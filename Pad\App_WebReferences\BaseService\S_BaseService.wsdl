<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="S_BaseService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="anyType" nillable="true" type="xs:anyType" />
      <xs:element name="anyURI" nillable="true" type="xs:anyURI" />
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary" />
      <xs:element name="boolean" nillable="true" type="xs:boolean" />
      <xs:element name="byte" nillable="true" type="xs:byte" />
      <xs:element name="dateTime" nillable="true" type="xs:dateTime" />
      <xs:element name="decimal" nillable="true" type="xs:decimal" />
      <xs:element name="double" nillable="true" type="xs:double" />
      <xs:element name="float" nillable="true" type="xs:float" />
      <xs:element name="int" nillable="true" type="xs:int" />
      <xs:element name="long" nillable="true" type="xs:long" />
      <xs:element name="QName" nillable="true" type="xs:QName" />
      <xs:element name="short" nillable="true" type="xs:short" />
      <xs:element name="string" nillable="true" type="xs:string" />
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte" />
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt" />
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong" />
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort" />
      <xs:element name="char" nillable="true" type="tns:char" />
      <xs:simpleType name="char">
        <xs:restriction base="xs:int" />
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration" />
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?" />
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S" />
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid" />
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}" />
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName" />
      <xs:attribute name="Id" type="xs:ID" />
      <xs:attribute name="Ref" type="xs:IDREF" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xs:complexType name="GOODS_TEMPLATE">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_TEMPLATE" nillable="true" type="tns:GOODS_TEMPLATE" />
      <xs:complexType name="PLAN_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="PLAN_BEGIN_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_BILL_DATE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CONFIRM_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CONFIRM_USER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CREATER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CREATE_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_END_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_FROM_DEPT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_FROM_USER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_RELATIVE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TO_DEPT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TO_USER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_MAIN" nillable="true" type="tns:PLAN_MAIN" />
      <xs:complexType name="GOODS_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_COLOR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY6" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY7" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY8" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_LIMIT_LOWER_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="GOODS_LIMIT_UPPER_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="GOODS_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_UNITS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_MAIN" nillable="true" type="tns:GOODS_MAIN" />
      <xs:complexType name="SYS_USER">
        <xs:sequence>
          <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
          <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER" />
      <xs:complexType name="MANAGE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_LIST_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="MANAGE_LIST_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST" />
      <xs:complexType name="MANAGE_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_CELL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_LEVEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_OPERATOR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_CELL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_MAIN" nillable="true" type="tns:MANAGE_MAIN" />
      <xs:complexType name="ArrayOfMANAGE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfMANAGE_LIST" nillable="true" type="tns:ArrayOfMANAGE_LIST" />
      <xs:complexType name="ArrayOfPLAN_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfPLAN_LIST" nillable="true" type="tns:ArrayOfPLAN_LIST" />
      <xs:complexType name="PLAN_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_LIST_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_LIST_FINISHED_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_LIST_ORDERED_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="PLAN_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
      <xs:complexType name="ObjectT">
        <xs:sequence>
          <xs:element minOccurs="0" name="RequestObject" nillable="true" type="xs:anyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ObjectT" nillable="true" type="tns:ObjectT" />
      <xs:complexType name="FIELD_DESCRIPTION">
        <xs:sequence>
          <xs:element minOccurs="0" name="AllowQuery" type="xs:int" />
          <xs:element minOccurs="0" name="Column" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ControlType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DataBind" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DbType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DefaultValue" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Header" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Order" type="xs:int" />
          <xs:element minOccurs="0" name="QueryOperation" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ReadOnly" type="xs:int" />
          <xs:element minOccurs="0" name="Remark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Validation" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="FIELD_DESCRIPTION" nillable="true" type="tns:FIELD_DESCRIPTION" />
      <xs:complexType name="FLOW_ACTION">
        <xs:sequence>
          <xs:element minOccurs="0" name="FLOW_ACTION_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_DEFAULT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_EVENT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_ACTION_IMAGE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_ACTION_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_ACTION_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_NODE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="FLOW_ACTION" nillable="true" type="tns:FLOW_ACTION" />
      <xs:complexType name="FLOW_NODE">
        <xs:sequence>
          <xs:element minOccurs="0" name="FLOW_NODE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_NODE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_NODE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_NODE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_NODE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_NODE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="FLOW_NODE" nillable="true" type="tns:FLOW_NODE" />
      <xs:complexType name="FLOW_PARA">
        <xs:sequence>
          <xs:element minOccurs="0" name="FLOW_PARA_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_PARA_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_PARA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_PARA_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_PARA_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_PARA_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="FLOW_PARA" nillable="true" type="tns:FLOW_PARA" />
      <xs:complexType name="FLOW_TYPE">
        <xs:sequence>
          <xs:element minOccurs="0" name="FLOW_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_TYPE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_TYPE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLOW_TYPE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="FLOW_TYPE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="FLOW_TYPE" nillable="true" type="tns:FLOW_TYPE" />
      <xs:complexType name="GOODS_CLASS">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_CLASS_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CLASS_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_CLASS_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CLASS_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_CLASS_PARENT_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_CLASS_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_CLASS" nillable="true" type="tns:GOODS_CLASS" />
      <xs:complexType name="GOODS_PROPERTY">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_PROPERTY_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_DATASOURCE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELD" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELDTYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY_VALID" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_PROPERTY" nillable="true" type="tns:GOODS_PROPERTY" />
      <xs:complexType name="GOODS_TEMPLATE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_TEMPLATE_LIST" nillable="true" type="tns:GOODS_TEMPLATE_LIST" />
      <xs:complexType name="GOODS_TYPE">
        <xs:sequence>
          <xs:element minOccurs="0" name="GOODS_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TYPE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TYPE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TYPE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_TYPE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="GOODS_TYPE" nillable="true" type="tns:GOODS_TYPE" />
      <xs:complexType name="IO_CONTROL_APPLY">
        <xs:sequence>
          <xs:element minOccurs="0" name="APPLY_TASK_STATUS" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_PARA01" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_PARA02" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ERROR_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CREATE_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ERROR_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="IO_CONTROL_APPLY" nillable="true" type="tns:IO_CONTROL_APPLY" />
      <xs:complexType name="IO_CONTROL">
        <xs:sequence>
          <xs:element minOccurs="0" name="CELL_GROUP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_BEGIN_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_END_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_STATUS" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_TASK_LEVEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_TASK_TYPE" type="xs:int" />
          <xs:element minOccurs="0" name="END_DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_WAREHOUSE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ERROR_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PRE_CONTROL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATIVE_CONTROL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="START_DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_WAREHOUSE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="IO_CONTROL" nillable="true" type="tns:IO_CONTROL" />
      <xs:complexType name="IO_CONTROL_APPLY_HIS">
        <xs:sequence>
          <xs:element minOccurs="0" name="APPLY_TASK_STATUS" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_PARA01" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_PARA02" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_APPLY_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ERROR_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CREATE_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ERROR_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="IO_CONTROL_APPLY_HIS" nillable="true" type="tns:IO_CONTROL_APPLY_HIS" />
      <xs:complexType name="IO_CONTROL_ROUTE">
        <xs:sequence>
          <xs:element minOccurs="0" name="CONTROL_ROUTE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ROUTE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_ROUTE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ROUTE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_ROUTE_STATUS" type="xs:int" />
          <xs:element minOccurs="0" name="CONTROL_ROUTE_TYPE" type="xs:int" />
          <xs:element minOccurs="0" name="END_DEVICE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_DEVICE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="IO_CONTROL_ROUTE" nillable="true" type="tns:IO_CONTROL_ROUTE" />
      <xs:complexType name="LED_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="AREA_HEIGHT" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_WIDTH" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_X" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_Y" type="xs:int" />
          <xs:element minOccurs="0" name="FILE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FONT_SIZE" type="xs:int" />
          <xs:element minOccurs="0" name="LED_ID" type="xs:int" />
          <xs:element minOccurs="0" name="LED_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="LED_LIST_PARA1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_LIST_PARA2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_LIST_PARA3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_LIST_PARA4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_LIST_PARA5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_LIST_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LINE_NO" type="xs:int" />
          <xs:element minOccurs="0" name="LINE_TEXT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RUN_SPEED" type="xs:int" />
          <xs:element minOccurs="0" name="SHOW_STUNT" type="xs:int" />
          <xs:element minOccurs="0" name="SHOW_TIME" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="LED_LIST" nillable="true" type="tns:LED_LIST" />
      <xs:complexType name="LED_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="AUTO_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_TYPE" type="xs:int" />
          <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_ID" type="xs:int" />
          <xs:element minOccurs="0" name="LED_IP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_PARA1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_PARA2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_PARA3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_PARA4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_PARA5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_MAIN_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LED_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LINE_NUM" type="xs:int" />
          <xs:element minOccurs="0" name="SCREEN_HEIGHT" type="xs:int" />
          <xs:element minOccurs="0" name="SCREEN_WIDTH" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="LED_MAIN" nillable="true" type="tns:LED_MAIN" />
      <xs:complexType name="Log">
        <xs:sequence>
          <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TimeStamp" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Log" nillable="true" type="tns:Log" />
      <xs:complexType name="MANAGE_ACTION_EXCUTE">
        <xs:sequence>
          <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="NEXT_NODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_ACTION_EXCUTE" nillable="true" type="tns:MANAGE_ACTION_EXCUTE" />
      <xs:complexType name="MANAGE_DETAIL">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_DETAIL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_DETAIL_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_DETAIL" nillable="true" type="tns:MANAGE_DETAIL" />
      <xs:complexType name="MANAGE_TYPE">
        <xs:sequence>
          <xs:element minOccurs="0" name="MANAGE_TYPE_CLASS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_GROUP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_INOUT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_ORDER" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_TYPE" nillable="true" type="tns:MANAGE_TYPE" />
      <xs:complexType name="MANAGE_TYPE_PARAM">
        <xs:sequence>
          <xs:element minOccurs="0" name="MANAGE_TYPE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PARAM_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARAM_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARAM_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PARAM_VALUE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="MANAGE_TYPE_PARAM" nillable="true" type="tns:MANAGE_TYPE_PARAM" />
      <xs:complexType name="PLAN_ACTION_EXCUTE">
        <xs:sequence>
          <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_ACTION_EXCUTE" nillable="true" type="tns:PLAN_ACTION_EXCUTE" />
      <xs:complexType name="PLAN_DETAIL">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_DETAIL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_DETAIL_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_DETAIL" nillable="true" type="tns:PLAN_DETAIL" />
      <xs:complexType name="PLAN_TYPE">
        <xs:sequence>
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CLASS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_GROUP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_TYPE_INOUT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_TYPE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="PLAN_TYPE" nillable="true" type="tns:PLAN_TYPE" />
      <xs:complexType name="QueryObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="Column" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Header" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Logic" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Operation" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Value" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="QueryObject" nillable="true" type="tns:QueryObject" />
      <xs:complexType name="RECORD_DETAIL">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RECORD_DETAIL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RECORD_DETAIL_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RECORD_LIST_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="RECORD_DETAIL" nillable="true" type="tns:RECORD_DETAIL" />
      <xs:complexType name="RECORD_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RECORD_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RECORD_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RECORD_LIST_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="RECORD_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="RECORD_LIST" nillable="true" type="tns:RECORD_LIST" />
      <xs:complexType name="RECORD_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="END_POSITION" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RECORD_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RECORD_OPERATOR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RECORD_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_POSITION" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="RECORD_MAIN" nillable="true" type="tns:RECORD_MAIN" />
      <xs:complexType name="ServiceMessage">
        <xs:sequence>
          <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
          <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Params" nillable="true" type="q1:ArrayOfanyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ServiceMessage" nillable="true" type="tns:ServiceMessage" />
      <xs:complexType name="ServiceResponse">
        <xs:sequence>
          <xs:element xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Params" nillable="true" type="q2:ArrayOfanyType" />
          <xs:element minOccurs="0" name="Result" nillable="true" type="xs:anyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ServiceResponse" nillable="true" type="tns:ServiceResponse" />
      <xs:complexType name="STORAGE_DETAIL">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STORAGE_DETAIL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_DETAIL_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="STORAGE_DETAIL" nillable="true" type="tns:STORAGE_DETAIL" />
      <xs:complexType name="STORAGE_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ENTRY_TIME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_LIST_QUANTITY" type="xs:decimal" />
          <xs:element minOccurs="0" name="STORAGE_LIST_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="UPDATE_TIME" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="STORAGE_LIST" nillable="true" type="tns:STORAGE_LIST" />
      <xs:complexType name="STORAGE_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="CELL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STORAGE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="STORAGE_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="STORAGE_MAIN" nillable="true" type="tns:STORAGE_MAIN" />
      <xs:complexType name="SYS_ITEM">
        <xs:sequence>
          <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_PARENT_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ITEM" nillable="true" type="tns:SYS_ITEM" />
      <xs:complexType name="SYS_ITEM_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ITEM_LIST_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ITEM_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
      <xs:complexType name="SYS_LOG">
        <xs:sequence>
          <xs:element minOccurs="0" name="LOG_DATE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOG_ID" type="xs:int" />
          <xs:element minOccurs="0" name="LOG_LEVEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOG_LOGGER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOG_MESSAGE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOG_THREAD" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_LOG" nillable="true" type="tns:SYS_LOG" />
      <xs:complexType name="SYS_MENU">
        <xs:sequence>
          <xs:element minOccurs="0" name="MENU_CHILDNODEFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_CLASS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_DEVELOPFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_GROUP" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_IMAGE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_IMAGE_SL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_PARAMETER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_PARAMETER_SL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_PARENT_ID" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_SELECTEDFLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MENU_SYSFLAG" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
      <xs:complexType name="SYS_RELATION">
        <xs:sequence>
          <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_ID2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATION_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATON_NAME1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELATON_NAME2" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_RELATION" nillable="true" type="tns:SYS_RELATION" />
      <xs:complexType name="SYS_RELATION_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_ID2" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RELATION_LIST_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
      <xs:complexType name="SYS_ROLE">
        <xs:sequence>
          <xs:element minOccurs="0" name="ROLE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROLE_START_MENU_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
      <xs:complexType name="SYS_ROLE_WINDOW">
        <xs:sequence>
          <xs:element minOccurs="0" name="CONTROL_HEADER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROLE_WINDOW_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
      <xs:complexType name="SYS_TABLE_CONVERTER_LIST">
        <xs:sequence>
          <xs:element minOccurs="0" name="COLUMN_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CONVERT_COLUMN_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ISNULL_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_LIST_ID" type="xs:int" />
          <xs:element minOccurs="0" name="TABLE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="UNIQUE_FLAG" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_TABLE_CONVERTER_LIST" nillable="true" type="tns:SYS_TABLE_CONVERTER_LIST" />
      <xs:complexType name="TECHNICS_MAIN">
        <xs:sequence>
          <xs:element minOccurs="0" name="AUTO_CREATE_CONTROL" type="xs:int" />
          <xs:element minOccurs="0" name="CANCEL_BIND_BARCODE" type="xs:int" />
          <xs:element minOccurs="0" name="CANCEL_STORAGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_CLASS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CHECK_END_POSITION_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="CHECK_START_POSITION_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="CREATE_STORAGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="DATA_ORDER" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="END_GOODS_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_LOGIC_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_POSITION" type="xs:int" />
          <xs:element minOccurs="0" name="EP_END_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_END_RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_START_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_START_RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FINISH_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="HAS_STORAGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="INIT_STORAGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MOVE_STROAGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="NEXT_GOODS_TECHNICS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="NEXT_STOCK_TECHNICS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_CHANGE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RELAVTIVE_TECHNICS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="SAME_LANEWAY" type="xs:int" />
          <xs:element minOccurs="0" name="SP_END_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SP_END_RUNS_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SP_START_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SP_START_RUNS_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="START_POSITION" type="xs:int" />
          <xs:element minOccurs="0" name="START_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="STATION_PROJECT_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="STATION_SORT_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="TECHNICS_ACTIONS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TECHNICS_DESCRIPTION" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TECHNICS_ID" type="xs:int" />
          <xs:element minOccurs="0" name="TECHNICS_REMARK" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="TECHNICS_MAIN" nillable="true" type="tns:TECHNICS_MAIN" />
      <xs:complexType name="TECHNICS_ROUTE">
        <xs:sequence>
          <xs:element minOccurs="0" name="CHECK_END_POSITION_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="CHECK_START_POSITION_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="DEVICE_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="END_NODE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="END_POSITION" type="xs:int" />
          <xs:element minOccurs="0" name="EP_END_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_END_RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_START_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EP_START_RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EXCEPTIONAL_ROUTE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="FINISH_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROUTE_ACTIONS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROUTE_DESCRIPTION" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ROUTE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="ROUTE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="ROUTE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SP_END_CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SP_END_RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="START_NODE_FLAG" type="xs:int" />
          <xs:element minOccurs="0" name="START_POSITION" type="xs:int" />
          <xs:element minOccurs="0" name="START_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TASK_LEVEL" type="xs:int" />
          <xs:element minOccurs="0" name="TASK_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TECHNICS_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="TECHNICS_ROUTE" nillable="true" type="tns:TECHNICS_ROUTE" />
      <xs:complexType name="WH_AREA">
        <xs:sequence>
          <xs:element minOccurs="0" name="AREA_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="AREA_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="AREA_GROUP" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="AREA_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="AREA_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="AREA_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="WH_AREA" nillable="true" type="tns:WH_AREA" />
      <xs:complexType name="WH_CELL">
        <xs:sequence>
          <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_FORK_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_GROUP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_HEIGHT" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_LOGICAL_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_STORAGE_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_WIDTH" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_X" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_Y" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_Z" type="xs:int" />
          <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LANE_WAY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOCK_CELL_ID" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
          <xs:element minOccurs="0" name="RUN_STATUS" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SHELF_NEIGHBOUR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SHELF_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="WH_CELL" nillable="true" type="tns:WH_CELL" />
      <xs:complexType name="WH_DESCRIPTION">
        <xs:sequence>
          <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_FORK_COUNT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_FORK_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_HEIGHT" type="xs:int" />
          <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_LOGICAL_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_STORAGE_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CELL_WIDTH" type="xs:int" />
          <xs:element minOccurs="0" name="DESCRIPTION_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DESCRIPTION_ID" type="xs:int" />
          <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DEVICE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="END_X" type="xs:int" />
          <xs:element minOccurs="0" name="END_Y" type="xs:int" />
          <xs:element minOccurs="0" name="END_Z" type="xs:int" />
          <xs:element minOccurs="0" name="LANE_WAY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
          <xs:element minOccurs="0" name="SHELF_NEIGHBOUR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SHELF_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="START_X" type="xs:int" />
          <xs:element minOccurs="0" name="START_Y" type="xs:int" />
          <xs:element minOccurs="0" name="START_Z" type="xs:int" />
          <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="WH_DESCRIPTION" nillable="true" type="tns:WH_DESCRIPTION" />
      <xs:complexType name="WH_LOGIC">
        <xs:sequence>
          <xs:element minOccurs="0" name="LOGIC_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_GROUP" type="xs:int" />
          <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
          <xs:element minOccurs="0" name="LOGIC_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="LOGIC_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LOGIC_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="WH_LOGIC" nillable="true" type="tns:WH_LOGIC" />
      <xs:complexType name="WH_WAREHOUSE">
        <xs:sequence>
          <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_FLAG" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
          <xs:element minOccurs="0" name="WAREHOUSE_NAME" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_ORDER" type="xs:int" />
          <xs:element minOccurs="0" name="WAREHOUSE_REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="WAREHOUSE_TYPE" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="WH_WAREHOUSE" nillable="true" type="tns:WH_WAREHOUSE" />
      <xs:complexType name="SYS_TABLE_CONVERTER">
        <xs:sequence>
          <xs:element minOccurs="0" name="CHILD_FOREIGN_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CHILD_TABLE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARENT_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PARENT_TABLE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_COLUMN" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_KEY" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SPLIT_PROPERTY_TYPE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
          <xs:element minOccurs="0" name="TABLE_CONVERTER_NAME" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
      <xs:complexType name="ObjectList">
        <xs:sequence>
          <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="RequestObject" nillable="true" type="q3:ArrayOfanyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ObjectList" nillable="true" type="tns:ObjectList" />
      <xs:complexType name="ArrayOfLog">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Log" nillable="true" type="tns:Log" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfLog" nillable="true" type="tns:ArrayOfLog" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System.Data" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="DataTable" nillable="true">
        <xs:complexType>
          <xs:annotation>
            <xs:appinfo>
              <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
            </xs:appinfo>
          </xs:annotation>
          <xs:sequence>
            <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
            <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:complexType name="ArrayOfanyType">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="anyType" nillable="true" type="xs:anyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfanyType" nillable="true" type="tns:ArrayOfanyType" />
      <xs:complexType name="ArrayOfstring">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfstring" nillable="true" type="tns:ArrayOfstring" />
    </xs:schema>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xs:import namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
      <xs:element name="Invoke">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="sPlanType" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="sMethod" nillable="true" type="xs:string" />
            <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="inParams" nillable="true" type="q1:ArrayOfanyType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InvokeResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="InvokeResult" type="xs:boolean" />
            <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Invoke1">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="sPlanType" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="sMethod" nillable="true" type="xs:string" />
            <xs:element xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="inParams" nillable="true" type="q2:ArrayOfanyType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Invoke1Response">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="Invoke1Result" type="xs:boolean" />
            <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="outParams" nillable="true" type="q3:ArrayOfanyType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ExecuteNonQuery_ReturnVoid">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="strSQL" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ExecuteNonQuery_ReturnVoidResponse">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="ExecuteNonQuery_ReturnInt">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="strSQL" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ExecuteNonQuery_ReturnIntResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ExecuteNonQuery_ReturnIntResult" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetList">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="strSQL" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="GetListResult" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetTableXmlSql">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="statementsql" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="paramObject" nillable="true" type="xs:anyType" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetTableXmlSqlResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="GetTableXmlSqlResult" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="Save">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="dt" nillable="true">
              <xs:complexType>
                <xs:annotation>
                  <xs:appinfo>
                    <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
                  </xs:appinfo>
                </xs:annotation>
                <xs:sequence>
                  <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="tablename" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="SaveResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="SaveResult" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetModel">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="statementName" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="parameterObject" nillable="true" type="xs:anyType" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetModelResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetModelResult" nillable="true" type="q4:ObjectT" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetListObject">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="statementName" nillable="true" type="xs:string" />
            <xs:element minOccurs="0" name="parameterObject" nillable="true" type="xs:anyType" />
            <xs:element minOccurs="0" name="DataAccess" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetListObjectResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetListObjectResult" nillable="true" type="q5:ObjectList" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MessageConverter_GetKeyValue">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
            <xs:element xmlns:q6="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Param" nillable="true" type="q6:ArrayOfanyType" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="MessageConverter_GetKeyValueResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="MessageConverter_GetKeyValueResult" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="AddLog">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="AddLogResponse">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="ReadLogFileList">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="ReadLogFileListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q7="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="ReadLogFileListResult" nillable="true" type="q7:ArrayOfstring" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetLogFromFile">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetLogFromFileResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetLogFromFileResult" nillable="true" type="q8:ArrayOfLog" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetLogFromContent">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="XmlContent" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GetLogFromContentResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GetLogFromContentResult" nillable="true" type="q9:ArrayOfLog" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="DeleteLogFile">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q10="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="FileList" nillable="true" type="q10:ArrayOfstring" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="DeleteLogFileResponse">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="InitSystem">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
      <xs:element name="InitSystemResponse">
        <xs:complexType>
          <xs:sequence />
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.EnumMessage" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.EnumMessage" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xs:simpleType name="SYSTEM_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="SystemException">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="SYSTEM_MESSAGE" nillable="true" type="tns:SYSTEM_MESSAGE" />
      <xs:simpleType name="SUCCESS_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OperationSuccess">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">51</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="SUCCESS_MESSAGE" nillable="true" type="tns:SUCCESS_MESSAGE" />
      <xs:simpleType name="CONTAINER_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="TaskExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">101</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="StorageNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">102</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CONTAINER_MESSAGE" nillable="true" type="tns:CONTAINER_MESSAGE" />
      <xs:simpleType name="CELL_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="StartCellIDNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">201</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="EndCellIDNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">202</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="CellIDNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">203</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="StartCellTypeInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">204</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="StartCellAreaTypeInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">205</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="EndCellCodeNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">206</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="EndCellTypeInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">207</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="EndCellInoutInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">208</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_MESSAGE" nillable="true" type="tns:CELL_MESSAGE" />
      <xs:simpleType name="ROUTE_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="RouteUnavailable">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">301</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="ROUTE_MESSAGE" nillable="true" type="tns:ROUTE_MESSAGE" />
      <xs:simpleType name="MANAGE_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ManageNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">401</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">402</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ControlExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">403</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="MANAGE_MESSAGE" nillable="true" type="tns:MANAGE_MESSAGE" />
      <xs:simpleType name="PLAN_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PlanExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">501</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="RecordExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">502</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="PlanNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">503</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="UpdatePlanStateFail">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">504</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="PLAN_MESSAGE" nillable="true" type="tns:PLAN_MESSAGE" />
      <xs:simpleType name="STORAGE_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="StorageNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">601</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="StorageListNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">602</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="STORAGE_MESSAGE" nillable="true" type="tns:STORAGE_MESSAGE" />
      <xs:simpleType name="CONTROL_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ControlNotComplete">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">701</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CONTROL_MESSAGE" nillable="true" type="tns:CONTROL_MESSAGE" />
      <xs:simpleType name="LED_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEDNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">801</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="AddLEDMainFirst">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">802</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="LEDLineNumInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">803</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="LEDLineNumXInvalid">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">804</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="SendTextLineCountAndFormatDiff">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">805</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="CreateLEDListFail">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">806</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="LineNumXNotExist">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">807</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="LED_MESSAGE" nillable="true" type="tns:LED_MESSAGE" />
      <xs:simpleType name="PLEASE_MESSAGE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="SelectEndStation">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">5001</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="SelectOperationRecord">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">5002</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="PLEASE_MESSAGE" nillable="true" type="tns:PLEASE_MESSAGE" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xs:simpleType name="FLAG">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Enable">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="DisEnable">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="FLAG" nillable="true" type="tns:FLAG" />
      <xs:simpleType name="CONTROL_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Up">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Down">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Move">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="MoveStation">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CONTROL_TYPE" nillable="true" type="tns:CONTROL_TYPE" />
      <xs:simpleType name="PLAN_TYPE_GROUP">
        <xs:restriction base="xs:string">
          <xs:enumeration value="StoreGroup">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="WorkStationGroup">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ProduceGroup">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="PLAN_TYPE_GROUP" nillable="true" type="tns:PLAN_TYPE_GROUP" />
      <xs:simpleType name="PLAN_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Waiting" />
          <xs:enumeration value="Executing" />
          <xs:enumeration value="Pause" />
          <xs:enumeration value="Delete" />
          <xs:enumeration value="Stop" />
          <xs:enumeration value="RouteWaiting" />
          <xs:enumeration value="ProduceWaiting" />
          <xs:enumeration value="ProduceExecuting" />
          <xs:enumeration value="ProducePause" />
          <xs:enumeration value="Finish" />
          <xs:enumeration value="Complete" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="PLAN_STATUS" nillable="true" type="tns:PLAN_STATUS" />
      <xs:simpleType name="PLAN_INOUT">
        <xs:restriction base="xs:string">
          <xs:enumeration value="In">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Out">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Move">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Sort">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="PLAN_INOUT" nillable="true" type="tns:PLAN_INOUT" />
      <xs:simpleType name="MANAGE_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IN">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">101</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="OUT">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">201</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="BINDING">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">301</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="CANCLEBINDING">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">302</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="MOVE">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">303</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="UP">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">401</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="UPPALLET">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">402</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="DOWN">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">501</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="DOWNPALLET">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">502</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageOut">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">503</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageDown">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">504</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageIn">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">505</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageInlocal">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">506</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ManageTemplateIn">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">507</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="StockOut">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">508</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="MANAGE_TYPE" nillable="true" type="tns:MANAGE_TYPE" />
      <xs:simpleType name="MANAGE_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="WaitingSend" />
          <xs:enumeration value="WaitingExecute" />
          <xs:enumeration value="Waiting" />
          <xs:enumeration value="Error" />
          <xs:enumeration value="Cancel" />
          <xs:enumeration value="Complete" />
          <xs:enumeration value="Executing">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">10</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="WaitConfirm">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">20</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="ConfirmFinish">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">30</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="MANAGE_STATUS" nillable="true" type="tns:MANAGE_STATUS" />
      <xs:simpleType name="CONTROL_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Wait" />
          <xs:enumeration value="Control_Readed">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">7</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Runing">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">10</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="DeviceRuning">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">11</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="LterRouteApply">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">30</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="LterRouteReplay">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">40</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="TaskAbend">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">990</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="TaskDelete">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">900</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="EmptyOutPut">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">980</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="RepeatInput">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">970</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Finish">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">999</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CONTROL_STATUS" nillable="true" type="tns:CONTROL_STATUS" />
      <xs:simpleType name="CONTROL_APPLY_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Waiting" />
          <xs:enumeration value="Read" />
          <xs:enumeration value="Finish" />
          <xs:enumeration value="Error" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CONTROL_APPLY_STATUS" nillable="true" type="tns:CONTROL_APPLY_STATUS" />
      <xs:simpleType name="AREA_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LiKu" />
          <xs:enumeration value="XuNiKu" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="AREA_TYPE" nillable="true" type="tns:AREA_TYPE" />
      <xs:simpleType name="CELL_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Cell" />
          <xs:enumeration value="Station" />
          <xs:enumeration value="ErrorStation" />
          <xs:enumeration value="WorkStation" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_TYPE" nillable="true" type="tns:CELL_TYPE" />
      <xs:simpleType name="CELL_STORAGE_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Single" />
          <xs:enumeration value="Multiple" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_STORAGE_TYPE" nillable="true" type="tns:CELL_STORAGE_TYPE" />
      <xs:simpleType name="CELL_FORK_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Normal" />
          <xs:enumeration value="Double" />
          <xs:enumeration value="Multi" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_FORK_TYPE" nillable="true" type="tns:CELL_FORK_TYPE" />
      <xs:simpleType name="CELL_INOUT">
        <xs:restriction base="xs:string">
          <xs:enumeration value="In">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Out">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="InOut">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_INOUT" nillable="true" type="tns:CELL_INOUT" />
      <xs:simpleType name="CELL_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Full" />
          <xs:enumeration value="Have" />
          <xs:enumeration value="Nohave" />
          <xs:enumeration value="Pallet" />
          <xs:enumeration value="Exception" />
          <xs:enumeration value="Forbiden" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_STATUS" nillable="true" type="tns:CELL_STATUS" />
      <xs:simpleType name="RUN_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Disable" />
          <xs:enumeration value="Enable" />
          <xs:enumeration value="Run" />
          <xs:enumeration value="Selected" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="RUN_STATUS" nillable="true" type="tns:RUN_STATUS" />
      <xs:simpleType name="DEVICE_TYPE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="system" />
          <xs:enumeration value="auto" />
          <xs:enumeration value="agv" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="DEVICE_TYPE" nillable="true" type="tns:DEVICE_TYPE" />
      <xs:simpleType name="GOODS_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Waiting">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Executing">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Finish">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Complet">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="GOODS_STATUS" nillable="true" type="tns:GOODS_STATUS" />
      <xs:simpleType name="WORK_MODE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Serial">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Parallel">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="WORK_MODE" nillable="true" type="tns:WORK_MODE" />
      <xs:simpleType name="ROUTE_STATUS">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Waiting" />
          <xs:enumeration value="Executing" />
          <xs:enumeration value="Pause" />
          <xs:enumeration value="Stop" />
          <xs:enumeration value="Finish" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="ROUTE_STATUS" nillable="true" type="tns:ROUTE_STATUS" />
      <xs:simpleType name="STATION_OP_MODE">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Send">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Produce">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Back">
            <xs:annotation>
              <xs:appinfo>
                <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
              </xs:appinfo>
            </xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="STATION_OP_MODE" nillable="true" type="tns:STATION_OP_MODE" />
      <xs:simpleType name="CELL_MODEL">
        <xs:restriction base="xs:string">
          <xs:enumeration value="W" />
          <xs:enumeration value="N" />
          <xs:enumeration value="S" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="CELL_MODEL" nillable="true" type="tns:CELL_MODEL" />
      <xs:simpleType name="MessageConverter">
        <xs:restriction base="xs:string">
          <xs:enumeration value="Null" />
          <xs:enumeration value="Type" />
          <xs:enumeration value="Length" />
          <xs:enumeration value="Weight" />
          <xs:enumeration value="Input" />
          <xs:enumeration value="Data" />
          <xs:enumeration value="Exists" />
          <xs:enumeration value="Save" />
          <xs:enumeration value="Delete" />
          <xs:enumeration value="Cancel" />
          <xs:enumeration value="Print" />
          <xs:enumeration value="Close" />
          <xs:enumeration value="Exit" />
          <xs:enumeration value="CheckStack" />
          <xs:enumeration value="CheckStorage" />
          <xs:enumeration value="CheckTechnicsType" />
          <xs:enumeration value="CheckStoragePosition" />
          <xs:enumeration value="CheckCellModel" />
          <xs:enumeration value="CheckStockBarCode" />
          <xs:enumeration value="CheckStackNo" />
          <xs:enumeration value="CheckWareHouse" />
          <xs:enumeration value="CheckArea" />
          <xs:enumeration value="CheckLaneway" />
          <xs:enumeration value="CheckStartArea" />
          <xs:enumeration value="CheckEndArea" />
          <xs:enumeration value="CheckPosition" />
          <xs:enumeration value="CheckStartPosition" />
          <xs:enumeration value="CheckEndPosition" />
          <xs:enumeration value="CheckStationCode" />
          <xs:enumeration value="CheckStationDeviceCode" />
          <xs:enumeration value="CheckLoginUser" />
          <xs:enumeration value="CheckAssisQuantity" />
          <xs:enumeration value="CheckBoxNo" />
          <xs:enumeration value="ConfirmAssembly" />
          <xs:enumeration value="ConfirmExecute" />
          <xs:enumeration value="ConfirmCreateTask" />
          <xs:enumeration value="ConfirmCreatePlan" />
          <xs:enumeration value="SelectCount" />
          <xs:enumeration value="AllowSelectOneOnly" />
          <xs:enumeration value="AffectCount" />
          <xs:enumeration value="FailRollBack" />
          <xs:enumeration value="TaskComplete" />
          <xs:enumeration value="CheckProType" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="MessageConverter" nillable="true" type="tns:MessageConverter" />
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="I_BaseService_Invoke_InputMessage">
    <wsdl:part name="parameters" element="tns:Invoke" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_Invoke_OutputMessage">
    <wsdl:part name="parameters" element="tns:InvokeResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_Invoke1_InputMessage">
    <wsdl:part name="parameters" element="tns:Invoke1" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_Invoke1_OutputMessage">
    <wsdl:part name="parameters" element="tns:Invoke1Response" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ExecuteNonQuery_ReturnVoid_InputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQuery_ReturnVoid" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ExecuteNonQuery_ReturnVoid_OutputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQuery_ReturnVoidResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ExecuteNonQuery_ReturnInt_InputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQuery_ReturnInt" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ExecuteNonQuery_ReturnInt_OutputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQuery_ReturnIntResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetList" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetTableXmlSql_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTableXmlSql" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetTableXmlSql_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTableXmlSqlResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:Save" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:GetModel" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetListObject_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListObject" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetListObject_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListObjectResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_MessageConverter_GetKeyValue_InputMessage">
    <wsdl:part name="parameters" element="tns:MessageConverter_GetKeyValue" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_MessageConverter_GetKeyValue_OutputMessage">
    <wsdl:part name="parameters" element="tns:MessageConverter_GetKeyValueResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_AddLog_InputMessage">
    <wsdl:part name="parameters" element="tns:AddLog" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_AddLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddLogResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ReadLogFileList_InputMessage">
    <wsdl:part name="parameters" element="tns:ReadLogFileList" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_ReadLogFileList_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReadLogFileListResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetLogFromFile_InputMessage">
    <wsdl:part name="parameters" element="tns:GetLogFromFile" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetLogFromFile_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetLogFromFileResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetLogFromContent_InputMessage">
    <wsdl:part name="parameters" element="tns:GetLogFromContent" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_GetLogFromContent_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetLogFromContentResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_DeleteLogFile_InputMessage">
    <wsdl:part name="parameters" element="tns:DeleteLogFile" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_DeleteLogFile_OutputMessage">
    <wsdl:part name="parameters" element="tns:DeleteLogFileResponse" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_InitSystem_InputMessage">
    <wsdl:part name="parameters" element="tns:InitSystem" />
  </wsdl:message>
  <wsdl:message name="I_BaseService_InitSystem_OutputMessage">
    <wsdl:part name="parameters" element="tns:InitSystemResponse" />
  </wsdl:message>
  <wsdl:portType name="I_BaseService">
    <wsdl:operation name="Invoke">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/Invoke" message="tns:I_BaseService_Invoke_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/InvokeResponse" message="tns:I_BaseService_Invoke_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Invoke1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/Invoke1" message="tns:I_BaseService_Invoke1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/Invoke1Response" message="tns:I_BaseService_Invoke1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ExecuteNonQuery_ReturnVoid">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnVoid" message="tns:I_BaseService_ExecuteNonQuery_ReturnVoid_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnVoidResponse" message="tns:I_BaseService_ExecuteNonQuery_ReturnVoid_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ExecuteNonQuery_ReturnInt">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnInt" message="tns:I_BaseService_ExecuteNonQuery_ReturnInt_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnIntResponse" message="tns:I_BaseService_ExecuteNonQuery_ReturnInt_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetList" message="tns:I_BaseService_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetListResponse" message="tns:I_BaseService_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTableXmlSql">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetTableXmlSql" message="tns:I_BaseService_GetTableXmlSql_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetTableXmlSqlResponse" message="tns:I_BaseService_GetTableXmlSql_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/Save" message="tns:I_BaseService_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/SaveResponse" message="tns:I_BaseService_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetModel" message="tns:I_BaseService_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetModelResponse" message="tns:I_BaseService_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListObject">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetListObject" message="tns:I_BaseService_GetListObject_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetListObjectResponse" message="tns:I_BaseService_GetListObject_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="MessageConverter_GetKeyValue">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/MessageConverter_GetKeyValue" message="tns:I_BaseService_MessageConverter_GetKeyValue_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/MessageConverter_GetKeyValueResponse" message="tns:I_BaseService_MessageConverter_GetKeyValue_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddLog">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/AddLog" message="tns:I_BaseService_AddLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/AddLogResponse" message="tns:I_BaseService_AddLog_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ReadLogFileList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/ReadLogFileList" message="tns:I_BaseService_ReadLogFileList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/ReadLogFileListResponse" message="tns:I_BaseService_ReadLogFileList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetLogFromFile">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetLogFromFile" message="tns:I_BaseService_GetLogFromFile_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetLogFromFileResponse" message="tns:I_BaseService_GetLogFromFile_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetLogFromContent">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/GetLogFromContent" message="tns:I_BaseService_GetLogFromContent_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/GetLogFromContentResponse" message="tns:I_BaseService_GetLogFromContent_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="DeleteLogFile">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/DeleteLogFile" message="tns:I_BaseService_DeleteLogFile_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/DeleteLogFileResponse" message="tns:I_BaseService_DeleteLogFile_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InitSystem">
      <wsdl:input wsaw:Action="http://tempuri.org/I_BaseService/InitSystem" message="tns:I_BaseService_InitSystem_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_BaseService/InitSystemResponse" message="tns:I_BaseService_InitSystem_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_BaseService" type="tns:I_BaseService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Invoke">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/Invoke" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Invoke1">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/Invoke1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ExecuteNonQuery_ReturnVoid">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnVoid" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ExecuteNonQuery_ReturnInt">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/ExecuteNonQuery_ReturnInt" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTableXmlSql">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetTableXmlSql" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Save">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModel">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListObject">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetListObject" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MessageConverter_GetKeyValue">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/MessageConverter_GetKeyValue" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddLog">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/AddLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadLogFileList">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/ReadLogFileList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLogFromFile">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetLogFromFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetLogFromContent">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/GetLogFromContent" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteLogFile">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/DeleteLogFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InitSystem">
      <soap:operation soapAction="http://tempuri.org/I_BaseService/InitSystem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_BaseService">
    <wsdl:port name="BasicHttpBinding_I_BaseService" binding="tns:BasicHttpBinding_I_BaseService">
      <soap:address location="http://localhost:8001/Service/BaseService" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>