﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     
 *       日期：     2020/11/7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using System.Data;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;

	using SiaSun.LMS.Model;

	/// <summary>
	/// LCD_LIST
	/// </summary>
	public class P_LCD_LIST : P_Base_House
	{
		public P_LCD_LIST()
		{
			//
			// TODO: 此处添加LCD_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<LCD_LIST> GetList()
		{
			return ExecuteQueryForList<LCD_LIST>("LCD_LIST_SELECT", null);
		}

		/// <summary>
		/// 得到数据表
		/// </summary>
		public DataTable GetTable()
		{
			return ExecuteQueryForDataTable("LCD_LIST_SELECT", null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public void Add(LCD_LIST lcd_list)
		{
			if (_isOracleProvider)
			{
				int id = this.GetPrimaryID("LCD_LIST");
				lcd_list.LCD_LIST_ID = id;
			}

			ExecuteInsert("LCD_LIST_INSERT", lcd_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public void Update(LCD_LIST lcd_list)
		{
			ExecuteUpdate("LCD_LIST_UPDATE", lcd_list);
		}

		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public LCD_LIST GetModel(System.Decimal LCD_LIST_ID)
		{
			return ExecuteQueryForObject<LCD_LIST>("LCD_LIST_SELECT_BY_ID", LCD_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public void Delete(System.Decimal LCD_LIST_ID)
		{
			ExecuteDelete("LCD_LIST_DELETE", LCD_LIST_ID);
		}


	}
}
