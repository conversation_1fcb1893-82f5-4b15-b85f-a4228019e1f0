﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.GOODS
{
    /// <summary>
    /// MENU_EDIT.xaml 的交互逻辑
    /// </summary>
    public partial class GOODS_CLASS_EDIT : AvalonDock.DocumentContent
    {
        public GOODS_CLASS_EDIT()
        {
            InitializeComponent();

            this.tvwGoodsClass.U_ItemSelectedChanged += new UC.ucTreeView.U_ItemSelectedChangedHandler(tvwMenu_U_ItemSelectedChanged);
        }

        /// <summary>
        /// 加载节点
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //添加根节点
            this.tvwGoodsClass.U_Header = "物料类别";
            this.tvwGoodsClass.U_AllowCheck = false;
            this.tvwGoodsClass.U_LoadTreeViewItems("物料类别", 0, "GOODS_CLASS", "GOODS_CLASS_NAME AS HEADER", "GOODS_CLASS_ID", "GOODS_CLASS_PARENT_ID", "0", "", "GOODS_CLASS_ORDER");
        }

        /// <summary>
        /// 选择节点
        /// </summary>
        void tvwMenu_U_ItemSelectedChanged(TreeViewItem itemSelected)
        {
            if (itemSelected == null) this.gridGoodsClass.U_DataSource = null;

            grpBoxMenu.Header = string.Format(grpBoxMenu.Tag.ToString(),itemSelected.Header.ToString());

            //加载菜单项
            Menu_Bind(itemSelected);
        }

        /// <summary>
        /// 加载菜单选项
        /// </summary>
        private void Menu_Bind(TreeViewItem item)
        {
            if (item.Tag != null)
            {
                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    int intParentID = Convert.ToInt32(item.Tag);

                    this.gridGoodsClass.U_Clear();
                    this.gridGoodsClass.U_WindowName = this.GetType().Name;
                    this.gridGoodsClass.U_TableName = "GOODS_CLASS";
                    this.gridGoodsClass.U_XmlTableName = "GOODS_CLASS";
                    this.gridGoodsClass.U_Where = string.Format("GOODS_CLASS_PARENT_ID = {0} ", intParentID);
                    this.gridGoodsClass.U_AllowOperatData = true;
                    this.gridGoodsClass.U_OrderField = "GOODS_CLASS_ORDER";
                    this.gridGoodsClass.U_DefaultRowValues.Add("GOODS_CLASS_PARENT_ID", intParentID.ToString());
                    this.gridGoodsClass.U_InitControl();
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }     
    }
}
