﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;
using System.Xml;

namespace SiaSun.LMS.WPFClient.UC
{
    class DataGridTemplateNobug : DataGrid
    {
        public string strCheckedColumnName = "选择";
        private string _windowName = null;
        bool boolAllowOperatData = true;

        /// <summary>
        /// 是否允许编辑数据
        /// </summary>
        public bool U_AllowOperatData
        {
            get { return boolAllowOperatData; }
            set { boolAllowOperatData = value; }
        }

        /// <summary>
        /// 关联窗体,若果不为空，则加载显示WindowStyles.xml中的样式，否则加载FieldDescription.xml中的样式
        /// </summary>
        public string U_WindowName
        {
            get { return this._windowName; }
            set { this._windowName = value; }
        }

        #region     ------拆分组合列属性

        private DataTable tableSplit = null;
        private string strSplitPropertyType = string.Empty;
        private string strSplitPropertyKey = string.Empty;
        private string strSplitColumnName = string.Empty;

        /// <summary>
        /// 拆分列分组类型,如：GOODS_TYPE
        /// </summary>
        public string U_SplitPropertyType
        {
            get { return strSplitPropertyType; }
            set { strSplitPropertyType = value; }
        }

        /// <summary>
        /// 拆分列分组标识的值,如：GOODS_TYPE_ID的值
        /// </summary>
        public string U_SplitPropertyKey
        {
            get { return strSplitPropertyKey; }
            set { strSplitPropertyKey = value; }
        }

        /// <summary>
        /// 数据源中Tab页显示分组依据列名,如：GOODS_PROPERTY
        /// </summary>
        public string U_SplitPropertyColumn
        {
            get { return strSplitColumnName; }
            set { strSplitColumnName = value; }
        }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public DataGridTemplateNobug()
        {
            //没有bug但慢
            this.DefaultStyleKey = typeof(DataGridTemplateNobug);

            this.LoadingRow += new EventHandler<DataGridRowEventArgs>(DataGridTemplate_LoadingRow);
            this.UnloadingRow += new EventHandler<DataGridRowEventArgs>(DataGridTemplate_UnloadingRow);
        }

        /// <summary>
        /// 加载行时，添加行号
        /// </summary>
        void DataGridTemplate_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = e.Row.GetIndex() + 1;
        }
        /// <summary>
        /// 移除行时，更新行号
        /// </summary>
        void DataGridTemplate_UnloadingRow(object sender, DataGridRowEventArgs e)
        {
            //int rowIndex = 0;
            //foreach (var item in this.ItemsSource)
            //{
            //    FrameworkElement fe = this.Columns.First().GetCellContent(item);
            //    DataGridRow.GetRowContainingElement(fe).Header = (++rowIndex).ToString();
            //}
        }

        #region     ------设置显示样式

        /// <summary>
        /// 显示样式
        /// </summary>
        public void U_TranslateDataGridViewStyle(string FormName, string XmlTableName, List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listFIELD_DESCRIPTION, bool AllowEdit)
        {
            CustomerDescriptions cusDescription = new CustomerDescriptions();
            //加载描述数据
            using (DataTable tableFieldDescription = string.IsNullOrEmpty(FormName) ? cusDescription.GetStyleDataTable(XmlTableName) : cusDescription.GetFormStyleDataTable(FormName, XmlTableName))
            {
                //清空所有列
                this.Columns.Clear();

                //根据序号排序并添加列显示
                DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                var queryField = from row in arRowField orderby Convert.ToInt32(row["Order"].ToString()) select row;

                //判断控件类型设置显示列样式
                foreach (DataRow rowField in queryField)
                {
                    try
                    {
                        //判断控件类型
                        string strControlType = rowField["ControlType"].ToString().ToLower();
                        if (!AllowEdit)
                            AllowEdit = !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"]));
                        switch (strControlType.ToLower())
                        {
                            case "checkbox":
                                this.U_AddCheckBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            case "combobox":
                                this.U_AddComboBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            case "elementcombox":
                                this.U_AddEelementComboBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            default:
                                this.U_AddTextBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception(string.Format("U_TranslateDataGridViewStyle():{0}," + ex.Message, rowField["Column"].ToString()), ex);
                    }
                }

                //追加列的描述
                this.AppenFieldsDataGridViewStyle(tableFieldDescription, listFIELD_DESCRIPTION, AllowEdit);
            }
        }

        /// <summary>
        /// 添加追加的列描述
        /// </summary>
        private void AppenFieldsDataGridViewStyle(DataTable tableFieldDescription, List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listFIELD_DESCRIPTION, bool AllowEdit)
        {
            //判断列表是否空
            if (listFIELD_DESCRIPTION == null)
                return;

            //添加新行
            DataRow rowDescription = tableFieldDescription.NewRow();

            //追加描述列
            foreach (SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION in listFIELD_DESCRIPTION)
            {
                try
                {
                    //赋值
                    rowDescription["Column"] = mFIELD_DESCRIPTION.Column;
                    rowDescription["Header"] = mFIELD_DESCRIPTION.Header;
                    rowDescription["DbType"] = mFIELD_DESCRIPTION.DbType;
                    rowDescription["Validation"] = mFIELD_DESCRIPTION.Validation;
                    rowDescription["DefaultValue"] = mFIELD_DESCRIPTION.DefaultValue;
                    rowDescription["ReadOnly"] = Convert.ToInt32(mFIELD_DESCRIPTION.ReadOnly);
                    rowDescription["ControlType"] = mFIELD_DESCRIPTION.ControlType;
                    rowDescription["Order"] = mFIELD_DESCRIPTION.Order;
                    rowDescription["DataBind"] = mFIELD_DESCRIPTION.DataBind;
                    rowDescription["Remark"] = mFIELD_DESCRIPTION.Remark;

                    //判断类型
                    switch (mFIELD_DESCRIPTION.ControlType.ToLower())
                    {
                        case "checkbox":
                            this.U_AddCheckBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        case "combobox":
                            this.U_AddComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        case "elementcombox":
                            this.U_AddEelementComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        default:
                            this.U_AddTextBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception(string.Format("AppenFieldsDataGridViewStyle():{0}," + ex.Message, rowDescription["Column"].ToString()), ex);
                }
            }
        }

        /// <summary>
        /// 添加追加的列描述
        /// </summary>
        public void U_TranslateDataGridViewStyle(string TableName, List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listFIELD_DESCRIPTION, bool AllowEdit)
        {
            //判断列表是否空
            if (listFIELD_DESCRIPTION == null)
                return;

            CustomerDescriptions cusDescription = new CustomerDescriptions();
            //加载描述数据
            using (DataTable tableFieldDescription = cusDescription.GetStyleDataTable(TableName))
            {
                //清除行，值获得结构
                tableFieldDescription.Rows.Clear();

                //添加新行
                DataRow rowDescription = tableFieldDescription.NewRow();

                //追加描述列
                foreach (SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION in listFIELD_DESCRIPTION)
                {
                    try
                    {
                        //赋值
                        rowDescription["Column"] = mFIELD_DESCRIPTION.Column;
                        rowDescription["Header"] = mFIELD_DESCRIPTION.Header;
                        rowDescription["DbType"] = mFIELD_DESCRIPTION.DbType;
                        rowDescription["Validation"] = mFIELD_DESCRIPTION.Validation;
                        rowDescription["DefaultValue"] = mFIELD_DESCRIPTION.DefaultValue;
                        rowDescription["ReadOnly"] = Convert.ToInt32(mFIELD_DESCRIPTION.ReadOnly);
                        rowDescription["ControlType"] = mFIELD_DESCRIPTION.ControlType;
                        rowDescription["Order"] = mFIELD_DESCRIPTION.Order;
                        rowDescription["DataBind"] = mFIELD_DESCRIPTION.DataBind;
                        rowDescription["Remark"] = mFIELD_DESCRIPTION.Remark;

                        //判断类型
                        switch (mFIELD_DESCRIPTION.ControlType.ToLower())
                        {
                            case "checkbox":
                                this.U_AddCheckBoxColumnStyle(rowDescription, AllowEdit, -1);
                                break;
                            case "combobox":
                                this.U_AddComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                                break;
                            case "elementcombox":
                                this.U_AddEelementComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                                break;

                            default:
                                this.U_AddTextBoxColumnStyle(rowDescription, AllowEdit, -1);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception(string.Format("U_TranslateDataGridViewStyle():{0}," + ex.Message, rowDescription["Column"].ToString()), ex);
                    }
                }
            }
        }

        /// <summary>
        /// 设置数据源列的默认值
        /// </summary>
        public void U_SetDataTableConstraints(string XmlTableName)
        {
            if (this.ItemsSource == null)
                return;

            //获得数据源
            using (DataTable tableSource = (this.ItemsSource as DataView).Table)
            {
                //加载描述数据
                CustomerDescriptions cusDescription = new CustomerDescriptions();
                using (DataTable tableFieldDescription = this._windowName == null ? cusDescription.GetStyleDataTable(XmlTableName) : cusDescription.GetFormStyleDataTable(this._windowName, XmlTableName))
                {
                    //获得数组
                    DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var query = from qRow in arRowField where qRow["Header"].ToString().Length > 0 || qRow["Column"].ToString() == "USER_PASSWORD" select qRow;

                    //设置每个列的默认值
                    foreach (DataRow rowField in query)
                    {
                        DataColumn column = tableSource.Columns[rowField["Column"].ToString()];
                        if (column == null)
                            continue;
                        try
                        {
                            //判断默认值是否存在
                            if (rowField.Table.Columns.Contains("DefaultValue") && !rowField.IsNull("DefaultValue") && !String.IsNullOrEmpty(rowField["DefaultValue"].ToString()))
                            {
                                //设置默认值
                                column.DefaultValue = rowField["DefaultValue"];
                                
                            }

                            //判断是否存在校验
                            if (rowField.Table.Columns.Contains("Validation") && !rowField.IsNull("Validation") && !String.IsNullOrEmpty(rowField["Validation"].ToString()))
                            {
                                //获得校验规则
                                string[] arStringValidate = rowField["Validation"].ToString().ToLower().Split('|');
                                foreach (string strValidation in arStringValidate)
                                {
                                    //设置校验约束
                                    //非空值
                                    if (strValidation.Contains("nonenull"))
                                    {
                                        column.AllowDBNull = false;
                                    }
                                    //值唯一
                                    if (strValidation.Contains("unique"))
                                    {
                                        column.Unique = true;
                                    }
                                    //最大长度
                                    if (strValidation.Contains("maxlength"))
                                    {
                                        string[] arValidateLength = strValidation.Split('=');
                                        if (arValidateLength.Length == 2)
                                        {
                                            column.MaxLength = Convert.ToInt32(strValidation.Split('=')[1]);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            string strResult = ex.Message;
                            continue;
                        }
                    }
                }
            }
        }

        #region     ------添加DataGridView列和样式

        /// <summary>
        /// 添加CheckBox列
        /// </summary>
        internal DataGridColumn U_AddCheckBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridCheckBoxColumn dgv = new DataGridCheckBoxColumn();
            dgv.IsThreeState = false;

            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                bind.TargetNullValue = "0";
                dgv.Binding = bind;
            }

            //添加列样式
            this.U_AddDataColumnStyle(dgv, rowField, AllowEdit, Index);
            return dgv;
        }

        /// <summary>
        /// 添加TextBox列
        /// </summary>
        internal DataGridColumn U_AddTextBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridTextColumn dgv = new DataGridTextColumn();

            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;

                
                dgv.Binding = bind;
                
            }

            //添加列样式
            this.U_AddDataColumnStyle(dgv, rowField, AllowEdit, Index);
            return dgv;
        }

        /// <summary>
        /// 添加ComboBox列
        /// </summary>
        internal DataGridColumn U_AddComboBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridComboBoxColumn dgvCmbCol = new DataGridComboBoxColumn();
            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                dgvCmbCol.SelectedValueBinding = bind;
            }

            dgvCmbCol.DisplayMemberPath = "NAME".ToLower();
            dgvCmbCol.SelectedValuePath = "VALUE".ToLower();
            dgvCmbCol.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;

            //添加列样式
            this.U_AddDataColumnStyle(dgvCmbCol, rowField, AllowEdit, Index);
            return dgvCmbCol;
        }

        /// <summary>
        /// 添加EelementBox列
        /// </summary>
        internal DataGridColumn U_AddEelementComboBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            List<string> listItem = new List<string>();
            DataGridComboBoxColumn dgvElementCmbCol = new DataGridComboBoxColumn();

            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                dgvElementCmbCol.TextBinding = bind;
            }

            foreach (string s in rowField["DataBind"].ToString().Split('|'))
            {
                //分解字符串添加ITEM数据
                if (s.TrimEnd().Length > 0)
                {
                    listItem.Add(s.TrimEnd());
                }
            }
            dgvElementCmbCol.ItemsSource = listItem;
            //添加列样式
            this.U_AddDataColumnStyle(dgvElementCmbCol, rowField, AllowEdit, Index);
            return dgvElementCmbCol;
        }




        /// <summary>
        /// 添加列并设置列样式
        /// </summary>
        internal void U_AddDataColumnStyle(DataGridColumn dgvCol, DataRow rowField, bool AllowEdit, int Index)
        {
            //添加列
            if (!this.Columns.Contains(dgvCol))
            {
                //是否显示
                dgvCol.Visibility = string.IsNullOrEmpty(rowField["Header"].ToString()) ? System.Windows.Visibility.Hidden : Visibility.Visible;

                //判断是否可见
                if (AllowEdit)
                {
                    if (dgvCol.Visibility == System.Windows.Visibility.Visible)
                    {
                        //是否是否可读
                        if (rowField.Table.Columns.Contains("ReadOnly"))
                        {
                            dgvCol.IsReadOnly = (!rowField.IsNull("ReadOnly") && Convert.ToInt32(rowField["ReadOnly"].ToString()) == 1);
                        }
                        else
                        {
                            dgvCol.IsReadOnly = !AllowEdit;
                        }
                    }
                    else
                    {
                        if (rowField.Table.Columns.Contains("ReadOnly"))
                        {
                            dgvCol.IsReadOnly = (!rowField.IsNull("ReadOnly") && Convert.ToInt32(rowField["ReadOnly"].ToString()) == 1);
                        }
                        else
                        {
                            dgvCol.IsReadOnly = !AllowEdit;
                        }
                    }
                }
                else
                {
                    dgvCol.IsReadOnly = !AllowEdit;
                }

                //设置标题，可编辑时，特殊显示
                dgvCol.Header = (dgvCol.IsReadOnly || dgvCol.Visibility == System.Windows.Visibility.Hidden) ? rowField["Header"].ToString() : string.Format("[{0}]", rowField["Header"].ToString());

                //add column
                if (Index < 0)
                {
                    this.Columns.Add(dgvCol);
                }
                else
                {
                    this.Columns.Insert(Index, dgvCol);
                }
            }
        }

        #endregion

        #endregion

        #region     ------拆分、组合属性列

        /// <summary>
        /// 获得拆分属性数据源
        /// </summary>
        private DataTable GetGoodsPropertyTable()
        {
            DataTable tableSplitSource = MainApp.I_DatabaseService.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", strSplitPropertyKey));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            //tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Field";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";

            return tableSplitSource;
        }

        /// <summary>
        /// 加载拆分列
        /// </summary>
        public void U_TranslateSplitColumnStyles(DataTable tableSource)
        {
            //检查是否设置拆分列
            if (strSplitPropertyKey.Length == 0)
                return;

            if (this.Columns.Count(r => r is DataGridTextColumn && (((r as DataGridTextColumn).Binding as Binding).Path as PropertyPath).Path == strSplitColumnName) == 0)
                return;

            //获得该列
            DataGridColumn columnProperty = this.Columns.First(r => r is DataGridTextColumn && (((r as DataGridTextColumn).Binding as Binding).Path as PropertyPath).Path == strSplitColumnName);
            
            
            ////获得索引
            int index = this.Columns.IndexOf(columnProperty);

            bool allowEdit = !columnProperty.IsReadOnly;

            this.Columns.RemoveAt(index);
            //获得拆分数据源
            tableSplit = GetGoodsPropertyTable();

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow orderby rowSplit["GOODS_PROPERTY_ORDER"] descending select rowSplit;
                StringBuilder strBuilderSplit = new StringBuilder();

                //获得属性列索引
                foreach (DataRow row in query)
                {
                    if (columnProperty == null)
                        continue;

                    DataGridColumn column = null;
                    //判断控件类型
                    string strControlType = row["ControlType"].ToString().ToLower();
                    switch (strControlType)
                    {
                        case "checkbox":
                            column = this.U_AddCheckBoxColumnStyle(row, allowEdit, index);
                            break;
                        case "combobox":
                            column = this.U_AddComboBoxColumnStyle(row, allowEdit, index);
                            break;
                        case "elementcombobox":
                            column = this.U_AddEelementComboBoxColumnStyle(row, allowEdit, index);
                            break;
                        default:
                            column = this.U_AddTextBoxColumnStyle(row, allowEdit, index);
                            break;
                    }

                    //添加DataTable列
                    if (!tableSource.Columns.Contains(row["Column"].ToString()))
                    {
                        tableSource.Columns.Add(row["Column"].ToString(), typeof(System.String));
                    }
                }
            }
        }

        /// <summary>
        /// 解析数据源,解析组合属性，设置各个属性值
        /// </summary>
        public void U_TanslateSplitDataRow(DataRow rowSource)
        {
            //检查是否设置拆分列
            if (rowSource == null || strSplitColumnName.Length == 0 || strSplitPropertyKey.Length == 0 || strSplitPropertyType.Length == 0)
                return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow orderby Convert.ToInt32(rowSplit["SYS_PROPERTY_ORDER"]) select rowSplit;

                //获得数据源表
                using (DataTable tableSource = rowSource.Table)
                {
                    //获得属性列索引
                    foreach (DataRow row in query)
                    {
                        //添加DataTable列
                        if (!tableSource.Columns.Contains(row["Column"].ToString()))
                        {
                            tableSource.Columns.Add(row["Column"].ToString(), typeof(System.String));
                        }
                    }

                    //合并组合各个属性值
                    int i = 0;
                    string[] arStrProperty = rowSource[strSplitColumnName].ToString().Split('|');
                    foreach (DataRow rowProperty in query)
                    {
                        rowSource[rowProperty["Column"].ToString()] = (i < arStrProperty.Length) ? arStrProperty[i] : string.Empty;
                        i++;
                    }
                }
            }
        }

        /// <summary>
        /// 解析数据源,解析组合属性，设置各个属性值
        /// </summary>
        public void U_TanslateSplitDataTable(DataTable tableSource, bool HasChanges)
        {
            //检查是否设置拆分列
            if (strSplitPropertyKey.Length == 0)
                return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow orderby rowSplit["GOODS_PROPERTY_ORDER"] select rowSplit;

                //获得属性列索引
                foreach (DataRow row in query)
                {
                    //添加DataTable列
                    if (!tableSource.Columns.Contains(row["Column"].ToString()))
                    {
                        tableSource.Columns.Add(row["Column"].ToString(), typeof(System.String));
                    }
                }

                //判断是否存在记录
                //if (tableSource.Rows.Count > 0)
                //{
                //    //解析DataTable中的行数据
                //    DataRow[] arRow = tableSource.Rows.Cast<DataRow>().ToArray<DataRow>();
                //    var queryRow = from row in arRow where (HasChanges ? (row.RowState != DataRowState.Unchanged) : (row.RowState == DataRowState.Unchanged)) select row;

                //    //合并组合各个属性值
                //    foreach (DataRow rowSource in queryRow)
                //    {
                //        int i = 0;
                //        string[] arStrProperty = rowSource[strSplitColumnName].ToString().Split('|');
                //        foreach (DataRow rowProperty in query)
                //        {
                //            rowSource[rowProperty["Column"].ToString()] = (i < arStrProperty.Length) ? arStrProperty[i] : string.Empty;
                //            i++;
                //        }
                //    }
                //}
            }
        }

        /// <summary>
        /// 更新组合属性数据源,将各个属性值组合并成属性字符串
        /// </summary>
        public void U_CombinSplitPropertyTable()
        {
            //检查是否设置拆分列
            //if (strSplitColumnName.Length == 0 || strSplitPropertyKey.Length == 0 || strSplitPropertyType.Length == 0)
            return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //获得数据源
                DataView dataViewSource = (this.ItemsSource as DataView);
                if (dataViewSource != null)
                {
                    //转化为数组
                    DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                    //排序
                    var query = from rowSplit in arSplitRow orderby Convert.ToInt32(rowSplit["SYS_PROPERTY_ORDER"]) select rowSplit;

                    //遍历每一行设置组合属性的值
                    DataRow[] arDataRow = dataViewSource.Table.Rows.Cast<DataRow>().ToArray();
                    //查找数据更改的行并遍历
                    var querySource = from rowQuery in arDataRow where rowQuery.RowState == DataRowState.Added || rowQuery.RowState == DataRowState.Detached || rowQuery.RowState == DataRowState.Modified select rowQuery;
                    foreach (DataRow rowSource in querySource)
                    {
                        StringBuilder strBuilderSplit = new StringBuilder();
                        foreach (DataRow row in query)
                        {
                            //拆分属性值
                            string strPropertyCode = row["Column"].ToString();
                            //获得默认值
                            string strDefaultValue = row.IsNull("DefaultValue") ? string.Empty : row["DefaultValue"].ToString();
                            //获得属性值
                            string strPropertyValue = rowSource.IsNull(strPropertyCode) ? strDefaultValue : rowSource[strPropertyCode].ToString();

                            if (strBuilderSplit.Length == 0)
                                strBuilderSplit.Append(strPropertyValue);
                            else
                                strBuilderSplit.Append('|').Append(strPropertyValue);
                        }

                        //获得组合后的值
                        rowSource[strSplitColumnName] = strBuilderSplit.ToString();
                    }
                }
            }
        }

        /// <summary>
        /// 更新组合属性数据源,将各个属性值组合并成属性字符串
        /// </summary>
        public void U_CombinSplitPropertyRow(DataRow rowSource)
        {
            //检查是否设置拆分列
            if (rowSource == null || strSplitColumnName.Length == 0 || strSplitPropertyKey.Length == 0 || strSplitPropertyType.Length == 0)
                return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //获得数据源
                using (DataTable tableSource = rowSource.Table)
                {
                    //转化为数组
                    DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();
                    //排序
                    var query = from rowSplit in arSplitRow orderby Convert.ToInt32(rowSplit["SYS_PROPERTY_ORDER"]) select rowSplit;

                    StringBuilder strBuilderSplit = new StringBuilder();
                    foreach (DataRow row in query)
                    {
                        //拆分属性值
                        string strPropertyCode = row["Column"].ToString();
                        //获得默认值
                        string strDefaultValue = row.IsNull("DefaultValue") ? string.Empty : row["DefaultValue"].ToString();
                        //获得属性值
                        string strPropertyValue = rowSource.IsNull(strPropertyCode) ? strDefaultValue : rowSource[strPropertyCode].ToString();

                        if (strBuilderSplit.Length == 0)
                            strBuilderSplit.Append(strPropertyValue);
                        else
                            strBuilderSplit.Append('|').Append(strPropertyValue);
                    }

                    //获得组合后的值
                    rowSource[strSplitColumnName] = strBuilderSplit.ToString();
                }
            }
        }

        /// <summary>
        /// 校验拆分属性填写是否合法
        /// </summary>
        public bool U_CheckSplitProperty(string ColumnName, out string sResult)
        {
            bool boolResult = true;
            sResult = string.Empty;

            //判断数据源是否空
            if (this.ItemsSource != null)
            {
                //获得数据源
                using (DataTable tableSource = (this.ItemsSource as DataView).Table.GetChanges(DataRowState.Added | DataRowState.Detached | DataRowState.Modified))
                {
                    if (tableSource != null)
                    {
                        //遍历所有行，检查是否合法
                        foreach (DataRow rowSource in tableSource.Rows)
                        {
                            //遍历拆分属性
                            foreach (DataRow rowSplit in tableSplit.Rows)
                            {
                                //检查是否空
                                string strPropertyCode = rowSplit["Column"].ToString();
                                //判断是否允许空
                                if (!rowSplit.IsNull("Validation") && rowSplit["Validation"].ToString() == "1")
                                {
                                    //判断属性值是否空
                                    if (rowSource.IsNull(strPropertyCode) || rowSource[strPropertyCode].ToString().Length == 0)
                                    {
                                        boolResult = false;
                                        sResult = string.Format("{0},{1}的属性值不允许空！", rowSplit["Validation"].ToString(), rowSplit["header"].ToString());
                                        return boolResult;
                                    }
                                }

                                //判断是否唯一
                                //if (!rowSplit.IsNull("IsUnique") && Convert.ToInt32(rowSplit["IsUnique"]) == 1)
                                //{ 
                                //判断是否存在类似的库存信息
                                //if (MainApp.I_BaseService.Exist("STORAGE_LIST", string.Format("{0} LIKE '%{1}%'",ColumnName,rowSource[strPropertyCode].ToString())))
                                //{
                                //    sResult = string.Format("请检查{0}是否已经存在！", rowSplit["header"].ToString());
                                //    boolResult = false;
                                //    return boolResult;
                                //}
                                //}
                            }
                        }
                    }
                }
            }
            return boolResult;
        }

        #endregion

        #region     ------CheckBox Column

        /// <summary>
        /// 向DataGridView中插入CheckBox列
        /// </summary>
        public void U_AddCheckBoxDataGridColumn()
        {
            //判断是否已经存在
            if (this.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) == 0)
            {
                DataGridCheckBoxColumn chkBoxCol = new DataGridCheckBoxColumn();
                chkBoxCol.Header = strCheckedColumnName;
                chkBoxCol.IsThreeState = false;
                chkBoxCol.CanUserReorder = false;
                this.Columns.Insert(0, chkBoxCol);
            }
        }

        /// <summary>
        /// 获得选中的数据行
        /// </summary>
        public DataRowView[] U_GetCheckDataRow()
        {
            List<DataRowView> listDataRow = new List<DataRowView>();
            if (this.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                foreach (object item in this.Items)
                {
                    if (item is DataRowView)
                    {
                        DataRowView viewRow = item as DataRowView;

                        DataGridColumn col = this.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                        CheckBox chkBox = col.GetCellContent(viewRow) as CheckBox;
                        if (chkBox != null && chkBox.IsChecked == true)
                        {
                            listDataRow.Add(viewRow);
                        }
                    }
                }
            }
            return listDataRow.ToArray();
        }

        /// <summary>
        /// 全选所有行
        /// </summary>
        public void U_CheckedAllRows(bool IsChecked)
        {
            if (this.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                foreach (object item in this.Items)
                {
                    if (item is DataRowView)
                    {
                        DataRowView viewRow = item as DataRowView;
                        DataGridColumn col = this.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                        CheckBox chkBox = col.GetCellContent(viewRow) as CheckBox;
                        if (chkBox != null)
                        {
                            chkBox.IsChecked = IsChecked;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置行是否选择
        /// </summary>
        public void U_CheckedRow(bool IsChecked, DataRowView rowViewChecked)
        {
            if (this.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                DataGridColumn col = this.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                CheckBox chkBox = col.GetCellContent(rowViewChecked) as CheckBox;
                if (chkBox != null)
                {
                    chkBox.IsChecked = IsChecked;
                }
            }
        }

        #endregion

        /// <summary>
        /// 结束编辑操作
        /// </summary>
        public void U_EndCurrentEdit()
        {
            //结束编辑
            this.CommitEdit(DataGridEditingUnit.Row, true);
        }

        /// <summary>
        /// 根据Xml文件，获得该数据表的第一个字段作为默认字段
        /// </summary>
        public string U_GetDefaultOrderColumn(string TableName)
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(MainApp.File_FieldDescription_Path);
            XmlNode xn = xmlDoc.SelectSingleNode(string.Format(@"/Tables/Table[@Name='{0}']", TableName));
            if (xn == null)
                throw new Exception(string.Format("未定义{0}的描述信息！", TableName));
            return xn.ChildNodes[0].Attributes["Column"].Value;
        }

        /// <summary>
        /// 根据条件选定DataGrid的行
        /// </summary>
        public bool U_SelectDataGridViewRow(string ColumnName, string CellValue)
        {
            bool finded = false;
            if (this.ItemsSource is DataView)
            {
                object[] arItems = this.Items.Cast<object>().ToArray();
                var query = from item in arItems where item is DataRowView && (item as DataRowView)[ColumnName].ToString() == CellValue select item;
                if (query.Count() > 0)
                {
                    this.SelectedItem = query.First();
                }
            }
            return finded;
        }
    }
}
