﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="STORAGE_LOCK" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="STORAGE_LOCK" type="SiaSun.LMS.Model.STORAGE_LOCK, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SELECTRESULT" class="STORAGE_LOCK">
      <result property="STORAGE_LOCK_ID" column="storage_lock_id" />
      <result property="STORAGE_LIST_ID" column="storage_list_id" />
      <result property="PLAN_LIST_ID" column="plan_list_id" />
      <result property="STORAGE_LOCK_QUANTITY" column="storage_lock_quantity" />
      <result property="STORAGE_LOCK_FLAG" column="storage_lock_flag" />
      <result property="DETAIL_FLAG" column="detail_flag" />
      <result property="STORAGE_LOCK_REMARK" column="storage_lock_remark" />
      <result property="LOCK_CODE" column="lock_code" />
      <result property="BACKUP_FIELD1" column="backup_field1" />
      <result property="BACKUP_FIELD2" column="backup_field2" />
      <result property="BACKUP_FIELD3" column="backup_field3" />
      <result property="BACKUP_FIELD4" column="backup_field4" />
      <result property="BACKUP_FIELD5" column="backup_field5" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="STORAGE_LOCK_SELECT" parameterClass="int" resultMap="SELECTRESULT">
      Select
      storage_lock_id,
      storage_list_id,
      plan_list_id,
      storage_lock_quantity,
      storage_lock_flag,
      detail_flag,
      storage_lock_remark,
      lock_code,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      From STORAGE_LOCK
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SELECTRESULT">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_STORAGE_LIST_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SELECTRESULT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_list_id=#STORAGE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SELECTRESULT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id=#PLAN_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    

    <insert id="STORAGE_LOCK_INSERT" parameterClass="STORAGE_LOCK">
      Insert Into STORAGE_LOCK (
      storage_lock_id,
      storage_list_id,
      plan_list_id,
      storage_lock_quantity,
      storage_lock_flag,
      detail_flag,
      storage_lock_remark,
      lock_code,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      )Values(
      #STORAGE_LOCK_ID#,
      #STORAGE_LIST_ID#,
      #PLAN_LIST_ID#,
      #STORAGE_LOCK_QUANTITY#,
      #STORAGE_LOCK_FLAG#,
      #DETAIL_FLAG#,
      #STORAGE_LOCK_REMARK#,
      #LOCK_CODE#,
      #BACKUP_FIELD1#,
      #BACKUP_FIELD2#,
      #BACKUP_FIELD3#,
      #BACKUP_FIELD4#,
      #BACKUP_FIELD5#
      )
      <!--<selectKey  resultClass="int" type="post" property="STORAGE_LOCK_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="STORAGE_LOCK_UPDATE" parameterClass="STORAGE_LOCK">
      Update STORAGE_LOCK Set
      <!--storage_lock_id=#STORAGE_LOCK_ID#,-->
      storage_list_id=#STORAGE_LIST_ID#,
      plan_list_id=#PLAN_LIST_ID#,
      storage_lock_quantity=#STORAGE_LOCK_QUANTITY#,
      storage_lock_flag=#STORAGE_LOCK_FLAG#,
      detail_flag=#DETAIL_FLAG#,
      storage_lock_remark=#STORAGE_LOCK_REMARK#,
      lock_code=#LOCK_CODE#,
      backup_field1=#BACKUP_FIELD1#,
      backup_field2=#BACKUP_FIELD2#,
      backup_field3=#BACKUP_FIELD3#,
      backup_field4=#BACKUP_FIELD4#,
      backup_field5=#BACKUP_FIELD5#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="STORAGE_LOCK_DELETE" parameterClass="int">
      Delete From STORAGE_LOCK
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>