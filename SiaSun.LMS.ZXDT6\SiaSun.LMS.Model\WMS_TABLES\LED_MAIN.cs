﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// LED_MAIN
	/// </summary>
	[Serializable]
    [DataContract]
	public class LED_MAIN
	{
        public LED_MAIN()
		{
		}

        private int _led_id;
        private string _led_ip;
        private string _device_code;
        private int _control_type;
        private int _screen_width;
        private int _screen_height;
        private int _line_num;
        private string _auto_flag;
        private string _led_status;
        private string _led_main_remark;
        private string _led_main_para1;
        private string _led_main_para2;
        private string _led_main_para3;
        private string _led_main_para4;
        private string _led_main_para5;

		
		///<sumary>
		/// 索引
		///</sumary>
        [DataMember]
        public int LED_ID
		{
            get { return _led_id; }
            set { _led_id = value; }
		}

        ///<sumary>
        /// 屏幕IP
        ///</sumary>
        [DataMember]
        public string LED_IP
        {
            get { return _led_ip; }
            set { _led_ip = value; }
        }

        ///<sumary>
        /// 设备编号:多个设备共用一块屏幕，用|分隔
        ///</sumary>
        [DataMember]
        public string DEVICE_CODE
        {
            get { return _device_code; }
            set { _device_code = value; }
        }
        ///<sumary>
        /// 控制卡型号
        ///</sumary>
        [DataMember]
        public int CONTROL_TYPE
        {
            get { return _control_type; }
            set { _control_type = value; }
        }
        ///<sumary>
        /// 屏幕宽度
        ///</sumary>
        [DataMember]
        public int SCREEN_WIDTH
        {
            get { return _screen_width; }
            set { _screen_width = value; }
        }

        ///<sumary>
        /// 屏幕高度
        ///</sumary>
        [DataMember]
        public int SCREEN_HEIGHT
        {
            get { return _screen_height; }
            set { _screen_height = value; }
        }

        ///<sumary>
        /// 屏幕行数
        ///</sumary>
        [DataMember]
        public int LINE_NUM
        {
            get { return _line_num; }
            set { _line_num = value; }
        }

        ///<sumary>
        /// 是否开启发送大屏的服务，1开启，0关闭
        ///</sumary>
        [DataMember]
        public string AUTO_FLAG
        {
            get { return _auto_flag; }
            set { _auto_flag = value; }
        }

        ///<sumary>
        /// 发送标识，1已发送，0未发送
        ///</sumary>
        [DataMember]
        public string LED_STATUS
        {
            get { return _led_status; }
            set { _led_status = value; }
        }

        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string LED_MAIN_REMARK
        {
            get { return _led_main_remark; }
            set { _led_main_remark = value; }
        }

        ///<sumary>
        /// 参数1
        ///</sumary>
        [DataMember]
        public string LED_MAIN_PARA1
        {
            get { return _led_main_para1; }
            set { _led_main_para1 = value; }
        }

        ///<sumary>
        /// 参数2
        ///</sumary>
        [DataMember]
        public string LED_MAIN_PARA2
        {
            get { return _led_main_para2; }
            set { _led_main_para2 = value; }
        }

        ///<sumary>
        /// 参数3
        ///</sumary>
        [DataMember]
        public string LED_MAIN_PARA3
        {
            get { return _led_main_para3; }
            set { _led_main_para3 = value; }
        }

        ///<sumary>
        /// 参数4
        ///</sumary>
        [DataMember]
        public string LED_MAIN_PARA4
        {
            get { return _led_main_para4; }
            set { _led_main_para4 = value; }
        }

        ///<sumary>
        /// 参数5
        ///</sumary>
        [DataMember]
        public string LED_MAIN_PARA5
        {
            get { return _led_main_para5; }
            set { _led_main_para5= value; }
        }
		
	}
}
