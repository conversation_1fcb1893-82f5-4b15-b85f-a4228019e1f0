﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 预充后入干燥静置库前上报组盘信息申请
    /// </summary>
    public class ApplyCreateTask : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyVerify.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.WH_CELL START_WH_CELL = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE);
                if (START_WH_CELL == null)
                {
                    bResult = false;
                    sResult = string.Format("申请站台未找到_传入值[{0}]",mIO_CONTROL_APPLY.DEVICE_CODE);
                    return bResult;
                }

                IList<Model.PLAN_MAIN> lsPLAN_MAIN = this._P_PLAN_MAIN.GetList_PLAN_STATUS(Enum.PLAN_STATUS.Executing.ToString());
                if (lsPLAN_MAIN == null || lsPLAN_MAIN.Count != 1)
                {
                    bResult = false;
                    sResult = string.Format("当前正在执行的计划数量不为1_请检查计划执行情况");
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = lsPLAN_MAIN[0];

                IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID);
                if (lsPLAN_LIST == null || lsPLAN_LIST.Count != 1)
                {
                    bResult = false;
                    sResult = string.Format("计划包含的计划单数量不为1_请检查计划是否制定错误_计划单号[{0}]", mPLAN_MAIN.PLAN_CODE);
                    return bResult;
                }

                string[] arBoxBarcode = mIO_CONTROL_APPLY.STOCK_BARCODE.Split('_');
                string[] arGoodsBarcodeCombine = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.Split('_');

                if(arBoxBarcode.Count()!= arGoodsBarcodeCombine.Count())
                {
                    bResult = false;
                    sResult = string.Format("托盘条码个数[{0}]与电池条码组数量[{1}]不等_托盘条码组[{2}]", arBoxBarcode.Count(), arGoodsBarcodeCombine.Count(), mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return bResult;
                }

                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                if (mMANAGE_MAIN != null)
                {
                    bResult = false;
                    sResult = string.Format("垛条码[{0}]存在任务_无法生成入干燥搁置库的任务", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return bResult;
                }

                Model.STOCK stock = new Model.STOCK();
                stock.BOX_LIST = new List<Model.BOX>();
                stock.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;

                for (int i = 0; i < arBoxBarcode.Count(); i++)
                {
                    Model.BOX box = new Model.BOX();
                    box.GOODS_BARCODE_LIST = new List<string>();
                    box.BOX_BARCODE = arBoxBarcode[i];

                    string[] arGoodsBarcode = arGoodsBarcodeCombine[i].Split('|');
                    foreach (var item in arGoodsBarcode)
                    {
                        box.GOODS_BARCODE_LIST.Add(item);
                    }

                    stock.BOX_LIST.Add(box);
                }

                int manageId = 0;
                bResult = new ManageIn().ManageCreate(mPLAN_MAIN, lsPLAN_LIST[0], stock, START_WH_CELL.CELL_ID, 0, true, false, out manageId, out sResult);

                string[] arrNextStation = this.applyTypeParam.U_NextStation.Split(';');

                var nextStationGroup = from r in arrNextStation
                                       where r.StartsWith(mIO_CONTROL_APPLY.DEVICE_CODE)
                                       select r.Split('-')[1];

                string strNextStation = string.Empty;
                if (nextStationGroup.Count()>0)
                {
                    strNextStation = nextStationGroup.First();
                }

                if (!string.IsNullOrEmpty(strNextStation))
                {
                    //如果配置了下一站台，则直接分配到下一站台
                    bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", strNextStation, "5", out sResult ,false);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                //if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.Waitting.ToString())
                //{
                //    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();
                //    this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                //}
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_Apply1", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyVerify.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
