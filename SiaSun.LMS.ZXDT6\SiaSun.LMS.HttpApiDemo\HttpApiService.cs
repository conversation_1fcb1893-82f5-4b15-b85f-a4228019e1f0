using System;
using Microsoft.Owin.Hosting;

namespace SiaSun.LMS.HttpApiDemo
{
    /// <summary>
    /// HTTP API服务管理类，用于在WinService中集成HTTP API
    /// </summary>
    public class HttpApiService
    {
        private static SimpleLogger _logger = new SimpleLogger("HttpApiService");
        private static IDisposable _httpApiHost;
        private static bool _isRunning = false;
        private static string _baseUrl;

        /// <summary>
        /// 启动HTTP API服务
        /// </summary>
        /// <param name="port">端口号，如果为空则从配置文件读取</param>
        /// <returns>是否启动成功</returns>
        public static bool Start(string port = null)
        {
            try
            {
                if (_isRunning)
                {
                    _logger.Warn("HTTP API服务已经在运行中");
                    return true;
                }

                // 从配置文件读取端口，如果没有则使用默认值
                if (string.IsNullOrEmpty(port))
                {
                    port = SimpleConfig.GetConfig("HttpApiPort", "9001");
                }

                _baseUrl = $"http://localhost:{port}";

                // 从配置文件读取WCF服务地址
                string wcfUrl = SimpleConfig.GetConfig("WcfServiceUrl", "http://127.0.0.1:8001/Service/Demo");
                WcfProxy.SetWcfUrl(wcfUrl);

                _logger.Info($"正在启动HTTP API服务: {_baseUrl}");

                // 启动HTTP API服务
                _httpApiHost = WebApp.Start<Startup>(_baseUrl);

                _isRunning = true;
                _logger.Info($"HTTP API服务启动成功: {_baseUrl}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("HTTP API服务启动失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止HTTP API服务
        /// </summary>
        /// <returns>是否停止成功</returns>
        public static bool Stop()
        {
            try
            {
                if (!_isRunning)
                {
                    _logger.Warn("HTTP API服务未在运行");
                    return true;
                }

                _logger.Info("正在停止HTTP API服务...");

                _httpApiHost?.Dispose();
                _httpApiHost = null;
                _isRunning = false;

                _logger.Info("HTTP API服务已停止");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("HTTP API服务停止失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取服务状态
        /// </summary>
        /// <returns>服务是否正在运行</returns>
        public static bool IsRunning()
        {
            return _isRunning;
        }

        /// <summary>
        /// 获取服务地址
        /// </summary>
        /// <returns>服务地址</returns>
        public static string GetServiceUrl()
        {
            return _baseUrl;
        }

        /// <summary>
        /// 获取服务信息
        /// </summary>
        /// <returns>服务信息</returns>
        public static object GetServiceInfo()
        {
            return new
            {
                isRunning = _isRunning,
                serviceUrl = _baseUrl,
                wcfUrl = WcfProxy.GetWcfUrl(),
                startTime = DateTime.Now,
                version = "1.0.0"
            };
        }
    }
}
