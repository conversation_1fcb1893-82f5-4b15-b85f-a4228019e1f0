### 测试状态接口
GET http://localhost:9001/api/demo/status

### 测试健康检查
GET http://localhost:9001/api/demo/health

### 测试载具请求 - 出库
POST http://localhost:9001/api/demo/carrier
Content-Type: application/json

{
  "carrierId": "C001",
  "action": "OUT",
  "position": "A-01-01",
  "timestamp": "2024-01-15T10:30:00"
}

### 测试载具请求 - 入库
POST http://localhost:9001/api/demo/carrier
Content-Type: application/json

{
  "carrierId": "C002",
  "action": "IN",
  "position": "B-02-05",
  "goods": [
    {
      "goodsId": "G001",
      "quantity": 100
    }
  ]
}

### 测试任务请求 - 拣选任务
POST http://localhost:9001/api/demo/task
Content-Type: application/json

{
  "taskId": "T001",
  "type": "PICK",
  "priority": "HIGH",
  "items": [
    {
      "itemId": "item1",
      "quantity": 10,
      "position": "A-01-01"
    },
    {
      "itemId": "item2",
      "quantity": 5,
      "position": "A-01-02"
    }
  ]
}

### 测试任务请求 - 补货任务
POST http://localhost:9001/api/demo/task
Content-Type: application/json

{
  "taskId": "T002",
  "type": "REPLENISH",
  "priority": "NORMAL",
  "sourcePosition": "B-01-01",
  "targetPosition": "A-01-01",
  "quantity": 50
}

### 测试错误处理 - 无效JSON
POST http://localhost:9001/api/demo/carrier
Content-Type: application/json

{
  "carrierId": "C001",
  "action": "INVALID_ACTION"
  // 故意的语法错误

### 测试错误处理 - 空请求体
POST http://localhost:9001/api/demo/task
Content-Type: application/json