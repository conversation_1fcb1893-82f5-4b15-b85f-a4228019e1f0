﻿using System;
using System.Data;
using System.Globalization;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class SignIn : Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            if (!Page.IsPostBack)
            {
                Page.DataBind();
                Session.Remove("name");
            }
        }
        catch
        {
        }
    }

    protected void tbUserName_Enter(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(this.tbUserName.Text))
            {
                this.tbUserName.Text = string.Empty;
                return;
            }
            if (string.IsNullOrEmpty(this.tbPassword.Text))
            {
                this.tbPassword.Text = string.Empty;
                return;
            }
            bSignIn_Click(null, null);
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void tbPassword_Enter(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(this.tbPassword.Text))
            {
                this.tbPassword.Text = string.Empty;
                return;
            }
            if (string.IsNullOrWhiteSpace(this.tbUserName.Text))
            {
                this.tbUserName.Text = string.Empty;
                return;
            }
            bSignIn_Click(null, null);
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bSignIn_Click(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(this.tbUserName.Text))
            {
                Common.LayerFailed(this, "请输入用户");
                return;
            }
            if (string.IsNullOrEmpty(this.tbPassword.Text))
            {
                Common.LayerFailed(this, "请输入密码");
                return;
            }
            System.SYS_USER su;
            string password = GetMD5(this.tbPassword.Text.Trim());
            if (!Common._I_SystemClient.USER_LOGIN(this.tbUserName.Text.Trim(), password, out su) )
            {
                Common.LayerFailed(this, "登录失败");
                return;
            }
            Session.Remove("name");
            Session.Add("name", su.USER_CODE);
            string url = this.Page.Request.QueryString.Get("url");
            if (string.IsNullOrWhiteSpace(url))
            {
                Common.LayerSuccess(this, "登录成功");
                Response.Redirect("Menu.aspx");
            } 
            else
            {
                Response.Redirect(url);
            }
        }
        catch (ThreadAbortException)
        {
        }
        catch (WebException ex)
        {
            Session.Remove("name");
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch (Exception ex)
        {
            Session.Remove("name");
            Common.LayerFailed(this, ex.Message);
        }
    }

    public string GetMD5(string password)
    {
        MD5 md5 = MD5.Create();

        byte[] t = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < t.Length; i++)
        {
            sb.Append(t[i].ToString("x").PadLeft(2, '0'));
        }

        return sb.ToString().Substring(0, 16);
    }

}
