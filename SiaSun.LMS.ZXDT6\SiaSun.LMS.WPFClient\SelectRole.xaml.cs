﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// Role_Select.xaml 的交互逻辑
    /// </summary>
    public partial class SelectRole : Window
    {
        public SelectRole()
        {
            InitializeComponent();

            this.Loaded += new RoutedEventHandler(SelectRole_Loaded);
        }

        void SelectRole_Loaded(object sender, RoutedEventArgs e)
        {
            grpRole.Visibility = System.Windows.Visibility.Collapsed;

            lbRole.DisplayMemberPath = "ROLE_NAME";

            lbRole.ItemsSource = MainApp.I_SystemService.ROLE_GetList(MainApp._USER.USER_ID);
            switch (this.lbRole.Items.Count)
            {
                case 0:
                    btnCancel_Click(null, null);
                    break;
                case 1:
                    lbRole.SelectedIndex = 0;
                    this.lbRole_MouseDoubleClick(null, null);
                    break;
                default:
                    grpRole.Visibility = System.Windows.Visibility.Visible;
                    break;
            }
        }

        /// <summary>
        /// 双击选择
        /// </summary>
        private void lbRole_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (this.lbRole.SelectedValue == null)
                return;
            MainApp._ROLE = this.lbRole.SelectedValue as Model.SYS_ROLE;
            this.DialogResult = true;
        }

        /// <summary>
        /// 确定选择
        /// </summary>
        private void btnOK_Click(object sender, RoutedEventArgs e)
        {
            lbRole_MouseDoubleClick(null, null);
        }

        /// <summary>
        /// 取消选择,关闭程序
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            if (MainApp._USER.USER_ID == 0)
            {
                MainApp._ROLE = new Model.SYS_ROLE();
                MainApp._ROLE.ROLE_NAME = "超级管理员";
                  
                this.DialogResult = true;
            }
            else
                this.DialogResult = false;
        }

    }
}
