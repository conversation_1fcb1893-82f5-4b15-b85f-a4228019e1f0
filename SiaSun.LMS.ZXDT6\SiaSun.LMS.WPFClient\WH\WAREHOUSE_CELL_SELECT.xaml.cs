﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.WH
{
    /// <summary>
    /// WAREHOUSE_CELL_SELECT.xaml 的交互逻辑
    /// </summary>
    public partial class WAREHOUSE_CELL_SELECT : Window
    {
        string strWAREHOUSE_TYPE = string.Empty;
        Model.WH_CELL mWH_CELL_Selected = null;

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string U_WareHouseType
        {
            get { return this.strWAREHOUSE_TYPE; }
            set { strWAREHOUSE_TYPE = value; }
        }

        /// <summary>
        /// 是否检查货位可用状态
        /// fst add 2018-12-01
        /// </summary>
        bool isCheckCellEnable = true;
        public bool U_CheckCellEnable
        {
            get { return this.isCheckCellEnable; }
            set { isCheckCellEnable = value; }
        }

        /// <summary>
        /// 货位实例对象
        /// </summary>
        public SiaSun.LMS.Model.WH_CELL U_WH_CELL
        {
            get { return mWH_CELL_Selected; }
            set { mWH_CELL_Selected = value; }
        }

        public WAREHOUSE_CELL_SELECT(string _WAREHOUSE_TYPE,Model.WH_CELL _WH_CELL)
        {
            InitializeComponent();

            this.strWAREHOUSE_TYPE = _WAREHOUSE_TYPE;
            this.mWH_CELL_Selected = _WH_CELL;

            this.Height = SystemParameters.PrimaryScreenHeight * 2 / 3;
            this.Width = SystemParameters.PrimaryScreenWidth * 2 / 3;

            this.ucCell.U_SelectCellConfirm += new UC.ucStoreCell.U_SelectCELLConfirmHandler(ucCell_U_SelectCellConfirm);
        }

        void ucCell_U_SelectCellConfirm(Model.WH_CELL ST_CELL)
        {
            if (ST_CELL == null)
                return;

            //fst add 2018-12-01 是否检查货位可用状态
            if (isCheckCellEnable)
            {
                if (this.mWH_CELL_Selected.CELL_STATUS != string.Empty && !this.mWH_CELL_Selected.CELL_STATUS.Contains(ST_CELL.CELL_STATUS) || ST_CELL.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    MainApp._MessageDialog.ShowException("请检查所选货位是否合法");
                    return;
                }
            }

            this.mWH_CELL_Selected = ST_CELL;
            this.DialogResult = true;
            this.Close();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            //this.ucCell.U_WareHouseID = this.intWareHouseID;

            //xcjt add 2016-12-08
            this.ucCell.U_ZoomScale = "80";

            this.ucCell.U_SelectedCell = this.mWH_CELL_Selected;
            this.ucCell.U_InitControl(this.U_WareHouseType);
        }
    }
}
