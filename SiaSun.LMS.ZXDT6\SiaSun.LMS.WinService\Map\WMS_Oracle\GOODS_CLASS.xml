﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="GOODS_CLASS" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="GOODS_CLASS" type="SiaSun.LMS.Model.GOODS_CLASS, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_CLASS">
			<result property="GOODS_CLASS_ID" column="goods_class_id" />
			<result property="GOODS_CLASS_PARENT_ID" column="goods_class_parent_id" />
      <result property="GOODS_TYPE_ID" column="goods_type_id" />
			<result property="GOODS_CLASS_CODE" column="goods_class_code" />
			<result property="GOODS_CLASS_NAME" column="goods_class_name" />
			<result property="GOODS_CLASS_REMARK" column="goods_class_remark" />
			<result property="GOODS_CLASS_ORDER" column="goods_class_order" />
			<result property="GOODS_CLASS_FLAG" column="goods_class_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	   <select id="GOODS_CLASS_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  goods_class_id,
				  goods_class_parent_id,
          goods_type_id,
				  goods_class_code,
				  goods_class_name,
				  goods_class_remark,
				  goods_class_order,
				  goods_class_flag
			From GOODS_CLASS
		</select>
		
		<select id="GOODS_CLASS_SELECT_BY_ID" parameterClass="int" extends = "GOODS_CLASS_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_class_id=#GOODS_CLASS_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="GOODS_CLASS_SELECT_BY_GOODS_CLASS_CODE" parameterClass="int" extends = "GOODS_CLASS_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_class_code=#GOODS_CLASS_CODE#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="GOODS_CLASS_INSERT" parameterClass="GOODS_CLASS">
      Insert Into GOODS_CLASS (
      goods_class_id,
      goods_class_parent_id,
      goods_type_id,
      goods_class_code,
      goods_class_name,
      goods_class_remark,
      goods_class_order,
      goods_class_flag
      )Values(
      #GOODS_CLASS_ID#,
      #GOODS_CLASS_PARENT_ID#,
      #GOODS_TYPE_ID#,
      #GOODS_CLASS_CODE#,
      #GOODS_CLASS_NAME#,
      #GOODS_CLASS_REMARK#,
      #GOODS_CLASS_ORDER#,
      #GOODS_CLASS_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="GOODS_CLASS_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="GOODS_CLASS_UPDATE" parameterClass="GOODS_CLASS">
      Update GOODS_CLASS Set
      <!--goods_class_id=#GOODS_CLASS_ID#,-->
      goods_class_parent_id=#GOODS_CLASS_PARENT_ID#,
      goods_type_id=#GOODS_TYPE_ID#,
      goods_class_code=#GOODS_CLASS_CODE#,
      goods_class_name=#GOODS_CLASS_NAME#,
      goods_class_remark=#GOODS_CLASS_REMARK#,
      goods_class_order=#GOODS_CLASS_ORDER#,
      goods_class_flag=#GOODS_CLASS_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					goods_class_id=#GOODS_CLASS_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="GOODS_CLASS_DELETE" parameterClass="int">
			Delete From GOODS_CLASS
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_class_id=#GOODS_CLASS_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>