using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement.Interface.WMS;
using System;
using System.Text.Json;

namespace SiaSun.LMS.Tests.WMS
{
    [TestClass]
    public class InboundReceiptSyncTests
    {
        private InboundReceiptSync _inboundReceiptSync;

        [TestInitialize]
        public void Setup()
        {
            _inboundReceiptSync = new InboundReceiptSync();
        }

        [TestMethod]
        public void IntefaceMethod_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var validInput = new
            {
                stockTakeResultId = "RESULT001",
                stockTakeResultCode = "RESULT_CODE001",
                stockTakeResultName = "盘点结果",
                isRedStorage = 0,
                money = 1000.50m,
                taxMoney = 1130.57m,
                storageStatus = "COMPLETED",
                storageGoodsSource = "1",
                storageDate = "2025-01-08",
                warehouseId = "WH001",
                warehouseName = "测试仓库",
                storageCode = "IN001",
                storageName = "入库单001",
                contractId = "CONTRACT001",
                contractCode = "CONTRACT_CODE001",
                contractName = "合同001",
                supplierId = "SUPPLIER001",
                supplier = "供应商001",
                supplierPhone = "13800138000",
                buyOrderId = "ORDER001",
                buyOrderCode = "ORDER_CODE001",
                buyOrderName = "采购订单001",
                buyOrderOperatorId = "OPERATOR001",
                buyOrderOperator = "采购员",
                secondParty = "乙方",
                secondPartyId = "PARTY001",
                receiveId = "RECEIVE001",
                receiveCode = "RECEIVE_CODE001",
                receiveName = "收货单001",
                receiveUserId = "USER001",
                receiveUser = "收货员",
                deliveryId = "DELIVERY001",
                deliveryCode = "DELIVERY_CODE001",
                deliveryName = "送货单001",
                checkedId = "CHECK001",
                checkedCode = "CHECK_CODE001",
                checkedName = "检验单001",
                isPass = 1,
                storageMoneyTax = 1130.57m,
                initId = "INIT001",
                storageInfoList = new[]
                {
                    new
                    {
                        contractCode = "CONTRACT_CODE001",
                        orderIndex = "1",
                        redStorageAllNum = 0,
                        isControlledByProductDate = "0",
                        deliveryWarehouseId = "WH001",
                        deliveryWarehouse = "测试仓库",
                        mgDeptName = "管理部门",
                        mgDeptId = "DEPT001",
                        storageId = "STORAGE001",
                        checkedInfoId = "CHECK_INFO001",
                        initId = "INIT001",
                        goodsId = "GOODS001",
                        goodsCode = "GOODS_CODE001",
                        goodsName = "测试物资",
                        goodsType = "原材料",
                        goodsSource = "1",
                        brand = "品牌A",
                        goodsVersion = "规格型号A",
                        goodsAttribute = "属性A",
                        needGoodsNum = 100,
                        contractGoodsNum = 100,
                        addGoodsNum = 0,
                        storageNum = 100,
                        unitId = "UNIT001",
                        unitName = "个",
                        warehouseId = "WH001",
                        warehouseName = "测试仓库",
                        shelfId = "SHELF001",
                        shelfName = "货架001",
                        batch = "BATCH001",
                        taxPrice = 11.31m,
                        taxMoney = 1131.00m,
                        tax = 0.13m,
                        price = 10.00m,
                        money = 1000.00m,
                        orgId = "ORG001",
                        orgName = "组织001",
                        deptId = "DEPT001",
                        deptName = "部门001",
                        bzId = "BZ001",
                        bzName = "班组001",
                        gbId = "GB001",
                        gbName = "工班001",
                        manageDeptId = "MANAGE001",
                        manageDeptName = "管理部门001",
                        lineId = "LINE001",
                        lineName = "线路001",
                        gkDeptId = "GK001",
                        gkDeptName = "归口部门001",
                        localSend = "现场直送",
                        remark = "备注",
                        createDate = "2025-01-08 10:00:00",
                        createUser = "USER001",
                        createName = "创建人",
                        updateDate = "2025-01-08 10:00:00",
                        updateUser = "USER001",
                        updateName = "更新人",
                        id = "ITEM001",
                        status = 1,
                        billCode = "BILL001"
                    }
                },
                createDate = "2025-01-08 10:00:00",
                createUser = "USER001",
                createName = "创建人",
                updateDate = "2025-01-08 10:00:00",
                updateUser = "USER001",
                updateName = "更新人",
                id = "ID001",
                status = 1,
                billCode = "BILL001"
            };

            string inputJson = JsonSerializer.Serialize(validInput);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Length > 0);
            Assert.IsTrue(response.GetProperty("traceId").GetString().Length > 0);
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_StorageCode_ReturnsError()
        {
            // Arrange
            var inputWithMissingStorageCode = new
            {
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
                // storageCode is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingStorageCode);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("storageCode"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_WarehouseId_ReturnsError()
        {
            // Arrange
            var inputWithMissingWarehouseId = new
            {
                storageCode = "IN001",
                storageDate = "2025-01-08",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
                // warehouseId is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingWarehouseId);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("warehouseId"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_StorageDate_ReturnsError()
        {
            // Arrange
            var inputWithMissingStorageDate = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
                // storageDate is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingStorageDate);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("storageDate"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingStorageStatus_ReturnsError()
        {
            // Arrange
            var inputWithMissingStorageStatus = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
                // storageStatus is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingStorageStatus);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("入库状态"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidDateFormat_ReturnsError()
        {
            // Arrange
            var inputWithInvalidDate = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "invalid-date",
                storageStatus = "COMPLETED",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidDate);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("日期格式错误"));
        }

        [TestMethod]
        public void IntefaceMethod_EmptyStorageInfoList_ReturnsError()
        {
            // Arrange
            var inputWithEmptyStorageInfoList = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageStatus = "COMPLETED",
                storageInfoList = new object[0] // Empty array
            };

            string inputJson = JsonSerializer.Serialize(inputWithEmptyStorageInfoList);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("入库明细列表不能为空"));
        }

        [TestMethod]
        public void IntefaceMethod_NullStorageInfoList_ReturnsError()
        {
            // Arrange
            var inputWithNullStorageInfoList = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageStatus = "COMPLETED",
                storageInfoList = (object[])null
            };

            string inputJson = JsonSerializer.Serialize(inputWithNullStorageInfoList);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("入库明细列表不能为空"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidStorageInfoItem_MissingGoodsCode_ReturnsError()
        {
            // Arrange
            var inputWithInvalidStorageInfoItem = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageStatus = "COMPLETED",
                storageInfoList = new[]
                {
                    new
                    {
                        // goodsCode is missing
                        goodsName = "测试物资",
                        storageNum = 100,
                        unitId = "UNIT001"
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidStorageInfoItem);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("入库明细项必填字段"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidStorageInfoItem_ZeroStorageNum_ReturnsError()
        {
            // Arrange
            var inputWithZeroStorageNum = new
            {
                storageCode = "IN001",
                warehouseId = "WH001",
                storageDate = "2025-01-08",
                storageStatus = "COMPLETED",
                storageInfoList = new[]
                {
                    new
                    {
                        goodsCode = "GOODS001",
                        goodsName = "测试物资",
                        storageNum = 0, // Invalid zero quantity
                        unitId = "UNIT001"
                    }
                }
            };

            string inputJson = JsonSerializer.Serialize(inputWithZeroStorageNum);

            // Act
            string result = _inboundReceiptSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("入库明细项必填字段"));
        }
    }
}