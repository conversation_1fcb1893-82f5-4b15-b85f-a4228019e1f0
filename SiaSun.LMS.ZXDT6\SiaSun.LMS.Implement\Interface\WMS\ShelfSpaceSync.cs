using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 货架位同步接口
    /// </summary>
    public class ShelfSpaceSync : InterfaceBase
    {
        class InputParam
        {
            public int isHaveMaterials { get; set; }
            public string belongingPlace { get; set; }
            public string gsId { get; set; }
            public string gsName { get; set; }
            public string lineId { get; set; }
            public string lineName { get; set; }
            public string remark { get; set; }
            public string addressType { get; set; }
            public bool hasGoods { get; set; }
            public string warehouseCharge { get; set; }
            public string warehouseChargeName { get; set; }
            public string warehouseAddress { get; set; }
            public string warehouseType { get; set; }
            public string qrCode { get; set; }
            public int shelfStatus { get; set; }
            public string describe { get; set; }
            public string warehouseCode { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            /// <summary>
            /// 货架名称（CELL_NAME）
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// 货架编码（CELL_CODE）
            /// </summary>
            public string code { get; set; }
            public string sourceType { get; set; }
            public string entityModelId { get; set; }
            public string processDefinitionId { get; set; }
            public string processInstanceId { get; set; }
            public string processInstanceKey { get; set; }
            public object nextProcessUserList { get; set; }
            public string attachmentFileName { get; set; }
            public string attachmentFileUrl { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                // JSON parsing
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    resultCode = 2;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Required field validation
                if (string.IsNullOrEmpty(inputParam.code))
                {
                    resultCode = 2;
                    message = "接口入参必填项[code]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    resultCode = 2;
                    message = "接口入参必填项[name]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate shelf status (should be a valid integer)
                if (inputParam.shelfStatus < 0)
                {
                    resultCode = 2;
                    message = "接口入参[shelfStatus]值无效，必须为非负整数";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Validate warehouse associations
                if (string.IsNullOrEmpty(inputParam.warehouseId))
                {
                    resultCode = 2;
                    message = "接口入参必填项[warehouseId]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.warehouseCode))
                {
                    resultCode = 2;
                    message = "接口入参必填项[warehouseCode]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Database operations - Process shelf space information
                // Since there's no specific shelf model in the current system, we'll use SYS_ITEM
                // which appears to be used for system configuration items including shelves
                
                // Check if shelf already exists by code
                //var existingCell = S_Base.sBase.pWH_CELL.GetModel("1" , $"{inputParam.code}");

                //if (existingCell != null)
                //{
                //    // Update existing shelf space
                //    var shelfItem = existingCell[0];
                //    shelfItem.ITEM_NAME = inputParam.name;
                //    shelfItem.ITEM_REMARK = $"WarehouseId:{inputParam.warehouseId}|WarehouseCode:{inputParam.warehouseCode}|Status:{inputParam.shelfStatus}|QRCode:{inputParam.qrCode}|Desc:{inputParam.describe}|HasGoods:{inputParam.hasGoods}|HasMaterials:{inputParam.isHaveMaterials}";
                //    shelfItem.ITEM_FLAG = inputParam.status.ToString();
                //    shelfItem.ITEM_PARENT_ID = string.IsNullOrEmpty(inputParam.warehouseId) ? 0 : int.TryParse(inputParam.warehouseId, out int warehouseId) ? warehouseId : 0;

                //    S_Base.sBase.pSYS_ITEM.Update(shelfItem);
                //    message = "货架位信息更新成功";
                //}
                //else
                //{
                //    // Create new shelf space
                //    var newShelfSpace = new Model.SYS_ITEM()
                //    {
                //        ITEM_CODE = inputParam.code,
                //        ITEM_NAME = inputParam.name,
                //        ITEM_TYPE = "SHELF_SPACE",
                //        ITEM_REMARK = $"WarehouseId:{inputParam.warehouseId}|WarehouseCode:{inputParam.warehouseCode}|Status:{inputParam.shelfStatus}|QRCode:{inputParam.qrCode}|Desc:{inputParam.describe}|HasGoods:{inputParam.hasGoods}|HasMaterials:{inputParam.isHaveMaterials}",
                //        ITEM_FLAG = inputParam.status.ToString(),
                //        ITEM_ORDER = 0,
                //        ITEM_PARENT_ID = string.IsNullOrEmpty(inputParam.warehouseId) ? 0 : int.TryParse(inputParam.warehouseId, out int warehouseId) ? warehouseId : 0
                //    };

                //    S_Base.sBase.pSYS_ITEM.Add(newShelfSpace);
                //    message = "货架位信息创建成功";
                //}

                message = "货架位信息处理成功";
                
                // Log the operation
                S_Base.sBase.Log.Info($"ShelfSpaceSync处理成功_入参[{inputJson}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"ShelfSpaceSync处理异常_入参[{inputJson}]_异常[{ex.Message}]_traceId[{traceId}]");
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = resultCode,
                    msg = message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = code,
                msg = msg,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}