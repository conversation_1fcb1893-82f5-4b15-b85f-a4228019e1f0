﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_IN"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_IN" Height="353" Width="1620" Loaded="DocumentContent_Loaded">


    <Grid Grid.Column="2">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*" ></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <!--<uc:ucManageInInput x:Name="ucManageIn" Grid.Row="0" U_CheckedDataGridRows="ucManageIn_U_CheckedDataGridRows"></uc:ucManageInInput>-->
        <GroupBox Name="grbGoodsList" Grid.Row="0" Header="双击选择物料清单" Margin="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <uc:ucQuickQuery x:Name="ucQueryGoods" Grid.Row="0" ></uc:ucQuickQuery>
                <uc:ucCommonDataGrid x:Name="gridGoods" Grid.Row="1"></uc:ucCommonDataGrid>
            </Grid>
        </GroupBox>
        <GridSplitter Name="splitter1" Height="2" HorizontalAlignment="Stretch" Grid.Row="1"></GridSplitter>
        <GroupBox Name="grpbManageList"  Grid.Row="2" Header="任务清单" Margin="1">
            <uc:ucSplitPropertyGridTab x:Name="gridManageList"></uc:ucSplitPropertyGridTab>
        </GroupBox>

        <WrapPanel Grid.Row="3" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="Button_Click">
            <uc:ucManagePosition x:Name="ucManagePosition"  Margin="5" ></uc:ucManagePosition>
            <CheckBox Name="rbRefresh" Width="100" VerticalAlignment="Center" Content="保留组盘信息" Visibility="Collapsed"/>
            <CheckBox Name="rbxGetStockFromIwms" Width="150" VerticalAlignment="Center" Content="向iWMS请求组托信息" Visibility="Collapsed" 
                      Checked="RbxGetStockFromIwms_CheckChanged" Unchecked="RbxGetStockFromIwms_CheckChanged"/>
            <Button Name="btnSave"  Width="70" Margin="5">保存</Button>
            <Button Name="btnRefresh"  Width="70" Margin="5">刷新</Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent >
