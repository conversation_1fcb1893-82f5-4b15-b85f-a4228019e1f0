﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.GOODS
{
    public partial class GOODS_EDIT : AvalonDock.DocumentContent
    {
        public GOODS_EDIT(int GOODS_TYPE_ID)
        {
            InitializeComponent();

            this.tvwGoodsClass.U_ItemSelectedChanged += new UC.ucTreeView.U_ItemSelectedChangedHandler
                ((treeViewItem) =>
                   {
                       this.grpGoods.Header = string.Format(this.grpGoods.Tag.ToString(), treeViewItem.Header.ToString());
                       this.GOODS_Bind(Convert.ToInt32(treeViewItem.Tag));
                   }

                );

            this.ucQueryGoods.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                ((QueryWhere) =>
                  {
                     this.GOODS_Bind(QueryWhere);
                  }              
                );
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitQueryControl();

            this.GoodsClass_Bind();
        }


        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQueryGoods.U_WindowName = this.GetType().Name;
            this.ucQueryGoods.U_XmlTableName = "GOODS_MAIN";
            this.ucQueryGoods.U_InitControl();
        }

        /// <summary>
        /// 加载物料类别
        /// </summary>
        private void GoodsClass_Bind()
        {
            this.tvwGoodsClass.U_Header = "物料类别";
            this.tvwGoodsClass.U_AllowCheck = false;
            //wdz alter 2017-08-16
            //string sItemHeader = MainApp._DatabaseType.ToLower() == "oracle" ? "GOODS_CLASS_CODE || GOODS_CLASS_NAME AS HEADER" : "GOODS_CLASS_CODE + GOODS_CLASS_NAME AS HEADER";
            string sItemHeader = MainApp._DatabaseType.ToLower() == "oracle" ? "GOODS_CLASS_NAME AS HEADER" : "GOODS_CLASS_NAME AS HEADER";
            
            this.tvwGoodsClass.U_LoadTreeViewItems("物料类别", 0, "GOODS_CLASS", sItemHeader, "GOODS_CLASS_ID", "GOODS_CLASS_PARENT_ID", "0", "GOODS_CLASS_FLAG='1'", "GOODS_CLASS_ORDER");
        }

        /// <summary>
        /// 加载物料信息
        /// </summary>
        private void GOODS_Bind(int GOODS_CLASS_ID)
        {
            try
            {
                this.gridGoods.U_Clear();
                this.gridGoods.U_WindowName = this.GetType().Name;
                this.gridGoods.U_TableName = "GOODS_MAIN";
                this.gridGoods.U_XmlTableName = "GOODS_MAIN";
                this.gridGoods.U_Where = string.Format("GOODS_CLASS_ID={0}", GOODS_CLASS_ID);
                this.gridGoods.U_OrderField = "GOODS_ORDER";

                this.gridGoods.U_AllowOperatData = true;
                if (MainApp._USER.USER_ID != 0)
                    this.gridGoods.U_AllowDelete = System.Windows.Visibility.Collapsed;
                this.gridGoods.U_AllowChecked = true;
                this.gridGoods.U_AllowPage = true;

                this.gridGoods.U_IsShowGoodsPositionTemplate = false;

                //设置默认值
                if (GOODS_CLASS_ID > 0)
                {
                    this.gridGoods.U_DefaultRowValues.Add("GOODS_CLASS_ID", GOODS_CLASS_ID.ToString());
                }

                this.gridGoods.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }


        /// <summary>
        /// 按条件加载物料信息
        /// </summary>
        private void GOODS_Bind(string QueryWhere)
        {
            try
            {
                this.gridGoods.U_Clear();
                this.gridGoods.U_WindowName = this.GetType().Name; this.gridGoods.U_TableName = "GOODS_MAIN";
                this.gridGoods.U_XmlTableName = "GOODS_MAIN";
                this.gridGoods.U_Where = string.Format("{0} GOODS_ID != 1", QueryWhere);
                this.gridGoods.U_AllowOperatData = true;
                this.gridGoods.U_OrderField = "GOODS_ID";

                if (MainApp._USER.USER_ID != 0)
                    this.gridGoods.U_AllowDelete = System.Windows.Visibility.Collapsed;
                
                this.gridGoods.U_AllowChecked = true;
                this.gridGoods.U_AllowPage = true;
                this.gridGoods.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 点击上下文菜单
        /// </summary>
        private void ContextMenu_Click(object sender, RoutedEventArgs e)
        {
            if (this.tvwGoodsClass.tvwList.SelectedItem == null)
                return;

            TreeViewItem itemSelected = this.tvwGoodsClass.tvwList.SelectedItem as TreeViewItem;
            if (itemSelected.Tag != null)
            {
                MenuItem menuItem = e.OriginalSource as MenuItem;
                if (menuItem != null)
                {
                    switch (menuItem.Name)
                    {
                        case "menuItemImportGoods":
                            new SiaSun.LMS.WPFClient.Dialog.ImportExcelDialog(true).ShowDialog();
                            this.ImportGoodsFromFile(Convert.ToInt32(itemSelected.Tag.ToString()));
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 从文件导入物料
        /// </summary>
        private void ImportGoodsFromFile(int GOODS_CLASS_ID)
        {
            try
            {
                using (System.Windows.Forms.OpenFileDialog openFileDialog = new System.Windows.Forms.OpenFileDialog())
                {
                    openFileDialog.Filter = "Excel 2007文件|*.xlsx|Excel 2003|*.xls";
                    if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                    {
                        string strOpenFilePath = openFileDialog.FileName;
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 刷新，重新加载物料类别
        /// </summary>
        private void Update()
        {
            //设置空数据源
            this.gridGoods.U_DataSource = null;

            //重新加载
            this.GoodsClass_Bind();
        }

    }
}
