﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  Control colors.  -->
    <Color x:Key="ContentAreaColorLight">#FFC5CBF9</Color>
    <Color x:Key="ContentAreaColorDark">#FF7381F9</Color>

    <Color x:Key="DisabledControlDarkColor">#FFC5CBF9</Color>
    <Color x:Key="ControlLightColor">White</Color>
    <Color x:Key="ControlMediumColor">#FF7381F9</Color>

    <Color x:Key="ControlPressedColor">#FF211AA9</Color>

    <!--  Border colors  -->
    <Color x:Key="BorderLightColor">#FFCCCCCC</Color>
    <Color x:Key="BorderMediumColor">#FF888888</Color>
    <Color x:Key="BorderDarkColor">#FF444444</Color>
    <Color x:Key="DisabledBorderLightColor">#FFAAAAAA</Color>

    <!--  ControlBackBrush  -->
    <LinearGradientBrush x:Key="ControlBackBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="#FFCDDBF5" />
                <GradientStop Offset="0.5" Color="White" />
                <GradientStop Offset="0.6" Color="White" />
                <GradientStop Offset="1.0" Color="#FFCDDBF5" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <!--  PanelBackBrush  -->
    <LinearGradientBrush x:Key="PanelBackBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="LightBlue" />
                <GradientStop Offset="0.6" Color="White" />
                <GradientStop Offset="1.0" Color="LightBlue" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <!--  NormalBorderBrush  -->
    <LinearGradientBrush x:Key="NormalBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="#CCC" />
                <GradientStop Offset="1.0" Color="#444" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <!--  默认鼠标滑过画刷  -->
    <LinearGradientBrush x:Key="mouseOverDefaultBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0.03" Color="#FFEDE47A" />
        <GradientStop Offset="1" Color="White" />
    </LinearGradientBrush>

    <!--  默认Window背景画刷  -->
    <LinearGradientBrush x:Key="windowBackgroundDefaultBrush" StartPoint="0.032,0.132" EndPoint="1.117,1.196">
        <GradientStop Color="#FF082E78" />
        <GradientStop Offset="1" Color="White" />
    </LinearGradientBrush>

    <!--  默认Menu画刷  -->
    <LinearGradientBrush x:Key="menuBackgroundDefaultBrush" StartPoint="0,1" EndPoint="0,0">
        <GradientStop Offset="0" Color="#FF6993EB" />
        <GradientStop Offset="1" Color="#FFCDDBF5" />
    </LinearGradientBrush>

    <!--  Menu Pup  -->
    <LinearGradientBrush x:Key="menuItemPopupBrush" StartPoint="0.5,0" EndPoint="0.5,1">
        <GradientStop Offset="0" Color="{DynamicResource ControlLightColor}" />
        <GradientStop Offset="0.5" Color="{DynamicResource ControlMediumColor}" />
        <GradientStop Offset="1" Color="{DynamicResource ControlLightColor}" />
    </LinearGradientBrush>

    <!--  默认MenuItem画刷  -->
    <LinearGradientBrush x:Key="menuItemBackgroundDefaultBrush" StartPoint="1,0" EndPoint="0,0">
        <GradientStop Offset="1" Color="CornflowerBlue" />
        <GradientStop Offset="0" Color="White" />
    </LinearGradientBrush>

    <!--  默认StatusBar画刷  -->
    <LinearGradientBrush x:Key="statusBarBackgroundDefaultBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0.528" Color="#FF3A60AD" />
        <GradientStop Offset="0.01" Color="#FFAFC2FB" />
        <GradientStop Offset="1" Color="#FF202E7E" />
    </LinearGradientBrush>

    <!--  默认Button背景画刷  -->
    <LinearGradientBrush x:Key="buttonBackgroundDefaultBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Offset="0" Color="WhiteSmoke" />
        <GradientStop Offset="0.5" Color="#FFCDDBF5" />
        <GradientStop Offset="1" Color="#FF3A60AD" />
    </LinearGradientBrush>

</ResourceDictionary>