﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="SYS_ROLE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="SYS_ROLE" type="SiaSun.LMS.Model.SYS_ROLE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_ROLE">
			<result property="ROLE_ID" column="role_id" />
			<result property="ROLE_CODE" column="role_code" />
			<result property="ROLE_NAME" column="role_name" />
			<result property="ROLE_START_MENU_ID" column="role_start_menu_id" />
			<result property="ROLE_REMARK" column="role_remark" />
			<result property="ROLE_ORDER" column="role_order" />
			<result property="ROLE_FLAG" column="role_flag" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="SYS_ROLE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			role_id,
			role_code,
			role_name,
			role_start_menu_id,
			role_remark,
			role_order,
			role_flag
			From SYS_ROLE
		</select>

		<select id="SYS_ROLE_SELECT_BY_ID" parameterClass="int" extends = "SYS_ROLE_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					role_id=#ROLE_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="SYS_ROLE_SELECT_BY_USER_ID" parameterClass="int" extends="SYS_ROLE_SELECT" resultMap="SelectResult">
			INNER JOIN V_SYS_RELATION ON SYS_ROLE.ROLE_ID=V_SYS_RELATION.RELATION_ID1
			WHERE V_SYS_RELATION.RELATION_ID2=#USER_ID# AND V_SYS_RELATION.RELATION_CODE='SYS_ROLE-SYS_USER'
			ORDER BY ROLE_ORDER
		</select>

		<insert id="SYS_ROLE_INSERT" parameterClass="SYS_ROLE">
			Insert Into SYS_ROLE (
			<!--role_id,-->
			role_code,
			role_name,
			role_start_menu_id,
			role_remark,
			role_order,
			role_flag
			)Values(
			<!--#ROLE_ID#,-->
			#ROLE_CODE#,
			#ROLE_NAME#,
			#ROLE_START_MENU_ID#,
			#ROLE_REMARK#,
			#ROLE_ORDER#,
			#ROLE_FLAG#
			)
			<selectKey  resultClass="int" type="post" property="ROLE_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="SYS_ROLE_UPDATE" parameterClass="SYS_ROLE">
			Update SYS_ROLE Set
			<!--role_id=#ROLE_ID#,-->
			role_code=#ROLE_CODE#,
			role_name=#ROLE_NAME#,
			role_start_menu_id=#ROLE_START_MENU_ID#,
			role_remark=#ROLE_REMARK#,
			role_order=#ROLE_ORDER#,
			role_flag=#ROLE_FLAG#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					role_id=#ROLE_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="SYS_ROLE_DELETE" parameterClass="int">
			Delete From SYS_ROLE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					role_id=#ROLE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>