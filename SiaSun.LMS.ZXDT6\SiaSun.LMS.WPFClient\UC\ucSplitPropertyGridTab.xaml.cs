﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucSplitPropertyGridTab.xaml 的交互逻辑
    /// </summary>
    public partial class ucSplitPropertyGridTab : UserControl
    {
        #region     ------定义变量属性

        private string strSourceSql = string.Empty;
        private string strTableName = string.Empty;
        private string strXmlTableName = string.Empty;
        private List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listAppendDescription = null;
        private string strField = "*";
        private string strOrderField = string.Empty;
        private string strAppendWhere = string.Empty;
        private string strWhere = string.Empty;
        private Dictionary<string, string> dicDefaultValue = new Dictionary<string, string>();
        private string strWindowName = null;
        //xcjt add 2017-01-03
        //视图中指定保存的数据表
        private string strSaveDataTable = string.Empty;
        //tzyg add 2017-03-16
        //控制是否显示物料位置模板图片窗体
        private bool isShowGoodsPositionTemplate = false;


        /// <summary>
        /// 强制设置数据源的查询语句，如果设置该值则说明是一个复杂的多表查询，则strField,strOrderField,strWhere等条件不起作用
        /// </summary>
        public string U_DataSource_SQL
        {
            get { return strSourceSql; }
            set { strSourceSql = value; }
        }

        /// <summary>
        /// 设置样式的窗体
        /// </summary>
        public string U_WindowName
        {
            get { return this.strWindowName; }
            set { this.strWindowName = value; }
        }

        /// <summary>
        /// 数据表名称
        /// </summary>
        public string U_TableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// XML文件中表名，用于设置显示样式
        /// </summary>
        public string U_XmlTableName
        {
            get { return strXmlTableName; }
            set { strXmlTableName = value; }
        }

        public List<SiaSun.LMS.Model.FIELD_DESCRIPTION> U_AppendFieldStyles
        {
            get { return listAppendDescription; }
            set { listAppendDescription = value; }
        }

        /// <summary>
        /// 查询字段
        /// </summary>
        public string U_Fields
        {
            get { return strField; }
            set { strField = value; }
        }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string U_OrderField
        {
            get { return strOrderField; }
            set { strOrderField = value; }
        }

        /// <summary>
        /// 查询条件
        /// </summary>
        public string U_Where
        {
            get { return strWhere; }
            set { strWhere = value; }

        }

        /// <summary>
        /// 追加查询条件
        /// </summary>
        public string U_AppendWhere
        {
            get { return strAppendWhere; }
            set { strAppendWhere = value; }
        }

        /// <summary>
        /// 默认列值集合
        /// </summary>
        public Dictionary<string, string> U_DefaultRowValues
        {
            get { return dicDefaultValue; }
            set { dicDefaultValue = value; }
        }

        /// <summary>
        /// 获得数据源
        /// </summary>
        public DataTable U_DataSource
        {
            get { return U_GetDataSource(); }
            set
            {
                if (value == null)
                    this.tabSplitProperty.Items.Clear();
            }
        }

        /// <summary>
        /// xcjt add 2017-01-03
        /// 视图中要保存的数据表 
        /// </summary>
        public string U_SaveDataTable
        {
            get { return strSaveDataTable; }
            set { strSaveDataTable = value; }
        }

        /// <summary>
        /// tzyg add 2017-03-16
        /// 控制是否显示物料位置模板图片窗体
        /// </summary>
        public bool U_IsShowGoodsPositionTemplate
        {
            get { return isShowGoodsPositionTemplate; }
            set { isShowGoodsPositionTemplate = value; }
        }
        #endregion

        #region     ------拆分组合列属性

        private string strSplitGroupHeader = string.Empty;
        private string strSplitGroupColumn = string.Empty;
        private string strSplitPropertyColumn = string.Empty;
        private string strSplitPropertyType = string.Empty;
        private string strSplitPropertyValue = string.Empty;

        /// <summary>
        /// 不同TapPage页显示的数据不同的分组依据,如：表V_PLAN_LIST中的GOODS_TYPE_ID
        /// </summary>
        public string U_SplitGroupColumn
        {
            get { return strSplitGroupColumn; }
            set { strSplitGroupColumn = value; }
        }

        /// <summary>
        /// 不同TapPage页显示的标题,如：表GOODS_TYPE中的GOODS_TYPE_NAME
        /// </summary>
        public string U_SplitGroupHeader
        {
            get { return strSplitGroupHeader; }
            set { strSplitGroupHeader = value; }
        }

        /// <summary>
        /// 数据源中所对应的拆分属性列名，如：PLAN_LIST表中的GOODS_PROPERTY
        /// </summary>
        public string U_SplitPropertyColumn
        {
            get { return strSplitPropertyColumn; }
            set { strSplitPropertyColumn = value; }
        }

        /// <summary>
        /// 属性划分依据类别,不同类型对应显示的属性不同，如：表GOODS_TYPE
        /// </summary>
        public string U_SplitPropertyType
        {
            get { return strSplitPropertyType; }
            set { strSplitPropertyType = value; }
        }

        /// <summary>
        /// 属性划分的值 ：优化查询 如果显示全部类型 =string.empty
        /// </summary>
        public string U_SplitPropertyValue
        {
            get { return strSplitPropertyValue; }
            set { strSplitPropertyValue = value; }
        }


        #endregion

        #region     ------明细属性

        string strDetailTableName;
        string strDetailColumnName;

        /// <summary>
        /// 明细表名
        /// </summary>
        public string U_DetailTableName
        {
            get { return this.strDetailTableName; }
            set { this.strDetailTableName = value; }
        }

        /// <summary>
        /// 明细表列名
        /// </summary>
        public string U_DetailRelatvieColumn
        {
            get { return this.strDetailColumnName; }
            set { this.strDetailColumnName = value; }
        }

        #endregion

        #region     ------外观属性

        private bool boolAllowChecked = false;
        private bool boolAllowPage = true;
        private Visibility boolAllowAdd = Visibility.Visible;
        private Visibility boolAllowEdit = Visibility.Visible;
        private Visibility boolAllowDelete = Visibility.Visible;
        private Visibility boolAllowCancel = Visibility.Visible;
        private Visibility boolAllowSave = Visibility.Visible;
        private bool boolAllowOperation = true;

        /// <summary>
        /// 是否显示全选、反选框
        /// </summary>
        public bool U_AllowChecked
        {
            get { return boolAllowChecked; }
            set { boolAllowChecked = value; }
        }

        /// <summary>
        /// 是否允许显示分页
        /// </summary>
        public bool U_AllowShowPage
        {
            get { return boolAllowPage; }
            set { boolAllowPage = value; }
        }

        /// <summary>
        /// 是否允许编辑数据，如果允许编辑则显示编辑工具栏
        /// </summary>
        public bool U_AllowOperatData
        {
            get { return boolAllowOperation; }
            set
            {
                boolAllowOperation = value;
                (this.Resources["contextMenu"] as ContextMenu).Visibility = System.Windows.Visibility.Hidden;
            }
        }

        /// <summary>
        /// 是否允许添加操作
        /// </summary>
        public Visibility U_AllowAdd
        {
            get { return boolAllowAdd; }
            set
            {
                boolAllowAdd = value;
            }
        }

        /// <summary>
        /// 是否显示编辑按钮
        /// </summary>
        public Visibility U_AllowEdit
        {
            get { return boolAllowEdit; }
            set
            {
                boolAllowEdit = value;
            }
        }

        /// <summary>
        /// 是否显示删除按钮
        /// </summary>
        public Visibility U_AllowDelete
        {
            get { return boolAllowDelete; }
            set
            {
                boolAllowDelete = value;
            }
        }

        /// <summary>
        /// 是否显示取消按钮
        /// </summary>
        public Visibility U_AllowCancel
        {
            get { return boolAllowCancel; }
            set
            {
                boolAllowCancel = value;
            }
        }

        /// <summary>
        /// 是否显示保存按钮
        /// </summary>
        public Visibility U_AllowSave
        {
            get { return boolAllowSave; }
            set
            {
                boolAllowSave = value;
            }
        }

        #endregion

        #region     ------合计属性

        private string strTotalColumnName = string.Empty;

        /// <summary>
        /// 合计总数列名
        /// </summary>
        public string U_TotalColumnName
        {
            get { return strTotalColumnName; }
            set { strTotalColumnName = value; }
        }

        #endregion





        /// <summary>
        /// 构造函数
        /// </summary>
        public ucSplitPropertyGridTab()
        {
            InitializeComponent();
        }

        #region      ------获得分组页面语句

        /// <summary>
        /// 获得分组语句
        /// </summary>
        private string GetSplitGroupSQL()
        {
            string strSql = string.Empty;
            if (strSourceSql.Length > 0)
                strSql = string.Format("SELECT {0} FROM ({1} WHERE 1=1 {2}) A GROUP BY {0} ORDER BY {0}", strSplitGroupColumn, strSourceSql, (strWhere.Length == 0 ? string.Empty : " AND " + strWhere));
            else
            {
                if (U_SplitPropertyValue == string.Empty)
                {
                    if (strWhere == string.Empty)
                        strSql = string.Format("SELECT {0} FROM {1} {2} GROUP BY {0} ORDER BY {0}", strSplitGroupColumn, strSplitPropertyType, (strWhere.Length == 0 ? string.Format("WHERE GOODS_TYPE_REMARK ='{0}' and GOODS_TYPE_FLAG='1'", strSplitPropertyType) : string.Format("WHERE GOODS_TYPE_REMARK ='{0}'  and GOODS_TYPE_FLAG='1' AND {1} ", strSplitPropertyType, strWhere)));
                    else
                        strSql = string.Format("SELECT {0} FROM {1} {2} GROUP BY {0} ORDER BY {0}", strSplitGroupColumn, strTableName, (strWhere.Length == 0 ? string.Empty : "WHERE " + strWhere));

                }
                else
                    strSql = string.Format("SELECT {0} FROM {1} {2} GROUP BY {0} ORDER BY {0}", strSplitGroupColumn, strSplitPropertyType, string.Format(" where {0} = {1}", strSplitGroupColumn, U_SplitPropertyValue));
            }

            return MainApp._DatabaseType.ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql;
        }

        /// <summary>
        /// 获得默认的分组查询语句
        /// </summary>
        private string GetSplitGroupDefaultSQL()
        {
            string strSql = string.Empty;
            strSql = string.Format("SELECT {0} FROM {1} GROUP BY {0}", strSplitGroupColumn, strTableName);
            return strSql;
        }

        #endregion

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //获得选定页面
            TabItem tbItem = this.tabSplitProperty.SelectedItem as TabItem;

            LoadTabPages();
            //重新选定
            if (tbItem != null)
            {
                foreach (TabItem item in this.tabSplitProperty.Items)
                {
                    if (item.Header != null && tbItem.Header != null && item.Header.ToString() == tbItem.Header.ToString())
                    {
                        this.tabSplitProperty.SelectedItem = item;
                        return;
                    }
                }
            }
        }

        #region     ------加载页面控件

        /// <summary>
        /// 根据类别加载页面
        /// </summary>
        private void LoadTabPages()
        {
            if (strSplitPropertyType.Length == 0 || strSplitGroupColumn.Length == 0 || strSplitGroupHeader.Length == 0 )
                return;

            //清除所有页面
            this.tabSplitProperty.Items.Clear();

            try
            {
                //加载分组页面
                using (DataTable tableSplitGroup = GetSplitGroupPageTable())
                {
                    if (tableSplitGroup.Rows.Count == 0)
                    {
                        //this.AddTabPage("");
                    }
                    else
                    {
                        foreach (DataRow rowGroup in tableSplitGroup.Rows)
                        {
                            //添加页面
                            this.AddTabPage(rowGroup[strSplitGroupColumn].ToString());
                        }
                    }
                }

                //加载数据控件
                if (this.tabSplitProperty.HasItems && this.tabSplitProperty.SelectedItem == null)
                {
                    this.tabSplitProperty.SelectedItem = this.tabSplitProperty.Items[0];
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得加载页面的数据
        /// </summary>
        private DataTable GetSplitGroupPageTable()
        {
            //根据获得数据获得分组页面
            

            DataTable tableSplitGroup = MainApp.I_DatabaseService.GetList(GetSplitGroupSQL());
            //if (tableSplitGroup.Rows.Count == 0)
            //{
            //    //获得默认分组
            //    tableSplitGroup = MainApp.I_DatabaseService.GetList(GetSplitGroupDefaultSQL());
            //}
            return tableSplitGroup;
        }

        /// <summary>
        /// 添加页面
        /// </summary>
        private void AddDefaultTablePage()
        {
            try
            {
                TabItem page = new TabItem();
                page.Header = "显示";
                this.tabSplitProperty.Items.Add(page);

                ucCommonDataGrid grid = new ucCommonDataGrid();
                page.Content = grid;

                grid.U_TableName = strTableName;
                grid.U_XmlTableName = strXmlTableName;

                //外观属性
                grid.U_AllowPage = this.boolAllowPage;
                grid.U_AllowOperatData = boolAllowOperation;
                if (boolAllowOperation)
                {
                    grid.U_AllowAdd = boolAllowAdd;
                    grid.U_AllowCancel = boolAllowCancel;
                    grid.U_AllowDelete = boolAllowDelete;
                    grid.U_AllowEdit = boolAllowEdit;
                    grid.U_AllowSave = boolAllowSave;
                }
                grid.U_TranslateDataGridViewStyle(this.strWindowName, strXmlTableName, null, boolAllowOperation);
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("AddDefaultTablePage()" + ex.Message), ex);
            }
        }

        /// <summary>
        /// 添加页面
        /// </summary>
        private TabItem AddTabPage(string SplitPropertyKey)
        {
            TabItem page = null;
            TabItem[] arPage = this.tabSplitProperty.Items.Cast<TabItem>().ToArray();
            if (arPage.Count(p => p.Tag.ToString() == SplitPropertyKey) > 0)
            {
                page = arPage.Single(p => p.Tag.ToString() == SplitPropertyKey);
            }
            else
            {
                page = new TabItem();
                page.Style = (Style)MainApp.GetStyleResource("styleDefaultTabItem");
                //设置分组标识
                page.Tag = SplitPropertyKey;
                //获得显示名称

                DataTable dtHeader =  MainApp.I_DatabaseService.GetList( string.Format("SELECT {0} FROM {1} WHERE {2}", strSplitGroupHeader, strSplitPropertyType, string.Format("{0}='{1}'", strSplitGroupColumn, page.Tag.ToString())));

                page.Header = dtHeader.Rows.Count==0 ? string.Empty : dtHeader.Rows[0][0].ToString();

                //添加页面
                this.tabSplitProperty.Items.Add(page);
            }

            return page;
        }

        /// <summary>
        /// 加载数据显示控件
        /// </summary>
        private void LoadSplitDataGrid(TabItem tabItem)
        {
            try
            {
                //清除控件
                tabItem.Content = null;

                ucCommonDataGrid grid = new ucCommonDataGrid();
                tabItem.Content = grid;

                //数据源属性
                grid.U_DataSource_SQL = strSourceSql;

                grid.U_TableName = strTableName;
                grid.U_XmlTableName = strXmlTableName;
                grid.U_AppendFieldStyles = listAppendDescription;
                grid.U_Fields = strField;
                //xcjt add 2017-01-03
                grid.U_SaveDataTable = strSaveDataTable;
                //tzyg add 2017-03-16
                grid.U_IsShowGoodsPositionTemplate = this.isShowGoodsPositionTemplate;

                grid.U_WindowName = this.strWindowName;


                //筛选条件，只显示分组数据
                string strFilter = string.Format("{0}='{1}'", strSplitGroupColumn, tabItem.Tag.ToString());
                grid.U_Where = (strWhere.Length == 0 ? strFilter : strWhere + " AND " + strFilter);
                grid.U_AppendWhere = strAppendWhere;

                grid.U_OrderField = strOrderField;
                grid.U_DefaultRowValues = dicDefaultValue;
                grid.U_TotalColumnName = strTotalColumnName;

                //外观属性
                grid.U_AllowChecked = this.boolAllowChecked;
                grid.U_AllowPage = this.boolAllowPage;
                grid.U_AllowOperatData = boolAllowOperation;
                if (boolAllowOperation)
                {
                    grid.U_AllowAdd = boolAllowAdd;
                    grid.U_AllowCancel = boolAllowCancel;
                    grid.U_AllowDelete = boolAllowDelete;
                    grid.U_AllowEdit = boolAllowEdit;
                    grid.U_AllowSave = boolAllowSave;
                }

                //拆分列属性
                grid.U_SplitPropertyKey = tabItem.Tag.ToString();
                grid.U_SplitPropertyType = strSplitPropertyType;
                grid.U_SplitPropertyColumn = strSplitPropertyColumn;

                //明细属性
                grid.U_DetailTableName = this.strDetailTableName;
                grid.U_DetailRelatvieColumn = this.strDetailColumnName;

                //合计
                grid.U_TotalColumnName = strTotalColumnName;

                //初始化控件
                grid.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        #endregion

        /// <summary>
        /// 选择更改页面
        /// </summary>
        private void tabSplitProperty_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            //加载数据控件
            TabItem item = this.tabSplitProperty.SelectedItem as TabItem;
            if (item != null && item.Content == null)
            {
                LoadSplitDataGrid(item);
            }
        }

        /// <summary>
        /// 添加数据
        /// </summary>
        public void U_AddTabPageRows(DataRow[] arDataRow)
        {
            foreach (DataRow row in arDataRow)
            {
                if (row == null)
                {
                    continue;
                }                    

                //获得分组值
                string strPropertyKey = row[strSplitGroupColumn].ToString();
                TabItem tabItem = null;

                //判断是否已经存在页面
                TabItem[] arPage = this.tabSplitProperty.Items.Cast<TabItem>().ToArray();
                if (arPage.Count(p => p.Tag.ToString() == strPropertyKey) > 0)
                {
                    tabItem = arPage.Single(p => p.Tag.ToString() == strPropertyKey);
                }
                else
                {
                    tabItem = this.AddTabPage(strPropertyKey);
                    //加载页面控件
                    this.LoadSplitDataGrid(tabItem);
                }

                //添加数据控件,如果第一个页面则直接添加，否则，选定其他页面添加
                this.tabSplitProperty.SelectedItem = tabItem;

                //获得数据源
                if (tabItem.Content != null)
                {
                    //添加行到数据源中
                    ucCommonDataGrid grid = (tabItem.Content as ucCommonDataGrid);
                    using (DataTable tableSource = grid.U_DataSource.Table)
                    {
                        tableSource.ImportRow(row);
                    }
                }
            }

            //每个页面数据源解析
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid grid = (item.Content as ucCommonDataGrid);
                    using (DataTable tableSource = grid.U_DataSource.Table)
                    {
                        //解析各个属性值
                        grid.U_TanslateDataTable(tableSource, true);
                    }                   
                }
            }
        }

        /// <summary>
        /// 获得页面
        /// </summary>
        public TabItem U_GetTabPage(string SplitPropertyKey)
        {
            TabItem item = null;
            TabItem[] aritem = this.tabSplitProperty.Items.Cast<TabItem>().ToArray();
            if (aritem.Count(p => p.Tag.ToString() == SplitPropertyKey) > 0)
            {
                item = aritem.Single(p => p.Tag.ToString() == SplitPropertyKey);
                this.tabSplitProperty.SelectedItem = item;
            }
            return item;
        }

        /// <summary>
        /// 获得数据源
        /// </summary>
        public DataTable U_GetDataSource()
        {
            DataTable tableSource = null;
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                    if (grid.U_DataSource != null)
                    {
                        using (DataTable table = grid.U_DataSource.Table.Copy())
                        {
                            if (tableSource == null)
                                tableSource = table;
                            else
                                tableSource.Merge(table);
                        }
                    }
                }
            }
            return tableSource;
        }

        /// <summary>
        /// 提交更新所有更新
        /// </summary>
        public int U_SaveData()
        {
            int intResult = 0;
            using (DataTable tableSource = U_GetDataSource())
            {
                if (tableSource != null)
                {
                    using (DataTable tableChanges = tableSource.GetChanges())
                    {
                        if (tableChanges != null)
                        {
                            intResult = MainApp.I_DatabaseService.Save(tableChanges, strTableName);
                        }
                    }
                }
            }
            return intResult;
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        public void U_Update()
        {
            TabItem item = this.tabSplitProperty.SelectedItem as TabItem;
            if (item != null)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid control = item.Content as ucCommonDataGrid;
                    control.U_Update();
                }
            }
        }

        /// <summary>
        /// 清除设置信息，初始化控件数据
        /// </summary>
        public void U_Clear()
        {
            this.dicDefaultValue.Clear();
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid control = item.Content as ucCommonDataGrid;
                    control.U_Clear();
                }
            }
        }

        /// <summary>
        /// 结束当前编辑
        /// </summary>
        public void U_EndCurrentEdit()
        {
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid control = item.Content as ucCommonDataGrid;
                    control.U_EndCurrentEdit();
                }
            }
        }

        /// <summary>
        /// 校验拆分属性填写是否合法
        /// </summary>
        public bool U_Check_Split_Property(string ColumnName,out string sResult)
        {
            bool boolResult = true;
            sResult = string.Empty;

            //遍历所有页面
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.Content != null)
                {
                    ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                    boolResult = grid.U_Check_Split_Property(ColumnName,out sResult);
                    if (!boolResult)
                    {
                        sResult = item.Header.ToString() + "-" + sResult;
                        boolResult = false;
                        return boolResult;
                    }
                }
            }
            return boolResult;
        }

        /// <summary>
        /// 上下文菜单
        /// </summary>
        private void ContextMenu_Click(object sender, RoutedEventArgs e)
        {
            MenuItem menuItem = e.OriginalSource as MenuItem;
            if (menuItem != null)
            {
                foreach (TabItem item in this.tabSplitProperty.Items)
                {
                    if (item.Content != null)
                    {
                        ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                        switch (menuItem.Name)
                        {
                            case "menuAllUpdate":
                                grid.U_Update();
                                break;
                        }
                    }
                }
            }
        }

        #region     ------CheckBox选中

        /// <summary>
        /// 获得所有选定的行
        /// </summary>
        public DataRowView[] U_GetCheckedDataRows()
        {
            DataRowView[] arRowView = null;
            if (this.tabSplitProperty.SelectedItem != null)
            {
                TabItem item = this.tabSplitProperty.SelectedItem as TabItem;
                ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                
                if (grid != null)
                {
                    arRowView = grid.U_GetCheckedDataRows();
                }
            }
            return arRowView;
        }

        public int U_GetSelectedGoodsTypeID()
        {
            int GOODS_TYPE_ID = 0;

            if (this.tabSplitProperty.SelectedItem != null)
            {
                TabItem item = this.tabSplitProperty.SelectedItem as TabItem;

                GOODS_TYPE_ID = Convert.ToInt32(item.Tag);

            }

            return GOODS_TYPE_ID;
        }

        /// <summary>
        /// 取消选中行
        /// </summary>
        public void U_RejectCheckedRow()
        {
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.HasContent)
                {
                    ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                    if (grid != null)
                    {
                        grid.U_RejectCheckedRow();
                    }
                }
            }
        }


        /// <summary>
        /// xcjt add 2017-04-12
        /// 选中特定行
        /// </summary>
        public void U_CheckRow(DataRowView parmDrv)
        {
            foreach (TabItem item in this.tabSplitProperty.Items)
            {
                if (item.HasContent)
                {
                    ucCommonDataGrid grid = item.Content as ucCommonDataGrid;
                    if (grid != null)
                    {
                        grid.U_CheckedRow(true, parmDrv);
                    }
                }
            }
        }

        #endregion

    }
}
