﻿using System.Collections.Generic;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(SYS_USER))]
    [ServiceKnownType(typeof(MANAGE_LIST))]
    [ServiceKnownType(typeof(MANAGE_MAIN))]
    [ServiceKnownType(typeof(List<MANAGE_LIST>))]
    [ServiceKnownType(typeof(List<PLAN_LIST>))]
    [ServiceKnownType(typeof(MANAGE_TYPE_PARAM))]
    public interface I_Manage
    {
        [OperationContract]
        bool PlanEventExecute(PLAN_ACTION_EXCUTE mPLAN_ACTION_EXCUTE, out string sResult);

        [OperationContract]
        bool ManageEventExecute(MANAGE_ACTION_EXCUTE mt, out string sResult);
        
        [OperationContract]
        string ContinusManageDown(SYS_USER user, int row, int startColumn, int endColumn);

        [OperationContract]
        bool ManageCreate(MANAGE_MAIN mMANAGE_MAIN, List<MANAGE_LIST> lsMANAGE_LIST, bool raiseTrans, bool checkStorage, bool checkManage, bool checkCellStatus, bool autoComplete, bool autoControl, bool doubleInAutoMove, out string message);

        [OperationContract]
        bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult);

        [OperationContract]
        bool ManageException(int MANAGE_ID, out string sResult);

        [OperationContract]
        bool ManageError(int MANAGE_ID, out string sResult);

        [OperationContract]
        bool ManageCancel(int MANAGE_ID, out string sResult, bool raiseTrans = true);

        [OperationContract]
        bool ControlApplyAdd(IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool bTrans, out string sResult);

        [OperationContract]
        string WsControlApply(string controlApplyType, string deviceCode, string stockBarcode, string controlApplyPara);

        [OperationContract]
        bool GoodsCreate(DataTable dt, int GOODS_CLASS_ID, out string sResult);


    }
}
