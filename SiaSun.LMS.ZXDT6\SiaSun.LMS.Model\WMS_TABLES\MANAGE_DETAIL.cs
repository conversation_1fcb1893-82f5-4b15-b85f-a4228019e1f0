﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// MANAGE_DETAIL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MANAGE_DETAIL
	{
		public MANAGE_DETAIL()
		{
			
		}
		
		private int _manage_detail_id;
		private int _manage_list_id;
		private string _box_barcode;
		private string _goods_barcode;
		private string _manage_detail_remark;

		private decimal _manage_detail_quantity;
		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;

		///<sumary>
		/// 任务明细编号
		///</sumary>
		[DataMember]
		public int MANAGE_DETAIL_ID
		{
			get{return _manage_detail_id;}
			set{_manage_detail_id = value;}
		}
		///<sumary>
		/// 任务列表编号
        ///</sumary>
        [DataMember]
		public int MANAGE_LIST_ID
		{
			get{return _manage_list_id;}
			set{_manage_list_id = value;}
		}
		///<sumary>
		/// 箱条码
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 物料条码
        ///</sumary>
        [DataMember]
		public string GOODS_BARCODE
		{
			get{return _goods_barcode;}
			set{_goods_barcode = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MANAGE_DETAIL_REMARK
		{
			get{return _manage_detail_remark;}
			set{_manage_detail_remark = value;}
		}

		///<sumary>
		/// 数量
		///</sumary>
		[DataMember]
		public System.Decimal MANAGE_DETAIL_QUANTITY
		{
			get { return _manage_detail_quantity; }
			set { _manage_detail_quantity = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
