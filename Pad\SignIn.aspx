﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="SignIn.aspx.cs" Inherits="SignIn" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
  <meta charset="UTF-8"/>
  <link rel="stylesheet" href="/layui/css/layui.css" />
  <link rel="stylesheet" href="/main.css" />
  <script src="/jquery-3.4.1.min.js"></script>
  <script src="/layui/layui.all.js"></script>
  <script src="/main.js"></script>
</head>
<body>
  <form runat="server" class="layui-form" style="width:360px;margin:0 auto;" autocomplete="off">
    <div class="layui-form-item" style="text-align:center;margin: 20px 0;">
      <img class="signin-img" src="/SignInTitle.png" />
    </div>
    <div class="layui-form-item" style="margin-bottom:20px;">
      <asp:Label runat="server" Text="用户名" CssClass="layui-form-label" style="line-height:36px;" for="tbUserName"></asp:Label>
      <asp:TextBox runat="server" ID="tbUserName" CssClass="layui-input-inline layui-input signin-input-width" />
      <asp:Button runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="tbUserName_Enter" />
    </div>
    <div class="layui-form-item" style="margin-bottom:20px;">
      <asp:Label runat="server" Text="密 码" CssClass="layui-form-label" style="line-height:36px;" for="tbPassword"></asp:Label>
      <asp:TextBox runat="server" ID="tbPassword" TextMode="Password" CssClass="layui-input-inline layui-input signin-input-width" />
      <asp:Button ID="Button1" runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="tbPassword_Enter" />
    </div>
    <div class="layui-form-item">
      <asp:Button runat="server" Text="登 录" CssClass="layui-input-inline layui-btn layui-btn-normal button-width" UseSubmitBehavior="false" OnClick="bSignIn_Click" />
    </div>
  </form>
</body>
</html>
