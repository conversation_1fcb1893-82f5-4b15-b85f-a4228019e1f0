﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="TB_TRAYINFO_CHECK" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="TB_TRAYINFO_CHECK" type="SiaSun.LMS.Model.TB_TRAYINFO_CHECK, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SELECTRESULT" class="TB_TRAYINFO_CHECK">
      <result property="MDL_NAME" column="mdl_name" />
      <result property="BATCH_ID" column="batch_id" />
      <result property="TRAY_NO" column="tray_no" />
      <result property="CELL_COUNT" column="cell_count" />
      <result property="INDEX_UPLOAD" column="index_upload" />
      <result property="CHECK_FLAG" column="check_flag" />
      <result property="CHECK_TIME" column="check_time" />
      <result property="REVERT_FLAG" column="revert_flag" />
      <result property="REVERT_TIME" column="revert_time" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="TB_TRAYINFO_CHECK_SELECT" parameterClass="int" resultMap="SELECTRESULT">
      Select
      mdl_name,
      batch_id,
      tray_no,
      cell_count,
      index_upload,
      check_flag,
      check_time,
      revert_flag,
      revert_time
      From TB_TRAYINFO_CHECK
    </select>

    <select id="TB_TRAYINFO_CHECK_SELECT_BY_ID" parameterClass="int" extends = "TB_TRAYINFO_CHECK_SELECT" resultMap="SELECTRESULT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          tray_no=#TRAY_NO#
        </isParameterPresent>
      </dynamic>
    </select>



    <insert id="TB_TRAYINFO_CHECK_INSERT" parameterClass="TB_TRAYINFO_CHECK">
      Insert Into TB_TRAYINFO_CHECK (
      mdl_name,
      batch_id,
      tray_no,
      cell_count,
      index_upload,
      check_flag,
      check_time
      )Values(
      #MDL_NAME#,
      #BATCH_ID#,
      #TRAY_NO#,
      #CELL_COUNT#,
      #INDEX_UPLOAD#,
      #CHECK_FLAG#,
      #CHECK_TIME#
      )
    </insert>

    <update id="TB_TRAYINFO_CHECK_UPDATE" parameterClass="TB_TRAYINFO_CHECK">
      Update TB_TRAYINFO_CHECK Set
      mdl_name=#MDL_NAME#,
      batch_id=#BATCH_ID#,
      tray_no=#TRAY_NO#,
      cell_count=#CELL_COUNT#,
      index_upload=#INDEX_UPLOAD#,
      check_flag=#CHECK_FLAG#,
      check_time=#CHECK_TIME#,
      revert_flag=#REVERT_FLAG#,
      revert_time=#REVERT_TIME#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          tray_no=#TRAY_NO#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="TB_TRAYINFO_CHECK_DELETE" parameterClass="int">
      Delete From TB_TRAYINFO_CHECK
      <dynamic prepend="WHERE">
        <isParameterPresent>
          tray_no=#TRAY_NO#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>