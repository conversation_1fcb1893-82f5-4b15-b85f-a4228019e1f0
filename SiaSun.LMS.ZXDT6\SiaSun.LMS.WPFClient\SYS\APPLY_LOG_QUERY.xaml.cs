﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// APPLY_LOG_QUERY.xaml 的交互逻辑
    /// xcjt add 2017-01-13
    /// </summary>
    public partial class APPLY_LOG_QUERY : AvalonDock.DocumentContent
    {
        public APPLY_LOG_QUERY()
        {
            InitializeComponent();

            this.ucQueryControl.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(QueryButtonAction);
            this.ucQueryControl.U_AddButtonClicked += new UC.ucQuickQuery.U_AddEventHandler(AddButtonAction);
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            InitQueryControl();
            ApplyListBind();
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if(btn!=null)
            {
                switch(btn.Name)
                {
                    case "btnApplyAgain":
                        this.ApplyAgain();
                        break;
                }
            }
        }

        /// <summary>
        /// 点击“查询”按钮的处理方法
        /// </summary>
        private void QueryButtonAction(string strQueryWhere)
        {
            try
            {
                this.ucApplyDataGrid.U_AppendWhere = strQueryWhere;
                this.ucApplyDataGrid.U_InitControl();
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 点击“添加”按钮的处理方法
        /// </summary>
        private void AddButtonAction(object sender, UC.ucQuickQuery.AddEventArgs e)
        {
            bool bResult =true;
            string sResult =string.Empty;
            Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY = new Model.IO_CONTROL_APPLY();

            try
            {
                if (e.DicDataPair == null || e.DicDataPair.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("申请信息未填写"); 
                    return;
                }

                mIO_CONTROL_APPLY = Common.CloneObjectValues.CloneModelValue(mIO_CONTROL_APPLY, e.DicDataPair);

                if (string.IsNullOrEmpty(mIO_CONTROL_APPLY.WAREHOUSE_CODE) || string.IsNullOrEmpty(mIO_CONTROL_APPLY.DEVICE_CODE) ||
                    string.IsNullOrEmpty(mIO_CONTROL_APPLY.STOCK_BARCODE) )
                {
                    bResult = false;
                    sResult = string.Format("申请信息填写不全");
                    return;
                }

                mIO_CONTROL_APPLY.CONTROL_APPLY_ID = 0;
                mIO_CONTROL_APPLY.APPLY_TASK_STATUS = 0;
                mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE = "0";
                mIO_CONTROL_APPLY.CREATE_TIME = Common.StringUtil.GetDateTime();
                mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK = "人工申请";
                
                bResult = MainApp.I_ManageService.ControlApplyAdd(mIO_CONTROL_APPLY, true, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("执行添加申请异常 {0}", ex.Message);
            }
            finally
            {
                //MainApp.I_BaseService.CreateSysLog(Enum.LogThread.Apply, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("APPLY_LOG_QUERY.AddButtonAction():执行添加申请{0} {1}", bResult ? string.Format("成功_条码[{0}]_设备[{1}]", mIO_CONTROL_APPLY.STOCK_BARCODE, mIO_CONTROL_APPLY.DEVICE_CODE) : "失败 ", sResult));
                MainApp._MessageDialog.ShowResult(bResult, sResult);

                MainApp.I_SystemService.CreateLog(Common.StringUtil.GetIpAddress(), 
                                                 bResult ? Enum.LogLevel.INFO : Enum.LogLevel.WARN, 
                                                 "APPLY_LOG_QUERY", 
                                                 "AddButtonAction()", 
                                                 Enum.LogType.record, 
                                                 MainApp._USER.USER_NAME, 
                                                 string.Format("执行添加申请{0} {1}", bResult ? string.Format("成功_条码[{0}]_设备[{1}]", mIO_CONTROL_APPLY.STOCK_BARCODE, mIO_CONTROL_APPLY.DEVICE_CODE) : "失败 ", sResult), 
                                                 null);
            }
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            try
            {
                //this.ucQueryControl.U_AddButtonVisable = true;
                this.ucQueryControl.U_XmlTableName = "IO_CONTROL_APPLY_HIS";
                this.ucQueryControl.U_WindowName = this.GetType().Name;
                this.ucQueryControl.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 绑定申请列表数据
        /// </summary>
        private void ApplyListBind()
        {
            try
            {
                this.ucApplyDataGrid.U_WindowName = this.GetType().Name;
                this.ucApplyDataGrid.U_TableName = "IO_CONTROL_APPLY_HIS";
                this.ucApplyDataGrid.U_OrderField = "CONTROL_APPLY_ID desc";
                
                this.ucApplyDataGrid.U_AllowChecked = true;
                this.ucApplyDataGrid.U_AllowOperatData = false;

                this.ucApplyDataGrid.U_InitControl();
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }            
        }

        /// <summary>
        /// 再次申请
        /// </summary>
        private void ApplyAgain()
        {
            bool bResult = true;
            string sResult = string.Empty;
            Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY = new Model.IO_CONTROL_APPLY();

            try
            {
                DataRowView[] listDataRowView = this.ucApplyDataGrid.U_GetCheckDataRow();
                if (listDataRowView == null || listDataRowView.ToList().Count != 1)
                {
                    bResult = false;
                    sResult = string.Format("未选中记录或选中了多条记录");
                    return;
                }

                new Common.CloneObjectValues().CloneModelValue(listDataRowView.First().Row, mIO_CONTROL_APPLY, null);

                if (string.IsNullOrEmpty(mIO_CONTROL_APPLY.WAREHOUSE_CODE) || string.IsNullOrEmpty(mIO_CONTROL_APPLY.DEVICE_CODE))
                {
                    bResult = false;
                    sResult = string.Format("申请信息填写不全");
                    return;
                }

                //申请成功的不允许再次申请
                if (MainApp._USER.USER_CODE != "super" && mIO_CONTROL_APPLY.APPLY_TASK_STATUS == 1)
                {
                    bResult = false;
                    sResult = string.Format("状态为[1]的申请为成功申请_已生成控制任务_如未执行请在调度系统中查看原因");
                    return;
                }

                mIO_CONTROL_APPLY.CONTROL_APPLY_ID = 0;
                mIO_CONTROL_APPLY.APPLY_TASK_STATUS = 0;
                mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK = "人工申请-操作人员：" + MainApp._USER.USER_NAME;
                mIO_CONTROL_APPLY.CREATE_TIME = Common.StringUtil.GetDateTime();

                bResult = MainApp.I_ManageService.ControlApplyAdd(mIO_CONTROL_APPLY, true, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("执行再次申请异常 {0}", ex.Message);
            }
            finally
            {
                //MainApp.I_BaseService.CreateSysLog(Enum.LogThread.Apply, MainApp._USER.USER_NAME, bResult ? Enum.LOG_LEVEL.Information:Enum.LOG_LEVEL.Error, string.Format("APPLY_LOG_QUERY.ApplyAgain():执行再次申请{0} {1}", bResult ? string.Format("成功_条码[{0}]_设备[{1}]", mIO_CONTROL_APPLY.STOCK_BARCODE, mIO_CONTROL_APPLY.DEVICE_CODE) : "失败 ", sResult));
                MainApp._MessageDialog.ShowResult(bResult, sResult);


                MainApp.I_SystemService.CreateLog(Common.StringUtil.GetIpAddress(),
                                     bResult ? Enum.LogLevel.INFO : Enum.LogLevel.WARN,
                                     "APPLY_LOG_QUERY",
                                     "ApplyAgain()",
                                     Enum.LogType.record,
                                     MainApp._USER.USER_NAME,
                                     string.Format("执行再次申请{0} {1}", bResult ? string.Format("成功_条码[{0}]_设备[{1}]", mIO_CONTROL_APPLY.STOCK_BARCODE, mIO_CONTROL_APPLY.DEVICE_CODE) : "失败 ", sResult),
                                     null);
            }
        }

        private void panelQuery_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
