﻿<Window x:Class="SiaSun.LMS.WPFClient.UC.InfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="InfoWindow" Height="300" Width="300">
    <Grid Name="MyGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="90*"></RowDefinition>
        </Grid.RowDefinitions>
        <WrapPanel Name="MyWrapPanel" Grid.Row="0">
        </WrapPanel>
        <DataGrid Name="dataGrid1"  AutoGenerateColumns="True"  Grid.Row="1"
                     AlternatingRowBackground="AliceBlue" RowHeaderWidth="20" 
                     VerticalScrollBarVisibility="Visible"
                     MouseDoubleClick="dataGrid1_MouseDoubleClick" MinWidth="100"/>

    </Grid>
</Window>
