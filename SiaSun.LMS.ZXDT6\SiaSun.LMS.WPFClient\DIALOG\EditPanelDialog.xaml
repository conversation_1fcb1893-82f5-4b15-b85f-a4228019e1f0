﻿<Window x:Class="SiaSun.LMS.WPFClient.Dialog.EditPanelDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="编辑窗口" Height="256" Width="377" MinWidth="400" WindowStyle="None" 
        WindowStartupLocation="CenterScreen"  SizeToContent="WidthAndHeight" MaxHeight="800" MaxWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <uc:ucWindowTopHeader x:Name="ucWinHeader" Grid.Row="0"></uc:ucWindowTopHeader>
        <GroupBox Grid.Row="1" Header="请输入相应编辑内容" Margin="2" >
            <uc:ucEditPanel x:Name="editPanel" Grid.Row="0" Margin="1" ></uc:ucEditPanel>
        </GroupBox>

        <WrapPanel Orientation="Horizontal" Grid.Row="2" Margin="5" HorizontalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
            <Button Name="btnOK"  Width="60" IsDefault="True" IsEnabled="True">确定</Button>
            <Button Name="btnClose"  Width="60" IsCancel="True">关闭</Button>
        </WrapPanel>
        
    </Grid>
</Window>
