﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     siasun
 *       日期：     2014/11/3
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// APPLY_TYPE_PARAM 
    /// </summary>
    [Serializable]
    [DataContract]
    public class APPLY_TYPE_PARAM
    {
        public APPLY_TYPE_PARAM()
        {
        }

        private bool bSendLedMessage;
        
        [DataMember]
        public bool U_SendLedMessage
        {
            get { return bSendLedMessage; }
            set { bSendLedMessage = value; }
        }

        private bool bCreateExceptionTask;

        [DataMember]
        public bool U_CreateExceptionTask
        {
            get { return bCreateExceptionTask; }
            set { bCreateExceptionTask = value; }
        }

        private bool bWriteHisData = true;
        [DataMember]
        public bool U_WriteHisData
        {
            get { return bWriteHisData; }
            set { bWriteHisData = value; }
        }

        private string strExceptionStation;
        [DataMember]
        public string U_ExceptionStation
        {
            get { return strExceptionStation; }
            set { strExceptionStation = value; } 
        }

        private string strNextStation;
        [DataMember]
        public string U_NextStation
        {
            get { return strNextStation; }
            set { strNextStation = value; }
        }

        private string strStorageVerifyStation;
        [DataMember]
        public string U_StorageVerifyStation
        {
            get { return strStorageVerifyStation; }
            set { strStorageVerifyStation = value; }
        }

        /// <summary>
        /// 检查是否存在任务
        /// 2021-01-27 16:19:34 双鹿电池临时当作申请失败时是否删除管理任务开关
        /// </summary>
        private bool bCheckManageExist;
        [DataMember]
        public bool U_CheckManageExist
        {
            get { return bCheckManageExist; }
            set { bCheckManageExist = value; }
        }

        /// <summary>
        /// 申请站台与显示LCD对应关系
        /// </summary>
        private string strLedStation;
        [DataMember]
        public string U_LedStation
        {
            get { return strLedStation; }
            set { strLedStation = value; }
        }

        
        /// <summary>
        /// 申请站台是否需要检查重量
        /// </summary>
        private string strCheckWeightStation;
        [DataMember]
        public string U_CheckWeightStation
        {
            get { return strCheckWeightStation; }
            set { strCheckWeightStation = value; }
        }

        /// <summary>
        /// 申请托盘如果存在库存是否向iWMS申请组盘信息
        /// </summary>
        private string strStorageApplyChange;
        [DataMember]
        public string U_StorageApplyChange
        {
            get { return strStorageApplyChange; }
            set { strStorageApplyChange = value; }
        }






        #region 福斯特fst
        /// <summary>
        /// 物料的属性 fst add 用于验证电池的状态
        /// </summary>
        private string strGoodsProperty;
        [DataMember]
        public string U_GoodsProperty
        {
            get { return strGoodsProperty; }
            set { strGoodsProperty = value; }
        }
        /// <summary>
        /// 关联流程 fst add
        /// </summary>
        private string strRelateProcess;
        [DataMember]
        public string U_RelateProcess
        {
            get { return strRelateProcess; }
            set { strRelateProcess = value; }
        }
        #endregion
    }
}
