﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="LED_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="LED_MAIN" type="SiaSun.LMS.Model.LED_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="LED_MAIN">
			<result property="LED_ID" column="led_id" />
			<result property="LED_IP" column="led_ip" />
			<result property="DEVICE_CODE" column="device_code" />
			<result property="CONTROL_TYPE" column="control_type" />
			<result property="SCREEN_WIDTH" column="screen_width" />
			<result property="SCREEN_HEIGHT" column="screen_height" />
      <result property="LINE_NUM" column="line_num" />
      <result property="AUTO_FLAG" column="auto_flag" />
      <result property="LED_STATUS" column="led_status" />
      <result property="LED_MAIN_REMARK" column="led_main_remark" />
      <result property="LED_MAIN_PARA1" column="led_main_para1" />
      <result property="LED_MAIN_PARA2" column="led_main_para2" />
      <result property="LED_MAIN_PARA3" column="led_main_para3" />
      <result property="LED_MAIN_PARA4" column="led_main_para4" />
      <result property="LED_MAIN_PARA5" column="led_main_para5" />
		</resultMap>
	</resultMaps>
  
  <statements>
	
	    <select id="LED_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        led_id,
        led_ip,
        device_code,
        control_type,
        screen_width,
        screen_height,
        line_num,
        auto_flag,
        led_status,
        led_main_remark,
        led_main_para1,
        led_main_para2,
        led_main_para3,
        led_main_para4,
        led_main_para5
        From LED_MAIN
      </select>
		
		<select id="LED_MAIN_SELECT_BY_ID" parameterClass="int" extends = "LED_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
          led_id=#LED_ID#
        </isParameterPresent>
			</dynamic>
		</select>
    
    <select id="LED_MAIN_SELECT_BY_LED_IP_DEVICE_CODE" parameterClass="Hashtable" extends = "LED_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          led_ip=#LED_IP# AND CHARINDEX(#DEVICE_CODE#, device_code)>0
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="LED_MAIN_SELECT_BY_DEVICE_CODE_AUTO_FLAG" parameterClass="Hashtable" extends = "LED_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          CHARINDEX(#DEVICE_CODE#, device_code)>0 AND auto_flag=#AUTO_FLAG#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="LED_MAIN_SELECT_BY_AUTO_FLAG_LED_STATUS" parameterClass="Hashtable" extends = "LED_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          auto_flag=#AUTO_FLAG# AND led_status=#LED_STATUS#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="LED_MAIN_INSERT" parameterClass="LED_MAIN">
      Insert Into LED_MAIN (
      led_id,
      led_ip,
      device_code,
      control_type,
      screen_width,
      screen_height,
      line_num,
      auto_flag,
      led_status,
      led_main_remark,
      led_main_para1,
      led_main_para2,
      led_main_para3,
      led_main_para4,
      led_main_para5
      )Values(
      #LED_ID#,
      #LED_IP#,
      #DEVICE_CODE#,
      #CONTROL_TYPE#,
      #SCREEN_WIDTH#,
      #SCREEN_HEIGHT#,
      #LINE_NUM#,
      #AUTO_FLAG#,
      #LED_STATUS#,
      #LED_MAIN_REMARK#,
      #LED_MAIN_PARA1#,
      #LED_MAIN_PARA2#,
      #LED_MAIN_PARA3#,
      #LED_MAIN_PARA4#,
      #LED_MAIN_PARA5#
      )
      <!--<selectKey  resultClass="int" type="post" property="LED_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>
		
		<update id="LED_MAIN_UPDATE" parameterClass="LED_MAIN">
      Update LED_MAIN Set
      <!--led_id=#LED_ID#,-->
      led_ip=#LED_IP#,
      device_code=#DEVICE_CODE#,
      control_type=#CONTROL_TYPE#,
      screen_width=#SCREEN_WIDTH#,
      screen_height=#SCREEN_HEIGHT#,
      line_num=#LINE_NUM#,
      auto_flag=#AUTO_FLAG#,
      led_status=#LED_STATUS#,
      led_main_remark=#LED_MAIN_REMARK#,
      led_main_para1=#LED_MAIN_PARA1#,
      led_main_para2=#LED_MAIN_PARA2#,
      led_main_para3=#LED_MAIN_PARA3#,
      led_main_para4=#LED_MAIN_PARA4#,
      led_main_para5=#LED_MAIN_PARA5#
      <dynamic prepend="WHERE">
				<isParameterPresent>
          led_id=#LED_ID#
        </isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="LED_MAIN_DELETE" parameterClass="int">
      Delete From LED_MAIN
      <dynamic prepend="WHERE">
				<isParameterPresent>
          led_id=#LED_ID#
        </isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>