﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Data;
//using System.Data.OracleClient;
using System.Data.SqlClient;
using System.Configuration;

namespace SiaSun.LMS.LED
{
    class Program
    {
        static void Main(string[] args)
        {
            bool firstLoop = true;
            bool failCloseConn = false;
            SqlConnection conn = null;
            byte byteSpeed = 16;
            SDK_TYPE SDKType = SDK_TYPE.BxdualSDK;

            try
            {
                Console.WriteLine(string.Format("{0}:启动服务", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));

                var confRrader = new AppSettingsReader();
                var ledIP = confRrader.GetValue("LED_IP", typeof(string)).ToString();
                if (string.IsNullOrEmpty(ledIP))
                {
                    Console.WriteLine(string.Format("{0}:未获取配置中的IP地址", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                    return;
                }
                var useSDK = confRrader.GetValue("USE_SDK", typeof(string)).ToString();
                if (string.IsNullOrEmpty(useSDK) || !Enum.TryParse(useSDK,out SDKType))
                {
                    Console.WriteLine(string.Format("{0}:配置中的SDK类型有误", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                    return;
                }
                var dbConn = confRrader.GetValue("DB_CONN", typeof(string)).ToString();
                if (string.IsNullOrEmpty(ledIP))
                {
                    Console.WriteLine(string.Format("{0}:未获取配置中的数据库连接字符串", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                    return;
                }  
                var speed = confRrader.GetValue("SHOW_SPEED", typeof(string)).ToString();
                if (string.IsNullOrEmpty(speed))
                {
                    Console.WriteLine(string.Format("{0}:未获取配置中的显示速度", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                    return;
                }
                if (!byte.TryParse(speed, out byteSpeed))
                {
                    Console.WriteLine(string.Format("{0}:配置中的显示速度有误", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                    return;
                }

                conn = new SqlConnection(dbConn);
                conn.Open();
                failCloseConn = true;

                if (SDKType == SDK_TYPE.BxdualSDK)
                {
                    //初始化动态库
                    int err = BxdualSDK.bxDual_InitSdk();
                    if (err < 0)
                    {
                        Console.WriteLine("初始化动态库失败");
                        return;
                    }
                }

                while (true)
                {
                    var dataAdapter = new SqlDataAdapter("select * from SYS_ITEM where ITEM_CLASS_ID=9 order by ITEM_CODE", conn);
                    var ledToUpdate = new DataTable();
                    dataAdapter.Fill(ledToUpdate);

                    if (ledToUpdate == null || ledToUpdate.Rows.Count != 2)
                    {
                        Console.WriteLine(String.Format("{0}:SYS_ITEM(ITEM_CLASS_ID=9 )中数据为空或者行数不等于2", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                        return;
                    }

                    var line1 = ledToUpdate.Rows[0]["ITEM_NAME"].ToString();
                    var line2 = ledToUpdate.Rows[1]["ITEM_NAME"].ToString();

                    if (firstLoop ||
                        line1 != ledToUpdate.Rows[0]["ITEM_REMARK1"].ToString() ||
                        line2 != ledToUpdate.Rows[1]["ITEM_REMARK1"].ToString())
                    {
                        line1 = line1 + "   ";
                        line2 = line2 + "   ";

                        bool result = true;
                        if (SDKType == SDK_TYPE.BxdualSDK)
                        {
                            result = BxdualSDK.SendText(ledIP, line1, line2, byteSpeed);
                        }
                        else
                        {
                            result = OnbonAPI.SendText(ledIP, line1, line2, byteSpeed);
                        }

                        if (result)
                        {
                            var command = new SqlCommand("update SYS_ITEM set ITEM_REMARK1=ITEM_NAME where ITEM_CLASS_ID=9", conn);
                            command.ExecuteNonQuery();
                            command.Dispose();
                        }
                        else
                        {
                            Console.WriteLine(String.Format("{0}:发送LED信息失败", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                        }
                    }

                    firstLoop = false;
                    Thread.Sleep(5000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(String.Format("{0}:异常_{ex.Message}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
            }
            finally
            {
                if (failCloseConn && conn != null)
                {
                    conn.Close();
                }
                if (SDKType == SDK_TYPE.BxdualSDK)
                {
                    //释放动态库
                    BxdualSDK.bxDual_ReleaseSdk();
                }
                Console.WriteLine(String.Format("{0}:服务停止", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                Console.ReadKey();
            }

        }

        public enum SDK_TYPE
        {
            OnbonAPI = 1,
            BxdualSDK = 2
        }
    }



}
