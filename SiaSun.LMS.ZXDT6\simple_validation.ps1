# Simple validation for GoodsInfoSync refactoring
Write-Host "Validating GoodsInfoSync refactored structure..." -ForegroundColor Green

$content = Get-Content -Path "SiaSun.LMS.Implement\Interface\WMS\GoodsInfoSync.cs" -Raw

# Key checks
$checks = @{
    "InputParam class" = "class InputParam"
    "daLeiId field" = "public string daLeiId"
    "goodsCode field" = "public string goodsCode"
    "UnitInformationEntityDTO class" = "class UnitInformationEntityDTO"
    "BrandVO class" = "class BrandVO"
    "OutputParam class" = "class OutputParam"
    "code field" = "public int code"
    "msg field" = "public string msg"
    "traceId field" = "public string traceId"
    "IntefaceMethod" = "internal override string IntefaceMethod"
}

$passed = 0
$total = $checks.Count

foreach ($check in $checks.GetEnumerator()) {
    if ($content -match [regex]::Escape($check.Value)) {
        Write-Host "✓ $($check.Key)" -ForegroundColor Green
        $passed++
    } else {
        Write-Host "✗ $($check.Key)" -ForegroundColor Red
    }
}

Write-Host "`nResult: $passed/$total checks passed" -ForegroundColor $(if ($passed -eq $total) { "Green" } else { "Yellow" })

if ($passed -eq $total) {
    Write-Host "Refactoring validation successful!" -ForegroundColor Green
} else {
    Write-Host "Some checks failed. Please review." -ForegroundColor Yellow
}