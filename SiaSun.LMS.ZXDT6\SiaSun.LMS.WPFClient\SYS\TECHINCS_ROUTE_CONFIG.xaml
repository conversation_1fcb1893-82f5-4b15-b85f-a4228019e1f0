﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.TECHINCS_ROUTE_CONFIG"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"       
      xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
      xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="300" d:DesignWidth="629"
	Title="TECHINCS_ROUTE_CONFIG" Loaded="DocumentContent_Loaded">

    <Grid>
        <Grid.Resources>
            <ContextMenu x:Key="menuTechnics"  MenuItem.Click="ContextMenu_Click">
                <MenuItem Name="menuItemRefresh" Header="刷新"></MenuItem>
            </ContextMenu>
        </Grid.Resources>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <Grid.RowDefinitions>
            <RowDefinition Height="1.5*"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <uc:ucTreeView x:Name="tvwBusinessType" Grid.Column="0" Grid.RowSpan="2" ContextMenu="{StaticResource menuTechnics}" ></uc:ucTreeView>
        <GridSplitter Grid.Column="1" Grid.RowSpan="2"  Width="2" VerticalAlignment="Stretch"></GridSplitter>

        <GroupBox Name="grpbTechnics" Grid.Row="0" Grid.Column="2" Header="流程设置" Tag=" {0}-请选择{0}输送流程记录，显示输送路线列表">
            <uc:ucCommonDataGrid x:Name="gridTechnics"  Margin="1" ></uc:ucCommonDataGrid>
        </GroupBox>

        <GroupBox Name="grpbRoute" Grid.Row="1" Grid.Column="2"   Header="路线设置" Tag=" {0}-输送路线列表">
            <uc:ucCommonDataGrid x:Name="gridRoute" Grid.Row="1" Grid.Column="2" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>

    </Grid>
</ad:DocumentContent>
