﻿<Application
    x:Class="SiaSun.LMS.WPFClient.MainApp"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Startup="Application_Startup">
    <!--<Application.Resources>-->

    <!--<ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="@Styles/BrushStyles.xaml"></ResourceDictionary>
        <ResourceDictionary Source="@Styles/ApplicationStyles.xaml"></ResourceDictionary>
        <ResourceDictionary Source="@Styles/ButtonStyles.xaml"></ResourceDictionary>
        <ResourceDictionary Source="@Styles/DataGridStyles.xaml"></ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>-->
    <!--<ResourceDictionary Source="pack://application:,,,/Fluent;Component/Themes/Office2010/Silver.xaml" />-->
    <!--<ResourceDictionary Source="pack://application:,,,/Fluent;Component/Themes/Office2010/Blue.xaml" />-->
    <!--<ResourceDictionary Source="pack://application:,,,/Fluent;Component/Themes/Office2010/Black.xaml" />-->

    <!--</Application.Resources>-->

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>

                <ResourceDictionary Source="@Styles/Shared.xaml" />
                <ResourceDictionary Source="@Styles/ButtonTemplate.xaml" />
                <ResourceDictionary Source="@Styles/Button.xaml" />
                <ResourceDictionary Source="@Styles/CheckBox.xaml" />
                <ResourceDictionary Source="@Styles/ComboBox.xaml" />
                <ResourceDictionary Source="@Styles/DataGrid.xaml" />
                <ResourceDictionary Source="@Styles/GridSplitter.xaml" />
                <ResourceDictionary Source="@Styles/GroupBox.xaml" />
                <ResourceDictionary Source="@Styles/Label.xaml" />
                <!--<ResourceDictionary Source="@Styles/ListBox.xaml" />-->
                <!--<ResourceDictionary Source="@Styles/ListView.xaml" />-->
                <ResourceDictionary Source="@Styles/Menu.xaml" />
                <ResourceDictionary Source="@Styles/ProgressBar.xaml" />
                <ResourceDictionary Source="@Styles/RadionButton.xaml" />
                <ResourceDictionary Source="@Styles/ScrollBar.xaml" />
                <ResourceDictionary Source="@Styles/Separator.xaml" />
                <ResourceDictionary Source="@Styles/Slider.xaml" />
                <ResourceDictionary Source="@Styles/StatusBar.xaml" />
                <ResourceDictionary Source="@Styles/TabControl.xaml" />
                <ResourceDictionary Source="@Styles/TextBlock.xaml" />
                <ResourceDictionary Source="@Styles/TextBox.xaml" />
                <ResourceDictionary Source="@Styles/ToggleButton.xaml" />
                <ResourceDictionary Source="@Styles/ToolBar.xaml" />
                <ResourceDictionary Source="@Styles/ToolTip.xaml" />
                <!--<ResourceDictionary Source="@Styles/TreeView.xaml" />-->
                <ResourceDictionary Source="@Styles/Window.xaml" />
                <ResourceDictionary Source="/AvalonDock;component/themes/aero.normalcolor.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>

</Application>
