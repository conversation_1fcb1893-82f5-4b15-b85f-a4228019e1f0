﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.EnumMessage
{
    /// <summary>
    /// 系统类信息提示
    /// 1-50
    /// </summary>
    public enum SYSTEM_MESSAGE
    {
        /// <summary>
        /// {0}
        /// </summary>
        SystemException = 1  
    }

    /// <summary>
    /// 成功类信息提示
    /// 51-100
    /// </summary>
    public enum SUCCESS_MESSAGE
    {
        /// <summary>
        /// 操作成功
        /// </summary>
        OperationSuccess = 51
    }

    /// <summary>
    /// 容器类信息提示
    /// 101-200
    /// </summary>
    public enum CONTAINER_MESSAGE
    {
        /// <summary>
        /// 容器[{0}]已经存在任务[{1}]
        /// </summary>
        TaskExist = 101,
        /// <summary>
        /// 容器[{0}]不存在库存
        /// </summary>
        StorageNotExist=102
    }

    /// <summary>
    /// 货位站台类信息提示
    /// 201-300
    /// </summary>
    public enum CELL_MESSAGE
    {
        /// <summary>
        /// 起始位置索引[{0}]不存在
        /// </summary>
        StartCellIDNotExist = 201,   
        /// <summary>
        /// 终止位置索引[{0}]不存在
        /// </summary>
        EndCellIDNotExist = 202,
        /// <summary>
        /// 货位索引[{0}]不存在
        /// </summary>
        CellIDNotExist = 203,
        /// <summary>
        /// 起始位置[{0}]类型不合法
        /// </summary>
        StartCellTypeInvalid = 204,
        /// <summary>
        /// 起始位置[{0}]库区类型[{1}]不合法
        /// </summary>
        StartCellAreaTypeInvalid = 205,
        /// <summary>
        /// 终止位置[{0}]不存在
        /// </summary>
        EndCellCodeNotExist = 206,
        /// <summary>
        /// 终止位置[{0}]类型不合法
        /// </summary>
        EndCellTypeInvalid = 207,
        /// <summary>
        /// 终止位置[{0}]入出类型[{1}]不合法
        /// </summary>
        EndCellInoutInvalid = 208
    }

    /// <summary>
    /// 任务路径类信息提示
    /// 301-400
    /// </summary>
    public enum ROUTE_MESSAGE
    {
        /// <summary>
        /// 起始位置[{0}]到终止位置[{1}]路径不可用
        /// </summary>
        RouteUnavailable = 301
    }

    /// 管理任务类信息提示
    /// 401-500
    /// </summary>
    public enum MANAGE_MESSAGE
    {
        /// <summary>
        ///{0}管理任务索引[{1}]不存在
        /// </summary>
        ManageNotExist = 401,
        /// <summary>
        ///{0}管理任务索引[{1}]已存在
        /// </summary>
        ManageExist = 402,
        /// <summary>
        ///{0}管理任务索引[{1}]已存在对应的控制任务
        /// </summary>
        ControlExist = 403
    }


    /// 计划类信息提示
    /// 501-600
    /// </summary>
    public enum PLAN_MESSAGE
    {
        /// <summary>
        ///计划单[{0}]已存在
        /// </summary>
        PlanExist = 501,
        /// <summary>
        ///  计划单[{0}]已生成入出库记录，不能编辑
        /// </summary>
        RecordExist = 502,
        /// <summary>
        ///计划索引[{0}]不存在
        /// </summary>
        PlanNotExist = 503,
        /// <summary>
        ///更新计划索引[{0}]状态失败
        /// </summary>
        UpdatePlanStateFail = 504
    }

     /// 库存类信息提示
    /// 601-700
    /// </summary>
    public enum STORAGE_MESSAGE
    {
        /// <summary>
        ///{0}库存索引[{1}]不存在
        /// </summary>
        StorageNotExist = 601,
        /// <summary>
        ///{0}库存列表索引[{1}]不存在
        /// </summary>
        StorageListNotExist = 602

    }

    /// 控制类信息提示
    /// 701-800
    /// </summary>
    public enum CONTROL_MESSAGE
    {
        /// <summary>
        ///{0}控制任务索引[{1}]的控制任务未完成
        /// </summary>
        ControlNotComplete = 701

    }

    /// LED类信息提示
    /// 801-900
    /// </summary>
    public enum LED_MESSAGE
    {
        /// <summary>
        ///设备[{0}]对应大屏幕不存在
        /// </summary>
        LEDNotExist = 801,

        /// <summary>
        ///请先添加LED_MAN信息
        /// </summary>
        AddLEDMainFirst = 802,

        /// <summary>
        ///设备[{0}]对应大屏幕行数设置不合法
        /// </summary>
        LEDLineNumInvalid = 803,

        /// <summary>
        ///设备[{0}]对应大屏幕行数不能设置为[{1}]
        /// </summary>
        LEDLineNumXInvalid = 804,

        /// <summary>
        ///设备[{0}]对应大屏幕发送内容行数[{1}]与内容[{2}]格式不一致 
        /// </summary>
        SendTextLineCountAndFormatDiff = 805,

        /// <summary>
        ///设备[{0}]对应大屏幕重新生成LED_LIST失败 
        /// </summary>
        CreateLEDListFail = 806,

        /// <summary>
        ///设备[{0}]对应大屏幕不存在第[{1}]行，请重新生成LED_LIST
        /// </summary>
        LineNumXNotExist = 807
    }


    /// <summary>
    /// 消息提示类型
    /// 5001-6000
    /// </summary>
    public enum PLEASE_MESSAGE
    {
        /// <summary>
        ///请选择下架站台
        /// </summary>
        SelectEndStation =5001,
        /// <summary>
        ///请选择要操作的记录
        /// </summary>
        SelectOperationRecord = 5002
    }
}
