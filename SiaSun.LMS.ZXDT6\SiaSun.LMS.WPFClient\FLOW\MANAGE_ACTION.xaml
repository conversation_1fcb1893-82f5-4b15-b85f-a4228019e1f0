﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.FLOW_ACTION.MANAGE_ACTION"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="MANAGE_ACTION" Height="384" Width="477" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <WrapPanel Grid.Row="0">
            <Border Name="panelDateTime"  MinWidth="140" Margin="0,0,10,0">
                <WrapPanel Name="panelDateSect" VerticalAlignment="Center">
                    <CheckBox Name="chkboxDate" IsChecked="True" VerticalAlignment="Center" Margin="5,5,0,5"></CheckBox>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="从" VerticalAlignment="Center" Margin="5,5,1,5"></TextBlock>
                        <DatePicker Name="dtpStart" Margin="0,5,0,5" Width="90"></DatePicker>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="至" VerticalAlignment="Center" Margin="1,5,1,5"></TextBlock>
                        <DatePicker Name="dtpEnd" Margin="0,5,0,5" Width="90"></DatePicker>
                    </StackPanel>
                </WrapPanel>
            </Border>
            <uc:ucQuickQuery x:Name="ucQueryManage" Margin="1,1,1,3" BorderBrush="Black"></uc:ucQuickQuery>
        </WrapPanel>        

        <GroupBox Margin="1,5,1,1" Header="任务" Grid.Row="1">
            <uc:ucCommonDataGrid x:Name="gridManage" Margin="1,2,1,1"  ></uc:ucCommonDataGrid>
        </GroupBox>

        <GridSplitter  Grid.Row="2" Height="2" HorizontalAlignment="Stretch" ></GridSplitter>

        <GroupBox Margin="1,5,1,1"  Header="任务单" Grid.Row="3">
            <uc:ucSplitPropertyGridTab x:Name="gridManageList" Margin="1"></uc:ucSplitPropertyGridTab>
        </GroupBox>

        <GroupBox Grid.Row="4" Header="操作区"  Margin="1,5,1,1" >
            <uc:ucStatusFlowActionsPanel x:Name="ucFlowManageAction" ></uc:ucStatusFlowActionsPanel>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
