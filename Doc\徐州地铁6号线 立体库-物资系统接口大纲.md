# 徐州地铁6号线 立体库-物资系统接口大纲

## 1. 入库

### 1.1 入库单下发

物资 => 立体库

```
下发
1. A
2. B
3. C
```

### 1.2 入库单完成上报

立体库 => 物资

主要信息：立体库上报入库单最终入库货位
```
回
1.A 01
2.B 02
3.B 03
4.C 04 

Q: 一起报还是分条报？
A: 分条报
```

## 2. 出库

### 2.1 出库单下发

物资 => 立体库

主要信息：以货位为出库依据

Q: 一个出库单包含一个货位出库，还是多个货位出库？

A: 多个货位出库

### 2.2 出库单完成上报

立体库 => 物资

<font color=red>问题：如果是拣选出库，拣选后重新入库的库存位置如果发生变化怎么同步？</font>

```
发
1.A 01位 100个
2.B 02位 50个
3.B 03位 40个
4.C 04位 30个 

Q: 一起报还是分条报？
A: 分条报

Q:拣选出库库存回库后位置会发生变化？
A:复用移库接口
```

## 3. 移库

立体库 => 物资

主要信息：立体库完成库内移库操作后调用此接口，上报移库前后货位变化。如`{beforeCell:"01-01-01",afterCell:"02-02-02"}`

## 4. 盘点

### 4.1 盘点计划下发

物资 => 立体库

### 4.2 盘点结果上报

立体库 => 物资

## 5. 库存同步

立体库 => 物资

主要信息：立体库定时调用此接口，上传当前立体库内库存信息



备注：因为立体库和物资系统业务字段不同，立体库不会持久化全部的下发字段，回传时也仅回传例如ID等唯一字段。

