﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     
 *       日期：     2020/9/5
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using System.Data;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;

	using SiaSun.LMS.Model;

	/// <summary>
	/// STORAGE_LOCK
	/// </summary>
	public class P_STORAGE_LOCK : P_Base_House
	{
		public P_STORAGE_LOCK()
		{
			//
			// TODO: 此处添加STORAGE_LOCK的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_LOCK> GetList(int STORAGE_LIST_ID)
		{
			return ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_STORAGE_LIST_ID", STORAGE_LIST_ID);
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_LOCK> GetListPlanListId(int PLAN_LIST_ID)
		{
			return ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_LOCK> GetList()
		{
			return ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT", null);
		}

		/// <summary>
		/// 得到数据表
		/// </summary>
		public DataTable GetTable()
		{
			return ExecuteQueryForDataTable("STORAGE_LOCK_SELECT", null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public void Add(STORAGE_LOCK storage_lock)
		{
			if (_isOracleProvider)
			{
				int id = this.GetPrimaryID("STORAGE_LOCK");
				storage_lock.STORAGE_LOCK_ID = id;
			}

			ExecuteInsert("STORAGE_LOCK_INSERT", storage_lock);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public void Update(STORAGE_LOCK storage_lock)
		{
			ExecuteUpdate("STORAGE_LOCK_UPDATE", storage_lock);
		}

		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public STORAGE_LOCK GetModel(System.Int64 STORAGE_LOCK_ID)
		{
			return ExecuteQueryForObject<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_ID", STORAGE_LOCK_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public void Delete(System.Int64 STORAGE_LOCK_ID)
		{
			ExecuteDelete("STORAGE_LOCK_DELETE", STORAGE_LOCK_ID);
		}


	}
}
