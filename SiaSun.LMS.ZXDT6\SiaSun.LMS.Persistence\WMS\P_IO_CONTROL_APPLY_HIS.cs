﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// IO_CONTROL_APPLY_HIS
	/// </summary>
	public class P_IO_CONTROL_APPLY_HIS : P_Base_House
	{
		public P_IO_CONTROL_APPLY_HIS ()
		{
			//
			// TODO: 此处添加IO_CONTROL_APPLY_HIS的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<IO_CONTROL_APPLY_HIS> GetList()
		{
			return ExecuteQueryForList<IO_CONTROL_APPLY_HIS>("IO_CONTROL_APPLY_HIS_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(IO_CONTROL_APPLY_HIS io_control_apply_his)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("IO_CONTROL_APPLY_HIS");
                io_control_apply_his.CONTROL_APPLY_ID = id;
            }

            return ExecuteInsert("IO_CONTROL_APPLY_HIS_INSERT",io_control_apply_his);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(IO_CONTROL_APPLY_HIS io_control_apply_his)
		{
			return ExecuteUpdate("IO_CONTROL_APPLY_HIS_UPDATE",io_control_apply_his);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IO_CONTROL_APPLY_HIS GetModel(System.Int32 CONTROL_APPLY_ID)
		{
			return ExecuteQueryForObject<IO_CONTROL_APPLY_HIS>("IO_CONTROL_APPLY_HIS_SELECT_BY_ID",CONTROL_APPLY_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 CONTROL_APPLY_ID)
		{
			return ExecuteDelete("IO_CONTROL_APPLY_HIS_DELETE",CONTROL_APPLY_ID);
		}
		

	}
}
