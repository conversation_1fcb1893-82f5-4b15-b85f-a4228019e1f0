﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.IO;
using System.Data;
using AvalonDock;
using SiaSun.LMS.Model;
//using Microsoft.Windows.Controls.Ribbon;
using Fluent;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : RibbonWindow
    {
        public static MainWindow mainWin;

        IList<Model.SYS_MENU> listSYS_MENU = null;

        public MainWindow()
        {
            InitializeComponent();
            mainWin = this;
            //ThemeFactory.ChangeTheme("siasun");

            this.Closing += new System.ComponentModel.CancelEventHandler(MainWindow_Closing);
            this.Closed += new EventHandler(MainWindow_Closed);

            //初始化窗体
            this.InitWindow();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        public void InitWindow()
        {
            MainWindow.mainWin.Cursor = Cursors.Wait;

            //初始化窗体
            //this.BeginInit();

            //清除所有菜单和窗口
            this.ribbon.Tabs.Clear();
            this.ribbon.QuickAccessItems.Clear();
            this.CloseAllDocuments();

            //标题
            this.Title = SiaSun.LMS.Common.AppSettings.GetValue("Title");

            //设置绑定关系
            //this.ucWinHeader.DataContext = this;

            //状态栏
            this.sbitemNote.Content = string.Format(SiaSun.LMS.Common.AppSettings.GetValue("Status"), MainApp._USER.USER_NAME, MainApp._ROLE.ROLE_NAME);

            //初始化菜单和工具栏
            this.listSYS_MENU = ((MainApp._ROLE.ROLE_ID == 0) ? MainApp.I_SystemService.MENU_GetList() : MainApp.I_SystemService.MENU_GetList_ROLE_Select(MainApp._ROLE.ROLE_ID, true));

            InitMenu();

            //激活起始页面
            InvorkStartWindow();

            //初始化窗体
            //this.EndInit();
            MainWindow.mainWin.Cursor = Cursors.Arrow;
        }

        /// <summary>
        /// 更换角色
        /// </summary>
        public void ChangeRole()
        {
            SelectRole _roleSelect = new SelectRole();
            if (_roleSelect.ShowDialog() == true)
            {
                this.InitWindow();
            }
        }

        #region     ------初始化菜单

        /// <summary>
        /// 移除指定菜单
        /// </summary>
        /// <param name="TSMI"></param>
        private void Remove(Fluent.MenuItem menuItem)
        {
            if (menuItem.Parent is Menu)
            {
                Menu parentMenu = (Menu)menuItem.Parent;
                if (null != parentMenu)
                {
                    parentMenu.Items.Remove(menuItem);
                }
            }
            else
            {
                Fluent.MenuItem parentItem = (Fluent.MenuItem)menuItem.Parent;
                if (null != parentItem)
                {
                    parentItem.Items.Remove(menuItem);
                    if (0 == parentItem.Items.Count)
                    {
                        this.Remove(parentItem);
                    }
                }
            }
        }


        /// <summary>
        /// 点击菜单
        /// </summary>
        private void MenuItem_Click(object sender, RoutedEventArgs e)
        {
            Fluent.MenuItem menuItem = sender as Fluent.MenuItem;
            this.MenuItemClick(menuItem.Tag.ToString());
        }

        /// <summary>
        /// 点击菜单选项处理
        /// </summary>
        public void MenuItemClick(string strCode)
        {
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;
                bResult = MainApp.EVENT_Execute(strCode, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (!bResult)
                {
                    MainApp._MessageDialog.ShowException(string.Format("调用错误-{0}", sResult));
                }
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        #endregion

        #region     ------窗体布局

        /// <summary>
        /// 窗体布局容器加载
        /// </summary>
        private void DockManager_Loaded(object sender, RoutedEventArgs e)
        {
            //判断布局保存文件是否存在
            if (File.Exists("layout.xml"))
            {
                int contentsBeforeRestore = DockManager.DockableContents.Count;
                DockManager.RestoreLayout(@"layout.xml");
            }
        }

        /// <summary>
        /// 清除所有布局窗口
        /// </summary>
        public void CloseAllDocuments()
        {
            for (int i = this.DockManager.Documents.Count - 1; i >= 0; i--)
            {
                this.DockManager.Documents[i].Close();
            }
        }

        /// <summary>
        /// 关闭活动窗体
        /// </summary>
        public void CloseActiveDocument()
        {
            if (this.DockManager.ActiveDocument != null)
            {
                this.DockManager.ActiveDocument.Close();
            }
        }

        #endregion

        #region     ------激活并显示窗体

        /// <summary>
        /// 激活起始页面
        /// </summary>
        private void InvorkStartWindow()
        {
            try
            {
                //判断是否设置起始菜单，并激活窗体
                SiaSun.LMS.Model.SYS_MENU menu = MainApp.I_SystemService.MENU_GetModel(MainApp._ROLE.ROLE_START_MENU_ID);
                if (menu != null && menu.MENU_PARAMETER.Length > 0)
                {
                    string sResult = string.Empty;
                    if (MainApp.EVENT_Execute(menu.MENU_PARAMETER.Replace("title", menu.MENU_NAME), out sResult))
                    {
                        //判断是否允许关闭
                        if (this.DockManager.ActiveDocument != null)
                        {
                            //this.DockManager.ActiveDocument.IsCloseable = false;
                        }
                    }
                    else
                    {
                        MainApp._MessageDialog.Show(sResult, null);
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(string.Format("激活起始页面.{0}", ex.Message));
            }
        }

        /// <summary>
        /// 激活窗体
        /// </summary>
        public bool ActivatForm(string winName, string winTitle, object[] arParaObj, out string sResult)
        {
            bool boolResult = true;
            sResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                DocumentContent[] cnts = this.DockManager.Documents.ToArray();
                if (cnts.Count(r => r.Title == winTitle) > 0)
                {
                    cnts.First(r => r.Title == winTitle).Activate();
                }
                else
                {
                    Type type = Type.GetType(winName, true);
                    if (type == null)
                    {
                        boolResult = false;
                        sResult = string.Format("{0}未找到窗体！", winName);
                        return boolResult;
                    }

                    DocumentContent newContent = (DocumentContent)Activator.CreateInstance(type, arParaObj);
                    if (newContent != null)
                    {
                        newContent.Title = winTitle;
                        newContent.Show(DockManager, false);
                        newContent.Activate();

                        //设置控件权限
                        this.InitRoleWindowControl(newContent, winName, winTitle);
                    }
                }
            }
            catch (Exception ex)
            {
                boolResult = false;
                sResult = ex.Message;
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
                if (!boolResult)
                {
                    MainApp._MessageDialog.ShowException(sResult);
                }
            }
            return boolResult;
        }

        /// <summary>
        /// 初始化角色对窗体的控件设置
        /// </summary>
        private void InitRoleWindowControl(FrameworkElement elem, string WindowName, string MENU_NAME)
        {
            //获得角色ID
            int intRoleID = MainApp._ROLE.ROLE_ID;
            if (intRoleID > 0)
            {
                try
                {
                    //获得菜单
                    if (this.listSYS_MENU.Count(r => !string.IsNullOrEmpty(r.MENU_CLASS) && r.MENU_CLASS == WindowName && r.MENU_NAME == MENU_NAME) > 0)
                    {
                        Model.SYS_MENU mMenu = this.listSYS_MENU.First(r => !string.IsNullOrEmpty(r.MENU_CLASS) && r.MENU_CLASS == WindowName && r.MENU_NAME == MENU_NAME);
                        //获得所有该角色、该窗体的列表
                        IList<Model.SYS_ROLE_WINDOW> listROLE_WINDOW = MainApp.I_SystemService.ROLE_WINDOW_GetList_ROLE_MENU(intRoleID, mMenu.MENU_ID);
                        foreach (Model.SYS_ROLE_WINDOW mROLE_WINDOW in listROLE_WINDOW)
                        {
                            DependencyObject obj = LogicalTreeHelper.FindLogicalNode(elem, mROLE_WINDOW.CONTROL_NAME);
                            if (obj != null)
                            {
                                bool flag = Convert.ToBoolean(mROLE_WINDOW.FLAG);
                                obj.GetType().GetProperty("IsEnabled").SetValue(obj, Convert.ToBoolean(mROLE_WINDOW.FLAG), null);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        #endregion

        #region     ------退出系统

        void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (null == MainApp._ROLE)
            {
                return;
            }
            
            if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Exit) == Sid.Windows.Controls.TaskDialogResult.Cancel)
            {
                e.Cancel = true;
                return;
            }
            this.DialogResult = false;
        }

        void MainWindow_Closed(object sender, EventArgs e)
        {
            
            MainApp.Current.Shutdown();
        }
        #endregion

        #region

        /// <summary>
        /// 初始化菜单
        /// </summary>
        private void InitMenu()
        {
            IList<SYS_MENU> listMenu = null;

            //主菜单MENU_PARENT_ID == 0
            listMenu = listSYS_MENU.Where(r => r.MENU_PARENT_ID == 0).OrderBy(r => r.MENU_ORDER).ToList();

            foreach (SYS_MENU drMENU in listMenu)
            {
                RibbonTabItem menuTab = new RibbonTabItem
                {
                    Header = drMENU.MENU_NAME,
                    Name = string.Format("RibbonTab{0}", drMENU.MENU_ID),
                    Tag = drMENU.MENU_ID
                };

                //添加tab页面下层菜单
                InitRibbonGroupButton(menuTab);

                //无下层菜单，则不显示主菜单
                if (drMENU.MENU_ID != 1 && menuTab.Groups.ToList().Count>0)
                    this.ribbon.Tabs.Add(menuTab);
            }
        }

        private void InitRibbonGroupButton(RibbonTabItem tab)
        {
            IList<SYS_MENU> listMenu = null;
            IList<SYS_MENU> lsdetailMenu = null;

            string sAction = string.Empty;

            listMenu = listSYS_MENU.Where(r => r.MENU_PARENT_ID == Convert.ToInt32(tab.Tag)).OrderBy(r => r.MENU_ORDER).ToList();

            RibbonGroupBox aGroup = new RibbonGroupBox();

            foreach (SYS_MENU drMENU in listMenu)
            {

                lsdetailMenu = listSYS_MENU.Where(r => r.MENU_PARENT_ID == drMENU.MENU_ID).OrderBy(r => r.MENU_ORDER).ToList();

                //二级菜单
                if ((lsdetailMenu != null) && (lsdetailMenu.Count() > 0))
                {
                    Fluent.DropDownButton menuItem = new Fluent.DropDownButton
                    {
                        Name = string.Format("RibbonMenuItem{0}", drMENU.MENU_ID),
                        Header = drMENU.MENU_NAME,
                        LargeIcon = new BitmapImage(new Uri(string.Format("/@Images/{0}", string.IsNullOrEmpty(drMENU.MENU_IMAGE) ? "role.png" : drMENU.MENU_IMAGE), UriKind.RelativeOrAbsolute)),
                        Tag = drMENU.MENU_PARAMETER,
                        //ToolTip = drMENU.MENU_NAME
                    };

                    foreach (SYS_MENU arow in lsdetailMenu)
                    {
                        sAction = arow.MENU_PARAMETER.ToString().Replace("title", arow.MENU_NAME);

                        Fluent.MenuItem adetailBtn = new Fluent.MenuItem
                        {
                            Name = string.Format("RibbonMenuButton{0}", arow.MENU_ID),
                            Header = arow.MENU_NAME,
                            Icon = new BitmapImage(new Uri(string.Format("/@Images/{0}", string.IsNullOrEmpty(drMENU.MENU_IMAGE) ? "role.png" : drMENU.MENU_IMAGE), UriKind.Relative)),
                            Tag = sAction
                        };

                        adetailBtn.Click += new RoutedEventHandler(menuBtn_Click);

                        menuItem.Items.Add(adetailBtn);

                    }
                    aGroup.Items.Add(menuItem);
                }
                else
                {
                    //sAction = drMENU.MENU_PARAMETER.ToString().Replace("title", drMENU.MENU_NAME);
                    if (drMENU.MENU_PARAMETER != null)
                    {
                        sAction = drMENU.MENU_PARAMETER == null ? string.Empty : drMENU.MENU_PARAMETER.ToString().Replace("title", drMENU.MENU_NAME);

                        Fluent.Button menuBtn = new Fluent.Button
                        {
                            Name = string.Format("RibbonButton{0}", drMENU.MENU_ID),
                            Header = drMENU.MENU_NAME,
                            LargeIcon = new BitmapImage(new Uri(string.Format("/@Images/{0}", string.IsNullOrEmpty(drMENU.MENU_IMAGE) ? "role.png" : drMENU.MENU_IMAGE), UriKind.RelativeOrAbsolute)),
                            Tag = sAction,
                            //ToolTip = drMENU.MENU_NAME
                        };

                        menuBtn.Click += new RoutedEventHandler(menuBtn_Click);

                        if (drMENU.MENU_PARENT_ID == 1)
                        {
                            menuBtn.Icon = menuBtn.LargeIcon;
                            menuBtn.ToolTip = menuBtn.Header;

                            QuickAccessMenuItem menuBtnContainer = new QuickAccessMenuItem
                            {
                                Target = menuBtn,
                                IsChecked = true
                            };

                            this.ribbon.QuickAccessItems.Add(menuBtnContainer);
                        }
                        else
                        {
                            aGroup.Items.Add(menuBtn);
                        }
                    }
                }
            }

            //主菜单tab页上有选项，再添加下层菜单
            if (aGroup.Items.Count > 0)
            {
                tab.Groups.Add(aGroup);
            }
        }

        private void menuBtn_Click(object sender, RoutedEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                bResult = MainApp.EVENT_Execute((sender as Control).Tag.ToString(), out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (!bResult)
                {
                    MainApp._MessageDialog.ShowException(string.Format("调用错误-{0}", sResult));
                }
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }


        /// <summary>
        /// ywz 2019-12-25 10:25
        /// 捕获根tree的按钮点击事件，用于写日志
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Grid_Click(object sender, RoutedEventArgs e)
        {
            var userButton = e.OriginalSource as System.Windows.Controls.Button;
            string formName = FindDockingManagerName(e.Source as DependencyObject);

            if (userButton != null)
            {
                MainApp.I_SystemService.CLog(MainApp._USER.USER_NAME, Common.StringUtil.GetIpv4Addr(), formName,
                    string.Format("操作[{0}]_事件[{1}]", userButton.Content, userButton.Tag));
            }
        }

        /// <summary>
        /// ywz 2019-12-25 10:26
        /// 遍历找到窗体的名字，写入日志
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        private string FindDockingManagerName(DependencyObject source)
        {
            if (source == null || source.DependencyObjectType.SystemType.Name == "MainWindow")
            {
                return string.Empty;
            }
            if (source.DependencyObjectType.SystemType.Name == "DockingManager")
            {
                var dockingWindow = source as DockingManager;
                if (dockingWindow != null)
                {
                    return dockingWindow.ActiveContent.ToString();
                }
                else
                {
                    return string.Empty;
                }
            }
            return FindDockingManagerName(VisualTreeHelper.GetParent(source));
        }

        #endregion
    }
}