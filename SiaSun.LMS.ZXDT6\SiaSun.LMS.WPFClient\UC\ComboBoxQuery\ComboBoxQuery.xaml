﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ComboBoxQuery"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300" Loaded="UserControl_Loaded">
    <Grid>
        <WrapPanel Name="myPanel">
            <ComboBox Name="myComboBox" Margin="0,0,0,0" SelectionChanged="myComboBox_SelectionChanged"></ComboBox>
            <Button Name="myButton" Margin="10,0,0,0" Click="myButton_Click"></Button>
        </WrapPanel>
    </Grid>
</UserControl>
