﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Sid.Windows.Controls;
using System.Data;

namespace SiaSun.LMS.WPFClient.Dialog
{   
    public class MessageDialog
    {
        //默认的关键字
        const string Key_Title = "Title";
        const string Key_Header = "Header";
        const string Key_ContentOK = "OK";
        const string Key_ContentFaile = "Faile";
        static string Title, Header, ContentOK, ContentFaile;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MessageDialog()
        {
            //根据关键字获得字典值
            Title = this.GetKeyMessage(Key_Title, null);
            Header = this.GetKeyMessage(Key_Header, null);
            ContentOK = this.GetKeyMessage(Key_ContentOK, null);
            
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        private void GetDefaultMessage()
        {
            if (Title == null) Title = this.GetKeyMessage(Key_Title, null);
            if (Header == null) Header = this.GetKeyMessage(Key_Header, null);                
            if (ContentOK == null) ContentOK = this.GetKeyMessage(Key_ContentOK, null);
            if (ContentFaile == null) ContentFaile = this.GetKeyMessage(Key_ContentFaile, null);
        }

        #region     -------Show()---根据关键字Key显示提示框

        /// <summary>
        /// 显示操作完毕信息
        /// </summary>
        private void Show()
        {
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.Show(Title, Title, ContentOK);
        }

        /// <summary>
        /// 显示操作完毕信息
        /// </summary>
        private void Show(string Message)
        {
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.Show(Title, Title, ContentOK + Message);
        }

        /// <summary>
        /// 显示信息提示
        /// </summary>
        public void Show(string Key,params object[] Param)
        { 
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.Show(Title, Header, this.GetKeyMessage(Key, Param));
        }

        /// <summary>
        /// 显示信息
        /// </summary>
        public void Show(bool Result)
        {
            if (Result)
                this.Show();
            else
                this.Show(ContentFaile, null);
        }

        #endregion

        #region     -------Show()---根据关键字MessageConverter显示提示框

        /// <summary>
        /// 显示信息提示
        /// </summary>
        public void Show(SiaSun.LMS.Enum.MessageConverter _MessageType)
        {
            this.Show(_MessageType, null);
        }

        /// <summary>
        /// 显示信息提示
        /// </summary>
        public void Show(SiaSun.LMS.Enum.MessageConverter _MessageType,params object[] Param)
        {
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.Show(Title, Header, this.GetKeyMessage(_MessageType.ToString(), Param));
        }

        #endregion

        #region     -------ShowDialog()---根据关键字MessageConverter显示提示框

        /// <summary>
        /// 显示信息提示
        /// </summary>
        public TaskDialogResult ShowDialog(SiaSun.LMS.Enum.MessageConverter _MessageType)
        {
            return ShowDialog(_MessageType.ToString(), null);
        }

        /// <summary>
        /// 显示信息提示
        /// </summary>
        public TaskDialogResult ShowDialog(SiaSun.LMS.Enum.MessageConverter _MessageType, params object[] Param)
        {
            return ShowDialog(_MessageType.ToString(), Param);
        }

        #endregion

        #region     -------ShowDialog()---根据关键字关键字Key显示提示框

        /// <summary>
        /// 显示信息提示，返回消息结果
        /// </summary>
        public TaskDialogResult ShowDialog(string Key, object[] Param)
        {
            //获取默认值
            this.GetDefaultMessage();
            return TaskDialog.Show(Title, Header, this.GetKeyMessage(Key,Param),"","",TaskDialogButton.OkCancel, TaskDialogResult.Ok,TaskDialogIcon.Information, TaskDialogIcon.None );
        }

        #endregion

        #region     -------ShowException()---根据关键字MessageConverter显示提示框

        /// <summary>
        /// 显示异常信息提示
        /// </summary>
        public void ShowException(string ExceptionMessage)
        {
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.ShowException(Title, ContentFaile, new Exception(ExceptionMessage));
        }

        /// <summary>
        /// 显示异常信息提示
        /// </summary>
        public void ShowException(Exception ex)
        {
            //获取默认值
            this.GetDefaultMessage();
            TaskDialog.ShowException(Title, ContentFaile, ex);
        }

        #endregion

        /// <summary>
        /// 显示信息
        /// </summary>
        public void ShowResult(bool Result, string Message)
        {
            if (Result)
                this.Show(Message);
            else
                this.ShowException(Message);
        }

        #region     ------根据关键字解析XML，获得信息描述

        /// <summary>
        /// 根据关键字获得信息描述
        /// </summary>
        private string GetKeyMessage(string Key,params object[] Param)
        {
            string strMessage = null;
            try
            {
                return MainApp.I_SystemService.MessageConverter_GetKeyValue(Key, Param);
            }
            catch
            {
                strMessage = Key;
            }
            return string.IsNullOrEmpty(strMessage) ? Key : strMessage;
        }

        #endregion
    }
}
