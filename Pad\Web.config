﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <customErrors mode="Off"/>
    <compilation debug="true" targetFramework="4.8" batch="false"/>
    <sessionState mode="StateServer" stateConnectionString="tcpip=127.0.0.1:42424" timeout="1440"/>
    <pages controlRenderingCompatibilityVersion="4.0"/>
  </system.web>
  <system.webServer>
	  <defaultDocument>
		  <files>
			  <add value="SignIn.aspx" />
		  </files>
	  </defaultDocument>
  </system.webServer>
  <appSettings/>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_I_ManageService" />
        <binding name="BasicHttpBinding_I_BaseService" />
        <binding name="BasicHttpBinding_I_SystemService" />
        <binding name="BasicHttpBinding_I_Manage" />
        <binding name="BasicHttpBinding_I_Database" />
        <binding name="BasicHttpBinding_I_System" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://127.0.0.1:8002/Service/Manage" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_ManageService" contract="ManageService.I_ManageService"
        name="BasicHttpBinding_I_ManageService" />
      <endpoint address="http://127.0.0.1:8002/Service/Base" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_BaseService" contract="BaseService.I_BaseService"
        name="BasicHttpBinding_I_BaseService" />
      <endpoint address="http://127.0.0.1:8002/Service/System" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_SystemService" contract="SystemService.I_SystemService"
        name="BasicHttpBinding_I_SystemService" />
      <endpoint address="http://127.0.0.1:8002/Service/Manage" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_Manage" contract="Manage.I_Manage"
        name="BasicHttpBinding_I_Manage" />
      <endpoint address="http://127.0.0.1:8002/Service/Database" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_Database" contract="Database.I_Database"
        name="BasicHttpBinding_I_Database" />
      <endpoint address="http://127.0.0.1:8002/Service/System" binding="basicHttpBinding"
        bindingConfiguration="BasicHttpBinding_I_System" contract="System.I_System"
        name="BasicHttpBinding_I_System" />
    </client>
  </system.serviceModel>
</configuration>