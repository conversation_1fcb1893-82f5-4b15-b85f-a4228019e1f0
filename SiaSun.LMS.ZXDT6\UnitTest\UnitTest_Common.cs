﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Common;

namespace UnitTest
{
    [TestClass]
    public class UnitTest_Common
    {
        [TestMethod]
        public void TestZicoxPrinter()
        {
        }

        [TestMethod]
        public void TestCoverName()
         {
            Console.WriteLine(StringUtil.CoverPartName(""));
            Console.WriteLine(StringUtil.CoverPartName("张"));
            Console.WriteLine(StringUtil.CoverPartName("张三"));
            Console.WriteLine(StringUtil.CoverPartName("张三丰"));
            Console.WriteLine(StringUtil.CoverPartName("欧阳三丰"));
            Console.WriteLine(StringUtil.CoverPartName("欧阳三千丰"));
            Console.WriteLine(StringUtil.CoverPartName("过来吧很随意"));
        }

        [TestMethod]
        public void TestReplaceChar()
        {
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", 0, 'a'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", 11, 'a'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", -11, 'a'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", 10, 'a'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", -10, 'b'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", 1, 'c'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", -1, 'd'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", 3, 'e'));
            Console.WriteLine(StringUtil.ReplaceChar("1234567890", -3, 'f'));
        }

        [TestMethod]
        public void TestReadExcel()
        {
            string filePath = @"C:\Users\<USER>\Desktop\CGC2项目信息登记表.xlsx";

            Excel excel = new Excel();

            var result = excel.ExcelToDataTable(filePath, true);

            Assert.IsNotNull(result);
        }
    }
}
