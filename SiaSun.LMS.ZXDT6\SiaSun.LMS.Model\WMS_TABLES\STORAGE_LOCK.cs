﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     
 *       日期：     2020/9/5
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// STORAGE_LOCK 
    /// </summary>
    [Serializable]
	[DataContract]
	public class STORAGE_LOCK
	{
		public STORAGE_LOCK()
		{

		}

		private int _storage_lock_id;
		private int _storage_list_id;
		private int _plan_list_id;
		private decimal _storage_lock_quantity;
		private string _storage_lock_flag;
		private string _detail_flag;
		private string _storage_lock_remark;
		private string _lock_code;
		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;

		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public int STORAGE_LOCK_ID
		{
			get { return _storage_lock_id; }
			set { _storage_lock_id = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public int STORAGE_LIST_ID
		{
			get { return _storage_list_id; }
			set { _storage_list_id = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public int PLAN_LIST_ID
		{
			get { return _plan_list_id; }
			set { _plan_list_id = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public decimal STORAGE_LOCK_QUANTITY
		{
			get { return _storage_lock_quantity; }
			set { _storage_lock_quantity = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string STORAGE_LOCK_FLAG
		{
			get { return _storage_lock_flag; }
			set { _storage_lock_flag = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DETAIL_FLAG
		{
			get { return _detail_flag; }
			set { _detail_flag = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string STORAGE_LOCK_REMARK
		{
			get { return _storage_lock_remark; }
			set { _storage_lock_remark = value; }
		}

		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string LOCK_CODE
		{
			get { return _lock_code; }
			set { _lock_code = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
