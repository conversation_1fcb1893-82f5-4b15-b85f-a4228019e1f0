﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.props" Condition="Exists('..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{19557CED-7A32-47B2-A2DB-29A49EBBCBCA}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>UnitTest</RootNamespace>
    <AssemblyName>UnitTest</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">15.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.1.3.2\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\MSTest.TestFramework.1.3.2\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.1.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\SiaSun.LMS.Lib\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="UnitTest_Common.cs" />
    <Compile Include="UnitTest_Implement.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UnitTest_Interface.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\SiaSun.LMS.WinService\BpmsMap.config">
      <Link>BpmsMap.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Log4Net.config">
      <Link>Log4Net.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\BPMS\TB_TRAYINFO_CHECK.xml">
      <Link>Map\BPMS\TB_TRAYINFO_CHECK.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\BPMS\TB_TRAYINFO_UPLOAD.xml">
      <Link>Map\BPMS\TB_TRAYINFO_UPLOAD.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\APPLY_TYPE.xml">
      <Link>Map\WMS_Oracle\APPLY_TYPE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\FLOW_ACTION.xml">
      <Link>Map\WMS_Oracle\FLOW_ACTION.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\FLOW_NODE.xml">
      <Link>Map\WMS_Oracle\FLOW_NODE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\FLOW_PARA.xml">
      <Link>Map\WMS_Oracle\FLOW_PARA.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\FLOW_TYPE.xml">
      <Link>Map\WMS_Oracle\FLOW_TYPE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\GOODS_CLASS.xml">
      <Link>Map\WMS_Oracle\GOODS_CLASS.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\GOODS_MAIN.xml">
      <Link>Map\WMS_Oracle\GOODS_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\GOODS_PROPERTY.xml">
      <Link>Map\WMS_Oracle\GOODS_PROPERTY.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\GOODS_TYPE.xml">
      <Link>Map\WMS_Oracle\GOODS_TYPE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\INTERFACE_QUEUE.xml">
      <Link>Map\WMS_Oracle\INTERFACE_QUEUE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\IO_CONTROL.xml">
      <Link>Map\WMS_Oracle\IO_CONTROL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\IO_CONTROL_APPLY.xml">
      <Link>Map\WMS_Oracle\IO_CONTROL_APPLY.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\IO_CONTROL_APPLY_HIS.xml">
      <Link>Map\WMS_Oracle\IO_CONTROL_APPLY_HIS.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\IO_CONTROL_ROUTE.xml">
      <Link>Map\WMS_Oracle\IO_CONTROL_ROUTE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\LCD_LIST.xml">
      <Link>Map\WMS_Oracle\LCD_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\LCD_MAIN.xml">
      <Link>Map\WMS_Oracle\LCD_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\LED_LIST.xml">
      <Link>Map\WMS_Oracle\LED_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\LED_MAIN.xml">
      <Link>Map\WMS_Oracle\LED_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\MANAGE_DETAIL.xml">
      <Link>Map\WMS_Oracle\MANAGE_DETAIL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\MANAGE_LIST.xml">
      <Link>Map\WMS_Oracle\MANAGE_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\MANAGE_MAIN.xml">
      <Link>Map\WMS_Oracle\MANAGE_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\MANAGE_TYPE.xml">
      <Link>Map\WMS_Oracle\MANAGE_TYPE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\PLAN_DETAIL.xml">
      <Link>Map\WMS_Oracle\PLAN_DETAIL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\PLAN_LIST.xml">
      <Link>Map\WMS_Oracle\PLAN_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\PLAN_MAIN.xml">
      <Link>Map\WMS_Oracle\PLAN_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\PLAN_TYPE.xml">
      <Link>Map\WMS_Oracle\PLAN_TYPE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\RECORD_DETAIL.xml">
      <Link>Map\WMS_Oracle\RECORD_DETAIL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\RECORD_LIST.xml">
      <Link>Map\WMS_Oracle\RECORD_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\RECORD_MAIN.xml">
      <Link>Map\WMS_Oracle\RECORD_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\STORAGE_DETAIL.xml">
      <Link>Map\WMS_Oracle\STORAGE_DETAIL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\STORAGE_LIST.xml">
      <Link>Map\WMS_Oracle\STORAGE_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\STORAGE_LOCK.xml">
      <Link>Map\WMS_Oracle\STORAGE_LOCK.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\STORAGE_MAIN.xml">
      <Link>Map\WMS_Oracle\STORAGE_MAIN.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_ITEM.xml">
      <Link>Map\WMS_Oracle\SYS_ITEM.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_ITEM_LIST.xml">
      <Link>Map\WMS_Oracle\SYS_ITEM_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_LOG.xml">
      <Link>Map\WMS_Oracle\SYS_LOG.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_MENU.xml">
      <Link>Map\WMS_Oracle\SYS_MENU.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_RELATION.xml">
      <Link>Map\WMS_Oracle\SYS_RELATION.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_RELATION_LIST.xml">
      <Link>Map\WMS_Oracle\SYS_RELATION_LIST.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_ROLE.xml">
      <Link>Map\WMS_Oracle\SYS_ROLE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_ROLE_WINDOW.xml">
      <Link>Map\WMS_Oracle\SYS_ROLE_WINDOW.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\SYS_USER.xml">
      <Link>Map\WMS_Oracle\SYS_USER.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\WH_AREA.xml">
      <Link>Map\WMS_Oracle\WH_AREA.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\WH_CELL.xml">
      <Link>Map\WMS_Oracle\WH_CELL.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\WH_DESCRIPTION.xml">
      <Link>Map\WMS_Oracle\WH_DESCRIPTION.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\WH_LOGIC.xml">
      <Link>Map\WMS_Oracle\WH_LOGIC.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\Map\WMS_Oracle\WH_WAREHOUSE.xml">
      <Link>Map\WMS_Oracle\WH_WAREHOUSE.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\providers.config">
      <Link>providers.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="..\SiaSun.LMS.WinService\App.config">
      <Link>App.config</Link>
    </None>
    <Content Include="..\SiaSun.LMS.WinService\HouseMap_Oracle.config">
      <Link>HouseMap_Oracle.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\SiaSun.LMS.WinService\HouseMap_SQLServer.config">
      <Link>HouseMap_SQLServer.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{fd10f3e5-a233-4f52-b4ee-33189d84dbef}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Implement\SiaSun.LMS.Implement.csproj">
      <Project>{10c5da09-0d1e-4ae2-80c4-fb7b916fa850}</Project>
      <Name>SiaSun.LMS.Implement</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="..\SiaSun.LMS.WinService\Map\MapBase.xml">
      <Link>Map\MapBase.xml</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.props'))" />
    <Error Condition="!Exists('..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.targets'))" />
  </Target>
  <Import Project="..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.targets" Condition="Exists('..\packages\MSTest.TestAdapter.1.3.2\build\net45\MSTest.TestAdapter.targets')" />
</Project>