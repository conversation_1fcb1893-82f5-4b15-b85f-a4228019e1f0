using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Filters;
using Owin;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace SiaSun.LMS.HttpApiDemo
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            HttpConfiguration config = new HttpConfiguration();

            // 启用CORS支持
            var cors = new EnableCorsAttribute("*", "*", "*");
            config.EnableCors(cors);

            // 配置路由 - 使用属性路由
            config.MapHttpAttributeRoutes();

            // 移除默认路由配置，只使用属性路由

            // 配置JSON序列化
            config.Formatters.JsonFormatter.SerializerSettings.Formatting =
                Newtonsoft.Json.Formatting.Indented;

            // 使用驼峰命名
            config.Formatters.JsonFormatter.SerializerSettings.ContractResolver =
                new CamelCasePropertyNamesContractResolver();

            // 移除XML格式化器，只使用JSON
            config.Formatters.Remove(config.Formatters.XmlFormatter);

            // 添加全局异常处理
            config.Filters.Add(new GlobalExceptionFilterAttribute());

            app.UseWebApi(config);
        }
    }

    // 全局异常处理过滤器
    public class GlobalExceptionFilterAttribute : ExceptionFilterAttribute
    {
        public override void OnException(HttpActionExecutedContext context)
        {
            var response = new
            {
                success = false,
                message = "服务器内部错误",
                error = context.Exception.Message,
                timestamp = DateTime.Now
            };

            context.Response = context.Request.CreateResponse(
                System.Net.HttpStatusCode.InternalServerError,
                response
            );
        }
    }
}