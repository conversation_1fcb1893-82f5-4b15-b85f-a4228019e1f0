using Microsoft.VisualStudio.TestTools.UnitTesting;
using SiaSun.LMS.Implement.Interface.WMS;
using System;
using System.Text.Json;

namespace SiaSun.LMS.Tests.WMS
{
    [TestClass]
    public class OrganizationalStructureInfoSyncTests
    {
        private OrganizationalStructureInfoSync _orgStructureSync;

        [TestInitialize]
        public void Setup()
        {
            _orgStructureSync = new OrganizationalStructureInfoSync();
        }

        [TestMethod]
        public void IntefaceMethod_ValidInput_ReturnsSuccess()
        {
            // Arrange
            var validInput = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                parentOrgCode = "PARENT001",
                oldOrgCode = "OLD001",
                orgStatus = 1,
                isDelete = 0,
                orgType = "DEPARTMENT",
                personInCharge = "负责人",
                type = 0,
                code = "CODE001",
                createDate = "2025-01-08 10:00:00",
                updateDate = "2025-01-08 10:00:00",
                id = "ID001"
            };

            string inputJson = JsonSerializer.Serialize(validInput);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Length > 0);
            Assert.IsTrue(response.GetProperty("traceId").GetString().Length > 0);
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_Code_ReturnsError()
        {
            // Arrange
            var inputWithMissingCode = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织"
                // code is missing
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingCode);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("code"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_OrgCode_ReturnsError()
        {
            // Arrange
            var inputWithMissingOrgCode = new
            {
                orgLevel = 1,
                // orgCode is missing
                orgName = "测试组织",
                code = "CODE001"
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingOrgCode);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("orgCode"));
        }

        [TestMethod]
        public void IntefaceMethod_MissingRequiredField_OrgName_ReturnsError()
        {
            // Arrange
            var inputWithMissingOrgName = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                // orgName is missing
                code = "CODE001"
            };

            string inputJson = JsonSerializer.Serialize(inputWithMissingOrgName);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("orgName"));
        }

        [TestMethod]
        public void IntefaceMethod_SelfParentRelationship_ReturnsError()
        {
            // Arrange
            var inputWithSelfParent = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                parentOrgCode = "ORG001", // Same as orgCode
                code = "CODE001",
                type = 0
            };

            string inputJson = JsonSerializer.Serialize(inputWithSelfParent);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("parentOrgCode"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidType_ReturnsError()
        {
            // Arrange
            var inputWithInvalidType = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 2 // Invalid type (should be 0 or 1)
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidType);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("type"));
        }

        [TestMethod]
        public void IntefaceMethod_InvalidIsDelete_ReturnsError()
        {
            // Arrange
            var inputWithInvalidIsDelete = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 0,
                isDelete = 2 // Invalid isDelete (should be 0 or 1)
            };

            string inputJson = JsonSerializer.Serialize(inputWithInvalidIsDelete);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(2, response.GetProperty("code").GetInt32());
            Assert.IsTrue(response.GetProperty("msg").GetString().Contains("isDelete"));
        }

        [TestMethod]
        public void IntefaceMethod_ValidTypeInternal_ReturnsSuccess()
        {
            // Arrange
            var inputWithInternalType = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 0, // Internal
                isDelete = 0
            };

            string inputJson = JsonSerializer.Serialize(inputWithInternalType);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_ValidTypeExternal_ReturnsSuccess()
        {
            // Arrange
            var inputWithExternalType = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 1, // External
                isDelete = 0
            };

            string inputJson = JsonSerializer.Serialize(inputWithExternalType);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_ValidIsDeleteFalse_ReturnsSuccess()
        {
            // Arrange
            var inputWithIsDeleteFalse = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 0,
                isDelete = 0 // Not deleted
            };

            string inputJson = JsonSerializer.Serialize(inputWithIsDeleteFalse);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }

        [TestMethod]
        public void IntefaceMethod_ValidIsDeleteTrue_ReturnsSuccess()
        {
            // Arrange
            var inputWithIsDeleteTrue = new
            {
                orgLevel = 1,
                orgCode = "ORG001",
                orgName = "测试组织",
                code = "CODE001",
                type = 0,
                isDelete = 1 // Deleted
            };

            string inputJson = JsonSerializer.Serialize(inputWithIsDeleteTrue);

            // Act
            string result = _orgStructureSync.IntefaceMethod(inputJson);

            // Assert
            Assert.IsNotNull(result);
            var response = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.AreEqual(0, response.GetProperty("code").GetInt32());
        }
    }
}