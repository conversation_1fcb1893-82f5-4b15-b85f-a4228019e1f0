﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_OUT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_DOWN" Height="561" Width="1200" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <uc:ucQuickQuery x:Name="ucQuery" Grid.Row="0"></uc:ucQuickQuery>
        <uc:ucSplitPropertyPanel x:Name="ucSplitPanel" Grid.Row="1"></uc:ucSplitPropertyPanel>

        <uc:ucSplitPropertyGridTab x:Name="ucStorageGroup" Grid.Row="2" ></uc:ucSplitPropertyGridTab>

        <WrapPanel Margin="3" HorizontalAlignment="Left" VerticalAlignment="Center" Grid.Row="3" ButtonBase.Click="WrapPanel_Click">
            <Button Name="btnConfirm"  Width="60" Margin="10,0,0,0">下达任务</Button>
            <Button Name="btnRefresh"  Width="60" Margin="10,0,0,0">刷新</Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent>
