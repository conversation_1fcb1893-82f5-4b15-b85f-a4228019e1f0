﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    //DisallowConcurrentExecution特性，程序会等任务执行完毕以后再去执行，否则会在任务的时间间隔 [Interval]时再启用新的线程执行
    [DisallowConcurrentExecution]
    public class ApplyJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                Program.hostLog.Debug("进入ApplyJob");

                Program.BaseService.sManage.HandleControlApply();
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("轮询处理申请异常", ex);
            }
            finally
            {
                Program.hostLog.Debug("离开ApplyJob");
            }            
        }
    }
}
