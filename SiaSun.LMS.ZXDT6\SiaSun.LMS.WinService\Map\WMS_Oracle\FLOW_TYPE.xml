﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="FLOW_TYPE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="FLOW_TYPE" type="SiaSun.LMS.Model.FLOW_TYPE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="FLOW_TYPE">
			<result property="FLOW_TYPE_ID" column="flow_type_id" />
			<result property="FLOW_TYPE_CODE" column="flow_type_code" />
			<result property="FLOW_TYPE_NAME" column="flow_type_name" />
			<result property="FLOW_TYPE_REMARK" column="flow_type_remark" />
			<result property="FLOW_TYPE_ORDER" column="flow_type_order" />
			<result property="FLOW_TYPE_FLAG" column="flow_type_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="FLOW_TYPE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  flow_type_id,
				  flow_type_code,
				  flow_type_name,
				  flow_type_remark,
				  flow_type_order,
				  flow_type_flag
			From FLOW_TYPE
		</select>
		
		<select id="FLOW_TYPE_SELECT_BY_ID" parameterClass="int" extends = "FLOW_TYPE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_type_id=#FLOW_TYPE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="FLOW_TYPE_INSERT" parameterClass="FLOW_TYPE">
      Insert Into FLOW_TYPE (
      flow_type_id,
      flow_type_code,
      flow_type_name,
      flow_type_remark,
      flow_type_order,
      flow_type_flag
      )Values(
      #FLOW_TYPE_ID#,
      #FLOW_TYPE_CODE#,
      #FLOW_TYPE_NAME#,
      #FLOW_TYPE_REMARK#,
      #FLOW_TYPE_ORDER#,
      #FLOW_TYPE_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="FLOW_TYPE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="FLOW_TYPE_UPDATE" parameterClass="FLOW_TYPE">
      Update FLOW_TYPE Set
      <!--flow_type_id=#FLOW_TYPE_ID#,-->
      flow_type_code=#FLOW_TYPE_CODE#,
      flow_type_name=#FLOW_TYPE_NAME#,
      flow_type_remark=#FLOW_TYPE_REMARK#,
      flow_type_order=#FLOW_TYPE_ORDER#,
      flow_type_flag=#FLOW_TYPE_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					flow_type_id=#FLOW_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="FLOW_TYPE_DELETE" parameterClass="int">
			Delete From FLOW_TYPE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_type_id=#FLOW_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>