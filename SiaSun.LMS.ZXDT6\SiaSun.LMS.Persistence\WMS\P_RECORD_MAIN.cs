﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// RECORD_MAIN
	/// </summary>
	public class P_RECORD_MAIN : P_Base_House
	{
		public P_RECORD_MAIN ()
		{
			//
			// TODO: 此处添加RECORD_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<RECORD_MAIN> GetList()
		{
			return ExecuteQueryForList<RECORD_MAIN>("RECORD_MAIN_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<RECORD_MAIN> GetListPlanId(int PLAN_ID)
        {
            return ExecuteQueryForList<RECORD_MAIN>("RECORD_MAIN_SELECT_BY_PLAN_ID", PLAN_ID);
        }


		/// <summary>
		/// 新建
		/// </summary>
		public int Add(RECORD_MAIN record_main)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("RECORD_MAIN");
                record_main.RECORD_ID = id;
            }

            return ExecuteInsert("RECORD_MAIN_INSERT",record_main);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(RECORD_MAIN record_main)
		{
			return ExecuteUpdate("RECORD_MAIN_UPDATE",record_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public RECORD_MAIN GetModel(System.Int32 RECORD_ID)
		{
			return ExecuteQueryForObject<RECORD_MAIN>("RECORD_MAIN_SELECT_BY_ID",RECORD_ID);
		}

        /// <summary>
        /// 得到明细
        /// tzyg add 2017-03-02
        /// </summary>
        public RECORD_MAIN GetModel_ManageId(System.Int32 MANAGE_ID)
        {
            return ExecuteQueryForObject<RECORD_MAIN>("RECORD_MAIN_SELECT_BY_MANAGE_ID", MANAGE_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 RECORD_ID)
		{
			return ExecuteDelete("RECORD_MAIN_DELETE",RECORD_ID);
		}
		

	}
}
