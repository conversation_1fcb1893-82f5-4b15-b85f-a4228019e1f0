﻿/***************************************************************************
 * 
 *       功能：     管理任务持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// MANAGE_MAIN
	/// </summary>
	public class P_MANAGE_MAIN : P_Base_House
	{
		public P_MANAGE_MAIN ()
		{
			//
			// TODO: 此处添加MANAGE_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<MANAGE_MAIN> GetList()
		{
			return ExecuteQueryForList<MANAGE_MAIN>("MANAGE_MAIN_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_MAIN> GetListPlanID(int PLAN_ID)
        {
            return ExecuteQueryForList<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_PLAN_ID", PLAN_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(MANAGE_MAIN manage_main)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("MANAGE_MAIN");
                manage_main.MANAGE_ID = id;
            }
            return ExecuteInsert("MANAGE_MAIN_INSERT",manage_main);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(MANAGE_MAIN manage_main)
		{
			return ExecuteUpdate("MANAGE_MAIN_UPDATE",manage_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public MANAGE_MAIN GetModel(System.Int32 MANAGE_ID)
		{
			return ExecuteQueryForObject<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_ID",MANAGE_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public MANAGE_MAIN GetModelStockBarcode(string STOCK_BARCODE)
        {
            return ExecuteQueryForObject<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_STOCK_BARCODE", STOCK_BARCODE);
        }
        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_MAIN> GetListByStockBarcodeAndManageType(string STOCK_BARCODE, string MANAGE_TYPE_CODE)
        {
            Hashtable ht = new Hashtable();
            ht.Add("STOCK_BARCODE", STOCK_BARCODE);
            ht.Add("MANAGE_TYPE_CODE", MANAGE_TYPE_CODE);
            
            return ExecuteQueryForList<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_STOCK_BARCODE_MANAGE_TYPE_CODE", ht);
        }
        /// <summary>
        /// 得到明细
        /// </summary>
        public MANAGE_MAIN GetModelRelateCode(string relateCode)
        {
            return ExecuteQueryForObject<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_RELATE_CODE", relateCode);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public MANAGE_MAIN GetModelCELLID(System.Int32 CELL_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("START_CELL_ID", CELL_ID);
            ht.Add("END_CELL_ID", CELL_ID);
            return ExecuteQueryForObject<MANAGE_MAIN>("MANAGE_MAIN_SELECT_BY_CELL_ID", ht);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 MANAGE_ID)
		{
			return ExecuteDelete("MANAGE_MAIN_DELETE",MANAGE_ID);
		}
		

	}
}
