﻿using System.Collections.Generic;
using System.ServiceModel;
using System.Data;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_Device
    {
        #region [LED]

        /*
        [OperationContract]
        bool INIT_LED_LIST(out string sResult);

        [OperationContract]
        SiaSun.LMS.Model.LED_MAIN LED_MAIN_GetModel(int LED_ID);

        [OperationContract]
        SiaSun.LMS.Model.LED_LIST LED_LIST_GetModel(int LED_LIST_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.LED_MAIN> LED_MAIN_GetList_AUTO_FLAG_LED_STATUS(string AUTO_FLAG, string LED_STATUS);

        [OperationContract]
        IList<SiaSun.LMS.Model.LED_LIST> LED_LIST_GetList_LED_ID(int LED_ID);

        [OperationContract]
        void LED_MAIN_Update(SiaSun.LMS.Model.LED_MAIN mLED_MAIN);

        [OperationContract]
        bool AddLedTxt(string DeviceCode, int LineCount, string SendTxt, out string sResult);

        [OperationContract]
        bool AddLedLineTxt(string DeviceCode, int LineNum, string SendTxt, out string sResult);

        [OperationContract]
        bool ClearLedLineTxt(string DeviceCode, int LineNum, out string sResult);

        [OperationContract]
        bool AddLedDefaltTxt(SiaSun.LMS.Model.LED_MAIN mLED_MAIN, int LineCount, string SendTxt, out string sResult);
        */
        #endregion [LED]

        #region [PDA]

        [OperationContract]
        DataTable GetList(string userName, string strSQL);

        [OperationContract]
        PdaMessage LoginValidate(string userName, string password);

        [OperationContract]
        PdaMessage<string> BulkSupplyGetInfo(string userName, string stockBarcode);
               

        #endregion [PDA]




        #region LCD

        [OperationContract]
        string GetStationNameByIp(string clientIp);

        [OperationContract]
        DataTable GetDisplayInfo(string querySql);

        #endregion

    }
}
