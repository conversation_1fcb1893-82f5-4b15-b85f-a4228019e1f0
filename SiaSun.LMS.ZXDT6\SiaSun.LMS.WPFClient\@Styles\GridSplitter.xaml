﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  默认GridSplitter样式  -->
    <Style TargetType="{x:Type GridSplitter}">
        <Setter Property="ShowsPreview" Value="False" />
        <Style.Triggers>
            <Trigger Property="HorizontalAlignment" Value="Stretch">
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="Margin" Value="20,2,20,2" />
                <Setter Property="Height" Value="3" />
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect />
                    </Setter.Value>
                </Setter>
                <Setter Property="Background" Value="#FFB1703C">
                    <!--<Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0.5">
                            <GradientStop Color="Gray" Offset="0.3"></GradientStop>
                            <GradientStop Color="Black" Offset="0.5"></GradientStop>
                            <GradientStop Color="Gray" Offset="0.8"></GradientStop>
                        </LinearGradientBrush>
                    </Setter.Value>-->
                </Setter>
            </Trigger>
            <Trigger Property="VerticalAlignment" Value="Stretch">
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Margin" Value="2,20,2,20" />
                <Setter Property="Width" Value="3" />
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect />
                    </Setter.Value>
                </Setter>
                <Setter Property="Background" Value="#FFB1703C">
                    <!--<Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0.5,1">
                            <GradientStop Color="Gray" Offset="0.3"></GradientStop>
                            <GradientStop Color="Black" Offset="0.5"></GradientStop>
                            <GradientStop Color="Gray" Offset="0.8"></GradientStop>
                        </LinearGradientBrush>
                    </Setter.Value>-->
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>