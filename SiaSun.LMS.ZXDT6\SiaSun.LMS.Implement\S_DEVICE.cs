﻿using System;
using System.Collections.Generic;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;
using SiaSun.LMS.Interface;
using System.Linq;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, 
                     InstanceContextMode = InstanceContextMode.Single, 
                     ConcurrencyMode = ConcurrencyMode.Multiple, 
                     MaxItemsInObjectGraph = int.MaxValue)]
    public class S_Device : I_Device
    {
        private readonly object deviceLockObj = new object();


        #region [LED]

        private const int RUN_SPEED = 10;      //运行速度
        private const int SHOW_TIME = 0;      //停留时间

        /// <summary>
        /// 根据LED_MAIN,生成LED_LIST，每一行基础高度为16，屏幕高度为16的整数倍
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool INIT_LED_LIST(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            string strSQL = string.Empty;
            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(true);

                #region 清空LED_LIST

                strSQL = string.Format(@"DELETE FROM LED_LIST");

                S_Base.sBase.sDatabase.ExecuteNonQuery(strSQL, "dynamicSQL");

                #endregion

                #region 生成LED_LIST

                IList<LED_MAIN> lsLED_MAIN = S_Base.sBase.pLED_MAIN.GetList();

                bResult = lsLED_MAIN.Count != 0;

                if (!bResult)
                {
                    sResult = "请先添加LED_MAN信息";

                    return bResult;
                }

                foreach (LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    bResult = ((mLED_MAIN.SCREEN_HEIGHT / 16) >= mLED_MAIN.LINE_NUM) && ((mLED_MAIN.SCREEN_HEIGHT % mLED_MAIN.LINE_NUM) == 0);

                    if (!bResult)
                    {
                        sResult = string.Format("设备{0}对应的屏幕的行数设置错误", mLED_MAIN.DEVICE_CODE);

                        return bResult;
                    }

                    int LINE_NO = 0;            //行号，图文区号，每行改变

                    int AREA_X = 0;

                    int AREA_Y = 0;             //图文区横纵坐标，每行改变     

                    int AREA_WIDTH = mLED_MAIN.SCREEN_WIDTH;

                    int AREA_HEIGHT = mLED_MAIN.SCREEN_HEIGHT / mLED_MAIN.LINE_NUM;

                    int FONT_SIZE = 10 * (AREA_HEIGHT / 16);

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = new LED_LIST();

                        mLED_LIST.LED_ID = mLED_MAIN.LED_ID;

                        mLED_LIST.LINE_NO = LINE_NO;

                        mLED_LIST.AREA_X = AREA_X;

                        mLED_LIST.AREA_Y = AREA_Y;

                        mLED_LIST.AREA_WIDTH = AREA_WIDTH;

                        mLED_LIST.AREA_HEIGHT = AREA_HEIGHT;

                        mLED_LIST.FILE_NAME = string.Format("{0}.{1}.txt", mLED_MAIN.LED_IP, LINE_NO);

                        mLED_LIST.LINE_TEXT = string.Empty;

                        mLED_LIST.FONT_SIZE = FONT_SIZE;

                        //偶数行，行号从0开始,向左连移
                        if (LINE_NO % 2 != 0)
                        {
                            //SHOW_STUNT = 4;

                            mLED_LIST.SHOW_STUNT = 4;
                        }
                        //奇数行，静止
                        else
                        {
                            //SHOW_STUNT = 1;

                            mLED_LIST.SHOW_STUNT = 1;
                        }

                        mLED_LIST.RUN_SPEED = RUN_SPEED;

                        mLED_LIST.SHOW_TIME = SHOW_TIME;

                        mLED_LIST.LED_LIST_REMARK = string.Empty;

                        mLED_LIST.LED_LIST_PARA1 = string.Empty;

                        mLED_LIST.LED_LIST_PARA2 = string.Empty;

                        mLED_LIST.LED_LIST_PARA3 = string.Empty;

                        mLED_LIST.LED_LIST_PARA4 = string.Empty;

                        mLED_LIST.LED_LIST_PARA5 = string.Empty;

                        S_Base.sBase.pLED_LIST.Add(mLED_LIST);

                        LINE_NO++;

                        AREA_Y += AREA_HEIGHT;
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult += string.Format("INIT_LED_LIST catch Exception:{0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(true);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(true);
                }
            }

            return bResult;
        }
        public SiaSun.LMS.Model.LED_MAIN LED_MAIN_GetModel(int LED_ID)
        {
            return S_Base.sBase.pLED_MAIN.GetModel(LED_ID);
        }

        public SiaSun.LMS.Model.LED_LIST LED_LIST_GetModel(int LED_LIST_ID)
        {
            return S_Base.sBase.pLED_LIST.GetModel(LED_LIST_ID);
        }

        public IList<SiaSun.LMS.Model.LED_MAIN> LED_MAIN_GetList_AUTO_FLAG_LED_STATUS(string AUTO_FLAG, string LED_STATUS)
        {
            return S_Base.sBase.pLED_MAIN.GetList_AUTO_FLAG_LED_STATUS(AUTO_FLAG, LED_STATUS);
        }

        public IList<SiaSun.LMS.Model.LED_LIST> LED_LIST_GetList_LED_ID(int LED_ID)
        {
            return S_Base.sBase.pLED_LIST.GetList(LED_ID);
        }

        public void LED_MAIN_Update(SiaSun.LMS.Model.LED_MAIN mLED_MAIN)
        {
            S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
        }

        /// <summary>
        /// 服务端保存LED发送的内容-发送内容用|分隔
        /// </summary>
        /// <param name="DeviceCode">设备编码</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        public bool AddLedTxt(string DeviceCode, int LineCount, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = S_Base.sBase.pLED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode, "1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }

                string[] arrSendTxt = SendTxt.Split('|');

                if (arrSendTxt.Length != LineCount)
                {
                    bResult = false;

                    sResult = string.Format("发送到设备{0}的内容行数{1}与内容{2}格式不一致", DeviceCode, LineCount, SendTxt);

                    return bResult;
                }

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    //内容行数与大屏设置行数不一致时，重新生成LED_LIST
                    if (mLED_MAIN.LINE_NUM != LineCount)
                    {
                        mLED_MAIN.LINE_NUM = LineCount;

                        bResult = INIT_LED_LIST_LED_MAIN(mLED_MAIN, out sResult);

                        //保存大屏当前设置
                        if (bResult)
                        {
                            S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                        }

                        else
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}重新生成LED_LIST失败", mLED_MAIN.DEVICE_CODE);

                            return bResult;
                        }
                    }

                    //逐行添加LED_LIST的内容
                    int LINE_NO = 0;

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = S_Base.sBase.pLED_LIST.GetModel(mLED_MAIN.LED_ID, LINE_NO);

                        if (mLED_LIST == null)
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LINE_NO);

                            return bResult;
                        }

                        //添加发送内容
                        mLED_LIST.LINE_TEXT = string.IsNullOrEmpty(arrSendTxt[LINE_NO]) ? mLED_LIST.LINE_TEXT : arrSendTxt[LINE_NO];

                        S_Base.sBase.pLED_LIST.Update(mLED_LIST);

                        LINE_NO++;
                    }

                    //更新发送标识,0-未发送
                    mLED_MAIN.LED_STATUS = "0";

                    S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }

        /// <summary>
        /// 服务端保存LED 发送指定行的显示信息
        /// </summary>
        /// <param name="DeviceCode">设备编码</param>
        /// <param name="LineNum">行号</param>
        /// <param name="SendTxt">发送内容</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool AddLedLineTxt(string DeviceCode, int LineNum, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = S_Base.sBase.pLED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode, "1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }


                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    SiaSun.LMS.Model.LED_LIST mLED_LIST = S_Base.sBase.pLED_LIST.GetModel(mLED_MAIN.LED_ID, LineNum);

                    if (mLED_LIST == null)
                    {
                        bResult = false;

                        sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LineNum);

                        return bResult;
                    }

                    if (mLED_LIST.LINE_TEXT.Contains("空闲"))
                        mLED_LIST.LINE_TEXT = string.Empty;


                    //添加发送内容
                    if (mLED_LIST.LINE_TEXT.Contains(SendTxt) && SendTxt != string.Empty)
                        mLED_LIST.LINE_TEXT = mLED_LIST.LINE_TEXT.Replace(SendTxt, string.Empty);
                    else
                    {
                        if (SendTxt.Contains("托盘"))
                            mLED_LIST.LINE_TEXT = SendTxt + mLED_LIST.LINE_TEXT;
                        else
                            mLED_LIST.LINE_TEXT = mLED_LIST.LINE_TEXT + SendTxt;

                    }

                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 0 && mLED_MAIN.DEVICE_CODE == "12010")
                        mLED_LIST.LINE_TEXT = "1#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 1 && mLED_MAIN.DEVICE_CODE == "12010")
                        mLED_LIST.LINE_TEXT = "2#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 0 && mLED_MAIN.DEVICE_CODE == "12011")
                        mLED_LIST.LINE_TEXT = "3#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 1 && mLED_MAIN.DEVICE_CODE == "12011")
                        mLED_LIST.LINE_TEXT = "4#垛机空闲";

                    S_Base.sBase.pLED_LIST.Update(mLED_LIST);
                    //更新发送标识,0-未发送
                    mLED_MAIN.LED_STATUS = "0";

                    S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }

        public bool ClearLedLineTxt(string DeviceCode, int LineNum, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = S_Base.sBase.pLED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode, "1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }


                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    SiaSun.LMS.Model.LED_LIST mLED_LIST = S_Base.sBase.pLED_LIST.GetModel(mLED_MAIN.LED_ID, LineNum);

                    if (mLED_LIST == null)
                    {
                        bResult = false;

                        sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LineNum);

                        return bResult;
                    }


                    if (mLED_LIST.LINE_TEXT.Contains("托盘"))
                    {
                        mLED_LIST.LINE_TEXT = string.Empty;

                        S_Base.sBase.pLED_LIST.Update(mLED_LIST);
                        //更新发送标识,0-未发送
                        mLED_MAIN.LED_STATUS = "0";

                        S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                    }
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }
       
        /// <summary>
        /// 根据LED_MAIN配置，生成对应的LED_LIST
        /// </summary>
        /// <param name="mLED_MAIN"></param>
        /// <param name="_Language"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool INIT_LED_LIST_LED_MAIN(SiaSun.LMS.Model.LED_MAIN mLED_MAIN, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            string strSQL = string.Empty;

            try
            {
                #region 清空对应的LED_LIST

                strSQL = string.Format(@"DELETE FROM LED_LIST WHERE LED_ID='{0}'", mLED_MAIN.LED_ID);

                S_Base.sBase.sDatabase.ExecuteNonQuery(strSQL, "dynamicSQL");

                #endregion

                #region 根据配置生成LED_LIST

                if (mLED_MAIN != null)
                {
                    bResult = ((mLED_MAIN.SCREEN_HEIGHT / 16) >= mLED_MAIN.LINE_NUM) && ((mLED_MAIN.SCREEN_HEIGHT % mLED_MAIN.LINE_NUM) == 0);

                    if (!bResult)
                    {
                        sResult = string.Format("设备{0}的显示屏行数不能设为{1}", mLED_MAIN.DEVICE_CODE, mLED_MAIN.LINE_NUM);

                        return bResult;
                    }

                    int LINE_NO = 0;            //行号，图文区号，每行改变

                    int AREA_X = 0;

                    int AREA_Y = 0;             //图文区横纵坐标，每行改变     

                    int AREA_WIDTH = mLED_MAIN.SCREEN_WIDTH;

                    int AREA_HEIGHT = mLED_MAIN.SCREEN_HEIGHT / mLED_MAIN.LINE_NUM;

                    int FONT_SIZE = 10 * (AREA_HEIGHT / 16);

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = new LED_LIST();

                        mLED_LIST.LED_ID = mLED_MAIN.LED_ID;

                        mLED_LIST.LINE_NO = LINE_NO;

                        mLED_LIST.AREA_X = AREA_X;

                        mLED_LIST.AREA_Y = AREA_Y;

                        mLED_LIST.AREA_WIDTH = AREA_WIDTH;

                        mLED_LIST.AREA_HEIGHT = AREA_HEIGHT;

                        mLED_LIST.FILE_NAME = string.Format("{0}.{1}.txt", mLED_MAIN.LED_IP, LINE_NO);

                        mLED_LIST.LINE_TEXT = string.Empty;

                        mLED_LIST.FONT_SIZE = FONT_SIZE;

                        //偶数行，行号从0开始,向左连移
                        if (LINE_NO % 2 != 0)
                        {
                            //SHOW_STUNT = 4;

                            mLED_LIST.SHOW_STUNT = 4;
                        }
                        //奇数行，静止
                        else
                        {
                            //SHOW_STUNT = 1;

                            mLED_LIST.SHOW_STUNT = 1;
                        }

                        mLED_LIST.RUN_SPEED = RUN_SPEED;

                        mLED_LIST.SHOW_TIME = SHOW_TIME;

                        mLED_LIST.LED_LIST_REMARK = string.Empty;

                        mLED_LIST.LED_LIST_PARA1 = string.Empty;

                        mLED_LIST.LED_LIST_PARA2 = string.Empty;

                        mLED_LIST.LED_LIST_PARA3 = string.Empty;

                        mLED_LIST.LED_LIST_PARA4 = string.Empty;

                        mLED_LIST.LED_LIST_PARA5 = string.Empty;

                        S_Base.sBase.pLED_LIST.Add(mLED_LIST);

                        LINE_NO++;

                        AREA_Y += AREA_HEIGHT;
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult += string.Format("INIT_LED_LIST_LED_MAIN catch Exception:{0}", ex.Message);
            }

            return bResult;
        }

        /// <summary>
        /// 向LED发送固定内容-发送内容用|分隔
        /// </summary>
        /// <param name="mLED_MAIN">屏幕Model</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        public bool AddLedDefaltTxt(SiaSun.LMS.Model.LED_MAIN mLED_MAIN, int LineCount, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                if (mLED_MAIN != null)
                {
                    string[] arrSendTxt = SendTxt.Split('|');

                    if (arrSendTxt.Length != LineCount)
                    {
                        bResult = false;

                        sResult = string.Format("发送到设备{0}的内容行数{1}与内容{2}格式不一致", mLED_MAIN.DEVICE_CODE, LineCount, SendTxt);

                        return bResult;
                    }

                    //内容行数与大屏设置行数不一致时，重新生成LED_LIST
                    if (mLED_MAIN.LINE_NUM != LineCount)
                    {
                        mLED_MAIN.LINE_NUM = LineCount;

                        bResult = INIT_LED_LIST_LED_MAIN(mLED_MAIN, out sResult);

                        //保存大屏当前设置
                        if (bResult)
                        {
                            S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                        }

                        else
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}重新生成LED_LIST失败", mLED_MAIN.DEVICE_CODE);

                            return bResult;
                        }
                    }

                    //逐行添加LED_LIST的内容
                    int LINE_NO = 0;

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = S_Base.sBase.pLED_LIST.GetModel(mLED_MAIN.LED_ID, LINE_NO);

                        if (mLED_LIST == null)
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", mLED_MAIN.DEVICE_CODE, LINE_NO);

                            return bResult;
                        }

                        //添加发送内容,空值则保留原值
                        mLED_LIST.LINE_TEXT = string.IsNullOrEmpty(arrSendTxt[LINE_NO]) ? mLED_LIST.LINE_TEXT : arrSendTxt[LINE_NO];

                        S_Base.sBase.pLED_LIST.Update(mLED_LIST);

                        LINE_NO++;
                    }

                    //1已发送,0未发送
                    mLED_MAIN.LED_STATUS = "0";

                    S_Base.sBase.pLED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedDefaltTxt catch Exception: {0}", ex));
            }

            return bResult;
        }

        private bool ledMessageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN)
        {
            bool bResult = true;

            string sResult = string.Empty;

            string ledMes = string.Empty;

            try
            {
                SiaSun.LMS.Model.WH_CELL mSTART_WM_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = S_Base.sBase.pWH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = S_Base.sBase.pMANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "1")
                {
                    switch (mEND_WH_CELL.DEVICE_CODE.TrimEnd())
                    {
                        case "18001":
                            ledMes = string.Format("1#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 0, ledMes, out sResult);
                            break;
                        case "18002":
                            ledMes = string.Format("2#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 1, ledMes, out sResult);
                            break;

                        case "18003":
                            ledMes = string.Format("3#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 0, ledMes, out sResult);
                            break;
                        case "18004":
                            ledMes = string.Format("4#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 1, ledMes, out sResult);
                            break;
                    }
                }

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "2")
                {
                    switch (mSTART_WM_CELL.DEVICE_CODE.TrimEnd())
                    {
                        case "18001":
                            ledMes = string.Format("1#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 0, ledMes, out sResult);
                            break;
                        case "18002":
                            ledMes = string.Format("2#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 1, ledMes, out sResult);
                            break;
                        case "18003":
                            ledMes = string.Format("3#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 0, ledMes, out sResult);
                            break;
                        case "18004":
                            ledMes = string.Format("4#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 1, ledMes, out sResult);
                            break;

                    }
                }
            }
            catch
            {
            }

            return bResult;


        }

        #endregion [LED]


        #region [PDA]

        /// <summary>
        /// 得到列表
        /// </summary>
        /// <param name="querySql"></param>
        /// <returns></returns>
        public DataTable GetList(string userName, string querySql)
        {
            DataTable result = null; 
            try
            {
                result = S_Base.sBase.sDatabase.GetList(querySql);
            }
            finally
            {
                S_Base.sBase.Log.Info($"参数userName[{userName}]_querySql[{querySql}]_返回值行数[{(result == null ? 0 : result.Rows.Count)}]");
            }
            return result;
        }

        /// <summary>
        /// 验证登录
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public PdaMessage LoginValidate(string userName, string password)
        {
            PdaMessage result = new PdaMessage();

            try
            {
                var user = S_Base.sBase.pSYS_USER.Login(userName,Common.Encrypt.GetHashCode(password));

                if (user != null)
                {
                    result.Flag = true;
                    result.Message = user.USER_NAME;
                }
                else
                {
                    result.Flag = false;
                    result.Message = $"用户名或密码错误";
                }
            }
            catch (Exception ex)
            {
                result.Flag = false;
                result.Message = $"服务端异常[{ex.Message}]";
                S_Base.sBase.Log.Error("处理登录异常", ex);
            }
            finally
            {
                S_Base.sBase.Log.Info($"参数userName[{userName}]|password[{password}]_返回值[{result.ToString()}]");
            }

            return result;
        }

        /// <summary>
        /// 零拣区补货通过条码获取任务信息
        /// </summary>
        /// <param name="stockBarcode"></param>
        /// <returns></returns>
        public PdaMessage<string> BulkSupplyGetInfo(string userName, string stockBarcode)
        {
            PdaMessage<string> result = new PdaMessage<string>();

            try
            {
                string queryString = $@"
                            select M.MANAGE_ID, L.MANAGE_LIST_ID, G.GOODS_CODE, G.GOODS_NAME, L.MANAGE_LIST_QUANTITY, C.CELL_NAME
                            from MANAGE_MAIN M 
                                inner join MANAGE_LIST L on M.MANAGE_ID = L.MANAGE_ID 
                                left join GOODS_MAIN G on L.GOODS_ID = G.GOODS_ID
                                left join WH_CELL C on M.END_CELL_ID = C.CELL_ID 
                                left join WH_AREA A on C.AREA_ID = A.AREA_ID
                            where M.MANAGE_TYPE_CODE ='{Enum.MANAGE_TYPE.ManageMove.ToString()}'
                                and M.MANAGE_STATUS = '{Enum.MANAGE_STATUS.WaitConfirm.ToString()}'
                                and M.STOCK_BARCODE = 'T000{Common.StringUtil.ReplaceChar(stockBarcode, -6, '-')}'
                                and M.MANAGE_SOURCE= '{Enum.SystemName.SSWMS.ToString()}'";

                var dtTaskInfo = S_Base.sBase.sDatabase.GetList(queryString);
                if (dtTaskInfo != null && dtTaskInfo.Rows.Count > 0)
                {
                    foreach (DataRow item in dtTaskInfo.Rows)
                    {
                        result.DataList.Add($"{item["MANAGE_ID"]}|{item["MANAGE_LIST_ID"]}|{item["GOODS_CODE"]}|{item["GOODS_NAME"]}|{item["MANAGE_LIST_QUANTITY"]}|{item["CELL_NAME"]}");
                    }
                    result.Flag = true;
                }
                else
                {
                    result.Flag = false;
                    result.Message = $"未找到数据";
                }
            }
            catch (Exception ex)
            {
                result.Flag = false;
                result.Message = $"服务端异常[{ex.Message}]";
                S_Base.sBase.Log.Error("异常", ex);
            }
            finally
            {
                S_Base.sBase.Log.Info($"参数userName[{userName}]_stockBarcode[{stockBarcode}]_返回值[{result.ToString()}]");
            }

            return result;
        }


        #endregion [PDA]


        #region LCD

        /// <summary>
        /// 根据IP地址获取站台编码
        /// </summary>
        /// <param name="clientIp"></param>
        /// <returns></returns>
        public string GetStationNameByIp(string clientIp)
        {
            string result = string.Empty;

            try
            {
                var lcdMain = S_Base.sBase.pLCD_MAIN.GetList().FirstOrDefault(r => r.LCD_IP == clientIp);
                if (lcdMain != null)
                {
                    result = $"{lcdMain.LCD_STATION}-{lcdMain.LCD_CODE}";
                }
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"异常_参数clientIp[{clientIp}]", ex);
            }

            return result;
        }

        /// <summary>
        /// 根据站台编码获取显示内容
        /// </summary>
        /// <param name="station"></param>
        /// <returns></returns>
        public DataTable GetDisplayInfo(string querySql)
        {
            return S_Base.sBase.sDatabase.GetList(querySql);
        }

        #endregion
    }
}
