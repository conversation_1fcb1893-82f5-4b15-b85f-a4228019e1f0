<?xml version="1.0" encoding="utf-8" ?>

<providers
  xmlns="http://ibatis.apache.org/providers"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<clear />
	<provider
		  name="sqlServer1.0"
		  description="Microsoft SQL Server 7.0/2000, provider V1.0.3300.0 in framework .NET V1.0"
		  enabled="false"
		  assemblyName="System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
		  connectionClass="System.Data.SqlClient.SqlConnection"
		  commandClass="System.Data.SqlClient.SqlCommand"
		  parameterClass="System.Data.SqlClient.SqlParameter"
		  parameterDbTypeClass="System.Data.SqlDbType"
		  parameterDbTypeProperty="SqlDbType"
		  dataAdapterClass="System.Data.SqlClient.SqlDataAdapter"
		  commandBuilderClass="System.Data.SqlClient.SqlCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix="@" />
	<provider
		  name="sqlServer1.1"
		  description="Microsoft SQL Server 7.0/2000, provider V1.0.5000.0 in framework .NET V1.1"
		  default="true"
		  assemblyName="System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
		  connectionClass="System.Data.SqlClient.SqlConnection"
		  commandClass="System.Data.SqlClient.SqlCommand"
		  parameterClass="System.Data.SqlClient.SqlParameter"
		  parameterDbTypeClass="System.Data.SqlDbType"
		  parameterDbTypeProperty="SqlDbType"
		  dataAdapterClass="System.Data.SqlClient.SqlDataAdapter"
		  commandBuilderClass="System.Data.SqlClient.SqlCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix="@"
	/>

	<provider name="sqlServer2.0"
	  enabled="true"
	  description="Microsoft SQL Server, provider V******* in framework .NET V2.0"
	  assemblyName="System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
	  connectionClass="System.Data.SqlClient.SqlConnection"
	  commandClass="System.Data.SqlClient.SqlCommand"
	  parameterClass="System.Data.SqlClient.SqlParameter"
	  parameterDbTypeClass="System.Data.SqlDbType"
	  parameterDbTypeProperty="SqlDbType"
	  dataAdapterClass="System.Data.SqlClient.SqlDataAdapter"
	  commandBuilderClass=" System.Data.SqlClient.SqlCommandBuilder"
	  usePositionalParameters = "false"
	  useParameterPrefixInSql = "true"
	  useParameterPrefixInParameter = "true"
	  parameterPrefix="@"
	  allowMARS="false" />

	<provider
		  name="OleDb1.1"
		  description="OleDb, provider V1.0.5000.0 in framework .NET V1.1"
		  enabled="true"
		  assemblyName="System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
		  connectionClass="System.Data.OleDb.OleDbConnection"
		  commandClass="System.Data.OleDb.OleDbCommand"
		  parameterClass="System.Data.OleDb.OleDbParameter"
		  parameterDbTypeClass="System.Data.OleDb.OleDbType"
		  parameterDbTypeProperty="OleDbType"
		  dataAdapterClass="System.Data.OleDb.OleDbDataAdapter"
		  commandBuilderClass="System.Data.OleDb.OleDbCommandBuilder"
		  usePositionalParameters = "true"
		  useParameterPrefixInSql = "false"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix = ""
	/>
	<provider
		  name="Odbc1.1"
		  description="Odbc, provider V1.0.5000.0 in framework .NET V1.1"
		  enabled="true"
		  assemblyName="System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
		  connectionClass="System.Data.Odbc.OdbcConnection"
		  commandClass="System.Data.Odbc.OdbcCommand"
		  parameterClass="System.Data.Odbc.OdbcParameter"
		  parameterDbTypeClass="System.Data.Odbc.OdbcType"
		  parameterDbTypeProperty="OdbcType"
		  dataAdapterClass="System.Data.Odbc.OdbcDataAdapter"
		  commandBuilderClass="System.Data.Odbc.OdbcCommandBuilder"
		  usePositionalParameters = "true"
		  useParameterPrefixInSql = "false"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix = "@"
	/>
	<provider
		  name="oracle9.2"
		  description="Oracle, Oracle provider V9.2.0.401"
		  enabled="false"
		  assemblyName="Oracle.DataAccess, Version=9.2.0.401, Culture=neutral, PublicKeyToken=89b483f429c47342"
		  connectionClass="Oracle.DataAccess.Client.OracleConnection"
		  commandClass="Oracle.DataAccess.Client.OracleCommand"
		  parameterClass="Oracle.DataAccess.Client.OracleParameter"
		  parameterDbTypeClass="Oracle.DataAccess.Client.OracleDbType"
		  parameterDbTypeProperty="OracleDbType"
		  dataAdapterClass="Oracle.DataAccess.Client.OracleDataAdapter"
		  commandBuilderClass="Oracle.DataAccess.Client.OracleCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix=":"
		  useDeriveParameters="false"
	/>
	<provider
		  name="oracle10.1"
		  description="Oracle, oracle provider V10.1.0.301"
		  enabled="false"
		  assemblyName="Oracle.DataAccess, Version=10.1.0.301, Culture=neutral, PublicKeyToken=89b483f429c47342"
		  connectionClass="Oracle.DataAccess.Client.OracleConnection"
		  commandClass="Oracle.DataAccess.Client.OracleCommand"
		  parameterClass="Oracle.DataAccess.Client.OracleParameter"
		  parameterDbTypeClass="Oracle.DataAccess.Client.OracleDbType"
		  parameterDbTypeProperty="OracleDbType"
		  dataAdapterClass="Oracle.DataAccess.Client.OracleDataAdapter"
		  commandBuilderClass="Oracle.DataAccess.Client.OracleCommandBuilder"
		  usePositionalParameters = "true"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix=":"
		  useDeriveParameters="false"
	/>
	<provider
		  name="oracleClient1.0"
		  description="Oracle, Microsoft provider V1.0.5000.0"
		  enabled="true"
		  default="false"
		  assemblyName="System.Data.OracleClient, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
		  connectionClass="System.Data.OracleClient.OracleConnection"
		  commandClass="System.Data.OracleClient.OracleCommand"
		  parameterClass="System.Data.OracleClient.OracleParameter"
		  parameterDbTypeClass="System.Data.OracleClient.OracleType"
		  parameterDbTypeProperty="OracleType"
		  dataAdapterClass="System.Data.OracleClient.OracleDataAdapter"
		  commandBuilderClass="System.Data.OracleClient.OracleCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix=":"
	/>
	<provider
		   name="OracleManagedDataAccess4"
		   description="Oracle, Oracle.ManagedDataAccess **********"
		   enabled="true"
		   assemblyName="Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342"
		   connectionClass="Oracle.ManagedDataAccess.Client.OracleConnection"
		   commandClass="Oracle.ManagedDataAccess.Client.OracleCommand"
		   parameterClass="Oracle.ManagedDataAccess.Client.OracleParameter"
		   parameterDbTypeClass="Oracle.ManagedDataAccess.Client.OracleDbType"
		   parameterDbTypeProperty="OracleDbType"
		   dataAdapterClass="Oracle.ManagedDataAccess.Client.OracleDataAdapter"
		   commandBuilderClass="Oracle.ManagedDataAccess.Client.OracleCommandBuilder"
		   usePositionalParameters = "false"
		   useParameterPrefixInSql = "true"
		   useParameterPrefixInParameter = "false"
		   parameterPrefix=":"
		   useDeriveParameters="false"
	/>
	<provider
		  name="ByteFx"
		  description="MySQL, ByteFx provider V0.7.6.15073"
		  enabled="false"
		  assemblyName="ByteFX.MySqlClient, Version=0.7.6.15073, Culture=neutral, PublicKeyToken=f2fef6fed1732fc1"
		  connectionClass="ByteFX.Data.MySqlClient.MySqlConnection"
		  commandClass="ByteFX.Data.MySqlClient.MySqlCommand"
		  parameterClass="ByteFX.Data.MySqlClient.MySqlParameter"
		  parameterDbTypeClass="ByteFX.Data.MySqlClient.MySqlDbType"
		  parameterDbTypeProperty="MySqlDbType"
		  dataAdapterClass="ByteFX.Data.MySqlClient.MySqlDataAdapter"
		  commandBuilderClass="ByteFX.Data.MySqlClient.MySqlCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix="@"
	/>
	<provider
		  name="MySql"
		  description="MySQL, MySQL provider V1.0.4.20163"
		  enabled="false"
		  assemblyName="MySql.Data, Version=1.0.4.20163, Culture=neutral, PublicKeyToken=c5687fc88969c44d"
		  connectionClass="MySql.Data.MySqlClient.MySqlConnection"
		  commandClass="MySql.Data.MySqlClient.MySqlCommand"
		  parameterClass="MySql.Data.MySqlClient.MySqlParameter"
		  parameterDbTypeClass="MySql.Data.MySqlClient.MySqlDbType"
		  parameterDbTypeProperty="MySqlDbType"
		  dataAdapterClass="MySql.Data.MySqlClient.MySqlDataAdapter"
		  commandBuilderClass="MySql.Data.MySqlClient.MySqlCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix="@"
	/>
	<provider
		  name="SQLite3"
		  description="SQLite, SQLite.NET provider V0.21.1869.3794"
		  enabled="false"
		  assemblyName="SQLite.NET, Version=0.21.1869.3794, Culture=neutral, PublicKeyToken=c273bd375e695f9c"
		  connectionClass="Finisar.SQLite.SQLiteConnection"
		  commandClass="Finisar.SQLite.SQLiteCommand"
		  parameterClass="Finisar.SQLite.SQLiteParameter"
		  parameterDbTypeClass="System.Data.DbType, System.Data"
		  parameterDbTypeProperty="DbType"
		  dataAdapterClass="Finisar.SQLite.SQLiteDataAdapter"
		  commandBuilderClass="Finisar.SQLite.SQLiteCommandBuilder"
		  usePositionalParameters = "true"
		  useParameterPrefixInSql = "false"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix=""
		  setDbParameterPrecision="false"
		  setDbParameterScale="false"
		  setDbParameterSize="false"
	/>
	<provider
		  name="Firebird1.7"
		  description="Firebird, Firebird SQL .NET provider V1.7.0.33200"
		  enabled="false"
		  assemblyName="FirebirdSql.Data.Firebird, Version=1.7.0.33200, Culture=neutral, PublicKeyToken=fa843d180294369d"
		  connectionClass="FirebirdSql.Data.Firebird.FbConnection"
		  commandClass="FirebirdSql.Data.Firebird.FbCommand"
		  parameterClass="FirebirdSql.Data.Firebird.FbParameter"
		  parameterDbTypeClass="FirebirdSql.Data.Firebird.FbDbType"
		  parameterDbTypeProperty="FbDbType"
		  dataAdapterClass="FirebirdSql.Data.Firebird.FbDataAdapter"
		  commandBuilderClass="FirebirdSql.Data.Firebird.FbCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix="@"
	/>
	<provider
		  name="PostgreSql0.7"
		  description="PostgreSql, Npgsql provider V*******"
		  enabled="false"
		  assemblyName="Npgsql, Version=*******, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7"
		  connectionClass="Npgsql.NpgsqlConnection"
		  commandClass="Npgsql.NpgsqlCommand"
		  parameterClass="Npgsql.NpgsqlParameter"
		  parameterDbTypeClass="NpgsqlTypes.NpgsqlDbType"
		  parameterDbTypeProperty="NpgsqlDbType"
		  dataAdapterClass="Npgsql.NpgsqlDataAdapter"
		  commandBuilderClass="Npgsql.NpgsqlCommandBuilder"
		  usePositionalParameters = "false"
		  useParameterPrefixInSql = "true"
		  useParameterPrefixInParameter = "true"
		  parameterPrefix=":"
	/>
	<provider
		  name="iDb2.10"
		  enabled="false"
		  assemblyName="IBM.Data.DB2.iSeries, Version=10.0.0.0,Culture=neutral, PublicKeyToken=9cdb2ebfb1f93a26, Custom=null"
		  connectionClass="IBM.Data.DB2.iSeries.iDB2Connection"
		  commandClass="IBM.Data.DB2.iSeries.iDB2Command"
		  parameterClass="IBM.Data.DB2.iSeries.iDB2Parameter"
		  parameterDbTypeClass="IBM.Data.DB2.iSeries.iDB2DbType"
		  parameterDbTypeProperty="iDB2DbType"
		  dataAdapterClass="IBM.Data.DB2.iSeries.iDB2DataAdapter"
		  commandBuilderClass="IBM.Data.DB2.iSeries.iDB2CommandBuilder"
		  usePositionalParameters = "true"
		  useParameterPrefixInSql = "false"
		  useParameterPrefixInParameter = "false"
		  parameterPrefix = "" />
</providers>