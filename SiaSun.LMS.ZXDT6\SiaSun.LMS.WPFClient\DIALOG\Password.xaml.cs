﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// Password.xaml 的交互逻辑
    /// </summary>
    public partial class Password : Window
    {
        private string _USER_CODE;

        /// <summary>
        /// 构造函数
        /// </summary>
        public Password(string USER_CODE)
        {
            InitializeComponent();
            this._USER_CODE = USER_CODE;
        }
        
        /// <summary>
        /// 加载窗体
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.txtUserCode.Text = this._USER_CODE;
            this.txtUserCode.IsEnabled = false;
            this.txtOldPwd.Focus();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                { 
                    case "btnOK":
                        this.SavePwd();
                        break;
                    case "btnCancel":
                        this.Close();
                        break;
                }
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        private void SavePwd()
        {
            if (string.IsNullOrEmpty(this.txtOldPwd.Password))
            {
                this.txtOldPwd.Focus();
                MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Null, "请填写旧密码!");
                return;
            }

            if (string.IsNullOrEmpty(this.txtNewPwd.Password.ToString()))
            {
                this.txtNewPwd.Focus();
                MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Null, "请填写新密码!");
                return;
            }

            if (string.IsNullOrEmpty(this.txtConfirmPwd.Password.ToString()))
            {
                this.txtConfirmPwd.Focus();
                MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Null, "请填写确认密码!");
                return;
            }

            if (!this.txtNewPwd.Password.Equals(this.txtConfirmPwd.Password))
            {
                this.txtConfirmPwd.Focus();
                MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Null, "新密码和确认密码不一致!");
                return;
            }

            string sResult = string.Empty;
            bool boolResult = true;
            try
            {
                boolResult = MainApp.I_SystemService.USER_PASSWORD(this.txtUserCode.Text,Common.Encrypt.GetHashCode(this.txtOldPwd.Password), Common.Encrypt.GetHashCode(this.txtNewPwd.Password), out sResult);
                this.txtOldPwd.Focus();
                this.DialogResult = boolResult;
            }
            catch (Exception ex)
            {
                boolResult = false;
                sResult = ex.Message;
            }

            //判断结果
            if (boolResult)
            {
                this.Close();
            }
            MainApp._MessageDialog.ShowResult(boolResult, sResult);
        }
    }
}
