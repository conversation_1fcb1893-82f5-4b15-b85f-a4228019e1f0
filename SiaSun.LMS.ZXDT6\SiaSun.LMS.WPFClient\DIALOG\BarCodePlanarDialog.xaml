﻿<Window x:Class="SiaSun.LMS.WPFClient.Dialog.BarCodePlanarDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        Title="二维条码打印" MaxWidth="300" MaxHeight="300" WindowStyle="None" WindowStartupLocation="CenterScreen"  SizeToContent="WidthAndHeight" Loaded="Window_Loaded" mc:Ignorable="d" d:DesignHeight="300" d:DesignWidth="300">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="25"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucWindowTopHeader x:Name="ucWinHeader" Grid.Row="0"></uc:ucWindowTopHeader>
        <uc:ucBarCodePlanarControl x:Name="ucBarCode" Margin="1" Grid.Row="1"> </uc:ucBarCodePlanarControl>       
        <Grid Name="panelData" Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" VerticalAlignment="Center" Margin="5">条码值：</TextBlock>
            <TextBox Grid.Column="1" Name="txtBarCode"  Margin="5" Text="{Binding ElementName=ucBarCode,Path=U_Data,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"></TextBox>
        </Grid>
        <WrapPanel Grid.Row="3" Margin="0,5,0,5" HorizontalAlignment="Center">
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Foreground="White">打印数量:</TextBlock>
                <TextBlock Name="lblCount" Foreground="White">0</TextBlock>
            </StackPanel>
            <Button DockPanel.Dock="Right" Name="btnPrintAll"  Width="70" Click="btnPrintAll_Click">打印</Button>
        </WrapPanel>
    </Grid>
</Window>
