﻿/***************************************************************************
 * 
 *       功能：     计划列表实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// PLAN_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class PLAN_LIST
	{
		public PLAN_LIST()
		{
			
		}
		
		private int _plan_list_id;
		private int _plan_id;
		private string _plan_list_code;
		private System.Decimal _plan_list_quantity;
		private System.Decimal _plan_list_ordered_quantity;
		private System.Decimal _plan_list_finished_quantity;
        private System.Decimal _plan_list_picked_quantity;

        private int _goods_id;
		private string _goods_property1;
		private string _goods_property2;
		private string _goods_property3;
		private string _goods_property4;
		private string _goods_property5;
		private string _goods_property6;
		private string _goods_property7;
		private string _goods_property8;
		private string _plan_list_remark;
        private System.Decimal _plan_list_quantity_append;
        private string _goods_property9;
        private string _goods_property10;
        private string _goods_property11;
        private string _goods_property12;
        private string _goods_property13;
        private string _goods_property14;
        private string _goods_property15;
        private string _goods_property16;
        private string _goods_property17;
        private string _goods_property18;
        private string _goods_property19;
        private string _goods_property20;
        private string _goods_property21;
        private string _goods_property22;
        private string _goods_property23;
        private string _goods_property24;
        private string _goods_property25;
        private string _goods_property26;
        private string _goods_property27;
        private string _goods_property28;
        private string _goods_property29;
        private string _goods_property30;
        private string _out_position;

        ///<sumary>
        /// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 计划编号
        ///</sumary>
        [DataMember]
		public int PLAN_ID
		{
			get{return _plan_id;}
			set{_plan_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string PLAN_LIST_CODE
		{
			get{return _plan_list_code;}
			set{_plan_list_code = value;}
		}
		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal PLAN_LIST_QUANTITY
		{
			get{return _plan_list_quantity;}
			set{_plan_list_quantity = value;}
		}
		///<sumary>
		/// 下达数量
        ///</sumary>
        [DataMember]
		public System.Decimal PLAN_LIST_ORDERED_QUANTITY
		{
			get{return _plan_list_ordered_quantity;}
			set{_plan_list_ordered_quantity = value;}
		}
		///<sumary>
		/// 完成数量
        ///</sumary>
        [DataMember]
		public System.Decimal PLAN_LIST_FINISHED_QUANTITY
		{
			get{return _plan_list_finished_quantity;}
			set{_plan_list_finished_quantity = value;}
		}
        ///<sumary>
		/// 完成数量
        ///</sumary>
        [DataMember]
        public System.Decimal PLAN_LIST_PICKED_QUANTITY
        {
            get { return _plan_list_picked_quantity; }
            set { _plan_list_picked_quantity = value; }
        }
        
        ///<sumary>
        /// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY1
		{
			get{return _goods_property1;}
			set{_goods_property1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY2
		{
			get{return _goods_property2;}
			set{_goods_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY3
		{
			get{return _goods_property3;}
			set{_goods_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY4
		{
			get{return _goods_property4;}
			set{_goods_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY5
		{
			get{return _goods_property5;}
			set{_goods_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY6
		{
			get{return _goods_property6;}
			set{_goods_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY7
		{
			get{return _goods_property7;}
			set{_goods_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY8
		{
			get{return _goods_property8;}
			set{_goods_property8 = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string PLAN_LIST_REMARK
		{
			get{return _plan_list_remark;}
			set{_plan_list_remark = value;}
		}
        ///<sumary>
        /// 附加数量
        ///</sumary>
        [DataMember]
        public System.Decimal PLAN_LIST_QUANTITY_APPEND
        {
            get { return _plan_list_quantity_append; }
            set { _plan_list_quantity_append = value; }
        }

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY9
        {
            get { return _goods_property9; }
            set { _goods_property9 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY10
        {
            get { return _goods_property10; }
            set { _goods_property10 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY11
        {
            get { return _goods_property11; }
            set { _goods_property11 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY12
        {
            get { return _goods_property12; }
            set { _goods_property12 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY13
        {
            get { return _goods_property13; }
            set { _goods_property13 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY14
        {
            get { return _goods_property14; }
            set { _goods_property14 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY15
        {
            get { return _goods_property15; }
            set { _goods_property15 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY16
        {
            get { return _goods_property16; }
            set { _goods_property16 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY17
        {
            get { return _goods_property17; }
            set { _goods_property17 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY18
        {
            get { return _goods_property18; }
            set { _goods_property18 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY19
        {
            get { return _goods_property19; }
            set { _goods_property19 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY20
        {
            get { return _goods_property20; }
            set { _goods_property20 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY21
        {
            get { return _goods_property21; }
            set { _goods_property21 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY22
        {
            get { return _goods_property22; }
            set { _goods_property22 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY23
        {
            get { return _goods_property23; }
            set { _goods_property23 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY24
        {
            get { return _goods_property24; }
            set { _goods_property24 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY25
        {
            get { return _goods_property25; }
            set { _goods_property25 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY26
        {
            get { return _goods_property26; }
            set { _goods_property26 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY27
        {
            get { return _goods_property27; }
            set { _goods_property27 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY28
        {
            get { return _goods_property28; }
            set { _goods_property28 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY29
        {
            get { return _goods_property29; }
            set { _goods_property29 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY30
        {
            get { return _goods_property30; }
            set { _goods_property30 = value; }
        }

        ///<sumary>
        /// 出库位置
        ///</sumary>
        [DataMember]
        public string OUT_POSITION
        {
            get { return _out_position; }
            set { _out_position = value; }
        }
    }
}
