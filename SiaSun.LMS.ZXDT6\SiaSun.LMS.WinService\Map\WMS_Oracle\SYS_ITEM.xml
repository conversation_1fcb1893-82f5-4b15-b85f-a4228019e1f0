﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_ITEM" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_ITEM" type="SiaSun.LMS.Model.SYS_ITEM, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_ITEM">
			<result property="ITEM_ID" column="item_id" />
			<result property="ITEM_PARENT_ID" column="item_parent_id" />
			<result property="ITEM_CODE" column="item_code" />
			<result property="ITEM_NAME" column="item_name" />
			<result property="ITEM_ORDER" column="item_order" />
			<result property="ITEM_REMARK" column="item_remark" />
			<result property="ITEM_FLAG" column="item_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_ITEM_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  item_id,
				  item_parent_id,
				  item_code,
				  item_name,
				  item_order,
				  item_remark,
				  item_flag
			From SYS_ITEM
		</select>
		
		<select id="SYS_ITEM_SELECT_BY_ID" parameterClass="int" extends = "SYS_ITEM_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					item_id=#ITEM_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="SYS_ITEM_INSERT" parameterClass="SYS_ITEM">
      Insert Into SYS_ITEM (
      item_id,
      item_parent_id,
      item_code,
      item_name,
      item_order,
      item_remark,
      item_flag
      )Values(
      #ITEM_ID#,
      #ITEM_PARENT_ID#,
      #ITEM_CODE#,
      #ITEM_NAME#,
      #ITEM_ORDER#,
      #ITEM_REMARK#,
      #ITEM_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="ITEM_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_ITEM_UPDATE" parameterClass="SYS_ITEM">
      Update SYS_ITEM Set
      <!--item_id=#ITEM_ID#,-->
      item_parent_id=#ITEM_PARENT_ID#,
      item_code=#ITEM_CODE#,
      item_name=#ITEM_NAME#,
      item_order=#ITEM_ORDER#,
      item_remark=#ITEM_REMARK#,
      item_flag=#ITEM_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					item_id=#ITEM_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_ITEM_DELETE" parameterClass="int">
			Delete From SYS_ITEM
			<dynamic prepend="WHERE">
				<isParameterPresent>
					item_id=#ITEM_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>