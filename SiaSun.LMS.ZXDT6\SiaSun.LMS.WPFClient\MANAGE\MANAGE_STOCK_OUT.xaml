﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_STOCK_OUT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_STOCK_TASK" Height="378" Width="455" Loaded="DocumentContent_Loaded">
    <Grid>
     
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucSotckOut x:Name="ucStockOut"  Grid.Row="0" Margin="1,5,1,1" Visibility="Collapsed"></uc:ucSotckOut>
        <uc:ucQuickQuery x:Name="ucQuery" Grid.Row="1" MaxWidth="{Binding ElementName=ucTechPosition,Path=MaxWidth}"></uc:ucQuickQuery>
        <GroupBox Name="grpbManage" Grid.Row="2" Header="空容器库存列表" Tag=" {0}-空容器库存列表">        
            <uc:ucSplitPropertyGridTab x:Name="ucStockStorageGroup" Grid.Row="2" ></uc:ucSplitPropertyGridTab>
        </GroupBox>

        <WrapPanel Grid.Row="3" HorizontalAlignment="Left" ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition"></uc:ucManagePosition>
            <Button Name="btnCreateTask"  MinWidth="70" Margin="5">下达任务</Button>
            <Button Name="btnRefresh"  MinWidth="70" Margin="5">刷新</Button>
        </WrapPanel>

    </Grid>
</ad:DocumentContent >
