<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="PLAN_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="OUT_POSITION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_FINISHED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_ORDERED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_PICKED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY_APPEND" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
  <xs:complexType name="SYS_USER">
    <xs:sequence>
      <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER" />
  <xs:complexType name="ArrayOfSYS_ROLE">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_ROLE" nillable="true" type="tns:ArrayOfSYS_ROLE" />
  <xs:complexType name="SYS_ROLE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ROLE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_START_MENU_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
  <xs:complexType name="ArrayOfSYS_ROLE_WINDOW">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_ROLE_WINDOW" nillable="true" type="tns:ArrayOfSYS_ROLE_WINDOW" />
  <xs:complexType name="SYS_ROLE_WINDOW">
    <xs:sequence>
      <xs:element minOccurs="0" name="CONTROL_HEADER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_WINDOW_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
  <xs:complexType name="ArrayOfSYS_ITEM_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_ITEM_LIST" nillable="true" type="tns:ArrayOfSYS_ITEM_LIST" />
  <xs:complexType name="SYS_ITEM_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
  <xs:complexType name="ArrayOfSYS_MENU">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_MENU" nillable="true" type="tns:ArrayOfSYS_MENU" />
  <xs:complexType name="SYS_MENU">
    <xs:sequence>
      <xs:element minOccurs="0" name="MENU_CHILDNODEFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_CLASS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_DEVELOPFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_GROUP" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_IMAGE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_IMAGE_SL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_PARAMETER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_PARAMETER_SL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_PARENT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_SELECTEDFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_SYSFLAG" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
  <xs:complexType name="SYS_RELATION">
    <xs:sequence>
      <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_ID2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATON_NAME1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATON_NAME2" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_RELATION" nillable="true" type="tns:SYS_RELATION" />
  <xs:complexType name="ArrayOfSYS_RELATION_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_RELATION_LIST" nillable="true" type="tns:ArrayOfSYS_RELATION_LIST" />
  <xs:complexType name="SYS_RELATION_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID2" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
  <xs:complexType name="ArrayOfSYS_TABLE_CONVERTER">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSYS_TABLE_CONVERTER" nillable="true" type="tns:ArrayOfSYS_TABLE_CONVERTER" />
  <xs:complexType name="SYS_TABLE_CONVERTER">
    <xs:sequence>
      <xs:element minOccurs="0" name="CHILD_FOREIGN_KEY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CHILD_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PARENT_KEY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PARENT_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_NAME" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
  <xs:complexType name="ArrayOfFLOW_PARA">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="FLOW_PARA" nillable="true" type="tns:FLOW_PARA" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfFLOW_PARA" nillable="true" type="tns:ArrayOfFLOW_PARA" />
  <xs:complexType name="FLOW_PARA">
    <xs:sequence>
      <xs:element minOccurs="0" name="FLOW_PARA_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_PARA_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_PARA_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FLOW_PARA" nillable="true" type="tns:FLOW_PARA" />
  <xs:complexType name="GOODS_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_COLOR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_LIMIT_LOWER_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_LIMIT_UPPER_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_UNITS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_MAIN" nillable="true" type="tns:GOODS_MAIN" />
  <xs:complexType name="GOODS_CLASS">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_CLASS_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_PARENT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_CLASS" nillable="true" type="tns:GOODS_CLASS" />
  <xs:complexType name="ArrayOfGOODS_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GOODS_MAIN" nillable="true" type="tns:GOODS_MAIN" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGOODS_MAIN" nillable="true" type="tns:ArrayOfGOODS_MAIN" />
  <xs:complexType name="ArrayOfGOODS_PROPERTY">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GOODS_PROPERTY" nillable="true" type="tns:GOODS_PROPERTY" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGOODS_PROPERTY" nillable="true" type="tns:ArrayOfGOODS_PROPERTY" />
  <xs:complexType name="GOODS_PROPERTY">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_DATASOURCE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELDTYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_KEYFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_VALID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_PROPERTY" nillable="true" type="tns:GOODS_PROPERTY" />
  <xs:complexType name="GOODS_TEMPLATE">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_TEMPLATE" nillable="true" type="tns:GOODS_TEMPLATE" />
  <xs:complexType name="ArrayOfGOODS_TEMPLATE">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GOODS_TEMPLATE" nillable="true" type="tns:GOODS_TEMPLATE" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGOODS_TEMPLATE" nillable="true" type="tns:ArrayOfGOODS_TEMPLATE" />
  <xs:complexType name="ArrayOfGOODS_TEMPLATE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GOODS_TEMPLATE_LIST" nillable="true" type="tns:GOODS_TEMPLATE_LIST" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGOODS_TEMPLATE_LIST" nillable="true" type="tns:ArrayOfGOODS_TEMPLATE_LIST" />
  <xs:complexType name="GOODS_TEMPLATE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CHILD_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CONVERT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MDL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MES_OPERATION_NO" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MES_RESOURCE_NO" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_TEMPLATE_LIST" nillable="true" type="tns:GOODS_TEMPLATE_LIST" />
  <xs:complexType name="ArrayOfWH_CELL">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="WH_CELL" nillable="true" type="tns:WH_CELL" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfWH_CELL" nillable="true" type="tns:ArrayOfWH_CELL" />
  <xs:complexType name="WH_CELL">
    <xs:sequence>
      <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_FORK_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_HEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_LOGICAL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_PROPERTY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_STORAGE_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_WIDTH" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_X" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Y" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Y_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Z" type="xs:int" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GET_IN_STATION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LANE_WAY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOCK_DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RUN_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SHELF_NEIGHBOUR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SHELF_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UNIT_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="UPDATETIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_CELL" nillable="true" type="tns:WH_CELL" />
</xs:schema>