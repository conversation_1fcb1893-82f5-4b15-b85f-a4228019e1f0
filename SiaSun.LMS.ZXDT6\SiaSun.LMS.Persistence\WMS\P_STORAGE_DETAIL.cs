﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// STORAGE_DETAIL
	/// </summary>
	public class P_STORAGE_DETAIL : P_Base_House
	{
		public P_STORAGE_DETAIL ()
		{
			//
			// TODO: 此处添加STORAGE_DETAIL的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<STORAGE_DETAIL> GetList()
		{
			return ExecuteQueryForList<STORAGE_DETAIL>("STORAGE_DETAIL_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_DETAIL> GetList(int STORAGE_LIST_ID)
        {
            return ExecuteQueryForList<STORAGE_DETAIL>("STORAGE_DETAIL_SELECT_BY_STORAGE_LIST_ID", STORAGE_LIST_ID);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(STORAGE_DETAIL storage_detail)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("STORAGE_DETAIL");
                storage_detail.STORAGE_DETAIL_ID = id;
            }

            return ExecuteInsert("STORAGE_DETAIL_INSERT",storage_detail);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(STORAGE_DETAIL storage_detail)
		{
			return ExecuteUpdate("STORAGE_DETAIL_UPDATE",storage_detail);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public STORAGE_DETAIL GetModel(System.Int32 STORAGE_DETAIL_ID)
		{
			return ExecuteQueryForObject<STORAGE_DETAIL>("STORAGE_DETAIL_SELECT_BY_ID",STORAGE_DETAIL_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public STORAGE_DETAIL GetModelGoodsBarcode(System.String GOODS_BARCODE)
        {
            return this.ExecuteQueryForObject<STORAGE_DETAIL>("STORAGE_DETAIL_SELECT_BY_GOODS_BARCODE", GOODS_BARCODE);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 STORAGE_DETAIL_ID)
		{
			return ExecuteDelete("STORAGE_DETAIL_DELETE",STORAGE_DETAIL_ID);
		}

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int DeleteStorageListId(System.Int32 STORAGE_LIST_ID)
        {
            return ExecuteDelete("STORAGE_DETAIL_DELETE_STORAGE_LIST_ID", STORAGE_LIST_ID);
        }
    }
}
