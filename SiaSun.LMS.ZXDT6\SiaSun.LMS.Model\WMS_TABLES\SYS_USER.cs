﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_USER 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_USER
	{
		public SYS_USER()
		{
			
		}
		
		private int _user_id;
		private string _user_code;
		private string _user_name;
		private string _user_password;
		private string _user_remark;
		private int _user_order;
		private string _user_flag;
		
		///<sumary>
		/// 用户编号
        ///</sumary>
        [DataMember]
		public int USER_ID
		{
			get{return _user_id;}
			set{_user_id = value;}
		}
		///<sumary>
		/// 用户编码
        ///</sumary>
        [DataMember]
		public string USER_CODE
		{
			get{return _user_code;}
			set{_user_code = value;}
		}
		///<sumary>
		/// 用户名
        ///</sumary>
        [DataMember]
		public string USER_NAME
		{
			get{return _user_name;}
			set{_user_name = value;}
		}
		///<sumary>
		/// 密码
        ///</sumary>
        [DataMember]
		public string USER_PASSWORD
		{
			get{return _user_password;}
			set{_user_password = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string USER_REMARK
		{
			get{return _user_remark;}
			set{_user_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int USER_ORDER
		{
			get{return _user_order;}
			set{_user_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string USER_FLAG
		{
			get{return _user_flag;}
			set{_user_flag = value;}
		}
	}
}
