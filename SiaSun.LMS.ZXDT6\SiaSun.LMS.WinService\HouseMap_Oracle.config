<?xml version="1.0"?>
<sqlMapConfig  xmlns="http://ibatis.apache.org/dataMapper" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<settings>
		<setting useStatementNamespaces="false" />
	</settings>

	<database>
		<!--<provider name="oracleClient1.0" />
		<dataSource name="SqlMap" connectionString="data source=127.0.0.1/orcl;user id=c##base;password=base" />-->

		<provider name="OracleManagedDataAccess4" />
		<dataSource name="SqlMap" connectionString="data source=127.0.0.1/orcl;user id=c##base;password=base" />
	</database>

	<sqlMaps>
		<sqlMap resource="Map\MapBase.xml" />

		<sqlMap resource="Map\WMS_Oracle\APPLY_TYPE.xml" />

		<sqlMap resource="Map\WMS_Oracle\FLOW_ACTION.xml" />
		<sqlMap resource="Map\WMS_Oracle\FLOW_NODE.xml" />
		<sqlMap resource="Map\WMS_Oracle\FLOW_PARA.xml" />
		<sqlMap resource="Map\WMS_Oracle\FLOW_TYPE.xml" />

		<sqlMap resource="Map\WMS_Oracle\GOODS_CLASS.xml" />
		<sqlMap resource="Map\WMS_Oracle\GOODS_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\GOODS_PROPERTY.xml" />
		<sqlMap resource="Map\WMS_Oracle\GOODS_TYPE.xml" />

		<sqlMap resource="Map\WMS_Oracle\INTERFACE_QUEUE.xml" />

		<sqlMap resource="Map\WMS_Oracle\IO_CONTROL.xml" />
		<sqlMap resource="Map\WMS_Oracle\IO_CONTROL_APPLY.xml" />
		<sqlMap resource="Map\WMS_Oracle\IO_CONTROL_APPLY_HIS.xml" />
		<sqlMap resource="Map\WMS_Oracle\IO_CONTROL_ROUTE.xml" />

		<sqlMap resource="Map\WMS_Oracle\LED_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\LED_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\LCD_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\LCD_LIST.xml" />
		
		<sqlMap resource="Map\WMS_Oracle\MANAGE_DETAIL.xml" />
		<sqlMap resource="Map\WMS_Oracle\MANAGE_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\MANAGE_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\MANAGE_TYPE.xml" />

		<sqlMap resource="Map\WMS_Oracle\PLAN_DETAIL.xml" />
		<sqlMap resource="Map\WMS_Oracle\PLAN_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\PLAN_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\PLAN_TYPE.xml" />

		<sqlMap resource="Map\WMS_Oracle\RECORD_DETAIL.xml" />
		<sqlMap resource="Map\WMS_Oracle\RECORD_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\RECORD_MAIN.xml" />

		<sqlMap resource="Map\WMS_Oracle\STORAGE_DETAIL.xml" />
		<sqlMap resource="Map\WMS_Oracle\STORAGE_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\STORAGE_MAIN.xml" />
		<sqlMap resource="Map\WMS_Oracle\STORAGE_LOCK.xml" />

		<sqlMap resource="Map\WMS_Oracle\SYS_ITEM.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_ITEM_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_LOG.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_MENU.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_RELATION.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_RELATION_LIST.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_ROLE.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_ROLE_WINDOW.xml" />
		<sqlMap resource="Map\WMS_Oracle\SYS_USER.xml" />

		<sqlMap resource="Map\WMS_Oracle\WH_AREA.xml" />
		<sqlMap resource="Map\WMS_Oracle\WH_CELL.xml" />
		<sqlMap resource="Map\WMS_Oracle\WH_DESCRIPTION.xml" />
		<sqlMap resource="Map\WMS_Oracle\WH_LOGIC.xml" />
		<sqlMap resource="Map\WMS_Oracle\WH_WAREHOUSE.xml" />

	</sqlMaps>
</sqlMapConfig>