﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="RECORD_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="RECORD_MAIN" type="SiaSun.LMS.Model.RECORD_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="RECORD_MAIN">
			<result property="RECORD_ID" column="record_id" />
      <result property="MANAGE_ID" column="manage_id" />
			<result property="PLAN_CODE" column="plan_code" />
			<result property="PLAN_TYPE_CODE" column="plan_type_code" />
			<result property="MANAGE_TYPE_CODE" column="manage_type_code" />
			<result property="STOCK_BARCODE" column="stock_barcode" />
			<result property="START_POSITION" column="start_position" />
			<result property="END_POSITION" column="end_position" />
			<result property="RECORD_OPERATOR" column="record_operator" />
			<result property="MANAGE_BEGIN_TIME" column="manage_begin_time" />
			<result property="MANAGE_END_TIME" column="manage_end_time" />
			<result property="MANAGE_CONFIRM_TIME" column="manage_confirm_time" />
			<result property="RECORD_REMARK" column="record_remark" />

      <result property="CELL_MODEL" column="cell_model" />
      <result property="MANAGE_SOURCE" column="manage_source" />
      <result property="MANAGE_RELATE_CODE" column="manage_relate_code" />
      <result property="PLAN_ID" column="plan_id" />
      <result property="STOCK_WEIGHT" column="stock_weight" />
      <result property="PICK_SEQ" column="pick_seq" />
      <result property="START_POSITION_ID" column="start_position" />
      <result property="END_POSITION_ID" column="end_position" />

    </resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="RECORD_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        record_id,
        manage_id,
        plan_code,
        plan_type_code,
        manage_type_code,
        stock_barcode,
        start_position,
        end_position,
        record_operator,
        manage_begin_time,
        manage_end_time,
        manage_confirm_time,
        record_remark,
        cell_model,
        manage_source,
        manage_relate_code,
        plan_id,
        stock_weight,
        pick_seq,
        start_position_id,
        end_position_id
        From RECORD_MAIN
      </select>
		
		<select id="RECORD_MAIN_SELECT_BY_ID" parameterClass="int" extends = "RECORD_MAIN_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_id=#RECORD_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="RECORD_MAIN_SELECT_BY_MANAGE_ID" parameterClass="int" extends = "RECORD_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </select>


    <select id="RECORD_MAIN_SELECT_BY_PLAN_ID" parameterClass="int" extends = "RECORD_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_id=#PLAN_ID#
        </isParameterPresent>
      </dynamic>
    </select>

				
		<insert id="RECORD_MAIN_INSERT" parameterClass="RECORD_MAIN">
      Insert Into RECORD_MAIN (
      record_id,
      manage_id,
      plan_code,
      plan_type_code,
      manage_type_code,
      stock_barcode,
      start_position,
      end_position,
      record_operator,
      manage_begin_time,
      manage_end_time,
      manage_confirm_time,
      record_remark,
      cell_model,
      manage_source,
      manage_relate_code,
      plan_id,
      stock_weight,
      pick_seq,
      start_position_id,
      end_position_id
      )Values(
      #RECORD_ID#,
      #MANAGE_ID#,
      #PLAN_CODE#,
      #PLAN_TYPE_CODE#,
      #MANAGE_TYPE_CODE#,
      #STOCK_BARCODE#,
      #START_POSITION#,
      #END_POSITION#,
      #RECORD_OPERATOR#,
      #MANAGE_BEGIN_TIME#,
      #MANAGE_END_TIME#,
      #MANAGE_CONFIRM_TIME#,
      #RECORD_REMARK#,
      #CELL_MODEL#,
      #MANAGE_SOURCE#,
      #MANAGE_RELATE_CODE#,
      #PLAN_ID#,
      #STOCK_WEIGHT#,
      #PICK_SEQ#,
      #START_POSITION_ID#,
      #END_POSITION_ID#
      )
      <!--<selectKey  resultClass="int" type="post" property="RECORD_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="RECORD_MAIN_UPDATE" parameterClass="RECORD_MAIN">
      Update RECORD_MAIN Set
      <!--record_id=#RECORD_ID#,-->
      manage_id=#MANAGE_ID#,
      plan_code=#PLAN_CODE#,
      plan_type_code=#PLAN_TYPE_CODE#,
      manage_type_code=#MANAGE_TYPE_CODE#,
      stock_barcode=#STOCK_BARCODE#,
      start_position=#START_POSITON#,
      end_position=#END_POSITION#,
      record_operator=#RECORD_OPERATOR#,
      manage_begin_time=#MANAGE_BEGIN_TIME#,
      manage_end_time=#MANAGE_END_TIME#,
      manage_confirm_time=#MANAGE_CONFIRM_TIME#,
      record_remark=#RECORD_REMARK#,
      cell_model=#CELL_MODEL#,
      manage_source=#MANAGE_SOURCE#,
      manage_relate_code=#MANAGE_RELATE_CODE#,
      plan_id=#PLAN_ID#,
      stock_weight=#STOCK_WEIGHT#,
      pick_seq=#PICK_SEQ#,
      start_position_id=#START_POSITON_ID#,
      end_position_id=#END_POSITION_ID#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					record_id=#RECORD_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="RECORD_MAIN_DELETE" parameterClass="int">
			Delete From RECORD_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_id=#RECORD_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>