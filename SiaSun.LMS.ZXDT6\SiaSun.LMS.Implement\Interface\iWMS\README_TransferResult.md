# 移库结果上报回调接口实现

## 概述

本文档描述了移库结果上报回调接口的实现，该接口用于向外部物资系统上报移库操作的结果。

## 文件结构

### 1. 核心实现文件
- **文件路径**: `SiaSun.LMS.Implement/Interface/iWMS/TransferResultCallback.cs`
- **功能**: 移库结果上报回调接口的核心实现

### 2. 示例代码文件
- **文件路径**: `SiaSun.LMS.Implement/Interface/iWMS/TransferResultCallback_Example.cs`
- **功能**: 提供完整的使用示例和测试代码

### 3. 使用说明文档
- **文件路径**: `SiaSun.LMS.Implement/Interface/iWMS/TransferResultCallback_Usage.md`
- **功能**: 详细的使用说明和参数文档

## 接口规范对照

### 接口地址
- **测试地址**: `http://10.92.234.42:9008/api/ws/AuthService?wsdl`
- **正式地址**: `http://10.92.233.34:9008/api/ws/AuthService?wsdl`

### 认证流程
1. **注册(regist)**: 获取系统密钥
2. **获取凭证(applyToken)**: 使用密钥获取访问凭证
3. **访问接口(accessInterface)**: 携带凭证调用业务接口

### 数据格式
按照文档要求，支持以下移库明细参数：
- `goodsCode`: 物资编码
- `removeNum`: 移库数量
- `warehouseCode`: 原库房编号
- `warehouseName`: 原库房名称
- `warehouseId`: 原库房id
- `shelfCode`: 原货架位编号
- `shelfName`: 原货架位名称
- `shelfId`: 原货位id
- `targetWarehouseCode`: 目标库房编号
- `targetWarehouseName`: 目标库房名称
- `targetWarehouseId`: 目标库房id
- `targetShelfCode`: 目标货架位编号
- `targetShelfName`: 目标货架位名称
- `targetShelfId`: 目标货位id

## 代码架构特点

### 1. 继承结构
```csharp
public class TransferResultCallback : InterfaceBase
```
- 继承自`InterfaceBase`，复用基础功能
- 遵循项目现有的接口实现模式

### 2. 数据模型
```csharp
public class TransferResultItem
{
    // 目标货位信息
    public string targetShelfCode { get; set; }
    public string targetShelfName { get; set; }
    public string targetShelfId { get; set; }
    public string targetWarehouseCode { get; set; }
    public string targetWarehouseName { get; set; }
    public string targetWarehouseId { get; set; }
    
    // 移库信息
    public int removeNum { get; set; }
    public string goodsCode { get; set; }
    
    // 原货位信息
    public string shelfName { get; set; }
    public string shelfCode { get; set; }
    public string shelfId { get; set; }
    public string warehouseCode { get; set; }
    public string warehouseName { get; set; }
    public string warehouseId { get; set; }
}
```

### 3. 方法重载
- **批量上报**: `IntefaceMethod(List<TransferResultItem> transferResultItems, out string message)`
- **单个上报**: `IntefaceMethod(string goodsCode, int removeNum, ...)`

### 4. 错误处理
- 完整的参数校验
- 网络异常处理
- 认证失败处理
- 详细的错误消息返回

### 5. 日志记录
- 成功操作日志
- 异常操作日志
- 包含traceId用于问题追踪

## 实现特色

### 1. 参数校验
- 必填字段校验（物资编码、原货位、目标货位）
- 数值范围校验（移库数量必须>0）
- 空值和空列表校验

### 2. 认证机制
- 自动处理三步认证流程
- 凭证自动获取和管理
- 认证失败自动重试机制

### 3. SOAP通信
- 标准SOAP Envelope构建
- XML安全转义处理
- HTTP请求头正确设置

### 4. 响应解析
- 支持XML包装的JSON响应
- 自动提取<String>节点内容
- 兼容多种响应格式

## 使用示例

### 基本用法
```csharp
var callback = new TransferResultCallback();
var items = new List<TransferResultCallback.TransferResultItem>
{
    new TransferResultCallback.TransferResultItem
    {
        goodsCode = "GOODS001",
        removeNum = 10,
        warehouseCode = "WH001",
        shelfCode = "SHELF001",
        targetWarehouseCode = "WH002",
        targetShelfCode = "SHELF002"
    }
};

bool success = callback.IntefaceMethod(items, out string message);
```

### 单个明细上报
```csharp
bool success = callback.IntefaceMethod(
    goodsCode: "GOODS001",
    removeNum: 10,
    warehouseCode: "WH001",
    warehouseName: "仓库1",
    warehouseId: "1",
    shelfCode: "SHELF001",
    shelfName: "货架1",
    shelfId: "1",
    targetWarehouseCode: "WH002",
    targetWarehouseName: "仓库2",
    targetWarehouseId: "2",
    targetShelfCode: "SHELF002",
    targetShelfName: "货架2",
    targetShelfId: "2",
    out string message
);
```

## 配置要求

确保配置文件中包含：
- `ExternalServiceUrl`: 外部服务地址
- 网络连通性到目标服务器

## 注意事项

1. 接口需要网络连接到外部物资系统
2. 认证凭证有时效性，接口会自动处理
3. 移库数量必须为正整数
4. 物资编码、原货位、目标货位为必填项
5. 建议在生产环境使用前进行充分测试

## 测试建议

1. 使用`TransferResultCallback_Example.cs`中的示例进行功能测试
2. 验证参数校验逻辑
3. 测试网络异常情况的处理
4. 验证日志记录功能
5. 确认与外部系统的数据格式兼容性
