﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// FLOW_TYPE
	/// </summary>
	public class P_FLOW_TYPE : P_Base_House
	{
		public P_FLOW_TYPE ()
		{
			//
			// TODO: 此处添加FLOW_TYPE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<FLOW_TYPE> GetList()
		{
			return ExecuteQueryForList<FLOW_TYPE>("FLOW_TYPE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(FLOW_TYPE flow_type)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("FLOW_TYPE");
                flow_type.FLOW_TYPE_ID = id;
            }
            return ExecuteInsert("FLOW_TYPE_INSERT",flow_type);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(FLOW_TYPE flow_type)
		{
			return ExecuteUpdate("FLOW_TYPE_UPDATE",flow_type);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public FLOW_TYPE GetModel(System.Int32 FLOW_TYPE_ID)
		{
			return ExecuteQueryForObject<FLOW_TYPE>("FLOW_TYPE_SELECT_BY_ID",FLOW_TYPE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 FLOW_TYPE_ID)
		{
			return ExecuteDelete("FLOW_TYPE_DELETE",FLOW_TYPE_ID);
		}
		

	}
}
