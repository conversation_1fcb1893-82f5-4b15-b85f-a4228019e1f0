﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using System.Collections.ObjectModel;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_OUT : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;
        string targetStorageAreaType = string.Empty;
        string targetStorageAreaCode = string.Empty;
        string endCellCode = string.Empty;

        public ObservableCollection<DataColumnChangeEventArgs> Errors { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_OUT(string manageTypeCode, string areaType,string goodsClassId, string areaCode, string endCellCode)
        {
            InitializeComponent();
            Errors = new ObservableCollection<DataColumnChangeEventArgs>();

            targetStorageAreaType = areaType;
            targetStorageAreaCode = areaCode;
            this.endCellCode = endCellCode;

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_DatabaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", manageTypeCode).RequestObject;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                ((QueryWhere) =>
                    {
                        QueryWhere = string.Format("{0} and {1}{2}{3}{4}",
                        this.ucSplitPanel.U_GetSplitPropertyWhere(),
                        string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                        areaType == string.Empty ? string.Empty : $" and V_STORAGE_LIST.AREA_TYPE = '{areaType}'",
                        areaCode == string.Empty ? string.Empty : $" and V_STORAGE_LIST.AREA_CODE in '{string.Join("','",areaCode.Split('|'))}'",
                        goodsClassId == string.Empty ? string.Empty : $" and V_STORAGE_LIST.GOODS_CLASS_ID = {goodsClassId}");
                        this.StorageListBind(QueryWhere);
                    }
                );
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //属性控件
            this.InitSplitPropertyPanel();
            //查询控件
            this.InitQueryControl();
        }

        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.ucStorageGroup.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "MANAGE_LIST_QUANTITY":
                    bResult = (Convert.ToDecimal(e.ProposedValue) <= Convert.ToDecimal(e.Row["STORAGE_LIST_QUANTITY"]));

                    sResult = string.Format("出库数量不能大于库存数量!");
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                        Errors.Add(e);

                    }
                    break;
            }
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = "V_STORAGE_LIST";
            this.ucQuery.U_InitControl();
        }

        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitSplitPropertyPanel()
        {
            this.ucSplitPanel.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucSplitPanel.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.ucSplitPanel.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.ucSplitPanel.U_SplitPropertyType = "GOODS_TYPE";
            this.ucSplitPanel.U_InitControl();
        }

        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";

            this.ucStorageGroup.U_Where = string.Format(" cell_id not in ( select start_cell_id from manage_main) and {0} ", QueryWhere);
            //this.ucStorageGroup.U_Where = string.Format(" cell_status <> 'Pallet'  and {0} ", QueryWhere);

            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = true;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();

            Register_DataTable_Event();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = true;
            bool boolSingleResult = true;
            string strResult = string.Empty;
            string sSingleReslut = string.Empty;

            try
            {
                //获得选中记录
                List<DataRowView> listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //校验是否选中记录
                if (listDataRowView.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "请查看是否选中记录");
                    return;
                }

                List<string> lsStockBarcode = new List<string>();
                foreach (DataRowView dv in listDataRowView)
                {
                    lsStockBarcode.Add(dv["STOCK_BARCODE"].ToString());
                }
                if (lsStockBarcode.Distinct().Count() != 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "只允许选择1条信息");
                    return;
                }

                //下架页面选中一条数据后将同托盘条码的条目都自动选中
                var checkedBarcode = (from r in listDataRowView select r["STOCK_BARCODE"]).ToList();
                foreach (DataRowView drv in listDataRowView[0].DataView)
                {
                    if (checkedBarcode.Contains(drv["STOCK_BARCODE"].ToString()))
                    {
                        this.ucStorageGroup.U_CheckRow(drv);
                    }
                }
                //重新获得选中记录
                listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //校验是否整垛操作
                DataTable dtStockCount = MainApp.I_DatabaseService.GetList(string.Format("select 0 from STORAGE_LIST where STORAGE_ID in (select STORAGE_ID from STORAGE_MAIN where STOCK_BARCODE ='{0}')", lsStockBarcode[0]));
                if (dtStockCount != null)
                {
                    if (listDataRowView.Count != dtStockCount.Rows.Count)
                    {
                        MainApp._MessageDialog.ShowResult(false, string.Format("选中行数[{0}]与库存托盘个数[{1}]不等_请使用垛条码查询出本垛所有的库存信息后再进行操作", listDataRowView.Count, dtStockCount.Rows.Count));
                        return;
                    }
                }
                else
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("根据垛条码[{0}]未找到库存单信息", lsStockBarcode[0]));
                    return;
                }

                foreach (DataRowView drv in listDataRowView)
                {
                    if (drv["MANAGE_LIST_QUANTITY"].ToString() == "0" || drv["MANAGE_LIST_QUANTITY"].ToString() == "0.0")
                    {
                        drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                    }
                }

                this.ucStorageGroup.U_EndCurrentEdit();

                //判断数据是否合法
                if (this.ucStorageGroup.U_DataSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }

                var cell_id_group = from v in listDataRowView
                                    group v by v["CELL_ID"].ToString() into a
                                    select a;

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //获得所有的选定的条码
                    foreach (var cell_id in cell_id_group)
                    {
                        //获得起始位置
                        //int intStartAearID = aa.Value.AREA_ID;
                        int intStartPositionID = Convert.ToInt32(cell_id.Key);
                        int intEndPostionID = 0;
                        DataTable outPositionID = MainApp.I_DatabaseService.GetList(string.Format(@"select CELL_ID from WH_CELL where CELL_TYPE = 'Station' and CELL_MODEL = (select DEVICE_CODE from WH_CELL where CELL_ID = {0})",intStartPositionID));
                        if(outPositionID != null && outPositionID.Rows.Count > 0)
                        {
                            intEndPostionID = Convert.ToInt32(outPositionID.Rows[0]["CELL_ID"]);
                        }

                        var value_ceLl_id = from v in listDataRowView
                                            where v["CELL_ID"].ToString() == cell_id.Key
                                            select v;


                        var lsMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(value_ceLl_id.Cast<DataRowView>().ToArray(), null);
                        //wdz add 2018-01-28 本地出库涉及到包含的计划信息全部清掉
                        foreach (var mMANAGE_LIST in lsMANAGE_LIST.Where(r => r.PLAN_LIST_ID != 0))
                        {
                            mMANAGE_LIST.PLAN_LIST_ID = 0;
                        }

                        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = value_ceLl_id.First()["CELL_MODEL"].ToString();
                        mMANAGE_MAIN.END_CELL_ID = targetStorageAreaType.Equals("LiKu") ? intEndPostionID :  0;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                        mMANAGE_MAIN.MANAGE_LEVEL = MainApp.I_SystemService.GetSysParameter("ManualOutLevel", "0");
                        mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.SystemName.SSWMS.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                        mMANAGE_MAIN.PLAN_ID = 0;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = intStartPositionID;
                        mMANAGE_MAIN.STOCK_BARCODE = value_ceLl_id.First()["STOCK_BARCODE"].ToString();
                        mMANAGE_MAIN.STOCK_WEIGHT = int.TryParse(value_ceLl_id.First()["STOCK_WEIGHT"].ToString(), out int stockWeight) ? stockWeight : 0;

                        bool autoCompleteFlag = (value_ceLl_id.First()["AREA_GROUP"].ToString() == Enum.AREA_GROUP.Manual.ToString("d"));

                        boolSingleResult = MainApp.I_ManageService.ManageCreate(
                            mMANAGE_MAIN,
                            lsMANAGE_LIST,
                            raiseTrans: true,
                            checkStorage: true,
                            checkManage: true,
                            checkCellStatus: true,
                            autoComplete: autoCompleteFlag,
                            autoControl: !autoCompleteFlag,
                            doubleInAutoMove: true,
                            out sSingleReslut);

                        if (!boolSingleResult)
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达失败 " + sSingleReslut + "\n";
                            break;
                        }
                        else
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达成功 " + "\n";
                            boolResult = true;
                            continue;
                        }
                    }

                    MainApp._MessageDialog.ShowResult(boolResult, strResult);

                    //刷新
                    this.Refresh();
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(DataRowView[] listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp.I_DatabaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            this.ucStorageGroup.U_InitControl();
        }
    }
}
