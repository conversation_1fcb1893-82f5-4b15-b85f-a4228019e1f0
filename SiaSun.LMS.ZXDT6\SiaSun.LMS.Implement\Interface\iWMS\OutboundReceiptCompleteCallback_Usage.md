# 出库单完成上报回调接口使用说明

## 概述

`OutboundReceiptCompleteCallback` 类实现了向外部物资系统上报出库单完成信息的功能。该接口遵循中央库接口文档中"出库单完成上报"的规范，采用三步认证流程：注册 -> 获取凭证 -> 调用接口。

## 接口特性

- **认证流程**：自动处理注册、获取凭证、调用接口的完整流程
- **数据校验**：对输入参数进行完整性和有效性校验
- **错误处理**：提供详细的错误信息和异常处理
- **日志记录**：自动记录操作日志和错误日志
- **方法重载**：支持单个明细和批量明细两种调用方式

## 使用方法

### 方法1：批量出库明细上报

```csharp
using SiaSun.LMS.Implement.Interface.iWMS;

// 创建出库单完成上报实例
var callback = new OutboundReceiptCompleteCallback();

// 准备出库明细数据
var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
{
    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
    {
        goodsNum = 100,                          // 数量
        warehouseCode = "WH001",                 // 仓库编码
        shelfCode = "SH001-A01",                 // 货架编码
        oId = "OUTBOUND_DETAIL_001",             // 出库明细原ID
        lId = "STEREO_WH_OUTBOUND_001",          // 立体仓系统出库明细ID
        outboundType = 63                        // 出库类型：63=出库单
    },
    // 可以添加更多明细...
};

// 调用接口
bool success = callback.IntefaceMethod(items, out string message);
if (success)
{
    Console.WriteLine($"出库单完成上报成功：{message}");
}
else
{
    Console.WriteLine($"出库单完成上报失败：{message}");
}
```

### 方法2：单个出库明细上报

```csharp
using SiaSun.LMS.Implement.Interface.iWMS;

// 创建出库单完成上报实例
var callback = new OutboundReceiptCompleteCallback();

// 调用接口（单个明细）
bool success = callback.IntefaceMethod(
    goodsNum: 50,                            // 数量
    warehouseCode: "WH001",                  // 仓库编码
    shelfCode: "SH001-A01",                  // 货架编码
    oId: "OUTBOUND_DETAIL_001",              // 出库明细原ID
    lId: "STEREO_WH_OUTBOUND_001",           // 立体仓系统出库明细ID
    outboundType: 63,                        // 出库类型
    out string message
);

if (success)
{
    Console.WriteLine($"出库明细上报成功：{message}");
}
else
{
    Console.WriteLine($"出库明细上报失败：{message}");
}
```

## 参数说明

### OutboundReceiptCompleteItem 类属性

| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| goodsNum | int | 是 | 数量 |
| warehouseCode | string | 是 | 仓库编码 |
| shelfCode | string | 是 | 货架编码 |
| oId | string | 是 | 出库明细原ID |
| lId | string | 是 | 立体仓系统出库明细ID |
| outboundType | int | 是 | 出库类型 |

### 出库类型说明

| 值 | 说明 |
|----|------|
| 63 | 出库单 |
| 72 | 入库红冲单 |
| 74 | 借用 |

## 返回值说明

### 成功响应
- `bool` 返回值为 `true`
- `message` 参数包含成功信息

### 失败响应
- `bool` 返回值为 `false`
- `message` 参数包含详细的错误信息

### 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "参数错误：outboundReceiptCompleteItems 不能为空" | 传入的明细列表为空 | 确保传入有效的明细数据 |
| "参数错误：明细项的warehouseCode、shelfCode、oId、lId不能为空" | 必填字段为空 | 检查并填写所有必填字段 |
| "参数错误：出库类型无效[xx]" | 出库类型不在允许范围内 | 使用有效的出库类型：63、72、74 |
| "调用regist失败" | 注册步骤失败 | 检查网络连接和服务配置 |
| "调用applyToken失败" | 获取凭证失败 | 检查注册返回的密钥是否有效 |
| "调用accessInterface失败" | 接口调用失败 | 检查凭证和请求参数 |
| "未配置ExternalServiceUrl" | 缺少服务地址配置 | 在配置文件中设置ExternalServiceUrl |

## 配置要求

确保在配置文件中设置了以下配置项：

```xml
<appSettings>
    <!-- 外部服务地址 -->
    <add key="ExternalServiceUrl" value="http://************:9008/api/ws/AuthService?wsdl" />
</appSettings>
```

## 日志记录

接口会自动记录以下日志：

### 成功日志
```
OutboundReceiptCompleteCallback成功_明细数[2]_traceId[xxx]_信息[出库单完成上报成功]
```

### 错误日志
```
OutboundReceiptCompleteCallback异常：具体异常信息
```

## 最佳实践

1. **批量处理**：尽量使用批量方式上报多个明细，提高效率
2. **错误处理**：始终检查返回值并处理错误情况
3. **重试机制**：对于网络错误等临时性问题，可以实现重试机制
4. **状态管理**：上报成功后及时更新本地数据状态
5. **日志监控**：定期检查日志文件，监控接口调用情况

## 示例代码

完整的使用示例请参考 `OutboundReceiptCompleteCallback_Example.cs` 文件，其中包含：

- 单个明细上报示例
- 批量明细上报示例  
- 完整业务流程示例
- 错误处理示例

## 注意事项

1. 确保在调用前已正确配置外部服务地址
2. 出库类型必须使用文档规定的值（63、72、74）
3. 所有字符串参数不能为空或null
4. 数量必须大于0
5. 接口调用有时间限制，建议及时处理返回结果
