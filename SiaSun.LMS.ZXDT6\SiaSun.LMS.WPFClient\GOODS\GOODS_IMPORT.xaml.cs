﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.GOODS
{
    /// <summary>
    /// GOODS_IMPORT.xaml 的交互逻辑
    /// </summary>
    public partial class GOODS_IMPORT : AvalonDock.DocumentContent
    {
        public GOODS_IMPORT()
        {
            InitializeComponent();
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if(btn != null)
            {
                switch (btn.Name)
                {
                    case "btnOpen":
                        this.ImportExcel();
                        break;
                    case "btnSave":
                        this.Save();
                        break;
                    case "btnRefresh":
                        this.gridImport.ItemsSource = null;
                        break;
                }
            }
        }

        private void ImportExcel()
        {
            using (System.Windows.Forms.OpenFileDialog openFileDialog = new System.Windows.Forms.OpenFileDialog())
            {
                //openFileDialog.Filter = "Excel 2007文件|*.xlsx|Excel 2003|*.xls";
                if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    string fileName = openFileDialog.FileName;
                    if (string.IsNullOrEmpty(fileName))
                    {
                        MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.PrintExcel);
                        return;
                    }

                    this.gridImport.ItemsSource = new SiaSun.LMS.Common.Excel().ImportToDataTable(fileName, true).DefaultView;
                }

            }
        }

        private void Save()
        {
            string mesResult = string.Empty;

            string sResult = string.Empty;

            bool bResult = true;

            try
            {
                DataTable dtImport = (this.gridImport.ItemsSource as DataView).Table;

                if (dtImport.Rows.Count == 0)
                {
                    MainApp._MessageDialog.Show("不存在导入的数据源");
                    return;
                }

                dtImport.TableName = "ExcelImport";

                bResult = MainApp.I_ManageService.GoodsCreate(dtImport, 1, out sResult);

                if (bResult)
                {
                    this.gridImport.ItemsSource = null;
                }

                MainApp._MessageDialog.Show(sResult);

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
