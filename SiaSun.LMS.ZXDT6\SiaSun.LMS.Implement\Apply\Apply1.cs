﻿using SiaSun.LMS.Common;
using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 入库-管理任务已存在-分配货位-下达控制任务
    /// </summary>
    public class Apply1 : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                result = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out message);

                if (!result)
                {
                    return result;
                }

                if (!string.IsNullOrEmpty(mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER) && !mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.Equals("1"))
                {
                    switch (mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.Trim())
                    {
                        case "39":
                            message = "外形检测超高";
                            break;
                        case "40":
                            message = "外形检测左超宽";
                            break;
                        case "41":
                            message = "外形检测右超宽";
                            break;
                        case "42":
                            message = "外形检测前超长";
                            break;
                        case "43":
                            message = "外形检测后超长";
                            break;
                        case "44":
                            message = "超重";
                            break;
                        default:
                            message = "未知错误码" + mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER;
                            break;
                    }
                    result = true;
                    message = string.Format("托盘入库校验信息失败：'{0}' ", message);
                    return result;
                }

                result = this.ValidateStockBarcode(mIO_CONTROL_APPLY, out message);

                if (!result)
                {
                    return result;
                }

                Model.WH_CELL applyCell = S_Base.sBase.pWH_CELL.GetModel(mIO_CONTROL_APPLY.WAREHOUSE_CODE, mIO_CONTROL_APPLY.DEVICE_CODE);
                if (applyCell == null)
                {
                    result = false;
                    message = string.Format("未能找到申请站台_站台号[{0}-{1}]", mIO_CONTROL_APPLY.WAREHOUSE_CODE, mIO_CONTROL_APPLY.DEVICE_CODE);
                    return result;
                }
                else
                {
                    if (!applyCell.CELL_MODEL.Equals("Apply"))
                    {
                        result = false;
                        message = string.Format(@"申请站台'{0}'不合法，请检查！",mIO_CONTROL_APPLY.DEVICE_CODE);
                        return result;
                    }
                }

                if (!string.IsNullOrEmpty(S_Base.sBase.sDatabase.IsExistData(
                    $"select * from IO_CONTROL where STOCK_BARCODE='{mIO_CONTROL_APPLY.STOCK_BARCODE}'")))
                {
                    result = false;
                    message = string.Format("托盘条码[{0}]存在控制任务", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return result;
                }

                Model.MANAGE_MAIN manageMain = S_Base.sBase.pMANAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);
                Model.STORAGE_MAIN storageMain = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);

                if (manageMain != null)
                {
                    result = false;
                    message = string.Format(@"当前托盘条码'{0}'存在管理任务",mIO_CONTROL_APPLY.STOCK_BARCODE);
                }
                else if (manageMain == null && storageMain != null)
                {
                    var storageCell = S_Base.sBase.pWH_CELL.GetModel(storageMain.CELL_ID);
                    if (storageCell == null)
                    {
                        result = false;
                        message = string.Format("未找到库存的货位信息_托盘条码[{0}]", storageMain.STOCK_BARCODE);
                        return result;
                    }

                    WH_CELL storage_cell = S_Base.sBase.pWH_CELL.GetModel(storageMain.CELL_ID);

                    //if(storage_cell.CELL_TYPE.Equals("Cell") || storage_cell.DEVICE_CODE != mIO_CONTROL_APPLY.DEVICE_CODE)
                    //{
                    //    result = false;
                    //    message = string.Format(@"绑定库存站台与入库实际巷道不符,站台:'{0}',巷道:'{1}'",storage_cell.CELL_NAME,mIO_CONTROL_APPLY.DEVICE_CODE);
                    //    return result;
                    //}

                    IList<Model.STORAGE_LIST> storageLists = S_Base.sBase.pSTORAGE_LIST.GetListStorageID(storageMain.STORAGE_ID);
                    if (storageLists == null || storageLists.Count < 1)
                    {
                        result = false;
                        message = string.Format("库存列表为空_托盘条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE);
                        return result;
                    }

                    manageMain = new Model.MANAGE_MAIN()
                    {
                        END_CELL_ID = 0,
                        FULL_FLAG = "",
                        GOODS_TEMPLATE_ID = 0,
                        MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                        MANAGE_LEVEL = "",
                        MANAGE_OPERATOR = Enum.KeyWords.扫码申请.ToString(),
                        MANAGE_RELATE_CODE = "",
                        MANAGE_SOURCE = Enum.SystemName.iWMS.ToString(),
                        MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString(),
                        MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageUp.ToString(),
                        PLAN_ID = 0,
                        PLAN_TYPE_CODE = "",
                        START_CELL_ID = applyCell.CELL_ID,
                        STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE
                    };

                    result = S_Base.sBase.sManage.ManageCreate(
                        mMANAGE_MAIN: manageMain,
                        lsMANAGE_LIST: null,
                        raiseTrans: false,
                        checkStorage: true,
                        checkManage: true,
                        checkCellStatus: true,
                        autoComplete: false,
                        autoControl: false,
                        doubleInAutoMove: false,
                        out message);

                    if (!result)
                    {
                        return result;
                    }
                }
                else
                {
                    GOODS_MAIN pallet = S_Base.sBase.pGOODS_MAIN.GetModel("emptyPallet");

                    if(pallet == null)
                    {
                        result = false;
                        message = string.Format(@"未能找到空托盘库存，请检查物料导入功能内空托盘库存情况!");
                        return result;
                    }

                    //空托盘入库
                    STORAGE_MAIN storage_main = new STORAGE_MAIN()
                    {
                        CELL_ID = applyCell.CELL_ID,
                        STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE,
                        CELL_MODEL = "default"
                    };
                    S_Base.sBase.pSTORAGE_MAIN.Add(storage_main);

                    STORAGE_LIST storage_list = new STORAGE_LIST()
                    {
                        STORAGE_ID = storage_main.STORAGE_ID,
                        PLAN_LIST_ID = 0,
                        STORAGE_LIST_QUANTITY = 1,
                        GOODS_ID = pallet.GOODS_ID,
                        ENTRY_TIME = StringUtil.GetDateTime()
                    };
                    S_Base.sBase.pSTORAGE_LIST.Add(storage_list);

                    manageMain = new MANAGE_MAIN()
                    {
                        END_CELL_ID = 0,
                        GOODS_TEMPLATE_ID = 0,
                        MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                        MANAGE_OPERATOR = Enum.KeyWords.扫码申请.ToString(),
                        MANAGE_SOURCE = Enum.SystemName.iWMS.ToString(),
                        MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString(),
                        MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageUp.ToString(),
                        PLAN_ID = 0,
                        PLAN_TYPE_CODE = "",
                        START_CELL_ID = applyCell.CELL_ID,
                        STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE
                    };

                    result = S_Base.sBase.sManage.ManageCreate(
                        mMANAGE_MAIN: manageMain,
                        lsMANAGE_LIST: null,
                        raiseTrans: false,
                        checkStorage: true,
                        checkManage: true,
                        checkCellStatus: true,
                        autoComplete: false,
                        autoControl: false,
                        doubleInAutoMove: false,
                        out message);
                }

                result = S_Base.sBase.sManage.ManageDownLoad(manageMain.MANAGE_ID, applyCell, false, out message);

                if (!result)
                {
                    return result;
                }

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("处理申请时发生异常_异常信息[{0}]", ex.Message);
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction();
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction();
                }
                if (!result)
                {
                    message += this.CreateApplyExceptionTask(mIO_CONTROL_APPLY);
                }
                this.WriteHisData(mIO_CONTROL_APPLY, result, message);
            }

            return result;
        }

    }
}
