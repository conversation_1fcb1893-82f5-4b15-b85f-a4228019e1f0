﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// FLOW_ACTION 
	/// </summary>
    [Serializable]
    [DataContract]
	public class FLOW_ACTION
	{
		public FLOW_ACTION()
		{
			
		}
		
		private int _flow_action_id;
		private int _flow_node_id;
		private string _flow_action_code;
		private string _flow_action_name;
		private string _flow_action_event;
		private string _flow_action_remark;
		private int _flow_action_order;
		private string _flow_action_image;
		private string _flow_action_flag;
		private string _flow_action_default;
		
		///<sumary>
		/// 流程动作编号
        ///</sumary>
        [DataMember]
		public int FLOW_ACTION_ID
		{
			get{return _flow_action_id;}
			set{_flow_action_id = value;}
		}
		///<sumary>
		/// 流程节点编号
        ///</sumary>
        [DataMember]
		public int FLOW_NODE_ID
		{
			get{return _flow_node_id;}
			set{_flow_node_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_CODE
		{
			get{return _flow_action_code;}
			set{_flow_action_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_NAME
		{
			get{return _flow_action_name;}
			set{_flow_action_name = value;}
		}
		///<sumary>
		/// 事件
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_EVENT
		{
			get{return _flow_action_event;}
			set{_flow_action_event = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_REMARK
		{
			get{return _flow_action_remark;}
			set{_flow_action_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int FLOW_ACTION_ORDER
		{
			get{return _flow_action_order;}
			set{_flow_action_order = value;}
		}
		///<sumary>
		/// 显示图标
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_IMAGE
		{
			get{return _flow_action_image;}
			set{_flow_action_image = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_FLAG
		{
			get{return _flow_action_flag;}
			set{_flow_action_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string FLOW_ACTION_DEFAULT
		{
			get{return _flow_action_default;}
			set{_flow_action_default = value;}
		}
	}
}
