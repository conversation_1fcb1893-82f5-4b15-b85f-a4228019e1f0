﻿using System;
using System.Collections.Generic;
using System.Data;
using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(Dictionary<string,string>))]
    [ServiceKnownType(typeof(Model.PLAN_LIST))]
    public interface I_System
    {
        #region [ROLE RELATION]

        [OperationContract]
        bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER USER);

        [OperationContract]
        bool USER_PASSWORD(string USER_CODE, string USER_PASSWORD_OLD, string USER_PASSWORD_NEW, out string sResult);
        
        [OperationContract]
        IList<SiaSun.LMS.Model.SYS_ROLE> ROLE_GetList(int USER_ID);

        [OperationContract]
        IList<Model.SYS_ROLE_WINDOW> ROLE_WINDOW_GetList_ROLE_MENU(int ROLE_ID, int MENU_ID);

        [OperationContract]
        Model.SYS_ROLE_WINDOW ROLE_WINDOW_GetModel_MENU_CONTROL(int ROLE_ID, int MENU_ID, string CONTROL_NAME);

        [OperationContract]
        bool ROLE_WINDOW_Save(int ROLE_ID, int MENU_ID, IList<Model.SYS_ROLE_WINDOW> listROLE_WINDOW, out string strResult);

        [OperationContract]
        DataTable ITEM_LIST_GetDictionary(string ITEM_CODE);

        [OperationContract]
        IList<SiaSun.LMS.Model.SYS_ITEM_LIST> ITEM_LIST_GetList_ITEM_CODE(string ITEM_CODE);
        
        [OperationContract()]
        IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList();

        [OperationContract()]
        IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList_ROLE_Select(int ROLE_ID, bool bSelect);

        [OperationContract]
        SiaSun.LMS.Model.SYS_MENU MENU_GetModel(int MENU_ID);

        [OperationContract]
        Model.SYS_RELATION RELATION_GetModel(string RELATION_CODE);

        [OperationContract]
        IList<Model.SYS_RELATION_LIST> RELATION_LIST_GetList_ID1(int RELATION_ID, int RELATION_ID1);

        [OperationContract]
        bool RELATION_LIST_Add(string RELATION_CODE, int RELATION_ID1, int[] List_RELATION_ID2, out string Result);

        [OperationContract]
        IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList();

        [OperationContract]
        DataSet SYS_TABLE_CONVERTER_Import(string TABLE_CONVERTER_CODE, DataTable tableImport, out string strResult);

        [OperationContract]
        int TABLE_CONVERTER_Save(Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, DataSet dsImport, out string strResult);
        
        #endregion [ROLE RELATION]
               

        #region [FLOW]

        [OperationContract]
        IList<SiaSun.LMS.Model.FLOW_PARA> FlowGetParameters(string FLOW_TYPE_CODE);

        [OperationContract]
        DataTable PlanGetAction(string PLAN_ID);

        [OperationContract(Name = "PlanGetAction1")]
        DataTable PlanGetAction(string PLAN_ID, string FLOW_ACTION_DEFAULT);

        [OperationContract]
        DataTable ManageGetAction(string MANAGE_ID);

        [OperationContract(Name = "ManageGetAction1")]
        DataTable ManageGetAction(string MANAGE_ID, string ACTION_CODE);

        [OperationContract]
        DataTable ControlGetAction(string MANAGE_ID);
        
        #endregion [FLOW]

        
        #region [GOODS]



        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsID(int GOODS_ID);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCode(string GOODS_CODE);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassID(int GOODS_CLASS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_MAIN> GoodsGetListGoodsClassID(int GOODS_CLASS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_PROPERTY> GoodsPropertyGetListGoodsTypeID(int GOODS_TYPE_ID);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_TEMPLATE GoodsTemplateGetModel(int TEMPLATE_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_TEMPLATE> GoodsTemplateGetList(int GOODS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> GoodsTemplateListGetList(int TEMPLATE_ID);


        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCodeContract(string GOODS_CODE, string CONTRACT);


        [OperationContract]
        SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassCode(string GOODS_CLASS_CODE);



        #endregion [GOODS]


        #region [CELL]

        [OperationContract]
        DataTable WAREHOUSE_GetList(int USER_ID, string WAREHOUSE_TYPE);

        [OperationContract]
        DataTable AREA_GetList(int WAREHOUSE_ID, string AREA_TYPE);

        [OperationContract]
        DataTable CELL_Z_GetList(int AREA_ID);

        [OperationContract]
        DataTable CELL_Z_GetList_AREA(int WAREHOUSEID, int AREA_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z(int WAREHOUSE_ID, string CELL_Z);

        [OperationContract]
        IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z_ByArea(int Area_ID, string CELL_Z);


        [OperationContract]
        bool CellInit();

        //[OperationContract]
        //void CellCreate(int WAREHOUSE_ID,
        //                int AREA_ID,
        //                int LOGIC_ID,
        //                int z_begin,
        //                int z_end,
        //                int x_begin,
        //                int x_end,
        //                int y_begin,
        //                int y_end,
        //                string DEVICE_CODE,
        //                string LANE_WAY,
        //                string SHELF_TYPE,
        //                string SHELF_NEIGHBOUR,
        //                string CELL_MODEL,
        //                string CELL_LOGICAL_NAME,
        //                string CELL_INOUT,
        //                string CELL_TYPE,
        //                string CELL_STORAGE_TYPE,
        //                string CELL_FORK_TYPE,
        //                int CELL_FORK_COUNT,
        //                int CELL_WIDTH,
        //                int CELL_HEIGHT
        //                 );

        //[OperationContract]
        //void StationCreate(int WAREHOUSE_ID,
        //                int AREA_ID,
        //                int LOGIC_ID,
        //                string DEVICE_CODE,
        //                string DEVICE_NAME,
        //                string LANE_WAY,
        //                string SHELF_TYPE,
        //                string SHELF_NEIGHBOUR,
        //                string CELL_MODEL,
        //                string CELL_LOGICAL_NAME,
        //                string CELL_INOUT,
        //                string CELL_TYPE,
        //                string CELL_STORAGE_TYPE,
        //                string CELL_FORK_TYPE
        //                );

        #endregion [CELL]



        [OperationContract]
        bool DynamicInvoke(string classType, string methodName, out string message, params object[] methodParams);

        [OperationContract]
        void CreateLog(string clientIp, Enum.LogLevel level, string logClass, string method, Enum.LogType type, string logOperator, string message, Exception exception);

        [OperationContract]
        string GetSysParameter(string paraKey, string paraDefault);

        [OperationContract]
        string MessageConverter_GetKeyValue(string Key, params object[] Param);

        [OperationContract]
        bool CloneGoodsProperty(int goodsId, object target, object source, out string message);

        [OperationContract]
        void CLog(string userName, string pcIp, string formName, string Message);

    }
}
