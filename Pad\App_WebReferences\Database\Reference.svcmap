<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ID="52ee50e4-3b4d-4d38-b616-dc267636a5c7" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://127.0.0.1:8002/Service/Database" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="Database.disco" MetadataType="Disco" ID="cc4a26ea-5a6f-46c9-8675-f79ac3684b15" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?disco" />
    <MetadataFile FileName="Database.xsd" MetadataType="Schema" ID="32e5075a-337c-4264-a703-428f9d88cd0b" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd1" />
    <MetadataFile FileName="Database1.xsd" MetadataType="Schema" ID="f7804c73-267b-4c2c-ace2-276bedc6f9fa" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd0" />
    <MetadataFile FileName="Database2.xsd" MetadataType="Schema" ID="7db39572-155b-44d3-985b-4773d50278e8" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd3" />
    <MetadataFile FileName="Database3.xsd" MetadataType="Schema" ID="0e892945-fca0-4896-be2a-422631d48b42" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd2" />
    <MetadataFile FileName="Database4.xsd" MetadataType="Schema" ID="d2676346-32bb-46e1-8584-95708ac4056e" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd5" />
    <MetadataFile FileName="Database5.xsd" MetadataType="Schema" ID="e96dde65-6d73-4bdf-a6fa-9406d1b12814" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd4" />
    <MetadataFile FileName="Database6.xsd" MetadataType="Schema" ID="4dc78429-f64d-49d3-8c87-6dee73fe2399" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?xsd=xsd6" />
    <MetadataFile FileName="S_Database.wsdl" MetadataType="Wsdl" ID="8d71b078-c56c-4229-a1cf-1a4a02cdeb5b" SourceId="1" SourceUrl="http://127.0.0.1:8002/Service/Database?wsdl" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>