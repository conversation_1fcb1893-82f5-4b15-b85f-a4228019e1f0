﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_OUT_LOG.xaml 的交互逻辑
    /// tzyg add 2017-03-02
    /// </summary>
    public partial class MANAGE_OUT_LOG : AvalonDock.DocumentContent
    {
        public MANAGE_OUT_LOG()
        {
            InitializeComponent();
            this.ucQueryControl.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(QueryButtonAction);
        }


        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            InitQueryControl();
            StockListBind();
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if(btn!=null)
            {
                switch(btn.Name)
                {
                    case "btnStockDown":
                        this.StockDown();
                        break;
                }
            }
        }

        /// <summary>
        /// 点击“查询”按钮的处理方法
        /// </summary>
        private void QueryButtonAction(string strQueryWhere)
        {
            if(string.IsNullOrEmpty(strQueryWhere))
            {
                return;
            }

            try
            {
                this.ucManageOutDataGrid.U_Fields = "top 1 * ";
                this.ucManageOutDataGrid.U_AppendWhere = strQueryWhere;
                this.ucManageOutDataGrid.U_InitControl();
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
        
        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            try
            {
                this.ucQueryControl.U_AddButtonVisable = false; ;
                this.ucQueryControl.U_XmlTableName = "MANAGE_OUT_LOG";
                this.ucQueryControl.U_WindowName = this.GetType().Name;
                this.ucQueryControl.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 绑定列表数据
        /// </summary>
        private void StockListBind()
        {
            try
            {
                this.ucManageOutDataGrid.U_WindowName = this.GetType().Name;
                this.ucManageOutDataGrid.U_TableName = "MANAGE_OUT_LOG";
                this.ucManageOutDataGrid.U_OrderField = "MANAGE_OUT_LOG_ID desc";

                this.ucManageOutDataGrid.U_AllowChecked = true;
                this.ucManageOutDataGrid.U_AllowOperatData = false;

                //this.ucManageOutDataGrid.U_InitControl();
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }            
        }

        /// <summary>
        /// 托盘下架
        /// </summary>
        private void StockDown()
        {
            bool bResult = true;
            string sResult = string.Empty;
            DataRowView[] listDataRowView = this.ucManageOutDataGrid.U_GetCheckDataRow();
            if (listDataRowView == null || listDataRowView.ToList().Count != 1)
            {
                bResult = false;
                sResult = string.Format("未选中记录或选中了多条记录");
                MainApp._MessageDialog.ShowResult(bResult, sResult);
                return;
            }

            try
            {
                bResult = SiaSun.LMS.WPFClient.MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.MANAGE.MANAGE_DOWN", "下架", new object[] { "ManageDown", listDataRowView[0].Row["MANAGE_OUT_LOG_STOCK"].ToString() }, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("执行托盘匹配异常 {0}", ex.Message);
            }
            finally
            {
                if (!bResult)
                {
                    MainApp._MessageDialog.ShowResult(bResult, sResult);
                }
            }
        }

        private void panelQuery_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
