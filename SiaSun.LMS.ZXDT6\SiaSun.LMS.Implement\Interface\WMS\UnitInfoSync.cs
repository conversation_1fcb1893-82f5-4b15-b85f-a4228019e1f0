using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 计量单位同步接口
    /// </summary>
    public class UnitInfoSync : InterfaceBase
    {
        class InputParam
        {
            public string measuringUnitStatus { get; set; }
            public string measuringUnitCode { get; set; }
            public string code { get; set; }
            public string parentId { get; set; }
            public string name { get; set; }
            public bool ignoreParent { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            int resultCode = 0;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                // JSON parsing
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    resultCode = 2;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Required field validation
                if (string.IsNullOrEmpty(inputParam.code))
                {
                    resultCode = 2;
                    message = "接口入参必填项[code]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.name))
                {
                    resultCode = 2;
                    message = "接口入参必填项[name]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                if (string.IsNullOrEmpty(inputParam.measuringUnitCode))
                {
                    resultCode = 2;
                    message = "接口入参必填项[measuringUnitCode]存在空值";
                    return FormatResponse(resultCode, message, traceId);
                }

                // Database operations - Process unit information
                // Since there's no specific unit model in the current system, we'll use SYS_ITEM
                // which appears to be used for system configuration items including units
                
                // Check if unit already exists by code
                //var existingUnits = S_Base.sBase.pSYS_ITEM.GetList($"ITEM_CODE = '{inputParam.code}'");
                
                //if (existingUnits != null && existingUnits.Count > 0)
                //{
                //    // Update existing unit
                //    var unitItem = existingUnits[0];
                //    unitItem.ITEM_NAME = inputParam.name;
                //    unitItem.ITEM_REMARK = $"Status:{inputParam.measuringUnitStatus}|MeasuringCode:{inputParam.measuringUnitCode}";
                //    unitItem.ITEM_FLAG = inputParam.status.ToString();
                    
                //    S_Base.sBase.pSYS_ITEM.Update(unitItem);
                //    message = "计量单位信息更新成功";
                //}
                //else
                //{
                //    // Create new unit
                //    var newUnit = new Model.SYS_ITEM()
                //    {
                //        ITEM_CODE = inputParam.code,
                //        ITEM_NAME = inputParam.name,
                //        ITEM_REMARK = $"Status:{inputParam.measuringUnitStatus}|MeasuringCode:{inputParam.measuringUnitCode}",
                //        ITEM_FLAG = inputParam.status.ToString(),
                //        ITEM_ORDER = 0,
                //        ITEM_PARENT_ID = string.IsNullOrEmpty(inputParam.parentId) ? 0 : int.TryParse(inputParam.parentId, out int parentId) ? parentId : 0
                //    };
                    
                //    S_Base.sBase.pSYS_ITEM.Add(newUnit);
                //    message = "计量单位信息创建成功";
                //}
                
                // Log the operation
                S_Base.sBase.Log.Info($"UnitInfoSync处理成功_入参[{inputJson}]_traceId[{traceId}]");
            }
            catch (Exception ex)
            {
                resultCode = 1;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"UnitInfoSync处理异常_入参[{inputJson}]_异常[{ex.Message}]_traceId[{traceId}]");
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = resultCode,
                    msg = message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        private string FormatResponse(int code, string msg, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = code,
                msg = msg,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}