﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using SiaSun.LMS.Model;
using System.Collections;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ApplyBase
    {
        protected APPLY_TYPE_PARAM applyTypeParam = new APPLY_TYPE_PARAM();

        protected bool ValidateStockBarcode(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            if (string.IsNullOrEmpty(mIO_CONTROL_APPLY.STOCK_BARCODE))
            {
                bResult = false;
                sResult = string.Format("申请托盘条码不能为空");
                return bResult;
            }

            if(string.IsNullOrEmpty(mIO_CONTROL_APPLY.STOCK_BARCODE.Replace("1","")))
            {
                bResult = false;
                sResult = string.Format("托盘条码扫描失败");
                return bResult;
            }

            string validRegexString = string.Empty;
            if (S_Base.sBase.sSystem.GetSysParameter("StockBarcodeValidRegex", out validRegexString) &&
                !Common.RegexValid.IsValidate(mIO_CONTROL_APPLY.STOCK_BARCODE, validRegexString))
            {
                bResult = false;
                sResult = string.Format("托盘条码未通过格式校验_托盘条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE);
                return bResult;
            }

            string index = mIO_CONTROL_APPLY.STOCK_BARCODE.Substring(0, 1);
            switch (index)
            {
                case "A":
                case "B":
                case "C":
                case "D":
                    if (mIO_CONTROL_APPLY.DEVICE_CODE.Equals("12006") || mIO_CONTROL_APPLY.DEVICE_CODE.Equals("12008"))
                    {
                        bResult = false;
                        sResult = string.Format(@"小规格托盘禁止入大托盘巷道，请检查!");
                        return bResult;
                    }
                    break;
                case "E":
                case "F":
                case "G":
                case "H":
                    if(mIO_CONTROL_APPLY.DEVICE_CODE.Equals("12002") || mIO_CONTROL_APPLY.DEVICE_CODE.Equals("12004"))
                    {
                        bResult = false;
                        sResult = string.Format(@"大规格托盘禁止入小托盘巷道，请检查!");
                        return bResult;
                    }
                    break;
                default:
                    break;
            }
            return bResult;
        }

        /// <summary>
        /// 初始化申请的参数
        /// </summary>
        /// <param name="sApplyType"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        protected bool InitApplyParam(string sApplyType, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)S_Base.sBase.sDatabase.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", sApplyType).RequestObject;

            Model.GOODS_TYPE mGOODS_TYPE = (Model.GOODS_TYPE)S_Base.sBase.sDatabase.GetModel("GOODS_TYPE_SELECT_BY_GOODS_TYPE_REMARK", "APPLY_TYPE").RequestObject;

            Hashtable ht = new Hashtable();

            string[] arStrProperty = mAPPLY_TYPE.APPLY_TYPE_PROPERTY.ToString().Split('|');

            DataTable tableSplitSource = S_Base.sBase.sDatabase.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", mGOODS_TYPE.GOODS_TYPE_ID));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            //tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Field";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";


            for (int i = 0; i < tableSplitSource.Rows.Count; i++)
            {
                ht.Add(tableSplitSource.Rows[i]["column"].ToString(), arStrProperty[i].ToString());
            }

            PropertyInfo[] propertys_info = this.GetType().GetProperties();

            foreach (PropertyInfo pi in propertys_info)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }

            PropertyInfo[] property_info_param = applyTypeParam.GetType().GetProperties();
            foreach (PropertyInfo pi in property_info_param)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(applyTypeParam, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(applyTypeParam, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }

            return bResult;
        }

        /// <summary>
        /// 发送给显示设备通知
        /// </summary>
        /// <param name="sMessage"></param>
        /// <returns></returns>
        protected void SendLEDMessage(string applyStation, string ledMessage)
        {
            try
            {
                if (string.IsNullOrEmpty(applyStation) || string.IsNullOrEmpty(ledMessage))
                {
                    return;
                }

                var lcdStation = this.applyTypeParam.U_LedStation.Split(';').FirstOrDefault(r => r.StartsWith(applyStation));
                if (!string.IsNullOrEmpty(lcdStation))
                {
                    var lcdMain = S_Base.sBase.pLCD_MAIN.GetList().FirstOrDefault(r => lcdStation.Contains(r.LCD_STATION));
                    if (lcdMain != null)
                    {
                        var lcdList = new Model.LCD_LIST()
                        {
                            DISPLAY_FLAG = Enum.FLAG.P.ToString("d"),
                            DISPLAY_TYPE = Enum.DISPLAY_TYPE.ByLifeTime.ToString(),
                            HANDLE_FLAG = "0",
                            LIFE_TIME = Common.StringUtil.FormatDateTime(DateTime.Now.AddMinutes(double.Parse(S_Base.sBase.sSystem.GetSysParameter("DefaultErrorLcdLifeTime", "5")))),
                            LCD_ID = lcdMain.LCD_ID,
                            LCD_LIST_ID = 0,
                            WRITE_TIME = Common.StringUtil.GetDateTime(),
                            DISPLAY_INFO1 = $"{applyStation}：{ledMessage}"
                        };

                        S_Base.sBase.pLCD_LIST.Add(lcdList);
                    }
                }
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error($"申请[{applyStation}]写入LCD消息[{ledMessage}]异常", ex);
            }
        }

        /// <summary>
        /// 申请处理失败后下达到异常口的任务
        /// </summary>
        /// <param name="mIO_CONTROL_APPLY"></param>
        /// <param name="ExceptionStation"></param>
        /// <returns></returns>
        protected string CreateApplyExceptionTask(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY)
        {
            string message = string.Empty;
            string end_device = string.Empty;

            try
            {
                switch (mIO_CONTROL_APPLY.DEVICE_CODE)
                {
                    case "12002":
                        end_device = "12001";
                        break;
                    case "12004":
                        end_device = "12003";
                        break;
                    case "12006":
                        end_device = "12005";
                        break;
                    case "12008":
                        end_device = "12007";
                        break;
                    default:
                        end_device = "0";
                        break;
                }
                Model.IO_CONTROL mIO_CONTROL = new IO_CONTROL();
                mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                mIO_CONTROL.MANAGE_ID = 0;
                mIO_CONTROL.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                mIO_CONTROL.CONTROL_TASK_LEVEL = "0";
                mIO_CONTROL.PRE_CONTROL_STATUS = 0;
                mIO_CONTROL.CONTROL_TASK_TYPE = 4;
                mIO_CONTROL.START_DEVICE_CODE = mIO_CONTROL_APPLY.DEVICE_CODE;
                mIO_CONTROL.END_DEVICE_CODE = end_device;
                mIO_CONTROL.CONTROL_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                mIO_CONTROL.CONTROL_STATUS = 0;

                Model.WH_CELL mWH_CELL_START = S_Base.sBase.pWH_CELL.GetModel(mIO_CONTROL_APPLY.WAREHOUSE_CODE, mIO_CONTROL.START_DEVICE_CODE);
                if (mWH_CELL_START != null && mWH_CELL_START.WAREHOUSE_ID != 0)
                {
                    Model.WH_WAREHOUSE mWH_WAREHOUSE_START = S_Base.sBase.pWH_WAREHOUSE.GetModel(mWH_CELL_START.WAREHOUSE_ID);
                    if (mWH_WAREHOUSE_START != null)
                    {
                        mIO_CONTROL.START_WAREHOUSE_CODE = mWH_WAREHOUSE_START.WAREHOUSE_CODE;
                    }
                }
                Model.WH_CELL mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(mIO_CONTROL_APPLY.WAREHOUSE_CODE, mIO_CONTROL.END_DEVICE_CODE);
                if (mWH_CELL_END != null && mWH_CELL_END.WAREHOUSE_ID != 0)
                {
                    Model.WH_WAREHOUSE mWH_WAREHOUSE_END = S_Base.sBase.pWH_WAREHOUSE.GetModel(mWH_CELL_END.WAREHOUSE_ID);
                    if (mWH_WAREHOUSE_END != null)
                    {
                        mIO_CONTROL.END_WAREHOUSE_CODE = mWH_WAREHOUSE_END.WAREHOUSE_CODE;
                    }
                }
                mIO_CONTROL.CELL_GROUP = "0";

                S_Base.sBase.pIO_CONTROL.Add(mIO_CONTROL);

                S_Base.sBase.Log.Info(string.Format("_下达到异常口控制任务成功_ControlId[{0}]_起点[{1}]_条码[{2}]_终点[{3}]", mIO_CONTROL.CONTROL_ID, mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.STOCK_BARCODE, mIO_CONTROL.END_DEVICE_CODE));
            }
            catch (Exception ex)
            {
                S_Base.sBase.Log.Error("下达到异常口控制任务时异常", ex);
                message = string.Format("_下达到异常口控制任务时异常_信息[{0}]", ex.Message);
            }
            finally
            {
                S_Base.sBase.Log.Info(message);
            }

            return message;
        }

        /// <summary>
        /// 写申请历史表
        /// </summary>
        /// <param name="mIO_CONTROL_APPLY"></param>
        /// <param name="applyResult"></param>
        /// <param name="errorMessage"></param>
        protected void WriteHisData(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool applyResult, string errorMessage)
        {
            //如果是因为SQLServer死锁导致的申请失败，则不写入历史，给再次处理的机会
            if (!applyResult && errorMessage.Contains("选作死锁牺牲品"))
            {
                return;
            }

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                Model.IO_CONTROL_APPLY_HIS mIO_CONTROL_APPLY_HIS = new Model.IO_CONTROL_APPLY_HIS();

                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_ID = mIO_CONTROL_APPLY.CONTROL_APPLY_ID;
                mIO_CONTROL_APPLY_HIS.CONTROL_ID = mIO_CONTROL_APPLY.CONTROL_ID;
                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_TYPE = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE;
                mIO_CONTROL_APPLY_HIS.WAREHOUSE_CODE = mIO_CONTROL_APPLY.WAREHOUSE_CODE;
                mIO_CONTROL_APPLY_HIS.DEVICE_CODE = mIO_CONTROL_APPLY.DEVICE_CODE;
                mIO_CONTROL_APPLY_HIS.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                mIO_CONTROL_APPLY_HIS.APPLY_TASK_STATUS = applyResult ? 1 : 0;
                mIO_CONTROL_APPLY_HIS.CREATE_TIME = mIO_CONTROL_APPLY.CREATE_TIME;
                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARAMETER = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER;
                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARA01 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA01;
                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARA02 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA02;
                mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_REMARK = mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK;
                mIO_CONTROL_APPLY_HIS.MANAGE_ERROR_TEXT = errorMessage;
                mIO_CONTROL_APPLY_HIS.HANDLE_TIME = Common.StringUtil.GetDateTime();

                S_Base.sBase.pIO_CONTROL_APPLY_HIS.Add(mIO_CONTROL_APPLY_HIS);
                S_Base.sBase.pIO_CONTROL_APPLY.Delete(mIO_CONTROL_APPLY.CONTROL_APPLY_ID);

                S_Base.sBase.sDatabase.CommitTransaction();
            }
            catch(Exception ex)
            {
                S_Base.sBase.Log.ErrorFormat("写入申请历史时异常", ex);
                S_Base.sBase.sDatabase.RollBackTransaction();
            }
        }
    }
}
