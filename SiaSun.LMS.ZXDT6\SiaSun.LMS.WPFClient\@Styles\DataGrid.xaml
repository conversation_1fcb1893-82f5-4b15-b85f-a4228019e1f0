﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC">

    <BooleanToVisibilityConverter x:Key="bool2VisibilityConverter" />

    <!--  Style and template for the button in the upper left corner of the DataGrid.  -->
    <Style x:Key="{ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle, TypeInTargetAssembly={x:Type DataGrid}}" TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid>
                        <Rectangle x:Name="Border" SnapsToDevicePixels="True">
                            <Rectangle.Stroke>
                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                    <GradientStop Offset="0" Color="{DynamicResource BorderLightColor}" />
                                    <GradientStop Offset="1" Color="{DynamicResource BorderMediumColor}" />
                                </LinearGradientBrush>
                            </Rectangle.Stroke>
                        </Rectangle>
                        <Polygon
                            x:Name="Arrow"
                            Margin="8,8,3,3"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Bottom"
                            Opacity="0.15"
                            Points="0,10 10,10 10,0"
                            Stretch="Uniform">
                            <Polygon.Fill>
                                <SolidColorBrush Color="Red" />
                            </Polygon.Fill>
                        </Polygon>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Fill).                         (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="LightBlue" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="Arrow" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style and template for the DataGrid.  -->
    <Style TargetType="{x:Type uc:DataGridTemplateNobug}">
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="RowDetailsVisibilityMode" Value="Collapsed" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="MinRowHeight" Value="23" />
        <Setter Property="HorizontalGridLinesBrush" Value="LightGray" />
        <Setter Property="VerticalGridLinesBrush" Value="LightGray" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGrid}">
                    <Border
                        x:Name="border"
                        Padding="{TemplateBinding Padding}"
                        Background="White"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True">
                        <ScrollViewer x:Name="DG_ScrollViewer" Focusable="false">
                            <ScrollViewer.Template>
                                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Border
                                            Grid.ColumnSpan="3"
                                            BorderBrush="#FF8A9199"
                                            BorderThickness="1">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                                    <GradientStop Color="#FFF0F4F8" />
                                                    <GradientStop Offset="1" Color="#FFD7DBE1" />
                                                </LinearGradientBrush>
                                            </Border.Background>
                                        </Border>
                                        <Border
                                            Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                            BorderBrush="#FF8A9199"
                                            BorderThickness="0,1,1,1" />
                                        <DataGridColumnHeadersPresenter
                                            x:Name="PART_ColumnHeadersPresenter"
                                            Grid.Column="1"
                                            Grid.ColumnSpan="2"
                                            Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                        <ScrollContentPresenter
                                            x:Name="PART_ScrollContentPresenter"
                                            Grid.Row="1"
                                            Grid.ColumnSpan="2"
                                            CanContentScroll="False" />
                                        <ScrollBar
                                            x:Name="PART_VerticalScrollBar"
                                            Grid.Row="1"
                                            Grid.Column="2"
                                            Maximum="{TemplateBinding ScrollableHeight}"
                                            Orientation="Vertical"
                                            ViewportSize="{TemplateBinding ViewportHeight}"
                                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                            Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                        <Grid Grid.Row="2" Grid.Column="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <ScrollBar
                                                x:Name="PART_HorizontalScrollBar"
                                                Grid.Column="1"
                                                Maximum="{TemplateBinding ScrollableWidth}"
                                                Orientation="Horizontal"
                                                ViewportSize="{TemplateBinding ViewportWidth}"
                                                Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                        </Grid>
                                    </Grid>
                                </ControlTemplate>
                            </ScrollViewer.Template>
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </ScrollViewer>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="border" Storyboard.TargetProperty="(Panel.Background).(SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{DynamicResource ControlLightColor}" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Normal" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsGrouping" Value="true">
                <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  Style and template for the DataGridCell.  -->
    <Style TargetType="{x:Type DataGridCell}">
        <Setter Property="Foreground" Value="Black" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridCell}">
                    <Border
                        x:Name="border"
                        Background="Transparent"
                        BorderBrush="Transparent"
                        BorderThickness="1"
                        SnapsToDevicePixels="True">
                        <ContentPresenter VerticalAlignment="Center" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="FocusStates">
                                <VisualState x:Name="Unfocused" />
                                <VisualState x:Name="Focused" />
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CurrentStates">
                                <VisualState x:Name="Regular" />
                                <VisualState x:Name="Current">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="border" Storyboard.TargetProperty="(Border.BorderBrush).                         (SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="CornflowerBlue" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style and template for the DataGridRow.  -->
    <Style TargetType="{x:Type DataGridRow}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}" />
        <Setter Property="ValidationErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <TextBlock
                        Margin="2,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="Red"
                        Text="!" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridRow}">
                    <Border
                        x:Name="DGR_Border"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="True">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                <GradientStop Offset="0" Color="Transparent" />
                                <GradientStop Offset="1" Color="White" />
                            </LinearGradientBrush>
                        </Border.Background>

                        <SelectiveScrollingGrid>
                            <SelectiveScrollingGrid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </SelectiveScrollingGrid.ColumnDefinitions>
                            <SelectiveScrollingGrid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </SelectiveScrollingGrid.RowDefinitions>
                            <DataGridCellsPresenter
                                Grid.Column="1"
                                ItemsPanel="{TemplateBinding ItemsPanel}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            <DataGridDetailsPresenter
                                Grid.Row="1"
                                Grid.Column="1"
                                SelectiveScrollingGrid.SelectiveScrollingOrientation="{Binding AreRowDetailsFrozen, ConverterParameter={x:Static SelectiveScrollingOrientation.Vertical}, Converter={x:Static DataGrid.RowDetailsScrollingConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                Visibility="{TemplateBinding DetailsVisibility}" />
                            <DataGridRowHeader
                                Grid.RowSpan="2"
                                SelectiveScrollingGrid.SelectiveScrollingOrientation="Vertical"
                                Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Row}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                        </SelectiveScrollingGrid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <!--  Provide a different appearance for every other row.  -->
                                <VisualState x:Name="Normal_AlternatingRow">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[0].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="White" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="Snow" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="Normal_Selected">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[0].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="LightBlue" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="Lavender" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[0].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="White" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="DGR_Border" Storyboard.TargetProperty="(Panel.Background).                         (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#FFE2F1FF" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>

                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <!--  Style and template for the resize control on the DataGridRowHeader.  -->
    <Style x:Key="RowHeaderGripperStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Height" Value="8" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Cursor" Value="SizeNS" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Padding="{TemplateBinding Padding}" Background="{TemplateBinding Background}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style and template for the DataGridRowHeader.  -->
    <Style TargetType="{x:Type DataGridRowHeader}">
        <Setter Property="MinHeight" Value="23" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridRowHeader}">
                    <Grid>
                        <Border
                            x:Name="rowHeaderBorder"
                            MinWidth="38"
                            BorderBrush="#FF8A9199"
                            BorderThickness="1,0,1,1">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                    <GradientStop Color="#FFF0F4F8" />
                                    <GradientStop Offset="1" Color="#FFD7DBE1" />
                                </LinearGradientBrush>
                            </Border.Background>
                            <StackPanel Margin="10,0" Orientation="Horizontal">
                                <ContentPresenter
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                <Control
                                    SnapsToDevicePixels="false"
                                    Template="{Binding ValidationErrorTemplate, RelativeSource={RelativeSource AncestorType={x:Type DataGridRow}}}"
                                    Visibility="{Binding (Validation.HasError), Converter={StaticResource bool2VisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGridRow}}}" />
                            </StackPanel>
                        </Border>
                        <Thumb
                            x:Name="PART_TopHeaderGripper"
                            VerticalAlignment="Top"
                            Style="{StaticResource RowHeaderGripperStyle}" />
                        <Thumb
                            x:Name="PART_BottomHeaderGripper"
                            VerticalAlignment="Bottom"
                            Style="{StaticResource RowHeaderGripperStyle}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style and template for the resize control on the DataGridColumnHeader.  -->
    <Style x:Key="ColumnHeaderGripperStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Width" Value="8" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Cursor" Value="SizeWE" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Padding="{TemplateBinding Padding}" Background="{TemplateBinding Background}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Style and template for the DataGridColumnHeader.  -->
    <Style TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="MinHeight" Value="23" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeader}">
                    <Grid>
                        <Border
                            x:Name="columnHeaderBorder"
                            Padding="3,0,3,0"
                            BorderBrush="#FF8A9199"
                            BorderThickness="0,1,1,1">
                            <ContentPresenter
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                        <Thumb
                            x:Name="PART_LeftHeaderGripper"
                            HorizontalAlignment="Left"
                            Style="{StaticResource ColumnHeaderGripperStyle}" />
                        <Thumb
                            x:Name="PART_RightHeaderGripper"
                            HorizontalAlignment="Right"
                            Style="{StaticResource ColumnHeaderGripperStyle}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type DataGridColumnHeadersPresenter}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeadersPresenter}">
                    <Grid>
                        <DataGridColumnHeader x:Name="PART_FillerColumnHeader" IsHitTestVisible="False" />
                        <ItemsPresenter />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>