<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/Manage?xsd=xsd1" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
  <xs:element name="PlanEventExecute">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mPLAN_ACTION_EXCUTE" nillable="true" type="q1:PLAN_ACTION_EXCUTE" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PlanEventExecuteResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PlanEventExecuteResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageEventExecute">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mt" nillable="true" type="q2:MANAGE_ACTION_EXCUTE" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageEventExecuteResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageEventExecuteResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ContinusManageDown">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="user" nillable="true" type="q3:SYS_USER" />
        <xs:element minOccurs="0" name="row" type="xs:int" />
        <xs:element minOccurs="0" name="startColumn" type="xs:int" />
        <xs:element minOccurs="0" name="endColumn" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ContinusManageDownResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ContinusManageDownResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageCreate">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mMANAGE_MAIN" nillable="true" type="q4:MANAGE_MAIN" />
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="lsMANAGE_LIST" nillable="true" type="q5:ArrayOfMANAGE_LIST" />
        <xs:element minOccurs="0" name="raiseTrans" type="xs:boolean" />
        <xs:element minOccurs="0" name="checkStorage" type="xs:boolean" />
        <xs:element minOccurs="0" name="checkManage" type="xs:boolean" />
        <xs:element minOccurs="0" name="checkCellStatus" type="xs:boolean" />
        <xs:element minOccurs="0" name="autoComplete" type="xs:boolean" />
        <xs:element minOccurs="0" name="autoControl" type="xs:boolean" />
        <xs:element minOccurs="0" name="doubleInAutoMove" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageCreateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageCreateResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageComplete">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="bTrans" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageCompleteResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageCompleteResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageException">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageExceptionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageExceptionResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageError">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageErrorResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageErrorResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageCancel">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="raiseTrans" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageCancelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageCancelResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ControlApplyAdd">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mIO_CONTROL_APPLY" nillable="true" type="q6:IO_CONTROL_APPLY" />
        <xs:element minOccurs="0" name="bTrans" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ControlApplyAddResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ControlApplyAddResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="WsControlApply">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="controlApplyType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="deviceCode" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="stockBarcode" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="controlApplyPara" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="WsControlApplyResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="WsControlApplyResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsCreate">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="dt" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsCreateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GoodsCreateResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>