﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="FLOW_PARA" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="FLOW_PARA" type="SiaSun.LMS.Model.FLOW_PARA, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="FLOW_PARA">
			<result property="FLOW_PARA_ID" column="flow_para_id" />
			<result property="FLOW_TYPE_ID" column="flow_type_id" />
			<result property="FLOW_PARA_CODE" column="flow_para_code" />
			<result property="FLOW_PARA_NAME" column="flow_para_name" />
			<result property="FLOW_PARA_REMARK" column="flow_para_remark" />
			<result property="FLOW_PARA_ORDER" column="flow_para_order" />
			<result property="FLOW_PARA_FLAG" column="flow_para_flag" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="FLOW_PARA_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			flow_para_id,
			FLOW_PARA.flow_type_id,
			flow_para_code,
			flow_para_name,
			flow_para_remark,
			flow_para_order,
			flow_para_flag
			From FLOW_PARA
		</select>

		<select id="FLOW_PARA_SELECT_BY_ID" parameterClass="int" extends = "FLOW_PARA_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_para_id=#FLOW_PARA_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="FLOW_PARA_SELECT_BY_FLOW_TYPE_CODE" parameterClass="string" extends = "FLOW_PARA_SELECT" resultMap="SelectResult">
			INNER JOIN FLOW_TYPE ON (FLOW_TYPE.FLOW_TYPE_ID = FLOW_PARA.FLOW_TYPE_ID)
			<dynamic prepend="WHERE">
				<isParameterPresent>
					FLOW_TYPE_CODE=#FLOW_TYPE_CODE# ORDER BY FLOW_PARA_ORDER
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="FLOW_PARA_INSERT" parameterClass="FLOW_PARA">
			Insert Into FLOW_PARA (
			<!--flow_para_id,-->
			flow_type_id,
			flow_para_code,
			flow_para_name,
			flow_para_remark,
			flow_para_order,
			flow_para_flag
			)Values(
			<!--#FLOW_PARA_ID#,-->
			#FLOW_TYPE_ID#,
			#FLOW_PARA_CODE#,
			#FLOW_PARA_NAME#,
			#FLOW_PARA_REMARK#,
			#FLOW_PARA_ORDER#,
			#FLOW_PARA_FLAG#
			)
			<selectKey  resultClass="int" type="post" property="FLOW_PARA_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="FLOW_PARA_UPDATE" parameterClass="FLOW_PARA">
			Update FLOW_PARA Set
			<!--flow_para_id=#FLOW_PARA_ID#,-->
			flow_type_id=#FLOW_TYPE_ID#,
			flow_para_code=#FLOW_PARA_CODE#,
			flow_para_name=#FLOW_PARA_NAME#,
			flow_para_remark=#FLOW_PARA_REMARK#,
			flow_para_order=#FLOW_PARA_ORDER#,
			flow_para_flag=#FLOW_PARA_FLAG#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_para_id=#FLOW_PARA_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="FLOW_PARA_DELETE" parameterClass="int">
			Delete From FLOW_PARA
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_para_id=#FLOW_PARA_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>