﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.IO;
using System.Drawing.Printing;
using Gma.QrCodeNet.Encoding;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucBarCodePlanarControl.xaml 的交互逻辑
    /// </summary>
    public partial class ucBarCodePlanarControl : UserControl
    {
        int intModuleSize = 4;
        string strHeader = "标题";
        string strData=null;

        /// <summary>
        /// 显示标题
        /// </summary>
        public string U_Header
        {
            get { return strHeader; }
            set { strHeader = value; this.txtHeader.Text = value; }
        }

        /// <summary>
        /// BarCode Data
        /// </summary>
        public string U_Data
        {
            get { return strData; }
            set { strData = value; this.qrImageControl.Text = strData; }
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ucBarCodePlanarControl()
        {
            InitializeComponent();
        }        

        /// <summary>
        /// 保存条码
        /// </summary>
        public void U_SaveBarCode()
        {
            try
            {
                System.Windows.Forms.SaveFileDialog sdlg = new System.Windows.Forms.SaveFileDialog();
                if (sdlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    QrEncoder qrEncoder = new QrEncoder(ErrorCorrectionLevel.H);
                    QrCode qrCode = new QrCode();
                    qrEncoder.TryEncode(this.strData, out qrCode);
                    Gma.QrCodeNet.Encoding.Windows.Controls.Renderer renderer = new Gma.QrCodeNet.Encoding.Windows.Controls.Renderer(intModuleSize, System.Drawing.Brushes.Black, System.Drawing.Brushes.White);
                    renderer.CreateImageFile(qrCode.Matrix, sdlg.FileName, System.Drawing.Imaging.ImageFormat.Png);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 打印条码
        /// </summary>
        public void U_PrintBarCode(PrintDialog pt,List<string> DataList)
        {
                foreach (string strData in DataList)
                {
                    this.qrImageControl.Text = strData;
                    pt.PrintVisual(this.qrImageControl, "打印二维码");
                }
        } 
    }
}
