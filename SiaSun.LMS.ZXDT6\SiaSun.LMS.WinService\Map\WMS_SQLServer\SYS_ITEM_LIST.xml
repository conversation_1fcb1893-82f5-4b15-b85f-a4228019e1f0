﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="SYS_ITEM_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="SYS_ITEM_LIST" type="SiaSun.LMS.Model.SYS_ITEM_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_ITEM_LIST">
			<result property="ITEM_LIST_ID" column="item_list_id" />
			<result property="ITEM_ID" column="item_id" />
			<result property="ITEM_LIST_CODE" column="item_list_code" />
			<result property="ITEM_LIST_NAME" column="item_list_name" />
			<result property="ITEM_LIST_ORDER" column="item_list_order" />
			<result property="ITEM_LIST_REMARK" column="item_list_remark" />
			<result property="ITEM_LIST_FLAG" column="item_list_flag" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="SYS_ITEM_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			item_list_id,
			item_id,
			item_list_code,
			item_list_name,
			item_list_order,
			item_list_remark,
			item_list_flag
			From SYS_ITEM_LIST
		</select>

		<select id="SYS_ITEM_LIST_SELECT_BY_ID" parameterClass="int" extends = "SYS_ITEM_LIST_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					item_list_id=#ITEM_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="SYS_ITEM_LIST_SELECT_BY_ITEM_CODE" parameterClass="int" extends = "SYS_ITEM_LIST_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					ITEM_ID IN(SELECT ITEM_ID FROM SYS_ITEM WHERE ITEM_CODE= #ITEM_CODE#) ORDER BY ITEM_LIST_ORDER
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="SYS_ITEM_LIST_INSERT" parameterClass="SYS_ITEM_LIST">
			Insert Into SYS_ITEM_LIST (
			<!--item_list_id,-->
			item_id,
			item_list_code,
			item_list_name,
			item_list_order,
			item_list_remark,
			item_list_flag
			)Values(
			<!--#ITEM_LIST_ID#,-->
			#ITEM_ID#,
			#ITEM_LIST_CODE#,
			#ITEM_LIST_NAME#,
			#ITEM_LIST_ORDER#,
			#ITEM_LIST_REMARK#,
			#ITEM_LIST_FLAG#
			)
			<selectKey  resultClass="int" type="post" property="ITEM_LIST_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="SYS_ITEM_LIST_UPDATE" parameterClass="SYS_ITEM_LIST">
			Update SYS_ITEM_LIST Set
			<!--item_list_id=#ITEM_LIST_ID#,-->
			item_id=#ITEM_ID#,
			item_list_code=#ITEM_LIST_CODE#,
			item_list_name=#ITEM_LIST_NAME#,
			item_list_order=#ITEM_LIST_ORDER#,
			item_list_remark=#ITEM_LIST_REMARK#,
			item_list_flag=#ITEM_LIST_FLAG#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					item_list_id=#ITEM_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="SYS_ITEM_LIST_DELETE" parameterClass="int">
			Delete From SYS_ITEM_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					item_list_id=#ITEM_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>