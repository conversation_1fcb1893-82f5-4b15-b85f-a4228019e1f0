<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" />
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/System" />
  <xs:element name="USER_LOGIN">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="USER_LOGINResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_LOGINResult" type="xs:boolean" />
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="USER" nillable="true" type="q1:SYS_USER" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="USER_PASSWORD">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="USER_PASSWORD_OLD" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="USER_PASSWORD_NEW" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="USER_PASSWORDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_PASSWORDResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="sResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_GetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_GetListResult" nillable="true" type="q2:ArrayOfSYS_ROLE" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_GetList_ROLE_MENU">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_GetList_ROLE_MENUResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_WINDOW_GetList_ROLE_MENUResult" nillable="true" type="q3:ArrayOfSYS_ROLE_WINDOW" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_GetModel_MENU_CONTROL">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
        <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_GetModel_MENU_CONTROLResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ROLE_WINDOW_GetModel_MENU_CONTROLResult" nillable="true" type="q4:SYS_ROLE_WINDOW" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_Save">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="listROLE_WINDOW" nillable="true" type="q5:ArrayOfSYS_ROLE_WINDOW" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ROLE_WINDOW_SaveResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ROLE_WINDOW_SaveResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ITEM_LIST_GetDictionary">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ITEM_LIST_GetDictionaryResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ITEM_LIST_GetDictionaryResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ITEM_LIST_GetList_ITEM_CODE">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ITEM_LIST_GetList_ITEM_CODEResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="ITEM_LIST_GetList_ITEM_CODEResult" nillable="true" type="q6:ArrayOfSYS_ITEM_LIST" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetList">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetListResult" nillable="true" type="q7:ArrayOfSYS_MENU" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetList_ROLE_Select">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="bSelect" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetList_ROLE_SelectResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetList_ROLE_SelectResult" nillable="true" type="q8:ArrayOfSYS_MENU" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetModel">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MENU_GetModelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="MENU_GetModelResult" nillable="true" type="q9:SYS_MENU" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_GetModel">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_GetModelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="RELATION_GetModelResult" nillable="true" type="q10:SYS_RELATION" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_LIST_GetList_ID1">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
        <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_LIST_GetList_ID1Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="RELATION_LIST_GetList_ID1Result" nillable="true" type="q11:ArrayOfSYS_RELATION_LIST" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_LIST_Add">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
        <xs:element xmlns:q12="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="List_RELATION_ID2" nillable="true" type="q12:ArrayOfint" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RELATION_LIST_AddResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RELATION_LIST_AddResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="Result" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="TABLE_CONVERTER_GetList">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="TABLE_CONVERTER_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q13="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="TABLE_CONVERTER_GetListResult" nillable="true" type="q13:ArrayOfSYS_TABLE_CONVERTER" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SYS_TABLE_CONVERTER_Import">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tableImport" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SYS_TABLE_CONVERTER_ImportResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SYS_TABLE_CONVERTER_ImportResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="TABLE_CONVERTER_Save">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q14="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="mTABLE_CONVERTER" nillable="true" type="q14:SYS_TABLE_CONVERTER" />
        <xs:element minOccurs="0" name="dsImport" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataSet" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:element ref="xs:schema" />
              <xs:any />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="TABLE_CONVERTER_SaveResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="TABLE_CONVERTER_SaveResult" type="xs:int" />
        <xs:element minOccurs="0" name="strResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FlowGetParameters">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="FLOW_TYPE_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FlowGetParametersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q15="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="FlowGetParametersResult" nillable="true" type="q15:ArrayOfFLOW_PARA" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PlanGetAction">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PLAN_ID" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PlanGetActionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PlanGetActionResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PlanGetAction1">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PLAN_ID" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="FLOW_ACTION_DEFAULT" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PlanGetAction1Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PlanGetAction1Result" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageGetAction">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageGetActionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageGetActionResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageGetAction1">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="ACTION_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ManageGetAction1Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ManageGetAction1Result" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ControlGetAction">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MANAGE_ID" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ControlGetActionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ControlGetActionResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsID">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsIDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q16="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsGetModelGoodsIDResult" nillable="true" type="q16:GOODS_MAIN" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsCode">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsCodeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q17="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsGetModelGoodsCodeResult" nillable="true" type="q17:GOODS_MAIN" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsClassGetModelGoodsClassID">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsClassGetModelGoodsClassIDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q18="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsClassGetModelGoodsClassIDResult" nillable="true" type="q18:GOODS_CLASS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetListGoodsClassID">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetListGoodsClassIDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q19="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsGetListGoodsClassIDResult" nillable="true" type="q19:ArrayOfGOODS_MAIN" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsPropertyGetListGoodsTypeID">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsPropertyGetListGoodsTypeIDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q20="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsPropertyGetListGoodsTypeIDResult" nillable="true" type="q20:ArrayOfGOODS_PROPERTY" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateGetModel">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="TEMPLATE_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateGetModelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q21="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsTemplateGetModelResult" nillable="true" type="q21:GOODS_TEMPLATE" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateGetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateGetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q22="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsTemplateGetListResult" nillable="true" type="q22:ArrayOfGOODS_TEMPLATE" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateListGetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="TEMPLATE_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsTemplateListGetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q23="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsTemplateListGetListResult" nillable="true" type="q23:ArrayOfGOODS_TEMPLATE_LIST" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsCodeContract">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_CODE" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="CONTRACT" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsGetModelGoodsCodeContractResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q24="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsGetModelGoodsCodeContractResult" nillable="true" type="q24:GOODS_MAIN" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsClassGetModelGoodsClassCode">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GOODS_CLASS_CODE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GoodsClassGetModelGoodsClassCodeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q25="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="GoodsClassGetModelGoodsClassCodeResult" nillable="true" type="q25:GOODS_CLASS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="WAREHOUSE_GetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
        <xs:element minOccurs="0" name="WAREHOUSE_TYPE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="WAREHOUSE_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="WAREHOUSE_GetListResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AREA_GetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="AREA_TYPE" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AREA_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AREA_GetListResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_Z_GetList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_Z_GetListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CELL_Z_GetListResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_Z_GetList_AREA">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="WAREHOUSEID" type="xs:int" />
        <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_Z_GetList_AREAResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CELL_Z_GetList_AREAResult" nillable="true">
          <xs:complexType>
            <xs:annotation>
              <xs:appinfo>
                <ActualType Name="DataTable" Namespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
              </xs:appinfo>
            </xs:annotation>
            <xs:sequence>
              <xs:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
              <xs:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_GetList_Z">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
        <xs:element minOccurs="0" name="CELL_Z" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_GetList_ZResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q26="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="CELL_GetList_ZResult" nillable="true" type="q26:ArrayOfWH_CELL" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_GetList_Z_ByArea">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="Area_ID" type="xs:int" />
        <xs:element minOccurs="0" name="CELL_Z" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CELL_GetList_Z_ByAreaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q27="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" minOccurs="0" name="CELL_GetList_Z_ByAreaResult" nillable="true" type="q27:ArrayOfWH_CELL" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CellInit">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="CellInitResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CellInitResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DynamicInvoke">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="classType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="methodName" nillable="true" type="xs:string" />
        <xs:element xmlns:q28="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="methodParams" nillable="true" type="q28:ArrayOfanyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DynamicInvokeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DynamicInvokeResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="clientIp" nillable="true" type="xs:string" />
        <xs:element xmlns:q29="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" minOccurs="0" name="level" type="q29:LogLevel" />
        <xs:element minOccurs="0" name="logClass" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="method" nillable="true" type="xs:string" />
        <xs:element xmlns:q30="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" minOccurs="0" name="type" type="q30:LogType" />
        <xs:element minOccurs="0" name="logOperator" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
        <xs:element xmlns:q31="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="exception" nillable="true" type="q31:Exception" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateLogResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSysParameter">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="paraKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="paraDefault" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSysParameterResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetSysParameterResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MessageConverter_GetKeyValue">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
        <xs:element xmlns:q32="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Param" nillable="true" type="q32:ArrayOfanyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="MessageConverter_GetKeyValueResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="MessageConverter_GetKeyValueResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CloneGoodsProperty">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="goodsId" type="xs:int" />
        <xs:element minOccurs="0" name="target" nillable="true" type="xs:anyType" />
        <xs:element minOccurs="0" name="source" nillable="true" type="xs:anyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CloneGoodsPropertyResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CloneGoodsPropertyResult" type="xs:boolean" />
        <xs:element minOccurs="0" name="message" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CLog">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="userName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcIp" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="formName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CLogResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
</xs:schema>