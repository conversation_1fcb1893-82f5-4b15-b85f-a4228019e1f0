﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="SiaSun.LMS.Model.WinXP" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <parameterMaps>
    <parameterMap id="paramMap" class ="Hashtable">
      <parameter property="TABLENAME" column="pTableName" />
      <parameter property="FIELDNAME" column="pKeyFieldName" />
      <parameter property="NEXTID" column="pReturnValue" direction ="Output" />
    </parameterMap>

     <parameterMap id="paramIInsertLog" class ="Hashtable">
       <parameter property="LOGTHREAD"  dbType="VARCHAR2" type="string" direction="Input" />
       <parameter property="LOGGER"  dbType="VARCHAR2" type="string" direction="Input" />
       <parameter property="LOGDATE"  dbType="VARCHAR2" type="string" direction="Input" />
       <parameter property="LOGLEVEL"   dbType="VARCHAR2" type="string" direction="Input" />
       <parameter property="LOGMESSAGE"  dbType="VARCHAR2" type="string" direction="Input" />
     </parameterMap>
     
     <parameterMap id="paramInit" class ="Hashtable">
     </parameterMap>
     
  </parameterMaps>

 
  
  <statements>
    <select id="dynamicSQL" resultClass="HashMap" remapResults="true">$sql$</select> 
    
    <procedure id="GetTableID" parameterMap="paramMap" >
      UP_GET_TABLEID
    </procedure>

    <procedure id="InitSystem" parameterMap="paramInit">
      UP_INIT_SYSTEM
    </procedure>

    <procedure id="P_INSERT_LOG" parameterMap="paramIInsertLog">
      P_INSERT_LOG
    </procedure>

  </statements>
</sqlMap>