﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="FLOW_NODE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="FLOW_NODE" type="SiaSun.LMS.Model.FLOW_NODE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="FLOW_NODE">
			<result property="FLOW_NODE_ID" column="flow_node_id" />
			<result property="FLOW_TYPE_ID" column="flow_type_id" />
			<result property="FLOW_NODE_CODE" column="flow_node_code" />
			<result property="FLOW_NODE_NAME" column="flow_node_name" />
			<result property="FLOW_NODE_REMARK" column="flow_node_remark" />
			<result property="FLOW_NODE_ORDER" column="flow_node_order" />
			<result property="FLOW_NODE_FLAG" column="flow_node_flag" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="FLOW_NODE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			flow_node_id,
			flow_type_id,
			flow_node_code,
			flow_node_name,
			flow_node_remark,
			flow_node_order,
			flow_node_flag
			From FLOW_NODE
		</select>

		<select id="FLOW_NODE_SELECT_BY_ID" parameterClass="int" extends = "FLOW_NODE_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_node_id=#FLOW_NODE_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="FLOW_NODE_INSERT" parameterClass="FLOW_NODE">
			Insert Into FLOW_NODE (
			<!--flow_node_id,-->
			flow_type_id,
			flow_node_code,
			flow_node_name,
			flow_node_remark,
			flow_node_order,
			flow_node_flag
			)Values(
			<!--#FLOW_NODE_ID#,-->
			#FLOW_TYPE_ID#,
			#FLOW_NODE_CODE#,
			#FLOW_NODE_NAME#,
			#FLOW_NODE_REMARK#,
			#FLOW_NODE_ORDER#,
			#FLOW_NODE_FLAG#
			)
			<selectKey  resultClass="int" type="post" property="FLOW_NODE_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="FLOW_NODE_UPDATE" parameterClass="FLOW_NODE">
			Update FLOW_NODE Set
			<!--flow_node_id=#FLOW_NODE_ID#,-->
			flow_type_id=#FLOW_TYPE_ID#,
			flow_node_code=#FLOW_NODE_CODE#,
			flow_node_name=#FLOW_NODE_NAME#,
			flow_node_remark=#FLOW_NODE_REMARK#,
			flow_node_order=#FLOW_NODE_ORDER#,
			flow_node_flag=#FLOW_NODE_FLAG#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_node_id=#FLOW_NODE_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="FLOW_NODE_DELETE" parameterClass="int">
			Delete From FLOW_NODE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					flow_node_id=#FLOW_NODE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>