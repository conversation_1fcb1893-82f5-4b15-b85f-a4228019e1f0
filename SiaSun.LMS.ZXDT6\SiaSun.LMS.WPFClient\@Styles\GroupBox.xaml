﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  默认GroupBox样式  -->
    <Style TargetType="{x:Type GroupBox}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            BorderThickness="1"
                            CornerRadius="2,2,0,0">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Offset="0.0" Color="#FFCCCCCC" />
                                            <GradientStop Offset="1.0" Color="#FF444444" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.BorderBrush>

                            <!--<Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Color="White" Offset="0.0" />
                                            <GradientStop Color="PaleGoldenrod" Offset="1.0" />
                                            <GradientStop Color="{DynamicResource ControlLightColor}" Offset="0.0" />
                                            <GradientStop Color="{DynamicResource ControlMediumColor}" Offset="1.0" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.Background>-->

                            <ContentPresenter
                                Margin="0"
                                ContentSource="Header"
                                RecognizesAccessKey="True" />
                        </Border>

                        <Border
                            Grid.Row="1"
                            Background="{StaticResource BorderBackground}"
                            BorderThickness="1,0,1,1"
                            CornerRadius="0,0,2,2">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="#FF888888" />
                            </Border.BorderBrush>
                            <ContentPresenter Margin="1" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>