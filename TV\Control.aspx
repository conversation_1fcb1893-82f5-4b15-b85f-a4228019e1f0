﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Control.aspx.cs" Inherits="Control"  ValidateRequest="false"  %>

<%--<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Namespace="System.Web.UI" TagPrefix="asp" %>--%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>南宁地铁五号线智能立库系统看板</title>  
  
    <meta http-equiv="refresh" content="1800;url=Control.aspx" />
 
    <script src="Scripts/echarts.js" type="text/javascript"   charset="utf-8"></script>
    <script type="text/javascript" src="Scripts/dark.js"></script>
<%--    <script src="Scripts/world.js" type="text/javascript"  charset="utf-8"></script>
    <script src="Scripts/china.js" type="text/javascript"  charset="utf-8"></script>--%>
    

    <script src="Scripts/theme2.js" type="text/javascript"></script>
    <script src="Scripts/theme1.js" type="text/javascript"></script>
    <link href="css/BackGround.css" rel="stylesheet" type="text/css" />
 
    <style type="text/css">
        .style1
        {
            width: 357px;
            height: 100px;
        }
        .style2
        {
            width: 277px;
            height: 30px;
        }
    </style> 
    
     
<style type="text/css">
                   body,ul,li,a,p,div {padding:0px;margin:0px;font-size:15px;}
        ul,li{list-style:none;}
        a{ text-decoration:none;color:#333;}
        #roll
        {
            overflow:hidden;
            width:100%;
             height:100px;
        }
           
    .style3
    {
        height: 10px;
    }
    </style>
    
     
</head>
<body style="height: 100%;  background-image:url(back.jpg); background-repeat:no-repeat; background-size:100%; margin: 0px; width: 100%;">
<%-- <div id="aa" style="font-size:50px;">123</div>
 <script type="text/javascript">
     document.getElementById("aa").innerHTML = window.screen.width + "*" + window.screen.height;
 </script>--%>
    <form id="form1" runat="server" class="linear"  style="font-family: 微软雅黑;border:0 solid #333333;">  
    <asp:ScriptManager ID="ScriptManager1" runat="server">            
    </asp:ScriptManager>
          
        <table style="width: 100%; height:100%">
            
            <tr style="width: 100%;height:5%">
           <td colspan="3">

           <table style="width: 100%;height:100%;">
           <tr>
           <td style="width:20%;">
               <img alt="" src="BHS.jpg" width="100px"/></td>
                <td  align="center" style="font-size:30px;font-weight:bolder;width:60%; color:White" >BHS智能立体库仓储看板</td>  
            <td style="width:20%;"> 
               <img alt="" align="right" src="logo.png" width="200px"/></td>             
               </td>        
            </td>
           </tr>
            </table>
            </td>
            </tr>
                     
            <tr style="width: 100%;height:95%">
 
                <td id="Div11" style="border:0 solid #333333;width: 50%; height:90%" valign="bottom" colspan="2">

               <asp:UpdatePanel style="height:100%" ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
               <ContentTemplate>
                <asp:Timer ID="Timer1" runat="server" Interval="5000" ontick="Timer_Tick" 
                          Enabled="true" >
                </asp:Timer>  
               <table id="tb_example"  align="center" style="width: 100%; height:5%;color:White; font-size:25px;background-color:rgba(0,93,170,0.8) ;border-top-left-radius:15px; border-top-right-radius:15px;">
            
           <asp:Repeater ID="ratTable"  runat="server">
               <ItemTemplate>           
                   <tr>
                        <td align="center" style="width: 100%; height:5%;color:White; font-size:25px;background-color:rgba(0,93,170,0.8) ;border-top-left-radius:15px; border-top-right-radius:15px;"><%#Eval("SHOW_CODE")%> </td>                   
                   </tr>                                            
               </ItemTemplate>
           </asp:Repeater>
        </table>  

                <div style="width: 100%;height:45%;background-color:rgba(5,113,165,0.4);border-bottom-left-radius:15px; border-bottom-right-radius:15px;">
                            
                <table style="width: 100%;height:100%;" border="0">
                        <tr style="width: 100%;height:5%">       
                                      
                        <td style="border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White ">入库任务</td>                                         
                        </tr>
                        <tr  style=" font-size:17px;width:100%;height:5%;">
                        <td colspan="3">
                        <table style="width: 100%;height:100%;">
                        <tr>                    
                        <td align="center" style="width: 25%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White">托盘条码

                        <td  align="center" style="width: 20%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >起始位置
                        </td>
                        <td  align="center" style="width: 20%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >终止位置
                        </td>
                        <td  align="center" style="width: 30%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >备注
                        </td>
                        </tr>
                        </table>
                        </td>
                      
                        </tr>
                        <tr style="width: 100%;height:90%">
                        <td><div style="width: 100%;height:90%">
                         <div id="roll">
                            <ul>   
                                <asp:GridView ID="managelist" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false"
                                 AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
    
                                  <Columns>
                                    <asp:BoundField>
                                      <ItemStyle HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:BoundField> 
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                      <ItemStyle Width="25%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("START_DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("END_DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("ERROR_TEXT")%></ItemTemplate>
                                      <ItemStyle Width="30%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField> 
                                  </Columns>
                                    <HeaderStyle Height="0px" />
                                </asp:GridView>
                             </ul>

                            <ul>   
                                <asp:GridView ID="managelist1" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false"
                                 AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
    
                                  <Columns>
                                    <asp:BoundField>
                                      <ItemStyle HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:BoundField> 
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                      <ItemStyle Width="25%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("START_DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("END_DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("ERROR_TEXT")%></ItemTemplate>
                                      <ItemStyle Width="30%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField> 
                                  </Columns>
                                    <HeaderStyle Height="0px" />
                                </asp:GridView>
                             </ul>
   
                            <script type="text/javascript">
                                  init_scrolltext();
                                  function init_scrolltext() {
                                      var demo = document.getElementById("roll");
                                      demo.scrollTop = 0;
                                      setInterval('scrollup()', 50);
                                  }
                                  function scrollup() {
                                      var demo = document.getElementById("roll");
                                      var demo2 = document.getElementById("managelist1");
                                      var demo1 = document.getElementById("managelist");
                                      if (demo1 != null && demo2 != null) {
                                          if (demo2.offsetTop - demo.scrollTop <= 0)
                                              demo.scrollTop -= demo1.offsetHeight;
                                          else {
                                              var t = demo.scrollTop;
                                              demo.scrollTop++;
                                              if (t == demo.scrollTop) {
                                                  if (demo.scrollTop >= demo1.offsetHeight) {
                                                      demo.scrollTop -= demo1.offsetHeight;
                                                  }
                                                  else {
                                                      demo.scrollTop = 0;
                                                  }
                                              }
                                          }
                                      }
                                  }</script>
                         </div>
                        </div></td>
                        </tr>
                </table>               
                </div>      
                    
                    
                <div style="width: 100%;height:50%;background-color:rgba(5,113,165,0.4);border-bottom-left-radius:15px; border-bottom-right-radius:15px;">
                            
                <table style="width: 100%;height:100%;" border="0">
                        <tr style="width: 100%;height:5%">       
                                      
                        <td style="border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White ">异常任务</td>                                         
                        </tr>
                        <tr  style=" font-size:17px;width:100%;height:5%;">
                        <td colspan="3">
                        <table style="width: 100%;height:100%;">
                        <tr>                    
                        <td align="center" style="width: 20%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White">托盘条码

                        <td  align="center" style="width: 15%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >起始位置
                        </td>
                        <td  align="center" style="width: 15%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >时间
                        </td>
                        <td  align="center" style="width: 50%; color:White; border-bottom:#cccccc solid 1px; border-bottom-color:White; color:White" >备注
                        </td>
                        </tr>
                        </table>
                        </td>
                      
                        </tr>
                        <tr style="width: 100%;height:90%">
                        <td><div style="width: 100%;height:90%">
                         <div id="roll2">
                            <ul>   
                                <asp:GridView ID="control" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false"
                                 AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
    
                                  <Columns>
                                    <asp:BoundField>
                                      <ItemStyle HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:BoundField> 
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("CREATE_TIME")%></ItemTemplate>
                                      <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("MANAGE_ERROR_TEXT")%></ItemTemplate>
                                      <ItemStyle Width="50%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField> 
                                  </Columns>
                                    <HeaderStyle Height="0px" />
                                </asp:GridView>
                             </ul>

                            <ul>   
                                <asp:GridView ID="control2" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false"
                                 AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
    
                                  <Columns>
                                    <asp:BoundField>
                                      <ItemStyle HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:BoundField> 
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                      <ItemStyle Width="20%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>   
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("DEVICE_CODE")%></ItemTemplate>
                                      <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>  
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("CREATE_TIME")%></ItemTemplate>
                                      <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField>    
                                    <asp:TemplateField>
                                      <ItemTemplate><%#Eval("MANAGE_ERROR_TEXT")%></ItemTemplate>
                                      <ItemStyle Width="50%" HorizontalAlign="Center" ForeColor="White" BorderWidth="0" Font-Bold="true" Font-Size="16px" />
                                    </asp:TemplateField> 
                                  </Columns>
                                    <HeaderStyle Height="0px" />
                                </asp:GridView>
                             </ul>
   
                            <script type="text/javascript">
                                  init_scrolltext();
                                  function init_scrolltext() {
                                      var demo = document.getElementById("roll2");
                                      demo.scrollTop = 0;
                                      setInterval('scrollup()', 50);
                                  }
                                  function scrollup() {
                                      var demo = document.getElementById("roll2");
                                      var demo2 = document.getElementById("control2");
                                      var demo1 = document.getElementById("control");
                                      if (demo1 != null && demo2 != null) {
                                          if (demo2.offsetTop - demo.scrollTop <= 0)
                                              demo.scrollTop -= demo1.offsetHeight;
                                          else {
                                              var t = demo.scrollTop;
                                              demo.scrollTop++;
                                              if (t == demo.scrollTop) {
                                                  if (demo.scrollTop >= demo1.offsetHeight) {
                                                      demo.scrollTop -= demo1.offsetHeight;
                                                  }
                                                  else {
                                                      demo.scrollTop = 0;
                                                  }
                                              }
                                          }
                                      }
                                  }</script>
                         </div>
                        </div></td>
                        </tr>
                </table>               
                </div> 
                </ContentTemplate>
                </asp:UpdatePanel>
                    
                </td>

            </tr>                
                
        </table>
           
    </form>
</body>
</html>
