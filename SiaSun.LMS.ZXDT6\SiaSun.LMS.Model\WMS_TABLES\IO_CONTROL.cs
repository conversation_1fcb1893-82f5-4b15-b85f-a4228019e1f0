﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// IO_CONTROL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class IO_CONTROL
	{
		public IO_CONTROL()
		{
			
		}
		
		private int _control_id;
		private int _relative_control_id;
		private int _manage_id;
		private string _cell_group;
		private string _stock_barcode;
		private int _pre_control_status;
		private int _control_task_type;
		private string _control_task_level;
		private string _start_warehouse_code;
		private string _end_warehouse_code;
		private string _start_device_code;
		private string _end_device_code;
		private int _control_status;
		private string _error_text;
		private string _control_begin_time;
		private string _control_end_time;
		private string _control_remark;

		private string _pallet_size;
		private string _control_batch;
		private string _device_code;
		private string _manage_task_type;
		private string _multiplex_flag;
		private string _stack_type;
		private string _stack_qty;

		private string _start_device_code2;
		private string _end_device_code2;
		private int _agv_no;


		///<sumary>
		/// 控制编号
		///</sumary>
		[DataMember]
		public int CONTROL_ID
		{
			get{return _control_id;}
			set{_control_id = value;}
		}
		///<sumary>
		/// 关联控制编号
        ///</sumary>
        [DataMember]
		public int RELATIVE_CONTROL_ID
		{
			get{return _relative_control_id;}
			set{_relative_control_id = value;}
		}
		///<sumary>
		/// 任务编号
        ///</sumary>
        [DataMember]
		public int MANAGE_ID
		{
			get{return _manage_id;}
			set{_manage_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_GROUP
		{
			get{return _cell_group;}
			set{_cell_group = value;}
		}
		///<sumary>
		/// 托盘条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}
		///<sumary>
		/// 管理任务类型
        ///</sumary>
        [DataMember]
		public int PRE_CONTROL_STATUS
		{
			get{return _pre_control_status;}
			set{_pre_control_status = value;}
		}
		///<sumary>
		/// 控制类型
        ///</sumary>
        [DataMember]
		public int CONTROL_TASK_TYPE
		{
			get{return _control_task_type;}
			set{_control_task_type = value;}
		}
		///<sumary>
		/// 优先级
        ///</sumary>
        [DataMember]
		public string CONTROL_TASK_LEVEL
		{
			get{return _control_task_level;}
			set{_control_task_level = value;}
		}
		///<sumary>
		/// 起始仓库
        ///</sumary>
        [DataMember]
		public string START_WAREHOUSE_CODE
		{
			get{return _start_warehouse_code;}
			set{_start_warehouse_code = value;}
		}
		///<sumary>
		/// 终止仓库
        ///</sumary>
        [DataMember]
		public string END_WAREHOUSE_CODE
		{
			get{return _end_warehouse_code;}
			set{_end_warehouse_code = value;}
		}
		///<sumary>
		/// 起始设备
        ///</sumary>
        [DataMember]
		public string START_DEVICE_CODE
		{
			get{return _start_device_code;}
			set{_start_device_code = value;}
		}
		///<sumary>
		/// 终止设备
        ///</sumary>
        [DataMember]
		public string END_DEVICE_CODE
		{
			get{return _end_device_code;}
			set{_end_device_code = value;}
		}
		///<sumary>
		/// 状态
        ///</sumary>
        [DataMember]
		public int CONTROL_STATUS
		{
			get{return _control_status;}
			set{_control_status = value;}
		}
		///<sumary>
		/// 错误描述
        ///</sumary>
        [DataMember]
		public string ERROR_TEXT
		{
			get{return _error_text;}
			set{_error_text = value;}
		}
		///<sumary>
		/// 开始时间
        ///</sumary>
        [DataMember]
		public string CONTROL_BEGIN_TIME
		{
			get{return _control_begin_time;}
			set{_control_begin_time = value;}
		}
		///<sumary>
		/// 结束时间
        ///</sumary>
        [DataMember]
		public string CONTROL_END_TIME
		{
			get{return _control_end_time;}
			set{_control_end_time = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string CONTROL_REMARK
		{
			get{return _control_remark;}
			set{_control_remark = value;}
		}

		///<sumary>
		/// 子托盘尺寸
		///</sumary>
		public string PALLET_SIZE
		{
			get { return _pallet_size; }
			set { _pallet_size = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		public string CONTROL_BATCH
		{
			get { return _control_batch; }
			set { _control_batch = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		public string DEVICE_CODE
		{
			get { return _device_code; }
			set { _device_code = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		public string MANAGE_TASK_TYPE
		{
			get { return _manage_task_type; }
			set { _manage_task_type = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		public string MULTIPLEX_FLAG
		{
			get { return _multiplex_flag; }
			set { _multiplex_flag = value; }
		}
		///<sumary>
		/// 素电垛型
		///</sumary>
		public string STACK_TYPE
		{
			get { return _stack_type; }
			set { _stack_type = value; }
		}
		///<sumary>
		/// 素电板盘数量
		///</sumary>
		public string STACK_QTY
		{
			get { return _stack_qty; }
			set { _stack_qty = value; }
		}

		///<sumary>
		/// 起始设备
		///</sumary>
		[DataMember]
		public string START_DEVICE_CODE2
		{
			get { return _start_device_code2; }
			set { _start_device_code2 = value; }
		}
		///<sumary>
		/// 终止设备
		///</sumary>
		[DataMember]
		public string END_DEVICE_CODE2
		{
			get { return _end_device_code2; }
			set { _end_device_code2 = value; }
		}
		///<sumary>
		/// AGV车号
		///</sumary>
		[DataMember]
		public int AGV_NO
		{
			get { return _agv_no; }
			set { _agv_no = value; }
		}
	}
}
