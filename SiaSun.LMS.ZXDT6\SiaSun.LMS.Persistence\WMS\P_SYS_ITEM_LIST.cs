﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_ITEM_LIST
	/// </summary>
	public class P_SYS_ITEM_LIST : P_Base_House
	{
		public P_SYS_ITEM_LIST ()
		{
			//
			// TODO: 此处添加SYS_ITEM_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_ITEM_LIST> GetList()
		{
			return ExecuteQueryForList<SYS_ITEM_LIST>("SYS_ITEM_LIST_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_ITEM_LIST> GetListItemCode(string ITEM_CODE)
        {
            return ExecuteQueryForList<SYS_ITEM_LIST>("SYS_ITEM_LIST_SELECT_BY_ITEM_CODE", ITEM_CODE);
        }


		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_ITEM_LIST sys_item_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_ITEM_LIST");
                sys_item_list.ITEM_LIST_ID = id;
            }

            return ExecuteInsert("SYS_ITEM_LIST_INSERT",sys_item_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_ITEM_LIST sys_item_list)
		{
			return ExecuteUpdate("SYS_ITEM_LIST_UPDATE",sys_item_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_ITEM_LIST GetModel(System.Int32 ITEM_LIST_ID)
		{
			return ExecuteQueryForObject<SYS_ITEM_LIST>("SYS_ITEM_LIST_SELECT_BY_ID",ITEM_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 ITEM_LIST_ID)
		{
			return ExecuteDelete("SYS_ITEM_LIST_DELETE",ITEM_LIST_ID);
		}
		

	}
}
