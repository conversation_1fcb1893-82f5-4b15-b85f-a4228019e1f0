﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Web.UI.WebControls;

public partial class ManageOut : SignInPage
{
    protected override void PageLoad()
    {
        bRefresh_Click(null, null);
    }

    protected void tbStockBarcode_Enter(object sender, EventArgs e)
    {
        try
        {
            string sStockBarcode = this.tbStockBarcode.Text.Trim();
            if (string.IsNullOrWhiteSpace(sStockBarcode))
            {
                this.tbStockBarcode.Text = string.Empty;
                return;
            }
            DataTable dtStorage = Common._I_BaseService.GetList(string.Format(
                "select CELL_ID,STORAGE_LIST_ID,GOODS_ID,GOODS_CODE,GOODS_NAME,STORAGE_LIST_QUANTITY from V_STORAGE_LIST where CELL_TYPE='Station' and STOCK_BARCODE='{0}' order by GOODS_CODE",
                sStockBarcode), "HouseMap");
            if (dtStorage.Rows.Count == 0)
            {
                this.tbStockBarcode.Text = string.Empty;
                Common.LayerFailed(this, string.Format("未找到托盘条码 {0}", sStockBarcode));
                return;
            }
            DataTable dt = Common.NewDataTable(7);
            foreach (DataRow dr in dtStorage.Rows)
            {
                Common.AddDataTable(dt, "0.00", dr["STORAGE_LIST_ID"].ToString(),
                    dr["CELL_ID"].ToString(), dr["GOODS_ID"].ToString(), dr["GOODS_CODE"].ToString(),
                    dr["GOODS_NAME"].ToString(), dr["STORAGE_LIST_QUANTITY"].ToString());
            }
            this.gvOutStorage.DataSource = dt.DefaultView;
            this.gvOutStorage.DataBind();
        }
        catch (WebException ex)
        {
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch (Exception ex)
        {
            bRefresh_Click(null, null);
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bSave_Click(object sender, EventArgs e)
    {
        try
        {
            string sStockBarcode = this.tbStockBarcode.Text.Trim();
            if (string.IsNullOrWhiteSpace(sStockBarcode))
            {
                this.tbStockBarcode.Text = string.Empty;
                Common.LayerFailed(this, "请输入托盘条码");
                return;
            }
            ManageService.MANAGE_MAIN mMANAGE_MAIN = new ManageService.MANAGE_MAIN();
            mMANAGE_MAIN.MANAGE_TYPE_CODE = "ManageOut";
            mMANAGE_MAIN.STOCK_BARCODE = sStockBarcode;
            mMANAGE_MAIN.CELL_MODEL = string.Empty;
            mMANAGE_MAIN.MANAGE_OPERATOR = SessionName;
            mMANAGE_MAIN.MANAGE_BEGIN_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            mMANAGE_MAIN.MANAGE_STATUS = "WaitingSend";
            mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;
            mMANAGE_MAIN.MANAGE_REMARK = string.Empty;
            List<ManageService.MANAGE_LIST> listMANAGE_LIST = new List<ManageService.MANAGE_LIST>();
            foreach (GridViewRow gvr in this.gvOutStorage.Rows)
            {
                TextBox tb = gvr.FindControl("value1") as TextBox;
                decimal dManageListQuantity = Convert.ToDecimal(tb.Text);
                decimal dStorageListQuantity = Convert.ToDecimal((gvr.FindControl("value7") as Label).Text);
                if (dManageListQuantity > dStorageListQuantity)
                {
                    Common.LayerFailed(this, string.Format("物资 {0} 出库数量不能大于库存数量", (gvr.FindControl("value5") as Label).Text));
                    return;
                }
                ManageService.MANAGE_LIST ml = new ManageService.MANAGE_LIST();
                ml.STORAGE_LIST_ID = Convert.ToInt32((gvr.FindControl("value2") as Label).Text);
                ml.GOODS_ID = Convert.ToInt32((gvr.FindControl("value4") as Label).Text);
                ml.MANAGE_LIST_QUANTITY = Convert.ToDecimal(tb.Text);
                if (ml.MANAGE_LIST_QUANTITY > 0)
                {
                    listMANAGE_LIST.Add(ml);
                    mMANAGE_MAIN.START_CELL_ID = Convert.ToInt32((gvr.FindControl("value3") as Label).Text);
                    mMANAGE_MAIN.END_CELL_ID = mMANAGE_MAIN.START_CELL_ID;
                }
            }
            if (listMANAGE_LIST.Count == 0)
            {
                Common.LayerFailed(this, "请输入出库数量");
                return;
            }
            string sResult = string.Empty;
            if (Common._I_ManageService.ManageOutAndroid(out sResult, mMANAGE_MAIN, listMANAGE_LIST.ToArray()))
            {
                Common.LayerSuccess(this, string.Format("出库成功 托盘条码 {0}", sStockBarcode));
                bRefresh_Click(null, null);
            }
            else
            {
                Common.LayerFailed(this, sResult);
            }
        }
        catch (WebException ex)
        {
            Common.LayerFailed(this, string.Format("网络连接失败\n{0}", ex.Message));
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }

    protected void bRefresh_Click(object sender, EventArgs e)
    {
        try
        {
            this.tbStockBarcode.Text = string.Empty;
            DataTable dt = Common.NewDataTable(7);
            this.gvOutStorage.DataSource = dt.DefaultView;
            this.gvOutStorage.DataBind();
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }
}
