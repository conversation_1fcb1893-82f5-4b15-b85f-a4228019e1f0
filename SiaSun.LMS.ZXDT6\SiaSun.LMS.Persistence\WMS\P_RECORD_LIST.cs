﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// RECORD_LIST
	/// </summary>
	public class P_RECORD_LIST : P_Base_House
	{
		public P_RECORD_LIST ()
		{
			//
			// TODO: 此处添加RECORD_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<RECORD_LIST> GetList()
		{
			return ExecuteQueryForList<RECORD_LIST>("RECORD_LIST_SELECT",null);
		}

		
		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<RECORD_LIST> GetListByPlanListId(int PLAN_LIST_ID)
		{
			return ExecuteQueryForList<RECORD_LIST>("RECORD_LIST_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(RECORD_LIST record_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("RECORD_LIST");
                record_list.RECORD_LIST_ID = id;
            }

            return ExecuteInsert("RECORD_LIST_INSERT",record_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(RECORD_LIST record_list)
		{
			return ExecuteUpdate("RECORD_LIST_UPDATE",record_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public RECORD_LIST GetModel(System.Int32 RECORD_LIST_ID)
		{
			return ExecuteQueryForObject<RECORD_LIST>("RECORD_LIST_SELECT_BY_ID",RECORD_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 RECORD_LIST_ID)
		{
			return ExecuteDelete("RECORD_LIST_DELETE",RECORD_LIST_ID);
		}
		

	}
}
