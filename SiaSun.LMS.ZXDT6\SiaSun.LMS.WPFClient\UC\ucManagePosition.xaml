﻿<UserControl
    x:Class="SiaSun.LMS.WPFClient.UC.ucManagePosition"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="211"
    d:DesignWidth="1000"
    mc:Ignorable="d">
    <Border>
        <WrapPanel ButtonBase.Click="WrapPanel_Click">

            <StackPanel
                Name="panelStockBarcode"
                Margin="5,5,5,5"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="托盘条码:" />
                <TextBox
                    Name="txtStockBarcode"
                    MinWidth="120"
                    MinHeight="23"
                    Margin="2,0,2,0"
                    KeyDown="StockBarcode_KeyDown" />
            </StackPanel>
            <StackPanel
                Name="panelCellModel"
                Margin="5,5,5,5"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="高低货位:" />
                <ComboBox
                    Name="cmbCellModel"
                    MinWidth="120"
                    MinHeight="23"
                    Margin="2,0,2,0"
                    SelectionChanged="cmbCellModel_SelectionChanged" />
            </StackPanel>

            <StackPanel
                Name="panelOccupyPercent"
                Margin="5,5,5,5"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="利用率:" />
                <ComboBox
                    Name="cmbOccupyPercent"
                    MinWidth="120"
                    MinHeight="23"
                    Margin="2,0,2,0"
                    SelectionChanged="cmbOccupyPercent_SelectionChanged" />
            </StackPanel>

            <StackPanel
                Name="panelStartPosition"
                Margin="{Binding ElementName=panelCellModel, Path=Margin}"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="起始位置:" />
                <ComboBox
                    Name="cmbStartPosition"
                    MinWidth="120"
                    Margin="2,0,0,0"
                    AllowDrop="True"
                    IsEditable="True"
                    IsTextSearchEnabled="True"
                    SelectionChanged="cmbStartPosition_SelectionChanged"
                    StaysOpenOnEdit="True" />
                <Button
                    Name="btnStartPosition"
                    Width="23"
                    Height="21"
                    Margin="1">
                    <!--<Button.Background>
                            <ImageBrush ImageSource="/@Images/cell.png">
                            </ImageBrush>
                        </Button.Background>-->
                </Button>
            </StackPanel>

            <StackPanel
                Name="panelEndPosition"
                Margin="{Binding ElementName=panelCellModel, Path=Margin}"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="目标位置:" />

                <ComboBox
                    Name="cmbEndPosition"
                    MinWidth="120"
                    Margin="2,0,0,1"
                    DisplayMemberPath="CELL_NAME"
                    IsEditable="True"
                    IsTextSearchEnabled="True"
                    SelectedValuePath="CELL_ID"
                    SelectionChanged="cmbEndPosition_SelectionChanged" />
                <Button
                    Name="btnEndPosition"
                    Width="23"
                    Height="21"
                    Margin="1"
                    Background="{Binding ElementName=btnStartPosition, Path=Background}" />
            </StackPanel>

        </WrapPanel>
    </Border>

</UserControl>
