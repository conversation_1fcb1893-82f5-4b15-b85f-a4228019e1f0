﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_PROPERTY 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_PROPERTY
	{
		public GOODS_PROPERTY()
		{
			
		}
		
		private int _goods_property_id;
		private int _goods_type_id;
		private string _goods_property_code;
		private string _goods_property_name;
		private string _goods_property_datasource;
		private string _goods_property_field;
		private string _goods_property_fieldtype;
		private int _goods_property_order;
		private string _goods_property_valid;
		private string _goods_property_flag;
        private string _goods_property_keyflag;

		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;


		///<sumary>
		/// 
		///</sumary>
		///
		[DataMember]
        public int GOODS_PROPERTY_ID
		{
			get{return _goods_property_id;}
			set{_goods_property_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int GOODS_TYPE_ID
		{
			get{return _goods_type_id;}
			set{_goods_type_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_CODE
		{
			get{return _goods_property_code;}
			set{_goods_property_code = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_NAME
		{
			get{return _goods_property_name;}
			set{_goods_property_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_DATASOURCE
		{
			get{return _goods_property_datasource;}
			set{_goods_property_datasource = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_FIELD
		{
			get{return _goods_property_field;}
			set{_goods_property_field = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_FIELDTYPE
		{
			get{return _goods_property_fieldtype;}
			set{_goods_property_fieldtype = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int GOODS_PROPERTY_ORDER
		{
			get{return _goods_property_order;}
			set{_goods_property_order = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_VALID
		{
			get{return _goods_property_valid;}
			set{_goods_property_valid = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY_FLAG
		{
			get{return _goods_property_flag;}
			set{_goods_property_flag = value;}
		}


        [DataMember]
        public string GOODS_PROPERTY_KEYFLAG
        {
            get { return _goods_property_keyflag; }
            set { _goods_property_keyflag = value; }
        }

		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
