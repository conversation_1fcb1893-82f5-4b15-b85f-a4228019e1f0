﻿/***************************************************************************
 * 
 *       功能：     任务列表实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// MANAGE_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MANAGE_LIST
	{
		public MANAGE_LIST()
		{
			
		}
		
		private int _manage_list_id;
		private int _storage_list_id;
		private int _plan_list_id;
		private int _manage_id;
		private int _goods_id;
		private System.Decimal _manage_list_quantity;
		private string _manage_list_remark;
		private string _box_barcode;
		private string _goods_property1;
		private string _goods_property2;
		private string _goods_property3;
		private string _goods_property4;
		private string _goods_property5;
		private string _goods_property6;
		private string _goods_property7;
		private string _goods_property8;
        private int _storage_lock_id;
        private string _detail_flag;
        private string _goods_property9;
        private string _goods_property10;
        private string _goods_property11;
        private string _goods_property12;
        private string _goods_property13;
        private string _goods_property14;
        private string _goods_property15;
        private string _goods_property16;
        private string _goods_property17;
        private string _goods_property18;
        private string _goods_property19;
        private string _goods_property20;
        private string _goods_property21;
        private string _goods_property22;
        private string _goods_property23;
        private string _goods_property24;
        private string _goods_property25;
        private string _goods_property26;
        private string _goods_property27;
        private string _goods_property28;
        private string _goods_property29;
        private string _goods_property30;

        private int _storage_list_id_split_source;
        private string _backup_field1;
        private string _backup_field2;
        private string _backup_field3;
        private string _backup_field4;
        private string _backup_field5;

        ///<sumary>
        /// 任务列表编号
        ///</sumary>
        [DataMember]
		public int MANAGE_LIST_ID
		{
			get{return _manage_list_id;}
			set{_manage_list_id = value;}
		}
		///<sumary>
		/// 库存列表编号
        ///</sumary>
        [DataMember]
		public int STORAGE_LIST_ID
		{
			get{return _storage_list_id;}
			set{_storage_list_id = value;}
		}
		///<sumary>
		/// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 任务编号
        ///</sumary>
        [DataMember]
		public int MANAGE_ID
		{
			get{return _manage_id;}
			set{_manage_id = value;}
		}
		///<sumary>
		/// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}

		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal MANAGE_LIST_QUANTITY
		{
			get{return _manage_list_quantity;}
			set{_manage_list_quantity = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MANAGE_LIST_REMARK
		{
			get{return _manage_list_remark;}
			set{_manage_list_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY1
		{
			get{return _goods_property1;}
			set{_goods_property1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY2
		{
			get{return _goods_property2;}
			set{_goods_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY3
		{
			get{return _goods_property3;}
			set{_goods_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY4
		{
			get{return _goods_property4;}
			set{_goods_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY5
		{
			get{return _goods_property5;}
			set{_goods_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY6
		{
			get{return _goods_property6;}
			set{_goods_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY7
		{
			get{return _goods_property7;}
			set{_goods_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY8
		{
			get{return _goods_property8;}
			set{_goods_property8 = value;}
		}

        ///<sumary>
		/// 
        ///</sumary>
        [DataMember]
        public int STORAGE_LOCK_ID
        {
            get { return _storage_lock_id; }
            set { _storage_lock_id = value; }
        }

        ///<sumary>
		/// 
        ///</sumary>
        [DataMember]
        public string DETAIL_FLAG
        {
            get { return _detail_flag; }
            set { _detail_flag = value; }
        }

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY9
        {
            get { return _goods_property9; }
            set { _goods_property9 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY10
        {
            get { return _goods_property10; }
            set { _goods_property10 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY11
        {
            get { return _goods_property11; }
            set { _goods_property11 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY12
        {
            get { return _goods_property12; }
            set { _goods_property12 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY13
        {
            get { return _goods_property13; }
            set { _goods_property13 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY14
        {
            get { return _goods_property14; }
            set { _goods_property14 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY15
        {
            get { return _goods_property15; }
            set { _goods_property15 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY16
        {
            get { return _goods_property16; }
            set { _goods_property16 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY17
        {
            get { return _goods_property17; }
            set { _goods_property17 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY18
        {
            get { return _goods_property18; }
            set { _goods_property18 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY19
        {
            get { return _goods_property19; }
            set { _goods_property19 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY20
        {
            get { return _goods_property20; }
            set { _goods_property20 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY21
        {
            get { return _goods_property21; }
            set { _goods_property21 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY22
        {
            get { return _goods_property22; }
            set { _goods_property22 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY23
        {
            get { return _goods_property23; }
            set { _goods_property23 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY24
        {
            get { return _goods_property24; }
            set { _goods_property24 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY25
        {
            get { return _goods_property25; }
            set { _goods_property25 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY26
        {
            get { return _goods_property26; }
            set { _goods_property26 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY27
        {
            get { return _goods_property27; }
            set { _goods_property27 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY28
        {
            get { return _goods_property28; }
            set { _goods_property28 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY29
        {
            get { return _goods_property29; }
            set { _goods_property29 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY30
        {
            get { return _goods_property30; }
            set { _goods_property30 = value; }
        }

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int STORAGE_LIST_ID_SPLIT_SOURCE
        {
            get { return _storage_list_id_split_source; }
            set { _storage_list_id_split_source = value; }
        }
        ///<sumary>
		/// 
		///</sumary>
		[DataMember]
        public string BACKUP_FIELD1
        {
            get { return _backup_field1; }
            set { _backup_field1 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BACKUP_FIELD2
        {
            get { return _backup_field2; }
            set { _backup_field2 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BACKUP_FIELD3
        {
            get { return _backup_field3; }
            set { _backup_field3 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BACKUP_FIELD4
        {
            get { return _backup_field4; }
            set { _backup_field4 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BACKUP_FIELD5
        {
            get { return _backup_field5; }
            set { _backup_field5 = value; }
        }
    }
}
