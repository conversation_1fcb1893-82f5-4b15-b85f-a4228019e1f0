﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Drawing.Drawing2D;
using System.IO;
using System.Runtime.InteropServices;

namespace SiaSun.LMS.Common
{
    class LED_API
    {
        [DllImport("BX_IV.dll")]
        public static extern int AddScreen(int nControlType, int nScreenNo,
        int nWidth, int nHeight, int nScreenType, int nPixelMode, int nDataDA,
           int nDataOE, int nRowOrder, int nFreqPar, string pCom, int nBaud,
           string pSocketIP, int nSocketPor, string pScreenStatusFile); //添加屏显

        [DllImport("BX_IV.dll")]
        public static extern int DeleteScreen(int nScreenNo);//删除屏显

        [DllImport("BX_IV.dll")]
        public static extern int SendScreenInfo(int nScreenNo, int nSendMode, int nSendCmd, int nOtherParam1);//发送相应命令到显示屏。 

        [DllImport("BX_IV.dll")]
        public static extern int AddScreenProgram(int nScreenNo, int nProgramType, int nPlayLength,
            int nStartYear, int nStartMonth, int nStartDay, int nEndYear, int nEndMonth, int nEndDay,
            int nMonPlay, int nTuesPlay, int nWedPlay, int nThursPlay, int bFriPlay, int nSatPlay, int nSunPlay,
            int nStartHour, int nStartMinute, int nEndHour, int nEndMinute); //向指定显示屏添加节目； 

        [DllImport("BX_IV.dll")]
        public static extern int DeleteScreenProgram(int nScreenNo, int nProgramOrd); //删除节目

        [DllImport("BX_IV.dll")]
        public static extern int DeleteScreenProgramArea(int nScreenNo, int nProgramOrd, int nAreaOrd);//删除区域

        [DllImport("BX_IV.dll")]
        public static extern int AddScreenProgramBmpTextArea(int nScreenNo, int nProgramOrd, int nX, int nY,
            int nWidth, int nHeight);//向指定显示屏指定节目添加图文区；

        [DllImport("BX_IV.dll")]
        public static extern int AddScreenProgramAreaBmpTextFile(int nScreenNo, int nProgramOrd, int nAreaOrd,
        string pFileName, int nShowSingle, string pFontName, int nFontSize, int nBold, int nFontColor,
            int nStunt, int nRunSpeed, int nShowTime); //向指定显示屏指定节目指定区域添加文件

        [DllImport("BX_IV.dll")]
        public static extern int DeleteScreenProgramAreaBmpTextFile(int nScreenNo, int nProgramOrd, int nAreaOrd, int nFileOrd); //删除指定显示屏指定节目指定图文区域的指定文件，删除文件成功后会将该文件信息删除

        [DllImport("BX_IV.dll")]
        public static extern int InitDLLResource(IntPtr AppHandle);

        [DllImport("BX_IV.dll")]
        public static extern int FreeDLLResource();

        public const int RETURN_ERROR_AERETYPE = 0xF7;//区域类型错误，在添加、删除图文区域文件时区域类型出错返回此类型错误。 
        public const int RETURN_ERROR_RA_SCREENNO = 0xF8;  //已经有该显示屏信息。如要重新设定请先DeleteScreen删除该显示屏再添加； 
        public const int RETURN_ERROR_NOFIND_AREAFILE = 0xF9; //没有找到有效的区域文件(图文区域)； 
        public const int RETURN_ERROR_NOFIND_AREA = 0xFA;  //没有找到有效的显示区域；可以使用AddScreenProgramBmpTextArea添加区域信息。 
        public const int RETURN_ERROR_NOFIND_PROGRAM = 0xFB;  //没有找到有效的显示屏节目；可以使用AddScreenProgram函数添加指定节目 
        public const int RETURN_ERROR_NOFIND_SCREENNO = 0xFC;  //系统内没有查找到该显示屏；可以使用AddScreen函数添加显示屏 
        public const int RETURN_ERROR_NOW_SENDING = 0xFD; //系统内正在向该显示屏通讯，请稍后再通讯；
        public const int RETURN_ERROR_OTHER = 0xFF; //其它错误； 
        public const int RETURN_NOERROR = 0; //没有错误 


        //------------------------------------------------------------------------------
        // 控制器类型
        public const int CONTROLLER_TYPE_3T = 0x10;
        public const int CONTROLLER_TYPE_3A = 0x20;
        public const int CONTROLLER_TYPE_3A1 = 0x21;
        public const int CONTROLLER_TYPE_3A2 = 0x22;
        public const int CONTROLLER_TYPE_3M = 0x30;

        public const int CONTROLLER_TYPE_4A1 = 0x0141;
        public const int CONTROLLER_TYPE_4A2 = 0x0241;
        public const int CONTROLLER_TYPE_4A3 = 0x0341;
        public const int CONTROLLER_TYPE_4AQ = 0x1041;
        public const int CONTROLLER_TYPE_4A = 0x0041;

        public const int CONTROLLER_TYPE_4M1 = 0x0142;
        public const int CONTROLLER_TYPE_4M = 0x0042;
        public const int CONTROLLER_TYPE_4MC = 0x0C42;
        public const int CONTROLLER_TYPE_4E = 0x0044;
        public const int CONTROLLER_TYPE_4C = 0x0043;
        public const int CONTROLLER_TYPE_4E1 = 0x0144;
        public const int CONTROLLER_TYPE_5M1 = 0x0052;
        public const int CONTROLLER_TYPE_5M2 = 0x0252;
        public const int CONTROLLER_TYPE_5M3 = 0x0352;
        public const int CONTROLLER_TYPE_5M4 = 0x0452;


        //------------------------------------------------------------------------------
        //==============================================================================
        // 控制器通讯模式
        public const int SEND_MODE_COMM = 0;
        public const int SEND_MODE_NET = 2;
        //==============================================================================

        //==============================================================================
        // 用户发送信息命令表
        public const int SEND_CMD_PARAMETER = 0xA1FF; //加载屏参数。
        public const int SEND_CMD_SCREENSCAN = 0xA1FE; //设置扫描方式。
        public const int SEND_CMD_SENDALLPROGRAM = 0xA1F0; //发送所有节目信息。
        public const int SEND_CMD_POWERON = 0xA2FF; //强制开机
        public const int SEND_CMD_POWEROFF = 0xA2FE; //强制关机
        public const int SEND_CMD_TIMERPOWERONOFF = 0xA2FD; //定时开关机
        public const int SEND_CMD_CANCEL_TIMERPOWERONOFF = 0xA2FC; //取消定时开关机
        public const int SEND_CMD_RESIVETIME = 0xA2FB; //校正时间。
        public const int SEND_CMD_ADJUSTLIGHT = 0xA2FA; //亮度调整。

        //==============================================================================
        //初始化设置
        public const int SCREEN_NO = 1;
        public const int SCREEN_TYPE = 1;
        public const int PIXEL_MODE = 1;
        public const int SCREEN_DATADA = 0;
        public const int SCREEN_DATAOE = 0;
        public const int SCREEN_ROWORDER = 0;
        public const int SCREEN_FREQPAR = 0;
        public const string SCREEN_COMM = "COM1";
        public const int SCREEN_BAUD = 57600;
        public const int SCREEN_SOCKETPORT = 5005;
        public const string SCREEN_STATUS_FILE = "C:\\ScreenStatus.ini";        
        
        //------------------------------------------------------------------------------
        // 显示特技
        public const int STUNT_SUIJIXIANSHI = 0x00;       //随机显示0
        public const int STUNT_JINGTAI = 0x01;         //静态1
        public const int STUNT_KUAISUDACHU = 0x02;       //快速打出2
        public const int STUNT_XIANGZUOYIDONG = 0x03;       //向左移动3
        public const int STUNT_XIANGZUOLIANYI = 0x04;       //向左连移4
        public const int STUNT_XIANGSHANGYIDONG = 0x05;       //向上移动5
        public const int STUNT_XIANGSHANGLIANYI = 0x06;       //向上连移6
        public const int STUNT_SHANSHUO = 0x07;   //闪烁7
        public const int STUNT_PIAOXUE = 0x08;    //飘雪8
        public const int STUNT_MAOPAO = 0x09;     //冒泡9
        public const int STUNT_ZHONGJIANYICHU = 0x0A;       //中间移出10
        public const int STUNT_ZUOYOUYIRU = 0x0B;       //左右移入11
        public const int STUNT_ZUOYOUJIAOCHAYIRU = 0x0C;       //左右交叉移入12
        public const int STUNT_SHANGXIAJIAOCHAYIRU = 0x0D;       //上下交叉移入13
        public const int STUNT_HUAJUANBIHE = 0x0E;       //画卷闭合14
        public const int STUNT_HUAJUANDAKAI = 0x0F;       //画卷打开15
        public const int STUNT_XIANGZUOLASHEN = 0x10;       //向左拉伸16
        public const int STUNT_XIANGYOULASHEN = 0x11;       //向右拉伸17
        public const int STUNT_XIANGSHANGLASHEN = 0x12;       //向上拉伸18
        public const int STUNT_XIANGXIALASHEN = 0x13;       //向下拉伸19
        public const int STUNT_XIANGZUOLEISHE = 0x14;       //向左镭射20
        public const int STUNT_XIANGYOULEISHE = 0x15;       //向右镭射21
        public const int STUNT_XSLEISHE = 0x16;       //向上镭射22
        public const int STUNT_XIANGXIALEISHE = 0x17;       //向下镭射23
        public const int STUNT_ZUOYOUJIAOCHALAMU = 0x18;       //左右交叉拉幕24
        public const int STUNT_SHANGXIAJIAOCHALAMU = 0x19;       //上下交叉拉幕25
        public const int STUNT_FENSANZUOLA = 0x1A;       //分散左拉26
        public const int STUNT_SHUIPINGBAIYE = 0x1B;       //水平百页27
        public const int STUNT_CHUIZHIBAIYE = 0x1C;       //垂直百页28
        public const int STUNT_XIANGZUOLAMU = 0x1D;       //向左拉幕29
        public const int STUNT_XIANGYOULAMU = 0x1E;       //向右拉幕30
        public const int STUNT_XIANGSHANGLAMU = 0x1F;       //向上拉幕31
        public const int STUNT_XIANGXIALAMU = 0x20;       //向下拉幕32
        public const int STUNT_ZUOYOUBIHE = 0x21;       //左右闭合33
        public const int STUNT_ZUOYOUDUIKAI = 0x22;       //左右对开34
        public const int STUNT_SHANGXIABIHE = 0x23;       //上下闭合35
        public const int STUNT_SHANGXIADUIKAI = 0x24;       //上下对开36
        public const int STUNT_XIANGYOUYIDONG = 0x25;       //向右移动37
        public const int STUNT_XIANGYOULIANYI = 0x26;       //向右连移38
        public const int STUNT_XIANGXIAYIDONG = 0x27;       //向下移动39
        public const int STUNT_XIANGXIALIANYI = 0x28;       //向下连移40

        //------------------------------------------------------------------------------
    }

    public class BX_5M
    {
        //显示通讯记录
        public bool GetErrorMessage(string FUNCTION_NAME, int RETURN_ERROR, out string sResult)
        {
            bool bResult = true;

            sResult = SiaSun.LMS.Common.StringUtil.GetCurDateTimeString() + "---执行函数：" + FUNCTION_NAME + "---返回结果：";

            if (RETURN_ERROR == LED_API.RETURN_NOERROR)
            {
                sResult += "函数执行成功/通讯成功";

                return bResult;
            }
            else
            {
                bResult = false;
            }

            switch (RETURN_ERROR)
            {
                case LED_API.RETURN_ERROR_AERETYPE:
                    sResult += "区域类型错误，在添加、删除图文区域文件时区域类型出错返回此类型错误";
                    break;
                case LED_API.RETURN_ERROR_RA_SCREENNO:
                    sResult += "已经有该显示屏信息，如要重新设定请先DeleteScreen删除该显示屏再添加";
                    break;
                case LED_API.RETURN_ERROR_NOFIND_AREAFILE:
                    sResult += "没有找到有效的区域文件(图文区域)";
                    break;
                case LED_API.RETURN_ERROR_NOFIND_AREA:
                    sResult += "没有找到有效的显示区域可以使用AddScreenProgramBmpTextArea添加区域信息";
                    break;
                case LED_API.RETURN_ERROR_NOFIND_PROGRAM:
                    sResult += "没有找到有效的显示屏节目可以使用AddScreenProgram函数添加指定节目";
                    break;
                case LED_API.RETURN_ERROR_NOFIND_SCREENNO:
                    sResult += "系统内没有查找到该显示屏可以使用AddScreen函数添加显示屏";
                    break;
                case LED_API.RETURN_ERROR_NOW_SENDING:
                    sResult += "系统内正在向该显示屏通讯，请稍后再通讯";
                    break;
                case LED_API.RETURN_ERROR_OTHER:
                    sResult += "其它错误";
                    break;
                case 0x01:
                case 0x02:
                case 0x03:
                case 0x04:
                case 0x05:
                case 0x06:
                case 0x07:
                case 0x08:
                case 0x09:
                case 0x0A:
                case 0x0B:
                case 0x0C:
                case 0x0D:
                case 0x0E:
                case 0x0F:
                case 0x10:
                case 0x11:
                case 0x12:
                case 0x13:
                case 0x14:
                case 0x15:
                case 0x16:
                case 0x17:
                case 0x18:
                case 0xFE:
                    sResult += "通讯错误";
                    break;
                default:
                    sResult += "未定义错误";
                    break;
            }

            return bResult;
        }

        //初始化动态库资源
        public int InitDLLResource(IntPtr AppHandle)
        {
            return LED_API.InitDLLResource(AppHandle);
        }

        //释放动态库资源
        public int FreeDLLResource()
        {
            return LED_API.FreeDLLResource();
        }

        //添加屏幕参数
        public int AddScreen(int CONTROL_TYPE, int SCREEN_WIDTH, int SCREEN_HEIGHT, string LED_IP)
        {
            int result = LED_API.AddScreen(CONTROL_TYPE, 
                                           LED_API.SCREEN_NO,
                                           SCREEN_WIDTH, 
                                           SCREEN_HEIGHT, 
                                           LED_API.SCREEN_TYPE, 
                                           LED_API.PIXEL_MODE,
                                           LED_API.SCREEN_DATADA,
                                           LED_API.SCREEN_DATAOE, 
                                           LED_API.SCREEN_ROWORDER, 
                                           LED_API.SCREEN_FREQPAR, 
                                           LED_API.SCREEN_COMM,
                                           LED_API.SCREEN_BAUD, 
                                           LED_IP,
                                           LED_API.SCREEN_SOCKETPORT, 
                                           LED_API.SCREEN_STATUS_FILE);
            return result;
        }

        //添加节目
        public int AddScreenProgram()
        {
            return LED_API.AddScreenProgram(LED_API.SCREEN_NO, 0, 0, 65535, 12, 3, 2011, 11, 26, 1, 1, 1, 1, 1, 1, 1, 8, 0, 18, 0);
        }

        //添加图文区
        public int AddScreenProgramBmpTextArea(int AREA_X, int AREA_Y, int AREA_WIDTH, int AREA_HEIGHT)
        {
            return LED_API.AddScreenProgramBmpTextArea(LED_API.SCREEN_NO,
                                                       0,         //ProgramOrd  
                                                       AREA_X, 
                                                       AREA_Y,
                                                       AREA_WIDTH, 
                                                       AREA_HEIGHT);
        }

        //添加文件到图文区
        //FILE_NAME需要写出根目录,如AppDomain.CurrentDomain.BaseDirectory+"@Files\192.168.100.31.txt"
        public int AddScreenProgramAreaBmpTextFile(int AREA_ORDER, string FILE_NAME, int FONT_SIZE, int SHOW_STUNT, int RUN_SPEED, int SHOW_TIME)
        {
            return LED_API.AddScreenProgramAreaBmpTextFile(LED_API.SCREEN_NO, 
                                                           0,        //ProgramOrd         
                                                           AREA_ORDER,
                                                           FILE_NAME,
                                                           1,        //nShowSingle
                                                           "宋体",
                                                           FONT_SIZE,
                                                           0,         //nBold
                                                           1,         //nFontColor 
                                                           SHOW_STUNT, 
                                                           RUN_SPEED, 
                                                           SHOW_TIME);
        }

        //发送数据
        public int SendScreenInfo()
        {
            return LED_API.SendScreenInfo(LED_API.SCREEN_NO, LED_API.SEND_MODE_NET, LED_API.SEND_CMD_SENDALLPROGRAM, 0);
        }

        //强制关机
        public int PowerOffScreen()
        {
            return LED_API.SendScreenInfo(LED_API.SCREEN_NO, LED_API.SEND_MODE_NET, LED_API.SEND_CMD_POWEROFF, 0);
        }

        //强制开机
        public int PowerOnScreen()
        {
            return LED_API.SendScreenInfo(LED_API.SCREEN_NO, LED_API.SEND_MODE_NET, LED_API.SEND_CMD_POWERON, 0);
        }

        //删除显示屏幕
        public int DeleteScreen()
        {
            return LED_API.DeleteScreen(LED_API.SCREEN_NO);
        }

    }

}
