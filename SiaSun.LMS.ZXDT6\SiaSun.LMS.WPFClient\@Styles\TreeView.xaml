<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type TreeView}">
        <Setter Property="Background" Value="{DynamicResource ControlBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TreeView}">
                    <Grid>
                        <Border
                            x:Name="Border"
                            Background="{DynamicResource ControlBackgroundBrush}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                            <ScrollViewer
                                Padding="4"
                                Background="{TemplateBinding Background}"
                                CanContentScroll="False"
                                Focusable="False"
                                HorizontalScrollBarVisibility="Auto"
                                VerticalScrollBarVisibility="Auto">
                                <ItemsPresenter />
                            </ScrollViewer>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="NuclearTreeViewItemToggleButton"
        d:IsControlPart="True"
        TargetType="{x:Type ToggleButton}">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <ColorAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                To="#FFFFCB66"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)"
                                To="#FFFFCB66"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                To="#FFFFCB66"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)"
                                To="#FFFFCB66"
                                Duration="0" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <ColorAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                To="#FF9EC6FB"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)"
                                To="#FF9EC6FB"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)"
                                To="#FFE56E17"
                                Duration="0" />
                            <ColorAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)"
                                To="#FFE56E17"
                                Duration="0" />
                        </Storyboard>
                        <Storyboard x:Key="CheckedOn">
                            <DoubleAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="0" />
                            <DoubleAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="Opacity"
                                To="1"
                                Duration="0" />
                        </Storyboard>
                        <Storyboard x:Key="CheckedOff">
                            <DoubleAnimation
                                Storyboard.TargetName="UncheckedVisual"
                                Storyboard.TargetProperty="Opacity"
                                To="1"
                                Duration="0" />
                            <DoubleAnimation
                                Storyboard.TargetName="CheckedVisual"
                                Storyboard.TargetProperty="Opacity"
                                To="0"
                                Duration="0" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid Margin="2,2,5,2" HorizontalAlignment="Right">
                        <Path
                            x:Name="UncheckedVisual"
                            Width="6"
                            Height="9"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Data="M 0,0 L 0,9 L 5,4.5 Z"
                            Fill="#FF9EC6FB"
                            Stroke="#FF9EC6FB"
                            StrokeLineJoin="Miter" />
                        <Path
                            x:Name="CheckedVisual"
                            Width="6"
                            Height="6"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Data="M 6,0 L 6,6 L 0,6 Z"
                            Fill="#FFE56E17"
                            Opacity="0"
                            Stroke="#FFE56E17"
                            StrokeLineJoin="Miter" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="CheckedOff_BeginStoryboard" Storyboard="{StaticResource CheckedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="CheckedOn_BeginStoryboard" Storyboard="{StaticResource CheckedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style d:IsControlPart="True" TargetType="{x:Type TreeViewItem}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="5,3,15,3" />
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TreeViewItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="select_gradient"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="select_gradient"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="hover_gradient"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.85" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.65" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="hover_gradient"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="InactiveOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="inactive"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="InactiveOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="inactive"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid x:Name="grid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" MinWidth="19" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <ToggleButton
                            x:Name="Expander"
                            ClickMode="Press"
                            IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                            Style="{DynamicResource NuclearTreeViewItemToggleButton}" />

                        <Rectangle
                            x:Name="select_gradient"
                            Grid.Column="1"
                            Fill="{DynamicResource PressedBrush}"
                            IsHitTestVisible="False"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource PressedBorderBrush}"
                            StrokeThickness="1" />
                        <Rectangle
                            x:Name="inactive"
                            Grid.Column="1"
                            Fill="#FF999999"
                            IsHitTestVisible="False"
                            Opacity="0"
                            RadiusX="2"
                            RadiusY="2"
                            Stroke="#FF333333"
                            StrokeThickness="1" />

                        <Rectangle
                            x:Name="hover_gradient"
                            Grid.Column="1"
                            Fill="{DynamicResource MouseOverBrush}"
                            IsHitTestVisible="False"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="{DynamicResource FocusBrush}"
                            StrokeThickness="1" />
                        <Rectangle
                            x:Name="highlight"
                            Grid.Column="1"
                            Margin="1"
                            IsHitTestVisible="False"
                            Opacity="0"
                            RadiusX="0.5"
                            RadiusY="0.5"
                            Stroke="{DynamicResource MouseOverHighlightBrush}"
                            StrokeThickness="1" />

                        <Border
                            x:Name="Selection_Border"
                            Grid.Column="1"
                            Padding="{TemplateBinding Padding}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter
                                x:Name="PART_Header"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                ContentSource="Header" />
                        </Border>
                        <ItemsPresenter
                            x:Name="ItemsHost"
                            Grid.Row="1"
                            Grid.Column="1"
                            Grid.ColumnSpan="2" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="Selection_Border" Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="HoverOn_BeginStoryboard" Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsExpanded" Value="false">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="Expander" Property="Visibility" Value="Hidden" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="SelectedOff_BeginStoryboard" Storyboard="{StaticResource SelectedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource SelectedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.ExitActions>
                                <BeginStoryboard x:Name="InactiveOff_BeginStoryboard" Storyboard="{StaticResource InactiveOff}" />
                            </MultiTrigger.ExitActions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard x:Name="InactiveOn_BeginStoryboard" Storyboard="{StaticResource InactiveOn}" />
                            </MultiTrigger.EnterActions>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsSelectionActive" Value="false" />
                            </MultiTrigger.Conditions>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>