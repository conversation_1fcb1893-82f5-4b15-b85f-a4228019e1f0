# Test script for InventoryPlanSync interface
Write-Host "Testing InventoryPlanSync Interface" -ForegroundColor Green

# Test 1: Valid JSON structure
Write-Host "`n=== Test 1: Valid JSON Structure ===" -ForegroundColor Yellow
$validJson = Get-Content "test_inventory_plan.json" -Raw
Write-Host "JSON Length: $($validJson.Length) characters"
Write-Host "JSON is valid format: $(try { $validJson | ConvertFrom-Json; $true } catch { $false })"

# Test 2: Required fields validation
Write-Host "`n=== Test 2: Required Fields Validation ===" -ForegroundColor Yellow
$jsonObj = $validJson | ConvertFrom-Json
$requiredFields = @("stockTakeName", "stockTakeCode", "stockTakeType", "stockTakeStartDate", "stockTakeEndDate")
foreach ($field in $requiredFields) {
    $value = $jsonObj.$field
    Write-Host "$field`: $value $(if ($value) { '[✓]' } else { '[✗]' })"
}

# Test 3: Nested arrays validation
Write-Host "`n=== Test 3: Nested Arrays Validation ===" -ForegroundColor Yellow
Write-Host "stockTakeWarehouseList count: $($jsonObj.stockTakeWarehouseList.Count)"
Write-Host "stockTakeGoodsList count: $($jsonObj.stockTakeGoodsList.Count)"

# Test 4: Date format validation
Write-Host "`n=== Test 4: Date Format Validation ===" -ForegroundColor Yellow
$dateFields = @("stockTakeStartDate", "stockTakeEndDate")
foreach ($field in $dateFields) {
    $dateValue = $jsonObj.$field
    try {
        $parsedDate = [DateTime]::Parse($dateValue)
        Write-Host "$field`: $dateValue [✓] -> $($parsedDate.ToString('yyyy-MM-dd'))"
    } catch {
        Write-Host "$field`: $dateValue [✗] -> Invalid date format"
    }
}

# Test 5: Date logic validation
Write-Host "`n=== Test 5: Date Logic Validation ===" -ForegroundColor Yellow
$startDate = [DateTime]::Parse($jsonObj.stockTakeStartDate)
$endDate = [DateTime]::Parse($jsonObj.stockTakeEndDate)
if ($startDate -le $endDate) {
    Write-Host "Date range validation: [✓] Start date ($($startDate.ToString('yyyy-MM-dd'))) <= End date ($($endDate.ToString('yyyy-MM-dd')))"
} else {
    Write-Host "Date range validation: [✗] Start date is after end date"
}

# Test 6: Warehouse list validation
Write-Host "`n=== Test 6: Warehouse List Validation ===" -ForegroundColor Yellow
foreach ($warehouse in $jsonObj.stockTakeWarehouseList) {
    $warehouseRequiredFields = @("warehouseId", "warehouseCode", "warehouseName")
    $allValid = $true
    foreach ($field in $warehouseRequiredFields) {
        if (-not $warehouse.$field) {
            $allValid = $false
            break
        }
    }
    Write-Host "Warehouse $($warehouse.warehouseCode): $(if ($allValid) { '[✓]' } else { '[✗]' })"
}

# Test 7: Goods list validation
Write-Host "`n=== Test 7: Goods List Validation ===" -ForegroundColor Yellow
foreach ($goods in $jsonObj.stockTakeGoodsList) {
    $goodsRequiredFields = @("goodsId", "goodsCode", "goodsName", "warehouseId")
    $allValid = $true
    foreach ($field in $goodsRequiredFields) {
        if (-not $goods.$field) {
            $allValid = $false
            break
        }
    }
    Write-Host "Goods $($goods.goodsCode): $(if ($allValid) { '[✓]' } else { '[✗]' })"
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
Write-Host "All validation tests completed successfully!" -ForegroundColor Green