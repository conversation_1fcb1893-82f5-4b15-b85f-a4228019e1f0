<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  
  <appSettings>
    <!-- HTTP API服务配置 -->
    <add key="HttpApiPort" value="9001" />
    
    <!-- WCF服务配置 -->
    <add key="WcfServiceUrl" value="http://127.0.0.1:8001/Service/Demo" />
    
    <!-- 日志配置 -->
    <add key="LogLevel" value="INFO" />
    <add key="LogPath" value="Logs" />
  </appSettings>
  
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IWcfDemoService" 
                 maxBufferSize="65536" 
                 maxReceivedMessageSize="65536"
                 receiveTimeout="00:10:00"
                 sendTimeout="00:10:00">
          <security mode="None" />
        </binding>
      </basicHttpBinding>
    </bindings>
    
    <client>
      <endpoint address="http://127.0.0.1:8001/Service/Demo"
                binding="basicHttpBinding"
                bindingConfiguration="BasicHttpBinding_IWcfDemoService"
                contract="SiaSun.LMS.HttpApiDemo.IWcfDemoService"
                name="BasicHttpBinding_IWcfDemoService" />
    </client>
  </system.serviceModel>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
