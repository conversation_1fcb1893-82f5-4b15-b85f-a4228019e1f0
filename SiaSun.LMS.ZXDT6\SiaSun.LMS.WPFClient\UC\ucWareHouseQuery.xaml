﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucWareHouseQuery"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="34" d:DesignWidth="442">
    <Border>
        <!--<Border.Effect>
            <DropShadowEffect Color="DarkBlue" ShadowDepth="2"></DropShadowEffect>
        </Border.Effect>-->
            <WrapPanel>
                <StackPanel Name="panelWare" Margin="5" Orientation="Horizontal">
                    <TextBlock Text="仓库:" Margin="2" VerticalAlignment="Center"></TextBlock>
                    <ComboBox Name="cmbWare" Margin="2" MinWidth="100" SelectionChanged="cmbWare_SelectionChanged"></ComboBox>
                </StackPanel>

                <StackPanel Name="panelArea" Margin="5" Orientation="Horizontal" UseLayoutRounding="True">
                    <TextBlock Text="库区:" Margin="2" VerticalAlignment="Center"></TextBlock>
                    <ComboBox Name="cmbArea" Margin="2" MinWidth="100" SelectionChanged="cmbArea_SelectionChanged"></ComboBox>
                </StackPanel>

                <StackPanel Name="panelLine" Margin="5" Orientation="Horizontal">
                    <TextBlock Text="排:" Margin="2" VerticalAlignment="Center"></TextBlock>
                    <ComboBox Name="cmbLine" Margin="2" MinWidth="100" SelectionChanged="cmbLine_SelectionChanged"></ComboBox>
                </StackPanel>
            </WrapPanel>
    </Border>
</UserControl>
