﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ManageOut.aspx.cs" Inherits="ManageOut" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
  <meta charset="UTF-8">
  <link rel="stylesheet" href="/layui/css/layui.css" />
  <link rel="stylesheet" href="/main.css" />
  <script src="/jquery-3.4.1.min.js"></script>
  <script src="/layui/layui.all.js"></script>
  <script src="/main.js"></script>
</head>
<body>
  <form runat="server" class="layui-form" autocomplete="off">
    <div class="layui-form-item" style="margin-top:10px;">
      <asp:Label runat="server" Text="托盘条码" CssClass="layui-form-label" for="tbStockBarcode"></asp:Label>
      <asp:TextBox runat="server" ID="tbStockBarcode" CssClass="layui-input input-width" wms-enter wms-focus />
      <asp:Button runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="tbStockBarcode_Enter" />
    </div>
    <div class="layui-form-item" style="padding:0 15px;min-height:200px;">
      <asp:GridView ID="gvOutStorage" runat="server" AutoGenerateColumns="False" style="width:100%;"
        ShowHeaderWhenEmpty="False" CellPadding="4" ForeColor="#333333" GridLines="None">
        <AlternatingRowStyle BackColor="White" />
        <Columns>
          <asp:TemplateField HeaderText="出库数量">
            <HeaderStyle CssClass="t-value1" />
            <ItemStyle CssClass="t-value1"/>
            <ItemTemplate>
              <asp:TextBox runat="server" ID="value1" CssClass="quantity" Text='<%#Eval("value1") %>' onclick="select();" onFocus="select();" />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="库存ID">
            <HeaderStyle CssClass="t-value2 d-none" />
            <ItemStyle CssClass="t-value2 d-none"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value2" Text='<%#Eval("value2") %>' />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="货位ID">
            <HeaderStyle CssClass="t-value3 d-none" />
            <ItemStyle CssClass="t-value3 d-none"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value3" Text='<%#Eval("value3") %>' />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="物资ID">
            <HeaderStyle CssClass="t-value4 d-none" />
            <ItemStyle CssClass="t-value4 d-none"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value4" Text='<%#Eval("value4") %>' />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="材料编码">
            <HeaderStyle CssClass="t-value5" />
            <ItemStyle CssClass="t-value5"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value5" Text='<%#Eval("value5") %>' />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="物资名称">
            <HeaderStyle CssClass="t-value6" />
            <ItemStyle CssClass="t-value6"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value6" Text='<%#Eval("value6") %>' />
            </ItemTemplate>
          </asp:TemplateField>
          <asp:TemplateField HeaderText="数量" >
            <HeaderStyle CssClass="t-value7" />
            <ItemStyle CssClass="t-value7"/>
            <ItemTemplate>
              <asp:Label runat="server" ID="value7" Text='<%#Eval("value7") %>' />
            </ItemTemplate>
          </asp:TemplateField>
        </Columns>
        <EditRowStyle BackColor="#2461BF" />
        <FooterStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <HeaderStyle BackColor="#507CD1" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="#2461BF" ForeColor="White" HorizontalAlign="Center" />
        <RowStyle BackColor="#EFF3FB" />
        <SelectedRowStyle BackColor="#D1DDF1" Font-Bold="True" ForeColor="#333333" />
        <SortedAscendingCellStyle BackColor="#F5F7FB" />
        <SortedAscendingHeaderStyle BackColor="#6D95E1" />
        <SortedDescendingCellStyle BackColor="#E9EBEF" />
        <SortedDescendingHeaderStyle BackColor="#4870BE" />
      </asp:GridView>
    </div>
    <div class="layui-form-item">
      <input type="button" class="layui-input-inline layui-btn layui-btn-normal button-width" value="出库" wms-confirm="确认出库" />
      <asp:Button runat="server" CssClass="d-none" UseSubmitBehavior="false" OnClick="bSave_Click" />
      <asp:Button runat="server" Text="刷新" CssClass="layui-input-inline layui-btn layui-btn-normal button-width" UseSubmitBehavior="false" OnClick="bRefresh_Click" />
    </div>
  </form>
</body>
</html>
