# 出库单完成上报回调接口

## 概述

`OutboundReceiptCompleteCallback` 是用于向外部物资系统上报出库单完成信息的回调接口。该接口遵循iWMS系统的标准调用流程：注册 → 获取凭证 → 携带凭证调用接口。

## 接口说明

### 类文件
- **主实现**: `OutboundReceiptCompleteCallback.cs` - 核心接口实现
- **使用示例**: `OutboundReceiptCompleteCallback_Example.cs` - 详细的使用示例
- **测试类**: `OutboundReceiptCompleteCallbackTest.cs` - 接口测试方法

### 接口地址
- **测试环境**: http://************:9008/api/ws/AuthService?wsdl
- **正式环境**: http://************:9008/api/ws/AuthService?wsdl

## 数据结构

### OutboundReceiptCompleteItem（出库明细项）
```csharp
public class OutboundReceiptCompleteItem
{
    public int goodsNum { get; set; }        // 数量
    public string warehouseCode { get; set; } // 仓库编码
    public string shelfCode { get; set; }     // 货架编码
    public string oId { get; set; }           // 出库明细原id ID
    public string lId { get; set; }           // 立体仓系统：出库明细id
    public int outboundType { get; set; }     // 出库类型
}
```

### 出库类型说明
- **63**: 出库单
- **72**: 入库红冲单
- **74**: 借用

## 使用方法

### 1. 单个明细上报
```csharp
var callback = new OutboundReceiptCompleteCallback();

bool success = callback.IntefaceMethod(
    goodsNum: 50,                           // 数量
    warehouseCode: "WH001",                 // 仓库编码
    shelfCode: "SH001-A01",                 // 货架编码
    oId: "OUTBOUND_DETAIL_001",             // 出库明细原ID
    lId: "STEREO_WH_OUTBOUND_001",          // 立体仓系统出库明细ID
    outboundType: 63,                       // 出库类型：63=出库单
    out string message);

if (success)
{
    Console.WriteLine($"上报成功：{message}");
}
else
{
    Console.WriteLine($"上报失败：{message}");
}
```

### 2. 批量明细上报
```csharp
var callback = new OutboundReceiptCompleteCallback();

var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
{
    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
    {
        goodsNum = 100,
        warehouseCode = "WH001",
        shelfCode = "SH001-A01",
        oId = "OUTBOUND_DETAIL_001",
        lId = "STEREO_WH_OUTBOUND_001",
        outboundType = 63    // 63=出库单
    },
    // 更多明细项...
};

bool success = callback.IntefaceMethod(items, out string message);
```

## 调用流程

1. **注册阶段**: 使用系统标识"ZYK"向物资系统注册，获取密钥(secrit)
2. **获取凭证**: 使用密钥和接口标识"OutboundReceiptCompleteCallback"获取访问凭证(certificate)
3. **调用接口**: 携带凭证在SOAP Header中调用accessInterface方法，传递出库明细数据

## 参数验证

接口会自动验证以下参数：
- `outboundItems` 不能为空或null
- 每个明细项的 `warehouseCode`、`shelfCode`、`oId`、`lId` 不能为空
- `outboundType` 必须为 63、72 或 74

## 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "msg_60299ecfb046",
  "traceId": "traceId_9d61457be7b5"
}
```

### 响应状态码说明
- **0**: 成功
- **1**: 失败
- **2**: 入参错误

## 错误处理

接口包含完整的错误处理机制：
- 参数验证错误
- 网络通信错误
- 外部系统响应错误
- 异常捕获和日志记录

## 日志记录

- **成功日志**: 记录上报成功信息，包含明细数量和traceId
- **错误日志**: 记录详细的错误信息和异常堆栈

## 配置要求

确保在配置文件中设置：
```xml
<add key="ExternalServiceUrl" value="http://************:9008/api/ws/AuthService?wsdl" />
```

## 测试方法

运行测试类验证接口功能：
```csharp
// 运行所有测试
OutboundReceiptCompleteCallbackTest.RunAllTests();

// 单独测试
OutboundReceiptCompleteCallbackTest.TestSingleOutboundItem();
OutboundReceiptCompleteCallbackTest.TestBatchOutboundItems();
OutboundReceiptCompleteCallbackTest.TestParameterValidation();
```

## 注意事项

1. **接口标识**: 确保使用正确的接口标识"OutboundReceiptCompleteCallback"
2. **系统标识**: 固定使用"ZYK"作为系统标识
3. **凭证过期**: 凭证有效期为300秒（tokenExpired="2"）
4. **网络配置**: 确保网络能够访问外部物资系统
5. **错误重试**: 建议在业务层实现失败重试机制

## 相关文档

- 中央库接口文档（新版）.md - 完整的接口规范文档
- InventoryResultCallback.cs - 参考实现模式
