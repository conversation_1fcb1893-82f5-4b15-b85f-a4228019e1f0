using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 移库结果上报回调接口使用示例
    /// </summary>
    public class TransferResultCallback_Example
    {
        /// <summary>
        /// 示例1：批量上报移库结果
        /// </summary>
        public static void Example_BatchTransferResult()
        {
            try
            {
                var callback = new TransferResultCallback();
                
                // 准备移库结果明细数据
                var transferItems = new List<TransferResultCallback.TransferResultItem>
                {
                    new TransferResultCallback.TransferResultItem
                    {
                        goodsCode = "GOODS001",
                        removeNum = 10,
                        warehouseCode = "WH001",
                        warehouseName = "中央仓库",
                        warehouseId = "1",
                        shelfCode = "A01-01",
                        shelfName = "A区1排1层",
                        shelfId = "101",
                        targetWarehouseCode = "WH002",
                        targetWarehouseName = "分拣仓库",
                        targetWarehouseId = "2",
                        targetShelfCode = "B01-01",
                        targetShelfName = "B区1排1层",
                        targetShelfId = "201"
                    },
                    new TransferResultCallback.TransferResultItem
                    {
                        goodsCode = "GOODS002",
                        removeNum = 5,
                        warehouseCode = "WH001",
                        warehouseName = "中央仓库",
                        warehouseId = "1",
                        shelfCode = "A01-02",
                        shelfName = "A区1排2层",
                        shelfId = "102",
                        targetWarehouseCode = "WH003",
                        targetWarehouseName = "临时仓库",
                        targetWarehouseId = "3",
                        targetShelfCode = "C01-01",
                        targetShelfName = "C区1排1层",
                        targetShelfId = "301"
                    }
                };

                // 调用移库结果上报接口
                bool success = callback.IntefaceMethod(transferItems, out string message);
                
                if (success)
                {
                    Console.WriteLine($"批量移库结果上报成功: {message}");
                    Console.WriteLine($"上报明细数量: {transferItems.Count}");
                }
                else
                {
                    Console.WriteLine($"批量移库结果上报失败: {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量移库结果上报异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 示例2：单个移库明细上报
        /// </summary>
        public static void Example_SingleTransferResult()
        {
            try
            {
                var callback = new TransferResultCallback();

                // 调用单个移库明细上报接口
                bool success = callback.IntefaceMethod(
                    goodsCode: "GOODS003",
                    removeNum: 8,
                    warehouseCode: "WH001",
                    warehouseName: "中央仓库",
                    warehouseId: "1",
                    shelfCode: "A02-01",
                    shelfName: "A区2排1层",
                    shelfId: "103",
                    targetWarehouseCode: "WH002",
                    targetWarehouseName: "分拣仓库",
                    targetWarehouseId: "2",
                    targetShelfCode: "B02-01",
                    targetShelfName: "B区2排1层",
                    targetShelfId: "202",
                    out string message
                );

                if (success)
                {
                    Console.WriteLine($"单个移库结果上报成功: {message}");
                }
                else
                {
                    Console.WriteLine($"单个移库结果上报失败: {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"单个移库结果上报异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 示例3：参数校验演示
        /// </summary>
        public static void Example_ParameterValidation()
        {
            var callback = new TransferResultCallback();

            // 测试空列表
            Console.WriteLine("=== 测试空列表 ===");
            bool result1 = callback.IntefaceMethod(new List<TransferResultCallback.TransferResultItem>(), out string msg1);
            Console.WriteLine($"结果: {result1}, 消息: {msg1}");

            // 测试缺少必填字段
            Console.WriteLine("\n=== 测试缺少物资编码 ===");
            var invalidItem = new List<TransferResultCallback.TransferResultItem>
            {
                new TransferResultCallback.TransferResultItem
                {
                    // goodsCode 为空
                    removeNum = 10,
                    warehouseCode = "WH001",
                    shelfCode = "A01-01",
                    targetWarehouseCode = "WH002",
                    targetShelfCode = "B01-01"
                }
            };
            bool result2 = callback.IntefaceMethod(invalidItem, out string msg2);
            Console.WriteLine($"结果: {result2}, 消息: {msg2}");

            // 测试无效移库数量
            Console.WriteLine("\n=== 测试无效移库数量 ===");
            var invalidNumItem = new List<TransferResultCallback.TransferResultItem>
            {
                new TransferResultCallback.TransferResultItem
                {
                    goodsCode = "GOODS001",
                    removeNum = 0, // 无效数量
                    warehouseCode = "WH001",
                    shelfCode = "A01-01",
                    targetWarehouseCode = "WH002",
                    targetShelfCode = "B01-01"
                }
            };
            bool result3 = callback.IntefaceMethod(invalidNumItem, out string msg3);
            Console.WriteLine($"结果: {result3}, 消息: {msg3}");
        }

        /// <summary>
        /// 示例4：完整的业务场景演示
        /// </summary>
        public static void Example_BusinessScenario()
        {
            Console.WriteLine("=== 移库业务场景演示 ===");
            
            try
            {
                // 模拟移库业务数据
                var transferData = new
                {
                    TransferTaskId = "TT20241219001",
                    TransferTaskName = "A区到B区物资移库",
                    OperatorId = "OP001",
                    OperatorName = "张三",
                    TransferTime = DateTime.Now,
                    Items = new[]
                    {
                        new { GoodsCode = "MAT001", GoodsName = "螺栓M8", Quantity = 100 },
                        new { GoodsCode = "MAT002", GoodsName = "螺母M8", Quantity = 100 },
                        new { GoodsCode = "MAT003", GoodsName = "垫片8mm", Quantity = 200 }
                    }
                };

                Console.WriteLine($"移库任务: {transferData.TransferTaskName}");
                Console.WriteLine($"操作员: {transferData.OperatorName}");
                Console.WriteLine($"移库时间: {transferData.TransferTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"移库物资数量: {transferData.Items.Length}");

                // 构建移库结果数据
                var callback = new TransferResultCallback();
                var transferItems = new List<TransferResultCallback.TransferResultItem>();

                foreach (var item in transferData.Items)
                {
                    transferItems.Add(new TransferResultCallback.TransferResultItem
                    {
                        goodsCode = item.GoodsCode,
                        removeNum = item.Quantity,
                        warehouseCode = "WH_CENTRAL",
                        warehouseName = "中央仓库",
                        warehouseId = "1",
                        shelfCode = $"A-{item.GoodsCode.Substring(3)}",
                        shelfName = $"A区-{item.GoodsName}专用货架",
                        shelfId = $"A{item.GoodsCode.Substring(3)}",
                        targetWarehouseCode = "WH_SORTING",
                        targetWarehouseName = "分拣仓库",
                        targetWarehouseId = "2",
                        targetShelfCode = $"B-{item.GoodsCode.Substring(3)}",
                        targetShelfName = $"B区-{item.GoodsName}专用货架",
                        targetShelfId = $"B{item.GoodsCode.Substring(3)}"
                    });
                }

                // 上报移库结果
                Console.WriteLine("\n开始上报移库结果...");
                bool success = callback.IntefaceMethod(transferItems, out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 移库结果上报成功: {message}");
                    Console.WriteLine($"  - 任务ID: {transferData.TransferTaskId}");
                    Console.WriteLine($"  - 上报明细: {transferItems.Count} 条");
                    //Console.WriteLine($"  - 总移库数量: {transferItems.Sum(x => x.removeNum)} 件");
                }
                else
                {
                    Console.WriteLine($"✗ 移库结果上报失败: {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"业务场景演示异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有示例
        /// </summary>
        public static void RunAllExamples()
        {
            Console.WriteLine("========================================");
            Console.WriteLine("移库结果上报回调接口使用示例");
            Console.WriteLine("========================================\n");

            Example_BatchTransferResult();
            Console.WriteLine("\n" + new string('-', 50) + "\n");

            Example_SingleTransferResult();
            Console.WriteLine("\n" + new string('-', 50) + "\n");

            Example_ParameterValidation();
            Console.WriteLine("\n" + new string('-', 50) + "\n");

            Example_BusinessScenario();
            Console.WriteLine("\n========================================");
        }
    }
}
