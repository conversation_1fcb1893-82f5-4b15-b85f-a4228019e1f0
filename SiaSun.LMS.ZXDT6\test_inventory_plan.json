{"stockTakeName": "Q1 2025 Inventory Plan", "stockTakeCode": "PLAN_2025_Q1_001", "description": "Q1 2025 Full Inventory", "stockTakeType": "Full", "stockTakeWay": "System", "stockTakeStartDate": "2025-01-01", "stockTakeEndDate": "2025-01-31", "unitId": "UNIT_001", "unitName": "Storage Management Dept", "operatorId": "OP_001", "operatorName": "<PERSON>", "stockTakeManageId": "MGR_001", "stockTakeManageName": "<PERSON>", "stockTakeWarehouseList": [{"stockTakeId": "PLAN_2025_Q1_001", "warehouseId": "WH_001", "warehouseCode": "WH001", "warehouseName": "Main Warehouse", "highGoods": "1", "stockTakeRatio": "100", "shelfId": "SHELF_001", "shelfName": "Zone A Shelf", "createDate": "2025-01-01 08:00:00", "createUser": "ADMIN", "createName": "Administrator", "updateDate": "2025-01-01 08:00:00", "updateUser": "ADMIN", "updateName": "Administrator", "id": "WH_REL_001", "status": 1, "billCode": "BILL_WH_001"}], "stockTakeGoodsList": [{"inventoryId": "INV_001", "stockTakeId": "PLAN_2025_Q1_001", "goodsId": "GOODS_001", "goodsCode": "G001", "goodsName": "Test Material A", "goodsVersion": "V1.0", "storageNum": 100, "unitId": "UNIT_PCS", "unitName": "Pieces", "brand": "Brand A", "warehouseId": "WH_001", "warehouseName": "Main Warehouse", "warehouseCode": "WH001", "shelfId": "SHELF_001", "shelfName": "Zone A Shelf", "shelfCode": "A001", "stockTakeUserId": "USER_001", "stockTakeUserName": "<PERSON>", "stockTakePlanStartDate": "2025-01-01", "stockTakePlanEndDate": "2025-01-31", "createDate": "2025-01-01 08:00:00", "createUser": "ADMIN", "createName": "Administrator", "updateDate": "2025-01-01 08:00:00", "updateUser": "ADMIN", "updateName": "Administrator", "id": "GOODS_REL_001", "status": 1, "billCode": "BILL_GOODS_001"}], "stockTakeMangeDeptId": "DEPT_001", "stockTakeMangeDeptName": "Storage Management Dept", "createDate": "2025-01-01 08:00:00", "createUser": "ADMIN", "createName": "Administrator", "updateDate": "2025-01-01 08:00:00", "updateUser": "ADMIN", "updateName": "Administrator", "id": "PLAN_2025_Q1_001", "status": 1, "billCode": "BILL_PLAN_001"}