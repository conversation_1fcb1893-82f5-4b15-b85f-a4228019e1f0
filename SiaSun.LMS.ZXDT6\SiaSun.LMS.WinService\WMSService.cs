﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Runtime.InteropServices;
using System.Threading;
using Quartz;
using Quartz.Impl;


namespace SiaSun.LMS.WinService
{
    public partial class WMSService : ServiceBase
    {

        ISchedulerFactory schedulerFactory = new StdSchedulerFactory();

        IScheduler scheduler;

        public WMSService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                Program._HouseUrl = SiaSun.LMS.Common.StringUtil.GetConfig("SiasunUrl");
                Program._InterfaceUrl = SiaSun.LMS.Common.StringUtil.GetConfig("InterfaceUrl");

                ServiceHostGroup.StartAllConfiguredServices("House", Program._HouseUrl);
                ServiceHostGroup.StartAllConfiguredServices("All", Program._InterfaceUrl);

                Program.hostLog.Info("启动新松立库系统应用服务成功");
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("启动新松立库系统应用服务失败", ex);
                throw;
            }
                       
            try
            {
                scheduler = schedulerFactory.GetScheduler();
                scheduler.Start();

                Program.hostLog.Info("启动定时调度作业成功");
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("启动定时调度作业失败 ", ex);
                throw;
            }
        }

        protected override void OnStop()
        {
            try
            {
                scheduler.Shutdown();

                Program.hostLog.Info("停止定时调度作业成功");
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("停止定时调度作业失败" , ex);
            }

            try
            {
                ServiceHostGroup.CloseAllServices();

                Program.hostLog.Info("停止新松立库系统应用服务成功");
            }
            catch (Exception ex)
            {
                Program.hostLog.Error("停止新松立库系统应用服务失败", ex);
            }
        }

    }
}
