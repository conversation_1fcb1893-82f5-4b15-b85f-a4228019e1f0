<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="S_Manage" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Manage?xsd=xsd2" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Manage?xsd=xsd0" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Manage?xsd=xsd1" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Manage?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System.Data" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="I_Manage_PlanEventExecute_InputMessage">
    <wsdl:part name="parameters" element="tns:PlanEventExecute" />
  </wsdl:message>
  <wsdl:message name="I_Manage_PlanEventExecute_OutputMessage">
    <wsdl:part name="parameters" element="tns:PlanEventExecuteResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageEventExecute_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageEventExecute" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageEventExecute_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageEventExecuteResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ContinusManageDown_InputMessage">
    <wsdl:part name="parameters" element="tns:ContinusManageDown" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ContinusManageDown_OutputMessage">
    <wsdl:part name="parameters" element="tns:ContinusManageDownResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageCreate_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageCreate" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageCreate_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageCreateResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageComplete_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageComplete" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageComplete_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageCompleteResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageException_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageException" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageException_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageExceptionResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageError_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageError" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageError_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageErrorResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageCancel_InputMessage">
    <wsdl:part name="parameters" element="tns:ManageCancel" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ManageCancel_OutputMessage">
    <wsdl:part name="parameters" element="tns:ManageCancelResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ControlApplyAdd_InputMessage">
    <wsdl:part name="parameters" element="tns:ControlApplyAdd" />
  </wsdl:message>
  <wsdl:message name="I_Manage_ControlApplyAdd_OutputMessage">
    <wsdl:part name="parameters" element="tns:ControlApplyAddResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_WsControlApply_InputMessage">
    <wsdl:part name="parameters" element="tns:WsControlApply" />
  </wsdl:message>
  <wsdl:message name="I_Manage_WsControlApply_OutputMessage">
    <wsdl:part name="parameters" element="tns:WsControlApplyResponse" />
  </wsdl:message>
  <wsdl:message name="I_Manage_GoodsCreate_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsCreate" />
  </wsdl:message>
  <wsdl:message name="I_Manage_GoodsCreate_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsCreateResponse" />
  </wsdl:message>
  <wsdl:portType name="I_Manage">
    <wsdl:operation name="PlanEventExecute">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/PlanEventExecute" message="tns:I_Manage_PlanEventExecute_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/PlanEventExecuteResponse" message="tns:I_Manage_PlanEventExecute_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageEventExecute">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageEventExecute" message="tns:I_Manage_ManageEventExecute_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageEventExecuteResponse" message="tns:I_Manage_ManageEventExecute_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ContinusManageDown">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ContinusManageDown" message="tns:I_Manage_ContinusManageDown_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ContinusManageDownResponse" message="tns:I_Manage_ContinusManageDown_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageCreate">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageCreate" message="tns:I_Manage_ManageCreate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageCreateResponse" message="tns:I_Manage_ManageCreate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageComplete">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageComplete" message="tns:I_Manage_ManageComplete_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageCompleteResponse" message="tns:I_Manage_ManageComplete_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageException">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageException" message="tns:I_Manage_ManageException_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageExceptionResponse" message="tns:I_Manage_ManageException_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageError">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageError" message="tns:I_Manage_ManageError_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageErrorResponse" message="tns:I_Manage_ManageError_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ManageCancel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ManageCancel" message="tns:I_Manage_ManageCancel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ManageCancelResponse" message="tns:I_Manage_ManageCancel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ControlApplyAdd">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/ControlApplyAdd" message="tns:I_Manage_ControlApplyAdd_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/ControlApplyAddResponse" message="tns:I_Manage_ControlApplyAdd_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="WsControlApply">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/WsControlApply" message="tns:I_Manage_WsControlApply_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/WsControlApplyResponse" message="tns:I_Manage_WsControlApply_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GoodsCreate">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Manage/GoodsCreate" message="tns:I_Manage_GoodsCreate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Manage/GoodsCreateResponse" message="tns:I_Manage_GoodsCreate_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_Manage" type="tns:I_Manage">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="PlanEventExecute">
      <soap:operation soapAction="http://tempuri.org/I_Manage/PlanEventExecute" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageEventExecute">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageEventExecute" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ContinusManageDown">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ContinusManageDown" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageCreate">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageCreate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageComplete">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageComplete" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageException">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageException" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageError">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageError" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ManageCancel">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ManageCancel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ControlApplyAdd">
      <soap:operation soapAction="http://tempuri.org/I_Manage/ControlApplyAdd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WsControlApply">
      <soap:operation soapAction="http://tempuri.org/I_Manage/WsControlApply" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsCreate">
      <soap:operation soapAction="http://tempuri.org/I_Manage/GoodsCreate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_Manage">
    <wsdl:port name="BasicHttpBinding_I_Manage" binding="tns:BasicHttpBinding_I_Manage">
      <soap:address location="http://127.0.0.1:8002/Service/Manage" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>