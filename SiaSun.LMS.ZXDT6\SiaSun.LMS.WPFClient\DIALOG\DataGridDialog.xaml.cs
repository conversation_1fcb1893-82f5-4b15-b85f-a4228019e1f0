﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// DataGridDialog.xaml 的交互逻辑
    /// </summary>
    public partial class DataGridDialog : Window
    {
        bool boolAllowEdit = true;
        string strTableName = string.Empty;
        string strOrderField = string.Empty;
        string strTotalColumn = string.Empty;
        string strWhere = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="TabText">窗体显示名</param>
        /// <param name="TableName">数据库表名</param>
        /// <param name="QueryWhere">查询条件</param>
        /// <param name="AllowChecked">是否显示选择列</param>
        /// <param name="AllowEdit">是否允许编辑数据</param>
        /// <param name="TotalColumnName">计算数量总和的列名</param>
        public DataGridDialog(string TabText, string TableName, string QueryWhere, string OrderField, bool AllowEdit, string TotalColumnName)
        {
            InitializeComponent();

            //设置空间属性
            this.Title = TabText;
            this.boolAllowEdit = AllowEdit;
            this.strTableName = TableName;
            this.strOrderField = OrderField;
            this.strTotalColumn = TotalColumnName;
            this.strWhere = QueryWhere;
        }

        //加载窗体
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.MinWidth = 600;

            //加载数据
            LoadDataGrid();
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadDataGrid()
        {
            try
            {
                this.gridApp.U_WindowName = this.GetType().Name;
                this.gridApp.U_TableName = this.strTableName;
                this.gridApp.U_Where = this.strWhere;
                this.gridApp.U_OrderField = this.strOrderField;
                this.gridApp.U_TotalColumnName = this.strTotalColumn;

                this.gridApp.U_AllowOperatData = this.boolAllowEdit;
                this.gridApp.U_AllowPage = false;
                this.gridApp.U_AllowChecked = this.boolAllowEdit;

                this.gridApp.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
