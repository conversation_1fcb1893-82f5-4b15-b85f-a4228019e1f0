﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="WH_WAREHOUSE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="WH_WAREHOUSE" type="SiaSun.LMS.Model.WH_WAREHOUSE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="WH_WAREHOUSE">
			<result property="WAREHOUSE_ID" column="warehouse_id" />
			<result property="WAREHOUSE_CODE" column="warehouse_code" />
			<result property="WAREHOUSE_NAME" column="warehouse_name" />
			<result property="WAREHOUSE_REMARK" column="warehouse_remark" />
			<result property="WAREHOUSE_FLAG" column="warehouse_flag" />
			<result property="WAREHOUSE_TYPE" column="warehouse_type" />
			<result property="WAREHOUSE_ORDER" column="warehouse_order" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="WH_WAREHOUSE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			warehouse_id,
			warehouse_code,
			warehouse_name,
			warehouse_remark,
			warehouse_flag,
			warehouse_type,
			warehouse_order
			From WH_WAREHOUSE
		</select>

		<select id="WH_WAREHOUSE_SELECT_BY_ID" parameterClass="int" extends = "WH_WAREHOUSE_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					warehouse_id=#WAREHOUSE_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="WH_WAREHOUSE_SELECT_BY_CELL_ID" parameterClass="int" extends = "WH_WAREHOUSE_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					warehouse_id IN (SELECT WAREHOUSE_ID FROM WH_AREA WHERE AREA_ID IN (SELECT AREA_ID FROM WH_CELL WHERE CELL_ID = #CELL_ID#))
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="WH_WAREHOUSE_INSERT" parameterClass="WH_WAREHOUSE">
			Insert Into WH_WAREHOUSE (
			<!--warehouse_id,-->
			warehouse_code,
			warehouse_name,
			warehouse_remark,
			warehouse_flag,
			warehouse_type,
			warehouse_order
			)Values(
			<!--#WAREHOUSE_ID#,-->
			#WAREHOUSE_CODE#,
			#WAREHOUSE_NAME#,
			#WAREHOUSE_REMARK#,
			#WAREHOUSE_FLAG#,
			#WAREHOUSE_TYPE#,
			#WAREHOUSE_ORDER#
			)
			<selectKey  resultClass="int" type="post" property="WAREHOUSE_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="WH_WAREHOUSE_UPDATE" parameterClass="WH_WAREHOUSE">
			Update WH_WAREHOUSE Set
			<!--warehouse_id=#WAREHOUSE_ID#,-->
			warehouse_code=#WAREHOUSE_CODE#,
			warehouse_name=#WAREHOUSE_NAME#,
			warehouse_remark=#WAREHOUSE_REMARK#,
			warehouse_flag=#WAREHOUSE_FLAG#,
			warehouse_type=#WAREHOUSE_TYPE#,
			warehouse_order=#WAREHOUSE_ORDER#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					warehouse_id=#WAREHOUSE_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="WH_WAREHOUSE_DELETE" parameterClass="int">
			Delete From WH_WAREHOUSE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					warehouse_id=#WAREHOUSE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>