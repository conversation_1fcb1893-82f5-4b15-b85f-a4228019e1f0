﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Data;

namespace SiaSun.LMS.Implement.Interface
{
    public class InterfaceBase
    {
        private static string externalServiceUrl = Common.StringUtil.GetConfig("ExternalServiceUrl");
        private static string externalServiceToken = Common.StringUtil.GetConfig("ExternalServiceToken");
        private static string interfaceServiceToken = Common.StringUtil.GetConfig("InterfaceToken");
        
        //新松服务的Token
        public string interfaceToken { get; set; }

        public string[] ZeroOne { get; set; } = new string[] { "0", "1" };
        public string[] One { get; set; } = new string[] { "1" };
        public string[] Two { get; set; } = new string[] { "1", "2" };
        public string[] Three { get; set; } = new string[] { "1", "2", "3" };
        public string[] Four { get; set; } = new string[] { "1", "2", "3", "4" };
        public string[] Five { get; set; } = new string[] { "1", "2", "3", "4", "5" };
        public string[] Six { get; set; } = new string[] { "1", "2", "3", "4", "5", "6" };
        public string[] Seven { get; set; } = new string[] { "1", "2", "3", "4", "5", "6", "7" };
        public string[] Eight { get; set; } = new string[] { "1", "2", "3", "4", "5", "6", "7", "8" };

        public InterfaceBase()
        {
            interfaceToken= interfaceServiceToken;
        }

        /// <summary>
        /// 参数头结构
        /// </summary>
        protected class PublicHeader
        {
            public string AppToken { get; set; } = externalServiceToken;
            public string TimeStamp { get; set; } = Common.StringUtil.GetDateTime();
        }

        /// <summary>
        /// 公共入参
        /// </summary>
        protected class PublicInputBody
        {
            public string SeqNo { get; set; }
        }

        /// <summary>
        /// 公共出参
        /// </summary>
        protected class PublicOutputBody
        {
            public string ResultFlag { get; set; }
            public string ErrorMessage { get; set; }
        }

        protected class PublicOutputParam
        {
            public PublicHeader Header { get; set; }
            public PublicOutputBody DataField { get; set; }
        }

        /// <summary>
        /// 物料属性
        /// </summary>
        protected class PublicGoodsProperty
        {
            /// <summary>
            /// 物料编码
            /// </summary>
            public string GoodsCode { get; set; }

            /// <summary>
            /// 组合码
            /// </summary>
            public string GroupCode { get; set; }

            /// <summary>
            /// 组合码主键
            /// </summary>
            public string KeyComponent { get; set; }

            /// <summary>
            /// 验货标识 优先出库标记
            /// </summary>
            public string CheckMark { get; set; }

            /// <summary>
            /// 物料条码
            /// </summary>
            public string ItemCode { get; set; }

            /// <summary>
            /// 拆托托盘 1-已拆标记；0-其他，流通装托盘已拆托，需要按照箱发货，优先送到箱拣区按箱发
            /// </summary>
            public string SplitPallet { get; set; }

            /// <summary>
            /// 订单号
            /// </summary>
            public string OrderCode { get; set; }

            /// <summary>
            /// 订单行项目
            /// </summary>
            public string OrderCodeItem { get; set; }

            /// <summary>
            /// 工厂编码
            /// </summary>
            public string FactoryCode { get; set; }

            /// <summary>
            /// 批次号
            /// </summary>
            public string BatchNo { get; set; }

            /// <summary>
            /// 供应商批次号
            /// </summary>
            public string SuBatchNo { get; set; }

            /// <summary>
            /// 客户品牌
            /// </summary>
            public string CustomerBrand { get; set; }

            /// <summary>
            /// 库存类别
            /// </summary>
            public string StockStatus { get; set; }

            /// <summary>
            /// 特殊库存标记
            /// </summary>
            public string SpecialFlag { get; set; }

            /// <summary>
            /// 特殊库存编号
            /// </summary>
            public string SpecialNo { get; set; }

            /// <summary>
            /// 检验批
            /// </summary>
            public string InspectionLot { get; set; }

            /// <summary>
            /// 单位
            /// </summary>
            public string Unit { get; set; }

            /// <summary>
            /// 到期日
            /// </summary>
            public string ExpiringData { get; set; }

            /// <summary>
            /// 包装形式
            /// </summary>
            public string PackType { get; set; }

            /// <summary>
            /// 托盘垛
            /// </summary>
            public string PalletStack { get; set; }

            /// <summary>
            /// 仓库编码
            /// </summary>
            public string WarehouseCode { get; set; }

            /// <summary>
            /// 库存地点
            /// </summary>
            public string StorageCode { get; set; }
                        
            /// <summary>
            /// 码垛方式
            /// </summary>
            public string StackMode { get; set; }

            /// <summary>
            /// 批次收货日期
            /// </summary>
            public string BatchDate { get; set; }

            /// <summary>
            /// 板盘物料
            /// </summary>
            public string PlateMat { get; set; }

            /// <summary>
            /// 流通标识
            /// </summary>
            public string LTBS { get; set; }
        }

        /// <summary>
        /// 接口实现虚函数 2020-04-28 09:54:14
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        internal virtual string IntefaceMethod(string inputJson)
        {
            return string.Empty;
        }

        /// <summary>
        /// 调用外部(WebService)
        /// </summary>
        protected bool InvokeExternal(string methodName, string inParam, out string outParam)
        {
            bool result = true;
            outParam = string.Empty;
            DateTime dtBegin = DateTime.Now;

            try
            {
                result = Common.WebServiceHelper.Invoke(externalServiceUrl, string.Empty, methodName, new object[] { inParam }, out outParam);
            }
            catch (Exception ex)
            {
                result = false;
                outParam = string.Format("调用外部接口异常_消息[{0}]", ex.Message);
            }
            finally
            {
                S_Base.sBase.Log.Info($"调用外部接口[{methodName}]_开始时间[{dtBegin.ToString("yyyy-MM-dd HH:mm:ss.ffff")}]_结束时间[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.ffff")}]_入参[{inParam}]_出参[{outParam}]");
            }
            return result;
        }


        /// <summary>
        /// 返回错误json字符串 2020-04-15 10:53
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        protected string FormatErrorMessage(string message, bool result =false)
        {
            PublicOutputParam outputParam = new PublicOutputParam()
            {
                Header = new PublicHeader(),
                DataField = new PublicOutputBody()
                {
                    ResultFlag = result ? "1" : "0",
                    ErrorMessage = message,
                }
            };
            return Common.JsonHelper.Serializer(outputParam);
        }

    }
}
