<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="S_Database" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/System.Data" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.EnumMessage" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" />
      <xsd:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd6" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="I_Database_ExecuteNonQuery_InputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQuery" />
  </wsdl:message>
  <wsdl:message name="I_Database_ExecuteNonQuery_OutputMessage">
    <wsdl:part name="parameters" element="tns:ExecuteNonQueryResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetList" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetTableXmlSql_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTableXmlSql" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetTableXmlSql_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTableXmlSqlResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_Save_InputMessage">
    <wsdl:part name="parameters" element="tns:Save" />
  </wsdl:message>
  <wsdl:message name="I_Database_Save_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetModel_InputMessage">
    <wsdl:part name="parameters" element="tns:GetModel" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetModel_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetModelResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetModel1_InputMessage">
    <wsdl:part name="parameters" element="tns:GetModel1" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetModel1_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetModel1Response" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetListObject_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListObject" />
  </wsdl:message>
  <wsdl:message name="I_Database_GetListObject_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListObjectResponse" />
  </wsdl:message>
  <wsdl:message name="I_Database_IsExistData_InputMessage">
    <wsdl:part name="parameters" element="tns:IsExistData" />
  </wsdl:message>
  <wsdl:message name="I_Database_IsExistData_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsExistDataResponse" />
  </wsdl:message>
  <wsdl:portType name="I_Database">
    <wsdl:operation name="ExecuteNonQuery">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/ExecuteNonQuery" message="tns:I_Database_ExecuteNonQuery_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/ExecuteNonQueryResponse" message="tns:I_Database_ExecuteNonQuery_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/GetList" message="tns:I_Database_GetList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/GetListResponse" message="tns:I_Database_GetList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTableXmlSql">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/GetTableXmlSql" message="tns:I_Database_GetTableXmlSql_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/GetTableXmlSqlResponse" message="tns:I_Database_GetTableXmlSql_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="Save">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/Save" message="tns:I_Database_Save_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/SaveResponse" message="tns:I_Database_Save_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetModel">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/GetModel" message="tns:I_Database_GetModel_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/GetModelResponse" message="tns:I_Database_GetModel_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetModel1">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/GetModel1" message="tns:I_Database_GetModel1_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/GetModel1Response" message="tns:I_Database_GetModel1_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListObject">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/GetListObject" message="tns:I_Database_GetListObject_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/GetListObjectResponse" message="tns:I_Database_GetListObject_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsExistData">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Database/IsExistData" message="tns:I_Database_IsExistData_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/I_Database/IsExistDataResponse" message="tns:I_Database_IsExistData_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_Database" type="tns:I_Database">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ExecuteNonQuery">
      <soap:operation soapAction="http://tempuri.org/I_Database/ExecuteNonQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetList">
      <soap:operation soapAction="http://tempuri.org/I_Database/GetList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTableXmlSql">
      <soap:operation soapAction="http://tempuri.org/I_Database/GetTableXmlSql" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Save">
      <soap:operation soapAction="http://tempuri.org/I_Database/Save" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModel">
      <soap:operation soapAction="http://tempuri.org/I_Database/GetModel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetModel1">
      <soap:operation soapAction="http://tempuri.org/I_Database/GetModel1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListObject">
      <soap:operation soapAction="http://tempuri.org/I_Database/GetListObject" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsExistData">
      <soap:operation soapAction="http://tempuri.org/I_Database/IsExistData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_Database">
    <wsdl:port name="BasicHttpBinding_I_Database" binding="tns:BasicHttpBinding_I_Database">
      <soap:address location="http://127.0.0.1:8002/Service/Database" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>