﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucQuickQuery.xaml 的交互逻辑
    /// </summary>
    public partial class ucQuickQuery : UserControl
    {
        /// <summary>
        /// 查询代理
        /// </summary>
        /// <param name="QueryWhere">获得的查询条件</param>
        public delegate void U_QueryEventHandler(string QueryWhere);

        /// <summary>
        /// 查询事件
        /// </summary>
        public event U_QueryEventHandler U_Query;

        /// <summary>
        /// 添加委托、事件
        /// xcjt add 2017-01-13
        /// </summary>
        public class AddEventArgs : RoutedEventArgs { public IDictionary<string, string> DicDataPair { get; set; } }
        public delegate void U_AddEventHandler(object sender, AddEventArgs e);
        public event U_AddEventHandler U_AddButtonClicked;
        protected virtual void OnAddButtonClicked(AddEventArgs e) 
        {
            if(U_AddButtonClicked!=null)
            {
                U_AddButtonClicked(this, e);
            }
        }

        string strTableName = null;
        string windowName = null;
        bool isAddButtonVisable = false;

        IDictionary<string, string> dicQueryPair = null;

        /// <summary>
        /// 关联XML表名
        /// </summary>
        public string U_XmlTableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 关联窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return windowName; }
            set { windowName = value; }
        }

        /// <summary>
        /// 添加按钮是否显示
        /// xcjt add 2017-01-13
        /// </summary>
        public bool U_AddButtonVisable
        {
            get { return isAddButtonVisable; }
            set { isAddButtonVisable = value; }
        }

        /// <summary>
        /// 查询的列和值对
        /// </summary>
        public IDictionary<string, string> U_QueryPair
        {
            get { return dicQueryPair; }
            set { dicQueryPair = value; }
        }

        public ucQuickQuery()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            try
            {
                //清空
                this.panelQuery.Children.Clear();

                //初始化值对
                dicQueryPair = new Dictionary<string, string>();

                //添加查询控件
                CustomerDescriptions cusDescription = new CustomerDescriptions();
                using (DataTable tableFieldDescription = string.IsNullOrEmpty(windowName) ? cusDescription.GetStyleDataTable(strTableName) : cusDescription.GetFormStyleDataTable(windowName, strTableName))
                {
                    //根据序号排序并添加列显示
                    DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var queryField = from row in arRowField orderby Convert.ToInt32(row["Order"].ToString()) select row;

                    //判断控件类型设置显示列样式
                    foreach (DataRow rowField in queryField)
                    {
                        //判断标题是否空
                        if (!rowField.IsNull("Header") && !String.IsNullOrEmpty(rowField["Header"].ToString()))
                        {
                            //判断是否可以快速查询
                            if (rowField.Table.Columns.Contains("AllowQuery") && !rowField.IsNull("AllowQuery") && rowField["AllowQuery"].ToString() == "1")
                            {
                                //判断类型
                                switch (rowField["ControlType"].ToString().ToLower())
                                {
                                    case "checkbox":
                                        this.AddCheckBox(rowField);
                                        break;
                                    case "combobox":
                                        this.AddComboBox(rowField);
                                        break;
                                    case "elementcombox":
                                        this.AddElmentComboBox(rowField);
                                        break;
                                    case "datetimepicker":
                                        this.AddDatePicker(rowField);
                                        break;
                                    default:
                                        this.AddTextBox(rowField);
                                        break;
                                }
                            }
                        }
                    }
                }
                //添加按钮
                this.panelQuery.Children.Add(this.panelButton);

                //xcjt add 2017-01-13
                this.btnAdd.Visibility = isAddButtonVisable ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        #region     ------添加查询控件

        /// <summary>
        /// 添加标题
        /// </summary>
        private void AddHeader(StackPanel panelItem, string Header)
        {
            //显示标题
            TextBlock txtHeader = new TextBlock();
            txtHeader.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            panelItem.Children.Add(txtHeader);

            //绑定标题
            txtHeader.Text = string.Format("{0}:", Header);
        }

        /// <summary>
        /// 添加面板
        /// </summary>
        private StackPanel AddStackPanel(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = new StackPanel();
            panelItem.Tag = rowField;
            panelItem.Margin = new Thickness(5, 1, 5, 1);
            panelItem.MinHeight = 21;
            panelItem.Orientation = Orientation.Horizontal;
            this.panelQuery.Children.Add(panelItem);
            return panelItem;
        }

        /// <summary>
        /// 添加文本编辑
        /// </summary>
        private void AddTextBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加内容控件
            TextBox txtContent = new TextBox();
            //txtContent.Style = (Style)MainApp.GetStyleResource("styleDefaultTextBox");
            txtContent.Width = 120;
            txtContent.Tag = rowField;
            txtContent.Margin = new Thickness(5, 0, 5, 0);
            txtContent.Text = rowField["DefaultValue"].ToString();
            //为特定输入框文本注册事件，用于自动搜索 tzyg add 2017-03-23
            if (rowField.Table.Columns.Contains("AutoQuery") && rowField["AutoQuery"].ToString() == "1")
            {
                txtContent.TextChanged += new TextChangedEventHandler(this.txtContent_TextChanged);
            }
            panelItem.Children.Add(txtContent);
        }

        /// <summary>
        /// 添加日期编辑
        /// </summary>
        private void AddDatePicker(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加内容控件
            DatePicker dtPicker = new DatePicker();
            dtPicker.Tag = rowField;
            dtPicker.Margin = new Thickness(5, 0, 5, 0);
            panelItem.Children.Add(dtPicker);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddCheckBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //控件定义
            CheckBox chkBox = new CheckBox();
            chkBox.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            chkBox.Tag = rowField;
            chkBox.IsThreeState = false;
            chkBox.Margin = new Thickness(10, 0, 5, 0);
            panelItem.Children.Add(chkBox);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddComboBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Tag = rowField;
            cmb.Width = 120;
            cmb.Height = 21;
            cmb.Margin = new Thickness(5, 0, 5, 0);

            cmb.DisplayMemberPath = MainApp._DatabaseType.ToLower() == "oracle" ? "NAME" : "name";
            cmb.SelectedValuePath = MainApp._DatabaseType.ToLower() == "oracle" ? "VALUE" : "value";

            cmb.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;

            cmb.SelectedIndex = -1;
            panelItem.Children.Add(cmb);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddElmentComboBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Tag = rowField;
            cmb.Width = 120;
            cmb.Height = 21;
            cmb.Margin = new Thickness(5, 0, 5, 0);
            panelItem.Children.Add(cmb);
            if (!rowField.IsNull("DataBind"))
            {
                string[] arStr = rowField["DataBind"].ToString().Split('|');
                cmb.ItemsSource = arStr;
            }
            cmb.SelectedIndex = -1;
        }

        #endregion

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void StackPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btnObj = e.OriginalSource as Button;
            if (btnObj != null)
            {
                switch (btnObj.Name)
                {
                    case "btnOK":
                        if (this.U_Query != null)
                        {
                            string strWhere = U_GetQuery();
                            this.U_Query(strWhere);
                        }
                        break;
                    case "btnClear":
                        this.Clear();
                        break;
                    case "btnAdd":
                        AddEventArgs args = new AddEventArgs() { DicDataPair = this.GetControlContent() };
                        this.OnAddButtonClicked(args);
                        break;
                }
            }
        }


        /// <summary>
        /// 外界控制按钮事件_托盘匹配时转到下架页面时使用
        /// tzyg add 2017-03-03
        /// </summary>
        public void U_ButtonClick(string stockBarcode)
        {
            string strWhere = string.Format("STOCK_BARCODE='{0}'",stockBarcode);
            this.U_Query(strWhere);
        }

        /// <summary>
        /// 获得查询条件
        /// </summary>
        public string U_GetQuery()
        {
            //清除值对
            if (dicQueryPair != null)
            {
                dicQueryPair.Clear();
            }

            StringBuilder strWhere = new StringBuilder();
            foreach (FrameworkElement element in this.panelQuery.Children)
            {
                StackPanel panelItem = element as StackPanel;
                if (panelItem != null)
                {
                    DataRow rowField = element.Tag as DataRow;
                    if (rowField != null)
                    {
                        string strColumn = rowField["Column"].ToString();
                        string strValue = string.Empty;
                        string strAppend = string.Empty;

                        //判断输入控件的控件类型
                        if (panelItem.Children[1] is CheckBox)
                        {
                            CheckBox chkBox = panelItem.Children[1] as CheckBox;
                            strValue = Convert.ToInt32(chkBox.IsChecked).ToString();

                            strAppend = string.Format("{0}='{1}'", rowField["Column"].ToString(), strValue);
                            strWhere.Append(strWhere.Length == 0 ? strAppend : " AND " + strAppend);

                            //添加到值对
                            AddDicPairValue(rowField["Column"].ToString(), strValue.ToString());
                        }
                        else if (panelItem.Children[1] is DatePicker)
                        {
                            DatePicker dtPicker = panelItem.Children[1] as DatePicker;
                            if (dtPicker.SelectedDate != null)
                            {
                                strValue = Convert.ToDateTime(dtPicker.SelectedDate).ToString("yyyy-MM-dd");
                                strAppend = GetOperationWhere(rowField, strValue);
                                strWhere.Append(strWhere.Length == 0 ? strAppend : " AND " + strAppend);

                                //添加到值对
                                AddDicPairValue(rowField["Column"].ToString(), strValue);
                            }
                        }
                        else if (panelItem.Children[1] is ComboBox)
                        {
                            ComboBox cmb = panelItem.Children[1] as ComboBox;
                            if (cmb.SelectedValue != null)
                            {
                                strValue = cmb.SelectedValue.ToString();

                                strAppend = GetOperationWhere(rowField, strValue);
                                strWhere.Append(strWhere.Length == 0 ? strAppend : " AND " + strAppend);

                                //添加到值对
                                AddDicPairValue(rowField["Column"].ToString(), strValue);
                            }
                        }
                        else if (panelItem.Children[1] is TextBox)
                        {
                            TextBox txt = panelItem.Children[1] as TextBox;
                            if (!string.IsNullOrEmpty(txt.Text))
                            {
                                strValue = txt.Text;

                                strAppend = GetOperationWhere(rowField, strValue);
                                strWhere.Append(strWhere.Length == 0 ? strAppend : " AND " + strAppend);

                                //添加到值对                                
                                AddDicPairValue(rowField["Column"].ToString(), strValue);
                            }
                        }
                    }
                }
            }
            return strWhere.ToString().TrimEnd();
        }


        /// <summary>
        /// 得到当前查询控件的输入值
        /// </summary>
        private Dictionary<string, string> GetControlContent()
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();

            foreach (FrameworkElement element in this.panelQuery.Children)
            {
                StackPanel panelItem = element as StackPanel;
                if (panelItem != null)
                {
                    DataRow rowField = element.Tag as DataRow;
                    if (rowField != null)
                    {
                        string strColumn = rowField["Column"].ToString();
                        string strValue = string.Empty;

                        //判断输入控件的控件类型
                        if (panelItem.Children[1] is CheckBox)
                        {
                            CheckBox chkBox = panelItem.Children[1] as CheckBox;
                            strValue = Convert.ToInt32(chkBox.IsChecked).ToString();

                            AddDicPairValue(ref dicResult, strColumn, strValue);
                        }
                        else if (panelItem.Children[1] is DatePicker)
                        {
                            DatePicker dtPicker = panelItem.Children[1] as DatePicker;
                            if (dtPicker.SelectedDate != null)
                            {
                                strValue = Convert.ToDateTime(dtPicker.SelectedDate).ToString("yyyy-MM-dd");

                                AddDicPairValue(ref dicResult, strColumn, strValue);
                            }
                        }
                        else if (panelItem.Children[1] is ComboBox)
                        {
                            ComboBox cmb = panelItem.Children[1] as ComboBox;
                            if (cmb.SelectedValue != null)
                            {
                                strValue = cmb.SelectedValue.ToString();

                                AddDicPairValue(ref dicResult, strColumn, strValue);
                            }
                        }
                        else if (panelItem.Children[1] is TextBox)
                        {
                            TextBox txt = panelItem.Children[1] as TextBox;
                            if (!string.IsNullOrEmpty(txt.Text))
                            {
                                strValue = txt.Text;

                                AddDicPairValue(ref dicResult, strColumn, strValue);
                            }
                        }
                    }
                }
            }

            return dicResult;
        }

        /// <summary>
        /// 添加值对
        /// </summary>
        private void AddDicPairValue(string key, string value)
        {
            if (!dicQueryPair.ContainsKey(key))
            {
                dicQueryPair.Add(key, value);
            }
            else
            {
                dicQueryPair[key] = value;
            }
        }


        /// <summary>
        /// 添加值对
        /// </summary>
        private void AddDicPairValue(ref Dictionary<string, string> dic, string key, string value)
        {
            if (!dic.ContainsKey(key))
            {
                dic.Add(key, value);
            }
            else
            {
                dic[key] = value;
            }
        }


        /// <summary>
        /// 获得查询条件
        /// </summary>
        private string GetOperationWhere(DataRow RowField, string ColumnValue)
        {
            string strWhere = string.Empty;
            string strColumn = RowField["Column"].ToString();
            string strQueryOperation = RowField["QueryOperation"].ToString().Trim().ToLower();
            if (strQueryOperation == "like")
            {
                strWhere = string.Format("{0} {1} '%{2}%'", strColumn, strQueryOperation, ColumnValue);
            }
            else
            {
                strWhere = string.Format("{0} {1} '{2}'", strColumn, strQueryOperation.Length == 0 ? "=" : strQueryOperation, ColumnValue);
            }
            return strWhere;
        }

        /// <summary>
        /// 清空重置
        /// </summary>
        private void Clear()
        {
            //清除值对
            if (dicQueryPair != null)
            {
                dicQueryPair.Clear();
            }

            foreach (FrameworkElement element in this.panelQuery.Children)
            {
                StackPanel panelItem = element as StackPanel;
                if (panelItem != null)
                {
                    //判断输入控件的控件类型
                    if (panelItem.Children[1] is CheckBox)
                    {
                        CheckBox chkBox = panelItem.Children[1] as CheckBox;
                        chkBox.IsChecked = false;
                    }
                    else if (panelItem.Children[1] is DatePicker)
                    {
                        DatePicker dtPicker = panelItem.Children[1] as DatePicker;
                        dtPicker.SelectedDate = DateTime.Now;
                    }
                    else if (panelItem.Children[1] is ComboBox)
                    {
                        ComboBox cmb = panelItem.Children[1] as ComboBox;
                        cmb.SelectedIndex = -1;
                    }
                    else if (panelItem.Children[1] is TextBox)
                    {
                        TextBox txt = panelItem.Children[1] as TextBox;
                        txt.Clear();
                    }
                }
            }
        }

        /// <summary>
        /// 添加控件
        /// </summary>
        public void U_AddControl(UIElement elem)
        {
            if (elem != null)
            {
                this.panelQuery.Children.Add(elem);
            }
        }

        /// <summary>
        /// 搜索输入框文本改变事件，用于自动搜索
        /// tzyg add 2017-03-23
        /// </summary>
        private void txtContent_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (this.U_Query != null)
            {
                string strWhere = U_GetQuery();
                this.U_Query(strWhere);
            }
        }
    }
}
