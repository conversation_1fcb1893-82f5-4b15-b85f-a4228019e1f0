﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 4.13 托盘搬运完成上报接口 【iWMS提供，SSWMS调用】
    /// </summary>
    public class PalletTransportComplete : InterfaceBase
    {
        class InputParam
        {
            public PublicHeader Header { get; set; }
            public InputDataField DataField { get; set; }
        }
        class InputDataField : PublicInputBody
        {
            public string TaskNo { get; set; }
            public string HandleFlag { get; set; }
            public string HandleMessage { get; set; }
        }

        class OutputParam
        {
            public PublicHeader Header { get; set; }
            public PublicOutputBody DataField { get; set; }
        }


        public bool IntefaceMethod(string taskNo, string handleMessage, out string message)
        {
            bool result = true;
            message = string.Empty;
            InputParam inputParam = new InputParam();

            try
            {
                if (handleMessage.StartsWith(Enum.KeyWords.Success.ToString()))
                {
                    inputParam.Header = new PublicHeader();
                    inputParam.DataField = new InputDataField()
                    {
                        SeqNo = S_Base.sBase.sDatabase.GetSequenceValue("CUSTOM_INTERFACE_SEQ", 6, "s"),
                        TaskNo = taskNo,
                        HandleFlag = handleMessage.StartsWith(Enum.KeyWords.Success.ToString()) ?
                            Enum.FLAG.P.ToString("d") : Enum.FLAG.N.ToString("d"),
                        HandleMessage = handleMessage
                    };
                }
                else
                {
                    inputParam.Header = new PublicHeader();
                    inputParam.DataField = new InputDataField()
                    {
                        SeqNo = S_Base.sBase.sDatabase.GetSequenceValue("CUSTOM_INTERFACE_SEQ", 6, "s"),
                        TaskNo = taskNo,
                        HandleFlag = Enum.FLAG.N.ToString("d"),
                        HandleMessage = handleMessage
                    };
                }

                String inputString = Common.JsonHelper.Serializer(inputParam);

                string outputString = string.Empty;
                OutputParam outputParam;
                result = this.InvokeExternal("PalletTransportComplete", inputString, out outputString);

                if (result)
                {
                    outputParam = Common.JsonHelper.Deserialize<OutputParam>(outputString);
                    if (outputParam == null)
                    {
                        result = false;
                        message = $"接口返回值[{outputString}]反序列化失败";
                        return result;
                    }

                    string token = outputParam.Header.AppToken;
                    string resultFlag = outputParam.DataField.ResultFlag;
                    string errorMessage = outputParam.DataField.ErrorMessage;

                    if (token != interfaceToken)
                    {
                        result = false;
                        message = $"接口调用对端提供的token[{token}]有误";
                        return result;
                    }
                    if (resultFlag != "1")
                    {
                        result = false;
                        message = $"接口调用对端返回失败_信息[{errorMessage}]";
                        return result;
                    }
                }
                else
                {
                    message = outputString;
                }

                if (!result)
                {
                    return result;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
            }

            return result;
        }
    }

}
