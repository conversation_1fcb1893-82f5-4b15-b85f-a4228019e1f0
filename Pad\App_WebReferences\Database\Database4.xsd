<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Enum" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:simpleType name="FLAG">
    <xs:restriction base="xs:string">
      <xs:enumeration value="P">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="N">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">0</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="FLAG" nillable="true" type="tns:FLAG" />
  <xs:simpleType name="PLAN_TYPE_GROUP">
    <xs:restriction base="xs:string">
      <xs:enumeration value="StoreGroup">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="WorkStationGroup">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="ProduceGroup">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PLAN_TYPE_GROUP" nillable="true" type="tns:PLAN_TYPE_GROUP" />
  <xs:simpleType name="PLAN_TYPE_CODE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="PlanCommonOut" />
      <xs:enumeration value="PlanCarrierOut" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PLAN_TYPE_CODE" nillable="true" type="tns:PLAN_TYPE_CODE" />
  <xs:simpleType name="PLAN_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Waiting" />
      <xs:enumeration value="Executing" />
      <xs:enumeration value="Complete" />
      <xs:enumeration value="WaitConfirm" />
      <xs:enumeration value="Pause" />
      <xs:enumeration value="Cancel" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PLAN_STATUS" nillable="true" type="tns:PLAN_STATUS" />
  <xs:simpleType name="PLAN_INOUT">
    <xs:restriction base="xs:string">
      <xs:enumeration value="In">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Out">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Move">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Sort">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="PLAN_INOUT" nillable="true" type="tns:PLAN_INOUT" />
  <xs:simpleType name="MANAGE_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ManageIn" />
      <xs:enumeration value="ManageOut" />
      <xs:enumeration value="ManageUp" />
      <xs:enumeration value="ManageDown" />
      <xs:enumeration value="ManageMove" />
      <xs:enumeration value="ManagePick" />
      <xs:enumeration value="ManageTransport" />
      <xs:enumeration value="ManageMoveLaneway" />
      <xs:enumeration value="ManageAdjust" />
      <xs:enumeration value="ManageAdjustIn" />
      <xs:enumeration value="ManageAdjustOut" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MANAGE_TYPE" nillable="true" type="tns:MANAGE_TYPE" />
  <xs:simpleType name="MANAGE_TYPE_INOUT">
    <xs:restriction base="xs:string">
      <xs:enumeration value="In">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Out">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Move">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Transport">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MANAGE_TYPE_INOUT" nillable="true" type="tns:MANAGE_TYPE_INOUT" />
  <xs:simpleType name="MANAGE_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Waiting" />
      <xs:enumeration value="Picking" />
      <xs:enumeration value="Picked" />
      <xs:enumeration value="Executing" />
      <xs:enumeration value="WaitPick" />
      <xs:enumeration value="WaitConfirm" />
      <xs:enumeration value="Complete" />
      <xs:enumeration value="ExceptionComplete" />
      <xs:enumeration value="Error" />
      <xs:enumeration value="Cancel" />
      <xs:enumeration value="Pause" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MANAGE_STATUS" nillable="true" type="tns:MANAGE_STATUS" />
  <xs:simpleType name="CONTROL_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Wait" />
      <xs:enumeration value="Pause">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">5</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Control_Readed">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">7</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Runing">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">10</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="DeviceRuning">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">11</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="AGVRuning">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">12</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="LterRouteApply">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">30</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="LterRouteReplay">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">40</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="TaskAbend">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">990</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="TaskDelete">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">900</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="EmptyOutPut">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">980</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="RepeatInput">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">970</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Finish">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">999</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="TempStatus">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">-7</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CONTROL_STATUS" nillable="true" type="tns:CONTROL_STATUS" />
  <xs:simpleType name="CONTROL_APPLY_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Waiting" />
      <xs:enumeration value="Read" />
      <xs:enumeration value="Finish" />
      <xs:enumeration value="Error" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CONTROL_APPLY_STATUS" nillable="true" type="tns:CONTROL_APPLY_STATUS" />
  <xs:simpleType name="CONTROL_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="In">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Out">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Move">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="MoveStation">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">4</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CONTROL_TYPE" nillable="true" type="tns:CONTROL_TYPE" />
  <xs:simpleType name="AREA_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="LiKu" />
      <xs:enumeration value="XuNiKu" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AREA_TYPE" nillable="true" type="tns:AREA_TYPE" />
  <xs:simpleType name="AREA_GROUP">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Manual">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AREA_GROUP" nillable="true" type="tns:AREA_GROUP" />
  <xs:simpleType name="CELL_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Cell" />
      <xs:enumeration value="Station" />
      <xs:enumeration value="Tank" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CELL_TYPE" nillable="true" type="tns:CELL_TYPE" />
  <xs:simpleType name="CELL_STORAGE_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Single" />
      <xs:enumeration value="Multiple" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CELL_STORAGE_TYPE" nillable="true" type="tns:CELL_STORAGE_TYPE" />
  <xs:simpleType name="SHELF_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Single">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="DoubleIn">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="DoubleOut">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="SHELF_TYPE" nillable="true" type="tns:SHELF_TYPE" />
  <xs:simpleType name="CELL_FORK_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Normal" />
      <xs:enumeration value="Double" />
      <xs:enumeration value="Multi" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CELL_FORK_TYPE" nillable="true" type="tns:CELL_FORK_TYPE" />
  <xs:simpleType name="CELL_INOUT">
    <xs:restriction base="xs:string">
      <xs:enumeration value="In">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Out">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="InOut">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CELL_INOUT" nillable="true" type="tns:CELL_INOUT" />
  <xs:simpleType name="CELL_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Full" />
      <xs:enumeration value="Have" />
      <xs:enumeration value="Nohave" />
      <xs:enumeration value="Pallet" />
      <xs:enumeration value="Exception" />
      <xs:enumeration value="Forbiden" />
      <xs:enumeration value="Remove" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CELL_STATUS" nillable="true" type="tns:CELL_STATUS" />
  <xs:simpleType name="RUN_STATUS">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Disable" />
      <xs:enumeration value="Enable" />
      <xs:enumeration value="Run" />
      <xs:enumeration value="Selected" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="RUN_STATUS" nillable="true" type="tns:RUN_STATUS" />
  <xs:simpleType name="DEVICE_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="system" />
      <xs:enumeration value="auto" />
      <xs:enumeration value="agv" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DEVICE_TYPE" nillable="true" type="tns:DEVICE_TYPE" />
  <xs:simpleType name="LogLevel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="DEBUG" />
      <xs:enumeration value="INFO" />
      <xs:enumeration value="WARN" />
      <xs:enumeration value="ERROR" />
      <xs:enumeration value="FATAL" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="LogLevel" nillable="true" type="tns:LogLevel" />
  <xs:simpleType name="LogType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="record" />
      <xs:enumeration value="alert" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="LogType" nillable="true" type="tns:LogType" />
  <xs:simpleType name="CellModel">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Stock" />
      <xs:enumeration value="Stack" />
      <xs:enumeration value="DoubleStack" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CellModel" nillable="true" type="tns:CellModel" />
  <xs:simpleType name="SelectLanewayOrderType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="CellFirst" />
      <xs:enumeration value="ManageFirst" />
      <xs:enumeration value="StorageFirst" />
      <xs:enumeration value="PairFirst" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="SelectLanewayOrderType" nillable="true" type="tns:SelectLanewayOrderType" />
  <xs:simpleType name="SystemName">
    <xs:restriction base="xs:string">
      <xs:enumeration value="SSWMS" />
      <xs:enumeration value="iWMS" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="SystemName" nillable="true" type="tns:SystemName" />
  <xs:simpleType name="AreaCode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Product" />
      <xs:enumeration value="Battery" />
      <xs:enumeration value="ZCQ" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AreaCode" nillable="true" type="tns:AreaCode" />
  <xs:simpleType name="KeyWords">
    <xs:restriction base="xs:string">
      <xs:enumeration value="AllotCell" />
      <xs:enumeration value="ByList" />
      <xs:enumeration value="Success" />
      <xs:enumeration value="Fail" />
      <xs:enumeration value="emptyPallet" />
      <xs:enumeration value="AGVExceptStation" />
      <xs:enumeration value="扫码申请" />
      <xs:enumeration value="外层自动倒库" />
      <xs:enumeration value="管理申请" />
      <xs:enumeration value="接口下达" />
      <xs:enumeration value="自动补空托盘" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="KeyWords" nillable="true" type="tns:KeyWords" />
  <xs:simpleType name="WarehouseID">
    <xs:restriction base="xs:string">
      <xs:enumeration value="成品库ID">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="分拣库ID">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="双伸库ID">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="WarehouseID" nillable="true" type="tns:WarehouseID" />
  <xs:simpleType name="AreaID">
    <xs:restriction base="xs:string">
      <xs:enumeration value="立库">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="素电立库区">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="成品站台区">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">5</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="素电站台区">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">7</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="AreaID" nillable="true" type="tns:AreaID" />
  <xs:simpleType name="MatchMode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="IgnoreNull" />
      <xs:enumeration value="ConsiderKey" />
      <xs:enumeration value="FullMatch" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MatchMode" nillable="true" type="tns:MatchMode" />
  <xs:simpleType name="DISPLAY_TYPE">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ByFlag" />
      <xs:enumeration value="ByLifeTime" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="DISPLAY_TYPE" nillable="true" type="tns:DISPLAY_TYPE" />
  <xs:simpleType name="MessageConverter">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Null" />
      <xs:enumeration value="Type" />
      <xs:enumeration value="Length" />
      <xs:enumeration value="Weight" />
      <xs:enumeration value="Input" />
      <xs:enumeration value="Data" />
      <xs:enumeration value="Exists" />
      <xs:enumeration value="Save" />
      <xs:enumeration value="Delete" />
      <xs:enumeration value="Cancel" />
      <xs:enumeration value="Print" />
      <xs:enumeration value="Close" />
      <xs:enumeration value="Exit" />
      <xs:enumeration value="CheckStack" />
      <xs:enumeration value="CheckStorage" />
      <xs:enumeration value="CheckTechnicsType" />
      <xs:enumeration value="CheckStoragePosition" />
      <xs:enumeration value="CheckCellModel" />
      <xs:enumeration value="CheckOccupyPercent" />
      <xs:enumeration value="CheckStockBarCode" />
      <xs:enumeration value="CheckStockBarCodeArea" />
      <xs:enumeration value="CheckStackNo" />
      <xs:enumeration value="CheckWareHouse" />
      <xs:enumeration value="CheckArea" />
      <xs:enumeration value="CheckLaneway" />
      <xs:enumeration value="CheckStartArea" />
      <xs:enumeration value="CheckEndArea" />
      <xs:enumeration value="CheckPosition" />
      <xs:enumeration value="CheckStartPosition" />
      <xs:enumeration value="CheckEndPosition" />
      <xs:enumeration value="CheckStationCode" />
      <xs:enumeration value="CheckStationDeviceCode" />
      <xs:enumeration value="CheckLoginUser" />
      <xs:enumeration value="CheckAssisQuantity" />
      <xs:enumeration value="CheckBoxNo" />
      <xs:enumeration value="ConfirmAssembly" />
      <xs:enumeration value="ConfirmExecute" />
      <xs:enumeration value="ConfirmCreateTask" />
      <xs:enumeration value="ConfirmCreatePlan" />
      <xs:enumeration value="SelectCount" />
      <xs:enumeration value="AllowSelectOneOnly" />
      <xs:enumeration value="AffectCount" />
      <xs:enumeration value="FailRollBack" />
      <xs:enumeration value="TaskComplete" />
      <xs:enumeration value="CheckProType" />
      <xs:enumeration value="IsContinue" />
      <xs:enumeration value="PrintExcel" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="MessageConverter" nillable="true" type="tns:MessageConverter" />
</xs:schema>