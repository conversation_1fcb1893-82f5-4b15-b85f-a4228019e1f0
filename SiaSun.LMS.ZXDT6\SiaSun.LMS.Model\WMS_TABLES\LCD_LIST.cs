﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     
 *       日期：     2020/11/7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// LCD_LIST 
    /// </summary>
    [Serializable]
	[DataContract]
	public class LCD_LIST
	{
		public LCD_LIST()
		{

		}

		private System.Decimal _lcd_list_id;
		private System.Decimal _lcd_id;
		private string _display_type;
		private string _display_flag;
		private string _write_time;
		private string _life_time;
		private string _handle_flag;
		private string _display_info1;
		private string _display_info2;
		private string _display_info3;
		private string _display_info4;
		private string _display_info5;
		private string _display_info6;
		private string _display_info7;
		private string _display_info8;
		private string _display_info9;
		private string _display_info10;
		private string _display_info11;
		private string _display_info12;
		private string _display_info13;
		private string _display_info14;
		private string _display_info15;
		private string _display_info16;
		private string _display_info17;
		private string _display_info18;
		private string _display_info19;
		private string _display_info20;
		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;

		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public System.Decimal LCD_LIST_ID
		{
			get { return _lcd_list_id; }
			set { _lcd_list_id = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public System.Decimal LCD_ID
		{
			get { return _lcd_id; }
			set { _lcd_id = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_TYPE
		{
			get { return _display_type; }
			set { _display_type = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_FLAG
		{
			get { return _display_flag; }
			set { _display_flag = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string WRITE_TIME
		{
			get { return _write_time; }
			set { _write_time = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string LIFE_TIME
		{
			get { return _life_time; }
			set { _life_time = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string HANDLE_FLAG
		{
			get { return _handle_flag; }
			set { _handle_flag = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO1
		{
			get { return _display_info1; }
			set { _display_info1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO2
		{
			get { return _display_info2; }
			set { _display_info2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO3
		{
			get { return _display_info3; }
			set { _display_info3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO4
		{
			get { return _display_info4; }
			set { _display_info4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO5
		{
			get { return _display_info5; }
			set { _display_info5 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO6
		{
			get { return _display_info6; }
			set { _display_info6 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO7
		{
			get { return _display_info7; }
			set { _display_info7 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO8
		{
			get { return _display_info8; }
			set { _display_info8 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO9
		{
			get { return _display_info9; }
			set { _display_info9 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO10
		{
			get { return _display_info10; }
			set { _display_info10 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO11
		{
			get { return _display_info11; }
			set { _display_info11 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO12
		{
			get { return _display_info12; }
			set { _display_info12 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO13
		{
			get { return _display_info13; }
			set { _display_info13 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO14
		{
			get { return _display_info14; }
			set { _display_info14 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO15
		{
			get { return _display_info15; }
			set { _display_info15 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO16
		{
			get { return _display_info16; }
			set { _display_info16 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO17
		{
			get { return _display_info17; }
			set { _display_info17 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO18
		{
			get { return _display_info18; }
			set { _display_info18 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO19
		{
			get { return _display_info19; }
			set { _display_info19 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string DISPLAY_INFO20
		{
			get { return _display_info20; }
			set { _display_info20 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
