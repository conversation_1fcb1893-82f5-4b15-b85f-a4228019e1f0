﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.APP_CONFIG"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="APP_CONFIG" Height="300" Width="300" >
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="70"></RowDefinition>
        </Grid.RowDefinitions>
        <GroupBox Grid.Row="0" Header="App信息配置-直接添加或Del删除App配置信息" >
            <uc:DataGridTemplate x:Name="gridApp" Margin="1,2,1,1" AutoGenerateColumns="True"></uc:DataGridTemplate>
        </GroupBox>
        <GroupBox Grid.Row="1" Header="操作区" >
            <Border >
                <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Button Name="btnSave" Margin="5" Width="60"  Content="保存(_S)" Click="btnSave_Click"></Button>
                    <Button Name="btnUpdate" Margin="5" Width="60"  Content="刷新(_U)" Click="btnUpdate_Click"></Button>
                </WrapPanel>
            </Border>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
