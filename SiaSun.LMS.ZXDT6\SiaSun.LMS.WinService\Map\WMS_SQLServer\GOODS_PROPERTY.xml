﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="GOODS_PROPERTY" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="GOODS_PROPERTY" type="SiaSun.LMS.Model.GOODS_PROPERTY, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_PROPERTY">
			<result property="GOODS_PROPERTY_ID" column="goods_property_id" />
			<result property="GOODS_TYPE_ID" column="goods_type_id" />
			<result property="GOODS_PROPERTY_CODE" column="goods_property_code" />
			<result property="GOODS_PROPERTY_NAME" column="goods_property_name" />
			<result property="GOODS_PROPERTY_DATASOURCE" column="goods_property_datasource" />
			<result property="GOODS_PROPERTY_FIELD" column="goods_property_field" />
			<result property="GOODS_PROPERTY_FIELDTYPE" column="goods_property_fieldtype" />
			<result property="GOODS_PROPERTY_ORDER" column="goods_property_order" />
			<result property="GOODS_PROPERTY_VALID" column="goods_property_valid" />
			<result property="GOODS_PROPERTY_FLAG" column="goods_property_flag" />
			<result property="GOODS_PROPERTY_KEYFLAG" column="goods_property_keyflag" />
			<result property="BACKUP_FIELD1" column="backup_field1" />
			<result property="BACKUP_FIELD2" column="backup_field2" />
			<result property="BACKUP_FIELD3" column="backup_field3" />
			<result property="BACKUP_FIELD4" column="backup_field4" />
			<result property="BACKUP_FIELD5" column="backup_field5" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="GOODS_PROPERTY_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			goods_property_id,
			goods_type_id,
			goods_property_code,
			goods_property_name,
			goods_property_datasource,
			goods_property_field,
			goods_property_fieldtype,
			goods_property_order,
			goods_property_valid,
			goods_property_flag,
			goods_property_keyflag,
			backup_field1,
			backup_field2,
			backup_field3,
			backup_field4,
			backup_field5
			From GOODS_PROPERTY
		</select>

		<select id="GOODS_PROPERTY_SELECT_BY_ID" parameterClass="int" extends = "GOODS_PROPERTY_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_property_id=#GOODS_PROPERTY_ID#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="GOODS_PROPERTY_SELECT_GOODS_TYPE_ID" parameterClass="int" extends = "GOODS_PROPERTY_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_type_id=#GOODS_TYPE_ID# and goods_property_flag ='1' ORDER BY GOODS_PROPERTY_ORDER
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="GOODS_PROPERTY_INSERT" parameterClass="GOODS_PROPERTY">
			Insert Into GOODS_PROPERTY (
			<!--goods_property_id,-->
			goods_type_id,
			goods_property_code,
			goods_property_name,
			goods_property_datasource,
			goods_property_field,
			goods_property_fieldtype,
			goods_property_order,
			goods_property_valid,
			goods_property_flag,
			goods_property_keyflag,
			backup_field1,
			backup_field2,
			backup_field3,
			backup_field4,
			backup_field5
			)Values(
			<!--#GOODS_PROPERTY_ID#,-->
			#GOODS_TYPE_ID#,
			#GOODS_PROPERTY_CODE#,
			#GOODS_PROPERTY_NAME#,
			#GOODS_PROPERTY_DATASOURCE#,
			#GOODS_PROPERTY_FIELD#,
			#GOODS_PROPERTY_FIELDTYPE#,
			#GOODS_PROPERTY_ORDER#,
			#GOODS_PROPERTY_VALID#,
			#GOODS_PROPERTY_FLAG#,
			#BACKUP_FIELD1#,
			#BACKUP_FIELD2#,
			#BACKUP_FIELD3#,
			#BACKUP_FIELD4#,
			#BACKUP_FIELD5#
			#GOODS_PROPERTY_KEYFLAG#
			)
			<selectKey  resultClass="int" type="post" property="GOODS_PROPERTY_ID">
				select @@IDENTITY as value
			</selectKey>
		</insert>

		<update id="GOODS_PROPERTY_UPDATE" parameterClass="GOODS_PROPERTY">
			Update GOODS_PROPERTY Set
			<!--goods_property_id=#GOODS_PROPERTY_ID#,-->
			goods_type_id=#GOODS_TYPE_ID#,
			goods_property_code=#GOODS_PROPERTY_CODE#,
			goods_property_name=#GOODS_PROPERTY_NAME#,
			goods_property_datasource=#GOODS_PROPERTY_DATASOURCE#,
			goods_property_field=#GOODS_PROPERTY_FIELD#,
			goods_property_fieldtype=#GOODS_PROPERTY_FIELDTYPE#,
			goods_property_order=#GOODS_PROPERTY_ORDER#,
			goods_property_valid=#GOODS_PROPERTY_VALID#,
			goods_property_flag=#GOODS_PROPERTY_FLAG#,
			goods_property_keyflag=#GOODS_PROPERTY_KEYFLAG#,
			backup_field1=#BACKUP_FIELD1#,
			backup_field2=#BACKUP_FIELD2#,
			backup_field3=#BACKUP_FIELD3#,
			backup_field4=#BACKUP_FIELD4#,
			backup_field5=#BACKUP_FIELD5#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_property_id=#GOODS_PROPERTY_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="GOODS_PROPERTY_DELETE" parameterClass="int">
			Delete From GOODS_PROPERTY
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_property_id=#GOODS_PROPERTY_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
	</statements>
</sqlMap>