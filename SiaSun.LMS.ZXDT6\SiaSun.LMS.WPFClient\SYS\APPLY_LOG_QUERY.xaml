﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.SYS.APPLY_LOG_QUERY"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="APPLY_LOG_QUERY"
    Width="300"
    Height="300"
    Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <!--<WrapPanel Name="panelQuery" Grid.Row="0" HorizontalAlignment="Left"  Visibility="Visible" ButtonBase.Click="panelQuery_Click">
            <Label>申请类型：</Label>
            <TextBox x:Name="tbxApplyType" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>库房编码：</Label>
            <TextBox x:Name="tbxWhCode" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>托盘条码：</Label>
            <TextBox x:Name="tbxBarcode" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>申请参数：</Label>
            <TextBox x:Name="tbxPara" Width="80" Margin="0,0,20,0"></TextBox>

            <Button Name="btnQuery" Width="50" Margin="8" >查询</Button>
            <Button Name="btnReset" Width="50" Margin="8" >重置</Button>
        </WrapPanel>-->

        <uc:ucQuickQuery
            x:Name="ucQueryControl"
            Grid.Row="0"
            Grid.ColumnSpan="2"
            Margin="1,1,1,3"
            BorderBrush="Black" />

        <GroupBox
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="1,5,1,1"
            Header="申请历史列表">
            <uc:ucCommonDataGrid x:Name="ucApplyDataGrid" Margin="1" />
        </GroupBox>

        <GroupBox
            Grid.Row="2"
            Grid.ColumnSpan="2"
            Margin="1,5,1,1"
            Header="操作区">
            <Border>
                <WrapPanel
                    Name="panelButton"
                    HorizontalAlignment="Center"
                    ButtonBase.Click="WrapPanel_Click"
                    Visibility="Visible">
                    <!--<Button Name="btnApplyAgain"  Width="80" Margin="8" >再次申请</Button>-->
                </WrapPanel>
            </Border>
        </GroupBox>

    </Grid>
</ad:DocumentContent>
