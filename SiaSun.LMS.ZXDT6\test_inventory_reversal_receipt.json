{"redStorageType": 1, "initId": "init_001", "buyOrderCode": "PO_001", "contractCode": "CONTRACT_001", "reason": "质量问题", "warehouseName": "主仓库", "warehouseId": "WH_001", "storageDate": "2025-01-30 10:00:00", "sourceUserName": "张三", "sourceUserId": "USER_001", "sourceUnitName": "供应商A", "sourceUnitId": "SUPPLIER_001", "operatorName": "李四", "operatorId": "OPERATOR_001", "storageOperatorName": "王五", "storageOperatorId": "STORAGE_OP_001", "storageCode": "STORAGE_001", "storageName": "入库单001", "storageGoodsSource": "1", "money": 1000.0, "taxMoney": 1130.0, "redStorageName": "红冲单001", "redStorageDetailList": [{"reason": "质量不合格", "lineId": "LINE_001", "lineName": "生产线1", "orderIndex": "1", "initId": "detail_init_001", "tax": 0.13, "goodsType": "原材料", "goodsVersion": "V1.0", "redStorageId": "RED_001", "remark": "红冲备注", "localSend": "否", "gkDeptName": "技术部", "gkDeptId": "DEPT_001", "manageDeptName": "管理部", "manageDeptId": "MGMT_001", "batch": "BATCH_001", "gbName": "工班1", "gbId": "GB_001", "bzName": "班组1", "bzId": "BZ_001", "deptName": "生产部", "deptId": "PROD_001", "orgName": "公司A", "orgId": "ORG_001", "zbCycle": "30天", "produceDate": "2025-01-01", "shelfName": "货架A", "shelfId": "SHELF_001", "warehouseName": "主仓库", "warehouseId": "WH_001", "taxAllPrice": 1130.0, "taxPrice": 113.0, "brand": "品牌A", "unitName": "个", "unitId": "UNIT_001", "redStorageNum": 5, "storageNum": 10, "goodsName": "测试物料", "goodsCode": "GOODS_001", "goodsId": "GOODS_ID_001", "storageType": "正常入库", "createDate": "2025-01-30 10:00:00", "createUser": "USER_001", "createName": "创建人", "updateDate": "2025-01-30 10:00:00", "updateUser": "USER_001", "updateName": "更新人", "id": "DETAIL_001", "status": 1, "billCode": "BILL_001"}], "createDate": "2025-01-30 10:00:00", "createUser": "USER_001", "createName": "创建人", "updateDate": "2025-01-30 10:00:00", "updateUser": "USER_001", "updateName": "更新人", "id": "MAIN_001", "status": 1, "billCode": "MAIN_BILL_001"}