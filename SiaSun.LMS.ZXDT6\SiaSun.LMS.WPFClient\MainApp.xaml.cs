﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Windows;
using System.Net;
using System.Xml;

using SiaSun.LMS.Interface;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// MainApp.xaml 的交互逻辑
    /// </summary>
    public partial class MainApp : Application
    {
        //本地样式文件路径
        public static string File_FieldDescription_Path = null;
        public static string File_FormStyles_Path = null;

        //本机地址
        public static IPAddress[] _IPAddressList;
        //应用程序路径
        public static string _APP_PATH;
        //服务地址
        public static string _BaseUrl;
        //语言
        public static string _Language;
        //数据库类型
        public static string _DatabaseType;
        //用户角色
        public static Model.SYS_ROLE _ROLE;
        //用户
        public static Model.SYS_USER _USER;
        //通用对话框
        public static Dialog.MessageDialog _MessageDialog;

        #region     业务接口定义

        static I_System _I_SystemService;
        public static I_System I_SystemService
        {
            get { return _I_SystemService; }
        }

        static I_Plan _I_PlanService;
        public static I_Plan I_PlanService
        {
            get { return _I_PlanService; }
        }

        static I_Manage _I_ManageService;
        public static I_Manage I_ManageService
        {
            get { return _I_ManageService; }
        }

        static I_Device _I_DeviceService;
        public static I_Device I_DeviceService
        {
            get { return _I_DeviceService; }
        }

        static I_Database _I_DatabaseService;
        public static I_Database I_DatabaseService
        {
            get { return _I_DatabaseService; }
        }

        #endregion

        /// <summary>
        /// 动态执行
        /// </summary>
        public static bool EVENT_Execute(string sEvent, out  string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Common.Compiler cp = new SiaSun.LMS.Common.Compiler();

            //-------------------------------加载SiaSun.LMS组件--------------------------------------------//
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.WPFClient.exe", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Model.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Interface.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Common.dll", _APP_PATH));

            ////-------------------------------加载布局组件--------------------------------------------//
            cp.lsRef.Add(string.Format("{0}/AvalonDock.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/Fluent.dll", _APP_PATH));

            //-------------------------------加载WPF组件--------------------------------------------//
            cp.lsRef.Add("System.Xaml.dll");
            cp.lsRef.Add(string.Format("{0}/@Dll/PresentationCore.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/@Dll/PresentationFramework.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/@Dll/WindowsBase.dll", _APP_PATH));


            //-------------------------------加添加程序集的引用--------------------------------------------//
            cp.lsUsing.Add("SiaSun.LMS.WPFClient");
            cp.lsUsing.Add("AvalonDock");
            cp.lsUsing.Add("Fluent");

            cp.strCode = sEvent;
            bResult = cp.Excute(out sResult);
            return bResult;
        }
        
        /// <summary>
        /// 获得样式表中定义的对象
        /// </summary>
        /// <returns></returns>
        public static object GetStyleResource(string Key)
        {
            return MainApp.Current.Resources[Key];
        }

        /// <summary>
        /// 启动项目
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                _Language = Common.AppSettings.GetValue("Language");
                _BaseUrl = Common.StringUtil.GetConfig("SiaSunSrvUrl");
                _APP_PATH = AppDomain.CurrentDomain.BaseDirectory;
                _IPAddressList = Dns.GetHostAddresses(Dns.GetHostName());
                _DatabaseType = Common.StringUtil.GetConfig("DatabaseType");

                _I_SystemService = Common.WCFHelper.Create<I_System>(string.Format(_BaseUrl + "/System"));
                _I_PlanService = Common.WCFHelper.Create<I_Plan>(string.Format(_BaseUrl + "/Plan"));
                _I_ManageService = Common.WCFHelper.Create<I_Manage>(string.Format(_BaseUrl + "/Manage"));
                _I_DeviceService = Common.WCFHelper.Create<I_Device>(string.Format(_BaseUrl + "/Device"));
                _I_DatabaseService = Common.WCFHelper.Create<I_Database>(string.Format(_BaseUrl + "/Database"));

                //获得文件存储的路径
                if (System.IO.File.Exists(_APP_PATH + string.Format(@"../../@Files\{0}\FieldDescription.xml", _Language)))
                {
                    File_FieldDescription_Path = _APP_PATH + string.Format(@"../../@Files\{0}\FieldDescription.xml", _Language);
                }
                else
                {
                    File_FieldDescription_Path = _APP_PATH + string.Format(@"@Files\{0}\FieldDescription.xml", _Language);
                }
                if (System.IO.File.Exists(_APP_PATH + string.Format(@"../../@Files\{0}\FormStyles.xml", _Language)))
                {
                    File_FormStyles_Path = _APP_PATH + string.Format(@"../../@Files\{0}\FormStyles.xml", _Language);
                }
                else
                {
                    File_FormStyles_Path = _APP_PATH + string.Format(@"@Files\{0}\FormStyles.xml", _Language);
                }

                _USER = new SiaSun.LMS.Model.SYS_USER();
                _MessageDialog = new Dialog.MessageDialog();

                base.OnStartup(e);
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("客户端初始化运行参数时发生异常 {0}",ex.Message));
            }

        }


        public void Application_Startup(object sender, StartupEventArgs e)
        {
            Application currApp = Application.Current;
            currApp.StartupUri = new Uri("Login.xaml", UriKind.RelativeOrAbsolute);
        }


    }
}
