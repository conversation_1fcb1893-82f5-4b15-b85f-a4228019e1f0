﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_OUT_LOG"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="MANAGE_OUT_LOG" Height="300" Width="300"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>     

        <uc:ucQuickQuery x:Name="ucQueryControl" Margin="1,1,1,3" Grid.Row="0" BorderBrush="Black" Grid.ColumnSpan="2"></uc:ucQuickQuery>

        <GroupBox Header="匹配列表"  Margin="1,5,1,1" Grid.Row="1" Grid.ColumnSpan="2">
            <uc:ucCommonDataGrid x:Name="ucManageOutDataGrid" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>

        <GroupBox Header="操作区" Margin="1,5,1,1" Grid.Row="2" Grid.ColumnSpan="2">
            <Border>
                <WrapPanel Name="panelButton" HorizontalAlignment="Center" Visibility="Visible" ButtonBase.Click="WrapPanel_Click">
                    <Button Name="btnStockDown" Width="80" Margin="8" >托盘下架</Button>
                </WrapPanel>
            </Border>
        </GroupBox>

    </Grid>
</ad:DocumentContent>
