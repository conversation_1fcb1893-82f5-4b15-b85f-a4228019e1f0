﻿/***************************************************************************
 * 
 *       功能：     存储区持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_LOGIC
	/// </summary>
	public class P_WH_LOGIC : P_Base_House
	{
		public P_WH_LOGIC ()
		{
			//
			// TODO: 此处添加WH_LOGIC的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_LOGIC> GetList()
		{
			return ExecuteQueryForList<WH_LOGIC>("WH_LOGIC_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_LOGIC wh_logic)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("WH_LOGIC");
                wh_logic.LOGIC_ID = id;
            }

            return ExecuteInsert("WH_LOGIC_INSERT",wh_logic);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_LOGIC wh_logic)
		{
			return ExecuteUpdate("WH_LOGIC_UPDATE",wh_logic);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_LOGIC GetModel(System.Int32 LOGIC_ID)
		{
			return ExecuteQueryForObject<WH_LOGIC>("WH_LOGIC_SELECT_BY_ID",LOGIC_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 LOGIC_ID)
		{
			return ExecuteDelete("WH_LOGIC_DELETE",LOGIC_ID);
		}
		

	}
}
