﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="StackerCrane4.aspx.cs" Inherits="StackerCrane4" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>徐州地铁6号线仓储任务看板</title>  
        <meta http-equiv="refresh" content="1800;url=Manage2.aspx" />
        <script src="Scripts/echarts.js" type="text/javascript"   charset="utf-8"></script>
        <script type="text/javascript" src="Scripts/dark.js"></script>
        <script src="Scripts/theme2.js" type="text/javascript"></script>
        <script src="Scripts/theme1.js" type="text/javascript"></script>
        <link href="css/BackGround.css" rel="stylesheet" type="text/css" />
        <style type="text/css">
            .style1
            {
                width: 357px;
                height: 100px;
            }
            .style2
            {
                width: 277px;
                height: 30px;
            }
        </style> 
        <style type="text/css">
            body,ul,li,a,p,div {padding:0px;margin:0px;font-size:15px;}
            ul,li{list-style:none;}
            a{ text-decoration:none;color:#333;}
            #roll
            {
                overflow:hidden;
                width:100%;
                height:315px;
            }
            .style3
            {
                height: 10px;
            }
        </style>
        <style type="text/css">
                       body,ul,li,a,p,div {padding:0px;margin:0px;font-size:15px;}
            ul,li{list-style:none;}
            a{ text-decoration:none;color:#333;}
            #roll
            {
                overflow:hidden;
                width:100%;
                 height:315px;
            }
           
        .style3
        {
            height: 10px;
        }
        </style>
    </head>
    <body style="height: 100%;  background-color:white ; background-repeat:no-repeat; background-size:100%; margin: 0px; width: 100%">
        <form id="form1" runat="server" class="linear"  style="font-family: 微软雅黑;border:0 solid #333333;">  
            <asp:ScriptManager ID="ScriptManager1" runat="server"/>
            <table style="width: 100%; height:85%">
                <tr>
                    <td align="center" style="font-size:30px;font-weight:bolder;width:100%; color:orange">徐州地铁六号线仓储任务看板</td>  
                    <td style="width:20%;"> 
                        <%--<img alt="" align="right" width="200px"/>--%>
                    </td>  
                </tr>
                <tr>
                    <td id="Div11" style="border:0 solid #333333;width: 50%; height:100%" valign="bottom" colspan="2">
                    <asp:UpdatePanel style="height:100%" ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                        <ContentTemplate>
                            <asp:Timer ID="Timer1" runat="server" Interval="5000" ontick="Timer_Tick" 
                                        Enabled="true" >
                            </asp:Timer>  
                            <div style="width: 100%;height:100%; ">
                                <table id="tb_example"  align="center" style="width: 100%; height:5%;color:White; font-size:25px;background-color:orange;border-top-left-radius:15px; border-top-right-radius:15px;">
                                </table>  
                                <div style="width: 100%;height:90%;background-color:navajowhite;border-bottom-left-radius:15px; border-bottom-right-radius:15px;">
                                    <table style="width: 100%;height:80%;" border="0">
                                        <tr style="width: 100%;height:5%">                         
                                            <td style="border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange ">当前巷道任务列表</td>                                         
                                        </tr>
                                        <tr  style=" font-size:25px;width:100%;height:5%;">
                                            <td colspan="3">
                                                <table style="width: 100%;height:100%;">
                                                    <tr>                    
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange" >容器条码</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">起始位置</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">终点位置</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">数量</td>
                                                        <td align="center" style="width: 25%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange" >物料名称</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange" >物料编码</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr style="width: 100%;height:70%">
                                            <td>
                                                <div style="width: 100%;height:90%">
                                                <div id="roll">
                                                    <ul>   
                                                        <asp:GridView ID="managelist" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false" AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
                                                            <Columns>
                                                            <asp:BoundField>
                                                                <ItemStyle HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="15px" />
                                                            </asp:BoundField> 
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                                                <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField>  
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("CELL_IN_NAME")%></ItemTemplate>
                                                                <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField>   
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("CELL_OUT_NAME")%></ItemTemplate>
                                                                <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField>   
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("MANAGE_LIST_QUANTITY")%></ItemTemplate>
                                                                <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField>   
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("GOODS_NAME")%></ItemTemplate>
                                                                <ItemStyle Width="25%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField> 
                                                            <asp:TemplateField>
                                                                <ItemTemplate><%#Eval("GOODS_CODE")%></ItemTemplate>
                                                                <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                            </asp:TemplateField>
                                                            </Columns>
                                                            <HeaderStyle Height="0px" />
                                                        </asp:GridView>
                                                    </ul>
                                                    <script type="text/javascript">
                                                        init_scrolltext();
                                                        function init_scrolltext() {
                                                            var demo = document.getElementById("roll");
                                                            demo.scrollTop = 0;
                                                            setInterval('scrollup()', 50);
                                                        }
                                                        function scrollup() {
                                                            var demo = document.getElementById("roll");
                                                            var demo2 = document.getElementById("managelist1");
                                                            var demo1 = document.getElementById("managelist");
                                                            if (demo1 != null && demo2 != null) {
                                                                if (demo2.offsetTop - demo.scrollTop <= 0)
                                                                    demo.scrollTop -= demo1.offsetHeight;
                                                                else {
                                                                    var t = demo.scrollTop;
                                                                    demo.scrollTop++;
                                                                    if (t == demo.scrollTop) {
                                                                        if (demo.scrollTop >= demo1.offsetHeight) {
                                                                            demo.scrollTop -= demo1.offsetHeight;
                                                                        }
                                                                        else {
                                                                            demo.scrollTop = 0;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }</script>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>   
                                    

                                    <table style="width: 100%;height:20%;" border="0">
                                        <tr style="width: 100%;height:5%">                         
                                            <td style="border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange ">当前巷道任务回退错误信息显示</td>                                         
                                        </tr>
                                        <tr  style=" font-size:25px;width:100%;height:5%;">
                                            <td colspan="3">
                                                <table style="width: 100%;height:100%;">
                                                    <tr>                    
                                                        <td align="center" style="width: 30%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange" >容器条码</td>
                                                        <td align="center" style="width: 10%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">回退站台</td>
                                                        <td align="center" style="width: 30%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">错误原因</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange">错误时间</td>
                                                        <td align="center" style="width: 15%; color:dimgrey; border-bottom:#cccccc solid 1px; border-bottom-color:dimgrey; color:orange" >申请错误码</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr style="width: 100%;height:70%">
                                            <td>
                                                <div style="width: 100%;height:90%">
                                                    <div id="roll2">
                                                        <ul>   
                                                            <asp:GridView ID="ErrorList" Width="100%"  BorderWidth="0" runat="server" ShowHeader="false" AutoGenerateColumns="False" HeaderStyle-Height="0"  OnRowDataBound="GridView_RowDataBound">
                                                                <Columns>
                                                                <asp:BoundField>
                                                                    <ItemStyle HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="15px" />
                                                                </asp:BoundField> 
                                                                <asp:TemplateField>
                                                                    <ItemTemplate><%#Eval("STOCK_BARCODE")%></ItemTemplate>
                                                                    <ItemStyle Width="30%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                                </asp:TemplateField>  
                                                                <asp:TemplateField>
                                                                    <ItemTemplate><%#Eval("DEVICE_CODE")%></ItemTemplate>
                                                                    <ItemStyle Width="10%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                                </asp:TemplateField>   
                                                                <asp:TemplateField>
                                                                    <ItemTemplate><%#Eval("MANAGE_ERROR_TEXT")%></ItemTemplate>
                                                                    <ItemStyle Width="30%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                                </asp:TemplateField>   
                                                                <asp:TemplateField>
                                                                    <ItemTemplate><%#Eval("CREATE_TIME")%></ItemTemplate>
                                                                    <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                                </asp:TemplateField>   
                                                                <asp:TemplateField>
                                                                    <ItemTemplate><%#Eval("CONTROL_APPLY_PARAMETER")%></ItemTemplate>
                                                                    <ItemStyle Width="15%" HorizontalAlign="Center" ForeColor="DimGray" BorderWidth="0" Font-Bold="true" Font-Size="25px" />
                                                                </asp:TemplateField> 
                                                                </Columns>
                                                                <HeaderStyle Height="0px" />
                                                            </asp:GridView>
                                                        </ul>
                                                        <script type="text/javascript">
                                                            init_scrolltext();
                                                            function init_scrolltext() {
                                                                var demo = document.getElementById("roll2");
                                                                demo.scrollTop = 0;
                                                                setInterval('scrollup()', 50);
                                                            }
                                                            function scrollup() {
                                                                var demo = document.getElementById("roll2");
                                                                var demo2 = document.getElementById("managelist1");
                                                                var demo1 = document.getElementById("ErrorList");
                                                                if (demo1 != null && demo2 != null) {
                                                                    if (demo2.offsetTop - demo.scrollTop <= 0)
                                                                        demo.scrollTop -= demo1.offsetHeight;
                                                                    else {
                                                                        var t = demo.scrollTop;
                                                                        demo.scrollTop++;
                                                                        if (t == demo.scrollTop) {
                                                                            if (demo.scrollTop >= demo1.offsetHeight) {
                                                                                demo.scrollTop -= demo1.offsetHeight;
                                                                            }
                                                                            else {
                                                                                demo.scrollTop = 0;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }</script>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>  
                                </div>              
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </td>
                </tr>
            </table>

            <table style="width: 100%; height:15%">
                <tr style="width: 100%; height:15%; color:navajowhite; font-size:30px;background-color:navajowhite ;border-top-left-radius:30px; border-top-right-radius:30px;">
                    <td style="width: 50%; solid #333333">
                        <table style="width: 100%; height:50%">
                            <tr style="width: 100%;height:50%">
                                <td align="center" style="width: 50%;font-size:30px; color: orange  ">当前巷道货位总数量：</td>
                                <td align="center" style="font-size:30px; color: dimgrey " id="empty"></td>
                            </tr>
                            <tr style="width: 100%;height:50%">
                                <td align="center" style="width: 50%;font-size:30px; color: orange ">当前巷道货位可用数量：</td>
                                <td align="center" style="font-size:30px; color: dimgrey " id="have"></td>
                            </tr>
                        </table>
                    </td>
                    <td style="width: 50%; solid #333333">
                        <table style="width: 100%; height:30%">
                            <tr style="width: 100%;height:50%">
                                <td align="center" style="width: 50%;font-size:30px; color: orange ">当前巷道空容器数量: </td>
                                <td align="center" style="font-size:30px; color: dimgrey " id="tp"></td>
                            </tr>
                            <tr style="width: 100%;height:50%">
                                <td align="center" style="width: 50%;font-size:30px; color: orange ">当前巷道实容器数量: </td>
                                <td align="center" style="font-size:30px; color: dimgrey " id="manageNum"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>

            <script type="text/javascript">
                function fu(empty, have, tp, manageNum) {
                    document.getElementById("empty").innerHTML = empty; //空货位
                    document.getElementById("have").innerHTML = have; //有货货位
                    document.getElementById("tp").innerHTML = tp; //托盘货位
                    document.getElementById("manageNum").innerHTML = manageNum; //任务数
                }
            </script> 
        </form>
    </body>
</html>
