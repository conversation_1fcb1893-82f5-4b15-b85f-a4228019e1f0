﻿using System;
using System.Threading;
using System.Web.UI.WebControls;

public partial class Menu : SignInPage
{
    protected void page_Click(object sender, EventArgs e)
    {
        try
        {
            Button b = sender as But<PERSON>;
            if (b != null)
            {
                switch (b.ID)
                {
                    case "ManageIn":
                        Response.Redirect("/ManageIn.aspx");
                        break;
                    case "ManageOut":
                        Response.Redirect("/ManageOut.aspx");
                        break;
                    case "SignIn":
                        Response.Redirect("/SignIn.aspx");
                        break;
                    default:
                        break;
                }
            }
        }
        catch (ThreadAbortException)
        {
        }
        catch (Exception ex)
        {
            Common.LayerFailed(this, ex.Message);
        }
    }
}
