﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    [Serializable]
    [DataContract]
    public class QueryObject
    {
        string _column = string.Empty;
        string _header = string.Empty;
        string _operation = "=";
        string _value = string.Empty;
        string _logic = "and";


        /// <summary>
        /// 查询字段
        /// </summary>
        [DataMember]
        public string Column
        {
            get { return _column; }
            set { _column = value; }
        }

        /// <summary>
        /// 显示信息
        /// </summary>
        [DataMember]
        public string Header
        {
            get { return _header; }
            set { _header=value; }
        }

        /// <summary>
        /// 操作符号
        /// </summary>
        [DataMember]
        public string Operation
        {
            get { return _operation; }
            set { _operation = value; }
        }


        /// <summary>
        /// 查询比较值
        /// </summary>
        [DataMember]
        public string Value
        {
            get { return _value; }
            set { _value = value; }
        }

        /// <summary>
        /// 逻辑操作符号
        /// </summary>
        [DataMember]
        public string Logic
        {
            get { return _logic; }
            set { _logic = value; }
        }
    }
}
