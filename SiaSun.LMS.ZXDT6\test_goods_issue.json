{
  "materialRoomWarehouseId": "warehouse_001",
  "materialRoomWarehouseName": "材料间仓库001",
  "pickingType": 1,
  "isToMaterialRoom": 1,
  "deliverTime": "2025-07-30 10:00:00",
  "name": "测试领料申请",
  "described": "测试用领料申请描述",
  "closeDate": "2025-07-30",
  "workOrderCode": "WO20250730001",
  "workOrderId": "wo_001",
  "applyType": 1,
  "applyStatus": 1,
  "changeDate": "2025-07-30",
  "applyDate": "2025-07-30",
  "deliverStatus": 0,
  "deliverPhone": "13800138000",
  "deliverPeople": "张三",
  "deliverAddress": "测试配送地址",
  "isDeliver": 1,
  "warehouse_MANAGE": "仓库主管",
  "manageDeptId": "dept_001",
  "manageDeptName": "管理部门001",
  "applyDeptId": "apply_dept_001",
  "applyDeptName": "申请部门001",
  "applyUserId": "user_001",
  "applyUserName": "申请人001",
  "applyGoodsCode": "APPLY20250730001",
  "applyGoodsInfoList": [
    {
      "remark": "测试备注",
      "orderIndex": "1",
      "canUseNum": 100,
      "mgDept": "管理部门",
      "mgDeptId": "mgdept_001",
      "materialRoomWarehouseId": "warehouse_001",
      "materialRoomWarehouseName": "材料间仓库001",
      "goodsSource": "采购",
      "gkDeptName": "归口部门001",
      "gkDeptId": "gkdept_001",
      "shelfCode": "SHELF001",
      "shelfName": "货架001",
      "applyGoodsId": "apply_goods_001",
      "describe": "测试物资描述",
      "workOrderId": "wo_001",
      "goodsVersion": "V1.0",
      "useFor": "测试用途",
      "num": 10,
      "lineId": "line_001",
      "lineName": "线路001"
    }
  ]
},
      
"forLineName": "用于线路001",
      "forLineId": "forline_001",
      "buyType": "自购",
      "shelfId": "shelf_001",
      "warehouseId": "warehouse_001",
      "warehouseName": "仓库001",
      "unitId": "unit_001",
      "unitName": "个",
      "brand": "测试品牌",
      "goodsAttribute": "测试属性",
      "goodsType": "测试类型",
      "goodsName": "测试物资",
      "goodsCode": "GOODS001",
      "inventoryId": "inventory_001",
      "goodsId": "goods_001",
      "storageInfoId": "storage_001",
      "actualNum": 10,
      "createDate": "2025-07-30 10:00:00",
      "createUser": "user_001",
      "createName": "创建人",
      "updateDate": "2025-07-30 10:00:00",
      "updateUser": "user_001",
      "updateName": "更新人",
      "id": "detail_001",
      "status": 1,
      "billCode": "BILL001"
    }
  ],
  "createDate": "2025-07-30 10:00:00",
  "createUser": "user_001",
  "createName": "创建人",
  "updateDate": "2025-07-30 10:00:00",
  "updateUser": "user_001",
  "updateName": "更新人",
  "id": "main_001",
  "status": 1,
  "billCode": "BILL001"
}