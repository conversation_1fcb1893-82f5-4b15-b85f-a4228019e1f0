﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     fst
 *       日期：     2018/10/18
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// TB_TRAYINFO_UPLOAD 
    /// </summary>
    [Serializable]
    [DataContract]
	public class TB_TRAYINFO_UPLOAD
    {
        public TB_TRAYINFO_UPLOAD()
		{			
		}

        private System.Int64 _data_index;
        private string _mdl_name;
        private string _batch_id;
        private string _tray_no;
        private int _cell_count;
        private int _line_id;
        private DateTime _time_update;
        private string _remark;

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public System.Int64 DATA_INDEX
        {
            get { return _data_index; }
            set { _data_index = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MDL_NAME
        {
            get { return _mdl_name; }
            set { _mdl_name = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BATCH_ID
        {
            get { return _batch_id; }
            set { _batch_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string TRAY_NO
        {
            get { return _tray_no; }
            set { _tray_no = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int CELL_COUNT
        {
            get { return _cell_count; }
            set { _cell_count = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int LINE_ID
        {
            get { return _line_id; }
            set { _line_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public DateTime TIME_UPDATE
        {
            get { return _time_update; }
            set { _time_update = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string REMARK
        {
            get { return _remark; }
            set { _remark = value; }
        }
    }
}
