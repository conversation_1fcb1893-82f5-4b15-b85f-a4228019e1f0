﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// PLAN_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class PLAN_MAIN
	{
		public PLAN_MAIN()
		{
			
		}
		
		private int _plan_id;
		private int _plan_relative_id;
		private string _plan_code;
		private string _plan_type_code;
		private string _plan_create_time;
		private string _plan_begin_time;
		private string _plan_end_time;
		private string _plan_bill_date;
		private string _plan_status;
		private string _plan_creater;
		private string _plan_from_dept;
		private string _plan_to_dept;
		private string _plan_from_user;
		private string _plan_to_user;
		private string _plan_remark;
		private string _plan_flag;
		private string _plan_confirm_time;
		private string _plan_confirm_user;
        private string _plan_inout_station;
        private string _plan_level;

		private string _plan_group;
		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;

		///<sumary>
		/// 计划编号
		///</sumary>
		[DataMember]
		public int PLAN_ID
		{
			get{return _plan_id;}
			set{_plan_id = value;}
		}
		///<sumary>
		/// 计划关联索引
        ///</sumary>
        [DataMember]
		public int PLAN_RELATIVE_ID
		{
			get{return _plan_relative_id;}
			set{_plan_relative_id = value;}
		}
		///<sumary>
		/// 计划单号
        ///</sumary>
        [DataMember]
		public string PLAN_CODE
		{
			get{return _plan_code;}
			set{_plan_code = value;}
		}
		///<sumary>
		/// 计划类型编号
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_CODE
		{
			get{return _plan_type_code;}
			set{_plan_type_code = value;}
		}
		///<sumary>
		/// 创建时间
        ///</sumary>
        [DataMember]
		public string PLAN_CREATE_TIME
		{
			get{return _plan_create_time;}
			set{_plan_create_time = value;}
		}
		///<sumary>
		/// 开始时间
        ///</sumary>
        [DataMember]
		public string PLAN_BEGIN_TIME
		{
			get{return _plan_begin_time;}
			set{_plan_begin_time = value;}
		}
		///<sumary>
		/// 结束时间
        ///</sumary>
        [DataMember]
		public string PLAN_END_TIME
		{
			get{return _plan_end_time;}
			set{_plan_end_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PLAN_BILL_DATE
		{
			get{return _plan_bill_date;}
			set{_plan_bill_date = value;}
		}
		///<sumary>
		/// 计划状态
        ///</sumary>
        [DataMember]
		public string PLAN_STATUS
		{
			get{return _plan_status;}
			set{_plan_status = value;}
		}
		///<sumary>
		/// 创建人
        ///</sumary>
        [DataMember]
		public string PLAN_CREATER
		{
			get{return _plan_creater;}
			set{_plan_creater = value;}
		}
		///<sumary>
		/// 发送单位
        ///</sumary>
        [DataMember]
		public string PLAN_FROM_DEPT
		{
			get{return _plan_from_dept;}
			set{_plan_from_dept = value;}
		}
		///<sumary>
		/// 接收单位
        ///</sumary>
        [DataMember]
		public string PLAN_TO_DEPT
		{
			get{return _plan_to_dept;}
			set{_plan_to_dept = value;}
		}
		///<sumary>
		/// 发送人
        ///</sumary>
        [DataMember]
		public string PLAN_FROM_USER
		{
			get{return _plan_from_user;}
			set{_plan_from_user = value;}
		}
		///<sumary>
		/// 接收人
        ///</sumary>
        [DataMember]
		public string PLAN_TO_USER
		{
			get{return _plan_to_user;}
			set{_plan_to_user = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string PLAN_REMARK
		{
			get{return _plan_remark;}
			set{_plan_remark = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string PLAN_FLAG
		{
			get{return _plan_flag;}
			set{_plan_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PLAN_CONFIRM_TIME
		{
			get{return _plan_confirm_time;}
			set{_plan_confirm_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PLAN_CONFIRM_USER
		{
			get{return _plan_confirm_user;}
			set{_plan_confirm_user = value;}
		}
        ///<sumary>
        /// 出入库站台 wdz
        ///</sumary>
        [DataMember]
        public string PLAN_INOUT_STATION
        {
            get { return _plan_inout_station; }
            set { _plan_inout_station = value; }
        }
        ///<sumary>
        /// 计划优先级
        ///</sumary>
        [DataMember]
        public string PLAN_LEVEL
        {
            get { return _plan_level; }
            set { _plan_level = value; }
        }
		///<sumary>
		/// 计划分组
		///</sumary>
		[DataMember]
		public string PLAN_GROUP
		{
			get { return _plan_group; }
			set { _plan_group = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
