<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/System" xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/System?xsd=xsd0" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:complexType name="Exception">
    <xs:sequence>
      <xs:any minOccurs="0" maxOccurs="unbounded" namespace="##local" processContents="skip" />
    </xs:sequence>
    <xs:attribute ref="ser:FactoryType" />
  </xs:complexType>
  <xs:element name="Exception" nillable="true" type="tns:Exception" />
</xs:schema>