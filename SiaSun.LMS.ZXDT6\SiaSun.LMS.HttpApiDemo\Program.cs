using System;
using Microsoft.Owin.Hosting;

namespace SiaSun.LMS.HttpApiDemo
{
    class Program
    {
        private static SimpleLogger _logger = new SimpleLogger("HttpApiDemo");

        static void Main(string[] args)
        {
            // 从配置文件读取端口，如果没有则使用默认值
            string port = SimpleConfig.GetConfig("HttpApiPort", "9001");
            string baseUrl = $"http://localhost:{port}";

            // 从配置文件读取WCF服务地址
            string wcfUrl = SimpleConfig.GetConfig("WcfServiceUrl", "http://127.0.0.1:8001/Service/Demo");
            WcfProxy.SetWcfUrl(wcfUrl);

            Console.WriteLine("=== SiaSun LMS HTTP API Demo ===");
            Console.WriteLine($"启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"服务地址: {baseUrl}");
            Console.WriteLine($"WCF地址: {wcfUrl}");
            Console.WriteLine("================================");

            try
            {
                _logger.Info("正在启动HTTP API Demo服务...");

                // 启动HTTP API服务
                using (WebApp.Start<Startup>(baseUrl))
                {
                    Console.WriteLine($"\n✓ HTTP API Demo服务已启动: {baseUrl}");
                    Console.WriteLine("\n可用接口:");
                    Console.WriteLine($"  GET  {baseUrl}/api/demo/status     - 获取服务状态");
                    Console.WriteLine($"  GET  {baseUrl}/api/demo/health     - 健康检查");
                    Console.WriteLine($"  POST {baseUrl}/api/demo/carrier    - 载具请求");
                    Console.WriteLine($"  POST {baseUrl}/api/demo/task       - 任务请求");

                    Console.WriteLine("\n测试示例:");
                    Console.WriteLine($"  curl {baseUrl}/api/demo/status");
                    Console.WriteLine($"  curl -X POST {baseUrl}/api/demo/carrier -H \"Content-Type: application/json\" -d '{{\"carrierId\":\"C001\",\"action\":\"OUT\"}}'");

                    Console.WriteLine("\n按任意键停止服务...");

                    _logger.Info("HTTP API Demo服务启动成功");
                    Console.ReadKey();

                    _logger.Info("正在停止HTTP API Demo服务...");
                }
            }
            catch (Exception ex)
            {
                string errorMsg = $"启动失败: {ex.Message}";
                Console.WriteLine($"\n✗ {errorMsg}");
                _logger.Error("HTTP API Demo服务启动失败", ex);

                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}