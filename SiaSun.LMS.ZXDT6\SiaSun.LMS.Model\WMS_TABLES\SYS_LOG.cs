﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// SYS_LOG 
    /// </summary>
    [Serializable]
    [DataContract]
    public class SYS_LOG
    {
        public SYS_LOG()
        {

        }

        private int _log_id;
        private string _log_thread;
        private string _log_level;
        private string _log_logger;
        private string _log_date;
        private string _log_message;
        private string _log_method;

        ///<sumary>
        /// 日志ID
        ///</sumary>
        [DataMember]
        public int LOG_ID
        {
            get { return _log_id; }
            set { _log_id = value; }
        }

        ///<sumary>
        /// 日志类型
        ///</sumary>
        [DataMember]
        public string LOG_THREAD
        {
            get { return _log_thread; }
            set { _log_thread = value; }
        }

        ///<sumary>
        /// 日志级别
        ///</sumary>
        [DataMember]
        public string LOG_LEVEL
        {
            get { return _log_level; }
            set { _log_level = value; }
        }

        ///<sumary>
        /// 操作者
        ///</sumary>
        [DataMember]
        public string LOG_LOGGER
        {
            get { return _log_logger; }
            set { _log_logger = value; }
        }

        ///<sumary>
        /// 日期
        ///</sumary>
        [DataMember]
        public string LOG_DATE
        {
            get { return _log_date; }
            set { _log_date = value; }
        }

        ///<sumary>
        /// 日志信息
        ///</sumary>
        [DataMember]
        public string LOG_MESSAGE
        {
            get { return _log_message; }
            set { _log_message = value; }
        }

        ///<sumary>
        /// 日志位置方法名
        ///</sumary>
        public string LOG_METHOD
        {
            get { return _log_method; }
            set { _log_method = value; }
        }
    }
}
