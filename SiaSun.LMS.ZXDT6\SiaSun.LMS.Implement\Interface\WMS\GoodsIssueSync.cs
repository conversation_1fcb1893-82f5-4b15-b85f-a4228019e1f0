using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 总库领料/材料间领料接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class GoodsIssueSync : InterfaceBase
    {
        class InputParam
        {
            public string materialRoomWarehouseId { get; set; }
            public string materialRoomWarehouseName { get; set; }
            public int pickingType { get; set; }
            public int isToMaterialRoom { get; set; }
            public string deliverTime { get; set; }
            public string name { get; set; }
            public string described { get; set; }
            public string closeDate { get; set; }
            public string workOrderCode { get; set; }
            public string workOrderId { get; set; }
            public int applyType { get; set; }
            public int applyStatus { get; set; }
            public string changeDate { get; set; }
            public string applyDate { get; set; }
            public int deliverStatus { get; set; }
            public string deliverPhone { get; set; }
            public string deliverPeople { get; set; }
            public string deliverAddress { get; set; }
            public int isDeliver { get; set; }
            public string warehouse_MANAGE { get; set; }
            public string manageDeptId { get; set; }
            public string manageDeptName { get; set; }
            public string applyDeptId { get; set; }
            public string applyDeptName { get; set; }
            public string applyUserId { get; set; }
            public string applyUserName { get; set; }
            public string applyGoodsCode { get; set; }
            public List<ApplyGoodsInfoItem> applyGoodsInfoList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class ApplyGoodsInfoItem
        {
            public string remark { get; set; }
            public string orderIndex { get; set; }
            public int canUseNum { get; set; }
            public string mgDept { get; set; }
            public string mgDeptId { get; set; }
            public string materialRoomWarehouseId { get; set; }
            public string materialRoomWarehouseName { get; set; }
            public string goodsSource { get; set; }
            public string gkDeptName { get; set; }
            public string gkDeptId { get; set; }
            public string shelfCode { get; set; }
            public string shelfName { get; set; }
            public string applyGoodsId { get; set; }
            public string describe { get; set; }
            public string workOrderId { get; set; }
            public string isCollect { get; set; }
            public string repairProcess { get; set; }
            public string isNeedWordOrder { get; set; }
            public string major { get; set; }
            public string projectInfo { get; set; }
            public string goodsVersion { get; set; }
            public string useForBuffer { get; set; }
            public string useFor { get; set; }
            public int num { get; set; }
            public string lineId { get; set; }
            public string lineName { get; set; }
            public string forLineName { get; set; }
            public string forLineId { get; set; }
            public string buyType { get; set; }
            public string shelfId { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string brand { get; set; }
            public string goodsAttribute { get; set; }
            public string goodsType { get; set; }
            public string goodsName { get; set; }
            public string goodsCode { get; set; }
            public string inventoryId { get; set; }
            public string goodsId { get; set; }
            public string storageInfoId { get; set; }
            public int actualNum { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.applyGoodsCode) || string.IsNullOrEmpty(inputParam.applyUserId) ||
                    string.IsNullOrEmpty(inputParam.applyDate) || inputParam.applyType <= 0)
                {
                    result = false;
                    message = "接口入参必填项存在空值：applyGoodsCode, applyUserId, applyDate, applyType";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证申领类型 (1=总库领料, 2=材料间领料)
                if (!Two.Contains(inputParam.applyType.ToString()))
                {
                    result = false;
                    message = $"申领类型无效：{inputParam.applyType}，有效值为：1=总库领料, 2=材料间领料";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证申请状态 (1=申请中, 2=申请同意, 3=申请拒绝)
                if (inputParam.applyStatus > 0 && !Three.Contains(inputParam.applyStatus.ToString()))
                {
                    result = false;
                    message = $"领料申请状态无效：{inputParam.applyStatus}，有效值为：1=申请中, 2=申请同意, 3=申请拒绝";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证领料类型 (1=材料间, 2=实际消耗)
                if (inputParam.pickingType > 0 && !Two.Contains(inputParam.pickingType.ToString()))
                {
                    result = false;
                    message = $"领料类型无效：{inputParam.pickingType}，有效值为：1=材料间, 2=实际消耗";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证配送状态 (0=未配送, 2=配送中, 3=配送完成)
                if (inputParam.deliverStatus > 0 && inputParam.deliverStatus != 2 && inputParam.deliverStatus != 3)
                {
                    result = false;
                    message = $"配送状态无效：{inputParam.deliverStatus}，有效值为：0=未配送, 2=配送中, 3=配送完成";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证日期格式
                DateTime applyDate;
                if (!DateTime.TryParse(inputParam.applyDate, out applyDate))
                {
                    result = false;
                    message = $"申请日期格式错误：{inputParam.applyDate}";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证变更日期格式（如果提供）
                if (!string.IsNullOrEmpty(inputParam.changeDate))
                {
                    DateTime changeDate;
                    if (!DateTime.TryParse(inputParam.changeDate, out changeDate))
                    {
                        result = false;
                        message = $"变更日期格式错误：{inputParam.changeDate}";
                        return FormatGoodsIssueErrorMessage(message, traceId);
                    }
                }

                // 验证关闭日期格式（如果提供）
                if (!string.IsNullOrEmpty(inputParam.closeDate))
                {
                    DateTime closeDate;
                    if (!DateTime.TryParse(inputParam.closeDate, out closeDate))
                    {
                        result = false;
                        message = $"申请单关闭时间格式错误：{inputParam.closeDate}";
                        return FormatGoodsIssueErrorMessage(message, traceId);
                    }
                }

                // 验证领料明细列表
                if (inputParam.applyGoodsInfoList == null || inputParam.applyGoodsInfoList.Count == 0)
                {
                    result = false;
                    message = "领料明细列表不能为空";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }

                // 验证领料明细项必填字段
                foreach (var item in inputParam.applyGoodsInfoList)
                {
                    if (string.IsNullOrEmpty(item.goodsCode) || string.IsNullOrEmpty(item.goodsName) ||
                        item.num <= 0 || string.IsNullOrEmpty(item.unitId))
                    {
                        result = false;
                        message = "领料明细项必填字段存在空值或无效值：goodsCode, goodsName, num, unitId";
                        return FormatGoodsIssueErrorMessage(message, traceId);
                    }
                }

                // 处理领料申请主表数据
                // 这里应该根据实际的数据库模型进行数据处理
                // 由于没有具体的数据库模型定义，这里只做基本的逻辑处理

                // 检查领料申请是否已存在
                // var existingApply = S_Base.sBase.pGOODS_ISSUE_MAIN.GetModel(inputParam.applyGoodsCode);

                // 处理领料申请数据
                // 这里应该包含：
                // 1. 创建或更新领料申请主表记录
                // 2. 处理领料明细数据
                // 3. 更新库存信息（如果是实际领料）
                // 4. 记录操作日志

                // 检查出库单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.applyGoodsCode);

                if (existingReceipt != null)
                {
                    result = false;
                    message = $"已存在领料单-{inputParam.applyGoodsCode}";
                    return FormatGoodsIssueErrorMessage(message, traceId);
                }
                else
                {
                    // 处理入库单数据
                    // 这里应该包含：
                    // 1. 创建或更新入库单主表记录
                    // 2. 处理入库明细数据
                    // 3. 更新库存信息
                    // 4. 记录操作日志
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.applyGoodsCode,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCommonOut.ToString(),
                        PLAN_CREATE_TIME = inputParam.createDate,
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (ApplyGoodsInfoItem item in inputParam.applyGoodsInfoList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST()
                        {
                            PLAN_LIST_QUANTITY = item.num,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(item.goodsCode).GOODS_ID,

                        };
                        lsPLAN_LIST.Add(mPLAN_LIST);
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }

                // 模拟数据处理成功
                S_Base.sBase.Log.Info($"领料申请同步成功 - 申请编码: {inputParam.applyGoodsCode}, 申领类型: {inputParam.applyType}, 明细数量: {inputParam.applyGoodsInfoList.Count}");

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"领料申请同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化领料申请错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatGoodsIssueErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}