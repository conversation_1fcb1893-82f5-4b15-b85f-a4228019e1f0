﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_TABLE_CONVERTER_LIST
	/// </summary>
	public class P_SYS_TABLE_CONVERTER_LIST : P_Base_House
	{
		public P_SYS_TABLE_CONVERTER_LIST ()
		{
			//
			// TODO: 此处添加SYS_TABLE_CONVERTER_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_TABLE_CONVERTER_LIST> GetList()
		{
			return ExecuteQueryForList<SYS_TABLE_CONVERTER_LIST>("SYS_TABLE_CONVERTER_LIST_SELECT",null);
		}

        public IList<SYS_TABLE_CONVERTER_LIST> GetList_ConverterID(int CONVERTER_ID)
        {
            return ExecuteQueryForList<SYS_TABLE_CONVERTER_LIST>("SYS_TABLE_CONVERTER_LIST_SELECT_CONVERTER_ID", CONVERTER_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_TABLE_CONVERTER_LIST sys_table_converter_list)
		{
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("SYS_TABLE_CONVERTER_LIST");
                sys_table_converter_list.TABLE_CONVERTER_LIST_ID = id;
            }
            return ExecuteInsert("SYS_TABLE_CONVERTER_LIST_INSERT",sys_table_converter_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_TABLE_CONVERTER_LIST sys_table_converter_list)
		{
			return ExecuteUpdate("SYS_TABLE_CONVERTER_LIST_UPDATE",sys_table_converter_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_TABLE_CONVERTER_LIST GetModel(System.Int32 TABLE_CONVERTER_LIST_ID)
		{
			return ExecuteQueryForObject<SYS_TABLE_CONVERTER_LIST>("SYS_TABLE_CONVERTER_LIST_SELECT_BY_ID",TABLE_CONVERTER_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 TABLE_CONVERTER_LIST_ID)
		{
			return ExecuteDelete("SYS_TABLE_CONVERTER_LIST_DELETE",TABLE_CONVERTER_LIST_ID);
		}
		

	}
}
