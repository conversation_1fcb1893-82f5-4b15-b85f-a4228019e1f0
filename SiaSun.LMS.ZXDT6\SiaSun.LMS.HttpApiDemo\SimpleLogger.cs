using System;
using System.Configuration;
using System.IO;

namespace SiaSun.LMS.HttpApiDemo
{
    /// <summary>
    /// 简化的日志类，替代SiaSun.LMS.Common.Log
    /// </summary>
    public class SimpleLogger
    {
        private readonly string _loggerName;
        private static readonly string _logPath;

        static SimpleLogger()
        {
            _logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(_logPath))
            {
                Directory.CreateDirectory(_logPath);
            }
        }

        public SimpleLogger(string loggerName)
        {
            _loggerName = loggerName;
        }

        public void Info(string message)
        {
            WriteLog("INFO", message);
        }

        public void Warn(string message)
        {
            WriteLog("WARN", message);
        }

        public void Error(string message, Exception ex = null)
        {
            string fullMessage = ex != null ? $"{message} - {ex}" : message;
            WriteLog("ERROR", fullMessage);
        }

        public void Debug(string message)
        {
            WriteLog("DEBUG", message);
        }

        private void WriteLog(string level, string message)
        {
            try
            {
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                string logMessage = $"[{timestamp}] [{level}] [{_loggerName}] {message}";
                
                // 输出到控制台
                Console.WriteLine(logMessage);
                
                // 输出到文件
                string logFile = Path.Combine(_logPath, $"HttpApiDemo_{DateTime.Now:yyyyMMdd}.log");
                File.AppendAllText(logFile, logMessage + Environment.NewLine);
            }
            catch
            {
                // 忽略日志写入错误
            }
        }
    }

    /// <summary>
    /// 简化的配置工具类，替代SiaSun.LMS.Common.StringUtil
    /// </summary>
    public static class SimpleConfig
    {
        public static string GetConfig(string key, string defaultValue = "")
        {
            try
            {
                string value = ConfigurationManager.AppSettings[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
