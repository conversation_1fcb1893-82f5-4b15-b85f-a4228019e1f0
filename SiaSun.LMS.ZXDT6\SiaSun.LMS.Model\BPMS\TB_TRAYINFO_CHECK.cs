﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     fst
 *       日期：     2018/10/18
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// TB_TRAYINFO_CHECK 
    /// </summary>
    [Serializable]
    [DataContract]
	public class TB_TRAYINFO_CHECK
    {
        public TB_TRAYINFO_CHECK()
		{			
		}

        private string _mdl_name;
        private string _batch_id;
        private string _tray_no;
        private int _cell_count;
        private System.Int64 _index_upload;
        private int _check_flag;
        private DateTime _check_time;
        private int _revert_flag;
        private DateTime _revert_time;

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MDL_NAME
        {
            get { return _mdl_name; }
            set { _mdl_name = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BATCH_ID
        {
            get { return _batch_id; }
            set { _batch_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string TRAY_NO
        {
            get { return _tray_no; }
            set { _tray_no = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int CELL_COUNT
        {
            get { return _cell_count; }
            set { _cell_count = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public System.Int64 INDEX_UPLOAD
        {
            get { return _index_upload; }
            set { _index_upload = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int CHECK_FLAG
        {
            get { return _check_flag; }
            set { _check_flag = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public DateTime CHECK_TIME
        {
            get { return _check_time; }
            set { _check_time = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int REVERT_FLAG
        {
            get { return _revert_flag; }
            set { _revert_flag = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public DateTime REVERT_TIME
        {
            get { return _revert_time; }
            set { _revert_time = value; }
        }
    }
}
