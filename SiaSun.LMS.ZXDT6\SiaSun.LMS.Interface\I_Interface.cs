﻿using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_Interface
    {
        /// <summary>
        /// 物资基本信息
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string GoodsInfoSync(string inputJson);
        
        /// <summary>
        /// 计量单位
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string UnitInfoSync(string inputJson);
        
        /// <summary>
        /// 仓库
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string WarehouseInfoSync(string inputJson);
        
        /// <summary>
        /// 组织架构
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string OrganizationalStructureInfoSync(string inputJson);
        
        /// <summary>
        /// 货架位
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string ShelfSpaceSync(string inputJson);

        /// <summary>
        /// 入库单
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string InboundReceiptSync(string inputJson);

        /// <summary>
        /// 入库红冲单
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string InventoryReversalReceiptSync(string inputJson);

        /// <summary>
        /// 出库单
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string OutboundReceiptSync(string inputJson);

        /// <summary>
        /// 总库领料/材料间领料
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string GoodsIssueSync(string inputJson);

        /// <summary>
        /// 出库红冲单
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string GoodsIssueReversalReceiptSync(string inputJson);

        /// <summary>
        /// 盘点计划表
        /// </summary>
        /// <param name="inputJson"></param>
        /// <returns></returns>
        [OperationContract]
        string InventoryPlanSync(string inputJson);
    }
}
