﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// 日志类 
    /// </summary>
    [Serializable]
    [DataContract]
    public class Log
    {
        string _timeStamp;
        string _message;

        [DataMember]
        public string TimeStamp
        {
            get { return _timeStamp; }
            set { _timeStamp = value; }
        }

        [DataMember]
        public string Message
        {
            get { return _message; }
            set { _message = value; }
        }

        public Log(string _TimeStamp, string _Message)
        {
            _timeStamp = _TimeStamp;
            _message = _Message;
        }
    }
}
