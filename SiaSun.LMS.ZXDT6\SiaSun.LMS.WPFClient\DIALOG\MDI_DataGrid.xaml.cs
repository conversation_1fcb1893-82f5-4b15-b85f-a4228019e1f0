﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="AllowEdit">是否允许编辑</param>
    /// <param name="ParentHeader">父级显示标题</param>
    /// <param name="ParentTableName">父级表名</param>
    /// <param name="ParentTag">父级关键字</param>
    /// <param name="ParentWhere">父级表查询条件</param>
    /// <param name="ChildHeader">子级显示标题</param>
    /// <param name="ChildTableName">子级表名</param>
    /// <param name="ChildTag">子级关键字，与父级关联</param>
    /// <param name="ChildWhere">子级查询条件</param>
    public partial class MDI_DataGrid : AvalonDock.DocumentContent
    {
        public MDI_DataGrid(bool AllowEdit, string ParentHeader, string ParentTableName, string ParentTag, string ParentWhere, string ChildHeader, string ChildTableName, string ChildTag, string ChildWhere)
        {
            InitializeComponent();

            //设置控件属性
            this.ucMdiDataGrid.U_AllowEdit = AllowEdit;
            this.ucMdiDataGrid.U_ParentHeader = ParentHeader;
            this.ucMdiDataGrid.U_ParentTableName = ParentTableName;
            this.ucMdiDataGrid.U_ParentTag = ParentTag;
            this.ucMdiDataGrid.U_ParentWhere = ParentWhere;
            this.ucMdiDataGrid.U_ChildHeader = ChildHeader;
            this.ucMdiDataGrid.U_ChildTableName = ChildTableName;
            this.ucMdiDataGrid.U_ChildTag = ChildTag;
            this.ucMdiDataGrid.U_ChildWhere = ChildWhere;
        }

        public MDI_DataGrid(bool AllowEdit, string ParentHeader, string ParentTableName, string ParentTag, string ParentWhere, string ParentOrder, string ChildHeader, string ChildTableName, string ChildTag, string ChildWhere, string ChildOrder) :
            this(AllowEdit, ParentHeader, ParentTableName, ParentTag, ParentWhere, ChildHeader, ChildTableName, ChildTag, ChildWhere)
        {
            this.ucMdiDataGrid.U_ParentOrder = ParentOrder;
            this.ucMdiDataGrid.U_ChildOrder = ChildOrder;
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.ucMdiDataGrid.U_InitControl();
        }
    }
}
