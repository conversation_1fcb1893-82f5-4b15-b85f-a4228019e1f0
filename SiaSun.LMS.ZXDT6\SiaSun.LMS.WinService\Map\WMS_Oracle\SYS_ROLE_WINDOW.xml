﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_ROLE_WINDOW" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_ROLE_WINDOW" type="SiaSun.LMS.Model.SYS_ROLE_WINDOW, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_ROLE_WINDOW">
			<result property="ROLE_WINDOW_ID" column="role_window_id" />
			<result property="MENU_ID" column="menu_id" />
			<result property="CONTROL_NAME" column="control_name" />
			<result property="CONTROL_HEADER" column="control_header" />
			<result property="FLAG" column="flag" />
			<result property="ROLE_ID" column="role_id" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_ROLE_WINDOW_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  role_window_id,
				  menu_id,
				  control_name,
				  control_header,
				  flag,
				  role_id
			From SYS_ROLE_WINDOW
		</select>
		
		<select id="SYS_ROLE_WINDOW_SELECT_BY_ID" parameterClass="int" extends = "SYS_ROLE_WINDOW_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					role_window_id=#ROLE_WINDOW_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="SYS_ROLE_WINDOW_SELECT_BY_ROLE_ID_MENU_ID" parameterClass="Hashtable" extends = "SYS_ROLE_WINDOW_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          role_id=#ROLE_ID# and menu_id=#MENU_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="SYS_ROLE_WINDOW_SELECT_BY_ROLE_ID_MENU_ID_CONTROL_NAME" parameterClass="Hashtable" extends = "SYS_ROLE_WINDOW_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          role_id=#ROLE_ID# and menu_id=#MENU_ID# and control_name=#CONTROL_NAME#
        </isParameterPresent>
      </dynamic>
    </select>
    
				
		<insert id="SYS_ROLE_WINDOW_INSERT" parameterClass="SYS_ROLE_WINDOW">
      Insert Into SYS_ROLE_WINDOW (
      role_window_id,
      menu_id,
      control_name,
      control_header,
      flag,
      role_id
      )Values(
      #ROLE_WINDOW_ID#,
      #MENU_ID#,
      #CONTROL_NAME#,
      #CONTROL_HEADER#,
      #FLAG#,
      #ROLE_ID#
      )
      <!--<selectKey  resultClass="int" type="post" property="ROLE_WINDOW_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_ROLE_WINDOW_UPDATE" parameterClass="SYS_ROLE_WINDOW">
      Update SYS_ROLE_WINDOW Set
      <!--role_window_id=#ROLE_WINDOW_ID#,-->
      menu_id=#MENU_ID#,
      control_name=#CONTROL_NAME#,
      control_header=#CONTROL_HEADER#,
      flag=#FLAG#,
      role_id=#ROLE_ID#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					role_window_id=#ROLE_WINDOW_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_ROLE_WINDOW_DELETE" parameterClass="int">
			Delete From SYS_ROLE_WINDOW
			<dynamic prepend="WHERE">
				<isParameterPresent>
					role_window_id=#ROLE_WINDOW_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>