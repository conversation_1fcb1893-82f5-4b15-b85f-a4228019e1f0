﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style
        x:Key="NuclearRepeatButton"
        d:IsControlPart="True"
        BasedOn="{x:Null}"
        TargetType="{x:Type RepeatButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value=".5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.4000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Rectangle
                            x:Name="Background"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="#FF8A9199"
                            StrokeThickness="1">
                            <Rectangle.Fill>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                    <GradientStop Color="#FFF0F4F8" />
                                    <GradientStop Offset="1" Color="#FFD7DBE1" />
                                </LinearGradientBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                        <Rectangle
                            x:Name="Highlight"
                            Margin="1"
                            Fill="White"
                            IsHitTestVisible="false"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1" />
                        <ContentPresenter
                            x:Name="ContentPresenter"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="HoverOn_BeginStoryboard" Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Background" Property="Opacity" Value="0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="NuclearThumbStyle"
        d:IsControlPart="True"
        BasedOn="{x:Null}"
        TargetType="{x:Type Thumb}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value=".5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames
                                BeginTime="00:00:00"
                                Storyboard.TargetName="Highlight"
                                Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.4000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Rectangle
                            x:Name="Background"
                            RadiusX="1"
                            RadiusY="1"
                            Stroke="#FF8A9199"
                            StrokeThickness="1">
                            <Rectangle.Fill>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                    <GradientStop Color="#FFF0F4F8" />
                                    <GradientStop Offset="1" Color="#FFD7DBE1" />
                                </LinearGradientBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                        <Rectangle
                            x:Name="Highlight"
                            Margin="1"
                            Fill="White"
                            IsHitTestVisible="false"
                            Opacity="0"
                            RadiusX="1"
                            RadiusY="1" />
                        <Path
                            Width="11"
                            Height="1"
                            Margin="0,-6,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M4.8333325,7.2499995 L12.012101,7.2499995"
                            Stretch="Fill"
                            Stroke="#FF848485"
                            StrokeThickness="1">
                            <Path.Fill>
                                <LinearGradientBrush StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
                                    <GradientStop Offset="0.487" Color="#FFC8C9CC" />
                                    <GradientStop Offset="0.518" Color="#FFF0F0F0" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path
                            Width="11"
                            Height="1"
                            Margin="0,-2,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M4.8333325,7.2499995 L12.012101,7.2499995"
                            Stretch="Fill"
                            Stroke="#FF848485"
                            StrokeThickness="1">
                            <Path.Fill>
                                <LinearGradientBrush StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
                                    <GradientStop Offset="0.487" Color="#FFC8C9CC" />
                                    <GradientStop Offset="0.518" Color="#FFF0F0F0" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path
                            Width="11"
                            Height="1"
                            Margin="0,0,0,-2"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M4.8333325,7.2499995 L12.012101,7.2499995"
                            Stretch="Fill"
                            Stroke="#FF848485"
                            StrokeThickness="1">
                            <Path.Fill>
                                <LinearGradientBrush StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
                                    <GradientStop Offset="0.487" Color="#FFC8C9CC" />
                                    <GradientStop Offset="0.518" Color="#FFF0F0F0" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path
                            Width="11"
                            Height="1"
                            Margin="0,0,0,-6"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="M4.8333325,7.2499995 L12.012101,7.2499995"
                            Stretch="Fill"
                            Stroke="#FF848485"
                            StrokeThickness="1">
                            <Path.Fill>
                                <LinearGradientBrush StartPoint="1.062,0.5" EndPoint="-0.062,0.5">
                                    <GradientStop Offset="0.487" Color="#FFC8C9CC" />
                                    <GradientStop Offset="0.518" Color="#FFF0F0F0" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="HoverOn_BeginStoryboard" Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Background" Property="Opacity" Value="0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ScrollBar}">
        <Setter Property="Stylus.IsFlicksEnabled" Value="false" />
        <!--<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />-->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollBar}">
                    <Grid
                        x:Name="GridRoot"
                        Width="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}"
                        RenderTransformOrigin="0.5,0.5">
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                <GradientStop Color="#FFD5DAE0" />
                                <GradientStop Offset="1" Color="#DFE4EB" />
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid.RowDefinitions>
                            <RowDefinition MaxHeight="18" />
                            <RowDefinition Height="*" />
                            <RowDefinition MaxHeight="18" />
                        </Grid.RowDefinitions>
                        <RepeatButton
                            x:Name="DecreaseRepeat"
                            Command="ScrollBar.LineUpCommand"
                            Style="{DynamicResource NuclearRepeatButton}">
                            <Grid>
                                <Path
                                    Width="10"
                                    Height="6"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="F1 M 541.537,173.589L 531.107,173.589L 536.322,167.49L 541.537,173.589 Z "
                                    IsHitTestVisible="False"
                                    Stretch="Uniform">
                                    <Path.Fill>
                                        <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                            <GradientStop Color="#FF5E6D91" />
                                            <GradientStop Offset="1" Color="#FF2B3B60" />
                                        </LinearGradientBrush>
                                    </Path.Fill>
                                </Path>
                            </Grid>
                        </RepeatButton>

                        <Track
                            x:Name="PART_Track"
                            Grid.Row="1"
                            IsDirectionReversed="true"
                            Orientation="Vertical">
                            <Track.Thumb>
                                <Thumb Style="{DynamicResource NuclearThumbStyle}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageUp"
                                    Command="ScrollBar.PageDownCommand"
                                    Style="{DynamicResource NuclearScrollRepeatButtonStyle}" />
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton
                                    x:Name="PageDown"
                                    Command="ScrollBar.PageUpCommand"
                                    Style="{DynamicResource NuclearScrollRepeatButtonStyle}" />
                            </Track.DecreaseRepeatButton>
                        </Track>

                        <RepeatButton
                            x:Name="IncreaseRepeat"
                            Grid.Row="2"
                            Command="ScrollBar.LineDownCommand"
                            Style="{DynamicResource NuclearRepeatButton}">
                            <Grid>
                                <Path
                                    Grid.Row="4"
                                    Width="10"
                                    Height="6"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Data="F1 M 531.107,321.943L 541.537,321.943L 536.322,328.042L 531.107,321.943 Z "
                                    IsHitTestVisible="False"
                                    Stretch="Uniform">
                                    <Path.Fill>
                                        <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                            <GradientStop Offset="0.004" Color="#FF5E6D91" />
                                            <GradientStop Offset="0.996" Color="#FF2B3B60" />
                                        </LinearGradientBrush>
                                    </Path.Fill>
                                </Path>
                            </Grid>
                        </RepeatButton>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="Orientation" Value="Horizontal">

                            <Setter TargetName="GridRoot" Property="LayoutTransform">
                                <Setter.Value>
                                    <TransformGroup>
                                        <RotateTransform Angle="90" />
                                        <ScaleTransform ScaleX="-1" ScaleY="1" />
                                    </TransformGroup>
                                </Setter.Value>
                            </Setter>

                            <Setter TargetName="PART_Track" Property="Orientation" Value="Vertical" />

                            <Setter TargetName="DecreaseRepeat" Property="Command" Value="ScrollBar.LineLeftCommand" />
                            <Setter TargetName="IncreaseRepeat" Property="Command" Value="ScrollBar.LineRightCommand" />
                            <Setter TargetName="PageDown" Property="Command" Value="ScrollBar.PageLeftCommand" />
                            <Setter TargetName="PageUp" Property="Command" Value="ScrollBar.PageRightCommand" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{x:Null}" TargetType="{x:Type ScrollViewer}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <ScrollContentPresenter
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="{TemplateBinding Padding}"
                            CanContentScroll="{TemplateBinding CanContentScroll}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}" />

                        <ScrollBar
                            x:Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Grid.Column="0"
                            AutomationProperties.AutomationId="HorizontalScrollBar"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Minimum="0"
                            Orientation="Horizontal"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{Binding Path=HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                        <ScrollBar
                            x:Name="PART_VerticalScrollBar"
                            Grid.Row="0"
                            Grid.Column="1"
                            AutomationProperties.AutomationId="VerticalScrollBar"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            Minimum="0"
                            Orientation="Vertical"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{Binding Path=VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>