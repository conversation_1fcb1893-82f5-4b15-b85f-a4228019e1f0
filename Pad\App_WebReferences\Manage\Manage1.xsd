<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="SYS_USER">
    <xs:sequence>
      <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER" />
  <xs:complexType name="MANAGE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DETAIL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="MANAGE_LIST_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID_SPLIT_SOURCE" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST" />
  <xs:complexType name="MANAGE_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LEVEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_OPERATOR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_RELATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_SOURCE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PICK_SEQ" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_WEIGHT" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_MAIN" nillable="true" type="tns:MANAGE_MAIN" />
  <xs:complexType name="ArrayOfMANAGE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfMANAGE_LIST" nillable="true" type="tns:ArrayOfMANAGE_LIST" />
  <xs:complexType name="ArrayOfPLAN_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfPLAN_LIST" nillable="true" type="tns:ArrayOfPLAN_LIST" />
  <xs:complexType name="PLAN_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="OUT_POSITION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_FINISHED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_ORDERED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_PICKED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY_APPEND" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
  <xs:complexType name="MANAGE_TYPE_PARAM">
    <xs:sequence>
      <xs:element minOccurs="0" name="U_AllowAutoEndPostion" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowAutoStartPostion" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowCellModel" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowEndPosition" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowOccupyPercent" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowStartPosition" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowStockBarcode" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AssembleResource" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_AutoCompleteTask" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AutoDownloadControlTask" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_CellModelDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_CheckStockExistStorage" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_EndCellInOut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndCellType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndPositionCellStatus" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndPositionDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartCellInOut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartCellType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartPositionCellStatus" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartPositionDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_WarehouseType" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_TYPE_PARAM" nillable="true" type="tns:MANAGE_TYPE_PARAM" />
  <xs:complexType name="PLAN_ACTION_EXCUTE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_ACTION_EXCUTE" nillable="true" type="tns:PLAN_ACTION_EXCUTE" />
  <xs:complexType name="MANAGE_ACTION_EXCUTE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="NEXT_NODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_ACTION_EXCUTE" nillable="true" type="tns:MANAGE_ACTION_EXCUTE" />
  <xs:complexType name="IO_CONTROL_APPLY">
    <xs:sequence>
      <xs:element minOccurs="0" name="APPLY_TASK_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA01" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA02" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARAMETER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CREATE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IO_CONTROL_APPLY" nillable="true" type="tns:IO_CONTROL_APPLY" />
</xs:schema>