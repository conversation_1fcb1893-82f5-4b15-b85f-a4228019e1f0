﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucQuery.xaml 的交互逻辑
    /// </summary>
    public partial class ucQuery : UserControl
    {
        IList<Model.SYS_ITEM_LIST> listCompareOp;
        IList<Model.SYS_ITEM_LIST> listLogicOp;

        string strTableName = null;
        string windowName = null;
        
        /// <summary>
        /// 关联XML表名
        /// </summary>
        public string U_XmlTableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 关联窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return windowName; }
            set { windowName = value; }
        }

        public ucQuery()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            try
            {
                //获得操作符号列表
                this.listCompareOp = MainApp.I_SystemService.ITEM_LIST_GetList_ITEM_CODE("CompareOperation");
                this.listLogicOp = MainApp.I_SystemService.ITEM_LIST_GetList_ITEM_CODE("LogicalOperation");

                //添加查询控件
                CustomerDescriptions cusDescription = new CustomerDescriptions();
                using (DataTable tableFieldDescription = string.IsNullOrEmpty(windowName) ? cusDescription.GetStyleDataTable(strTableName) : cusDescription.GetFormStyleDataTable(windowName, strTableName))
                {
                    //根据序号排序并添加列显示
                    DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var queryField = from row in arRowField orderby Convert.ToInt32(row["Order"].ToString()) select row;

                    //开始初始化 
                    this.BeginInit();

                    //判断控件类型设置显示列样式
                    foreach (DataRow rowField in queryField)
                    {
                        if (!rowField.IsNull("Header") && !String.IsNullOrEmpty(rowField["Header"].ToString()))
                        {
                            //判断类型
                            switch (rowField["ControlType"].ToString().ToLower())
                            {
                                case "checkbox":
                                    this.AddCheckBox(rowField);
                                    break;
                                case "combobox":
                                    this.AddComboBox(rowField);
                                    break;
                                case "elementcombox":
                                    this.AddElmentComboBox(rowField);
                                    break;
                                case "datetimepicker":
                                    this.AddDatePicker(rowField);
                                    break;
                                default:
                                    this.AddTextBox(rowField);
                                    break;
                            }
                        }
                    }
                    //结束初始化 
                    this.EndInit();
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        #region     ------添加查询控件

        /// <summary>
        /// 添加比较操作符号
        /// </summary>
        private void AddCompareOp(StackPanel panelItem)
        {
            ComboBox cmbOp = new ComboBox();
            cmbOp.Width = 65;
            cmbOp.Tag = "CompareOp";
            cmbOp.Margin = new Thickness(1);
            panelItem.Children.Add(cmbOp);

            cmbOp.DisplayMemberPath = "ITEM_LIST_NAME";
            cmbOp.SelectedValuePath = "ITEM_LIST_CODE";
            cmbOp.ItemsSource = this.listCompareOp;
            cmbOp.SelectedIndex = 0;
        }

        /// <summary>
        /// 添加逻辑操作符号
        /// </summary>
        private void AddLogicOp(StackPanel panelItem)
        {
            ComboBox cmbOp = new ComboBox();
            cmbOp.Width = 60;
            cmbOp.Tag = "LogicOp";
            cmbOp.Margin = new Thickness(1);
            panelItem.Children.Add(cmbOp);

            cmbOp.DisplayMemberPath = "ITEM_LIST_NAME";
            cmbOp.SelectedValuePath = "ITEM_LIST_CODE";
            cmbOp.ItemsSource = this.listLogicOp;
            cmbOp.SelectedIndex = 0;
        }

        /// <summary>
        /// 添加面板
        /// </summary>
        private StackPanel AddStackPanel(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = new StackPanel();
            panelItem.Tag = rowField;
            panelItem.Margin = new Thickness(10,1,10,1);
            panelItem.Orientation = Orientation.Horizontal;
            this.panelQuery.Children.Add(panelItem);

            //添加分割线
            Separator spr = new Separator();
            spr.Style = MainApp.GetStyleResource("styleDefaltSeparator") as Style;
            spr.Height = 2;
            spr.Margin = new Thickness(5, 1, 5, 1);
            this.panelQuery.Children.Add(spr);
            return panelItem;
        }

        /// <summary>
        /// 添加标题
        /// </summary>
        private void AddHeader(StackPanel panelItem,string Header)
        {
            //显示标题
            TextBlock txtHeader = new TextBlock();
            txtHeader.Width = 100;
            txtHeader.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            panelItem.Children.Add(txtHeader);

            //绑定标题
            txtHeader.Text = string.Format("{0}:",Header);
        }

        /// <summary>
        /// 添加文本编辑
        /// </summary>
        private void AddTextBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = this.AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加操作符号
            this.AddCompareOp(panelItem);

            //添加内容控件
            TextBox txtContent = new TextBox();
            txtContent.Style = (Style)MainApp.GetStyleResource("styleDefaultTextBox");
            txtContent.Width = 150;
            txtContent.Tag = rowField;
            txtContent.Margin = new Thickness(10, 2, 5, 2);
            panelItem.Children.Add(txtContent);        

            //添加逻辑符号
            this.AddLogicOp(panelItem);
        }

        /// <summary>
        /// 添加日期编辑
        /// </summary>
        private void AddDatePicker(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = this.AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加操作符号
            this.AddCompareOp(panelItem);

            //添加内容控件
            DatePicker dtPicker = new DatePicker();
            dtPicker.Width = 150;
            dtPicker.Tag = rowField;
            dtPicker.Margin = new Thickness(10, 2, 5, 2);
            panelItem.Children.Add(dtPicker);     

            //添加逻辑符号
            this.AddLogicOp(panelItem);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddCheckBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = this.AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加操作符号
            this.AddCompareOp(panelItem);

            //控件定义
            CheckBox chkBox = new CheckBox();
            chkBox.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            chkBox.Width = 150;
            chkBox.Tag = rowField;
            chkBox.IsThreeState = false;
            chkBox.Margin = new Thickness(10, 2, 5, 2);
            panelItem.Children.Add(chkBox);

            //添加逻辑符号
            this.AddLogicOp(panelItem);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddComboBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = this.AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加操作符号
            this.AddCompareOp(panelItem);

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Tag = rowField;
            cmb.Width = 150;
            cmb.Height = 21;
            cmb.Margin = new Thickness(10, 2, 5, 2);
            cmb.DisplayMemberPath = MainApp._DatabaseType.ToLower() == "oracle" ? "NAME" : "name";
            cmb.SelectedValuePath = MainApp._DatabaseType.ToLower() == "oracle" ? "VALUE" : "value";
            cmb.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;
            cmb.SelectedIndex = -1;
            panelItem.Children.Add(cmb);

            //添加逻辑符号
            this.AddLogicOp(panelItem);
       }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddElmentComboBox(DataRow rowField)
        {
            //添加面板
            StackPanel panelItem = this.AddStackPanel(rowField);

            //显示标题
            AddHeader(panelItem, rowField["Header"].ToString());

            //添加操作符号
            this.AddCompareOp(panelItem);

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Width = 150;
            cmb.Height = 21;
            cmb.Tag = rowField;
            cmb.Margin = new Thickness(10, 2, 5, 2);
            panelItem.Children.Add(cmb);
            if (!rowField.IsNull("DataBind"))
            {
                string[] arStr = rowField["DataBind"].ToString().Split('|');
                cmb.ItemsSource = arStr;
            }
            cmb.SelectedIndex = -1;

            //添加逻辑符号
            this.AddLogicOp(panelItem);
        }

        #endregion

        /// <summary>
        /// 获得查询条件
        /// </summary>
        public string U_GetQueryWhere()
        {           
            //查询对象集合
            IList<SiaSun.LMS.Model.QueryObject> listQueryObject = new List<SiaSun.LMS.Model.QueryObject>();

            foreach (FrameworkElement element in this.panelQuery.Children)
            {
                StackPanel panelItem = element as StackPanel;
                if (panelItem != null)
                {
                    DataRow rowField = element.Tag as DataRow;
                    if (rowField != null)
                    {
                        string strColumn = rowField["Column"].ToString();
                        string strControlType = rowField["ControlType"].ToString().TrimEnd();
                        string strCompareOperation = (panelItem.Children[1] as ComboBox).SelectedValue.ToString();
                        string strLogicalOperation = (panelItem.Children[3] as ComboBox).SelectedValue.ToString();
                        string strValue = string.Empty;

                        //判断输入控件的控件类型
                        if (panelItem.Children[2] is CheckBox)
                        {
                            CheckBox chkBox = panelItem.Children[2] as CheckBox;
                            strValue = Convert.ToInt32(chkBox.IsChecked).ToString();
                            listQueryObject.Add(GetQueryObject(strColumn, strCompareOperation, strValue, strLogicalOperation));
                        }
                        else if (panelItem.Children[2] is DatePicker)
                        {
                            DatePicker dtPicker = panelItem.Children[2] as DatePicker;
                            if (dtPicker.SelectedDate != null || (strCompareOperation.Contains("NULL")) || (strCompareOperation.Contains("LIKE")))
                            {
                                strValue = Convert.ToDateTime(dtPicker.SelectedDate).ToString("yyyy-MM-dd");
                                listQueryObject.Add(GetQueryObject(strColumn, strCompareOperation, strValue, strLogicalOperation));
                            }
                        }
                        else if (panelItem.Children[2] is ComboBox)
                        {
                            ComboBox cmb = panelItem.Children[2] as ComboBox;
                            if (cmb.SelectedValue != null || (strCompareOperation.Contains("NULL")) || (strCompareOperation.Contains("LIKE")))
                            {
                                strValue = cmb.SelectedValue.ToString();
                                listQueryObject.Add(GetQueryObject(strColumn, strCompareOperation, strValue, strLogicalOperation));
                            }
                        }
                        else if(panelItem.Children[2] is TextBox)
                        {
                            TextBox txt = panelItem.Children[2] as TextBox;
                            if (!string.IsNullOrEmpty(txt.Text) || (strCompareOperation.Contains("NULL")) || (strCompareOperation.Contains("LIKE")))
                            {
                                strValue = txt.Text;
                                listQueryObject.Add(GetQueryObject(strColumn, strCompareOperation, strValue, strLogicalOperation));
                            }
                        }
                    }
                }
            }            

            return GetWhere(listQueryObject);
        }

        /// <summary>
        /// 获得查询对象
        /// </summary>
        private SiaSun.LMS.Model.QueryObject GetQueryObject(string Column, string Operation, string Value, string Logic)
        {
            SiaSun.LMS.Model.QueryObject queryObj = new SiaSun.LMS.Model.QueryObject();
            queryObj.Column = Column;
            queryObj.Operation = Operation;
            queryObj.Value = Value;
            queryObj.Logic = Logic;
            return queryObj;
        }

        /// <summary>
        /// 获得条件
        /// </summary>
        private string GetWhere(IList<SiaSun.LMS.Model.QueryObject> QueryObjectList)
        {
            StringBuilder strbWhere = new StringBuilder();

            for (int i = 0; i < QueryObjectList.Count; i++)
            {
                SiaSun.LMS.Model.QueryObject queryObj = QueryObjectList[i];
                string Column = queryObj.Column;
                string Operation = queryObj.Operation;
                string Value = queryObj.Value;
                string Logic = (i == (QueryObjectList.Count - 1)) ? string.Empty : queryObj.Logic;
                //判断查询操作符号
                switch (queryObj.Operation.ToUpper())
                {
                    case "LIKE":
                    case "NOT LIKE":
                        strbWhere.Append(string.Format(" ({0} {1} '%{2}%') {3} ", Column, Operation, Value, Logic));
                        break;
                    case "IS NULL":
                    case "IS NOT NULL":
                        strbWhere.Append(string.Format(" ({0} {1}) {2} ", Column, Operation, Logic));
                        break;
                    case "NVL":
                        strbWhere.Append(string.Format("({0}({1},{2})='{2}') {3}", Operation, Column, Value, Logic));
                        break;
                    case "BETWEEN":
                        string[] arStrFromTo = Value.Split('-');
                        if (arStrFromTo.Length == 2)
                        {
                            strbWhere.Append(string.Format("({0} {1} '{2}' AND '{3}') {4}", Column, Operation, arStrFromTo[0], arStrFromTo[1], Logic));
                        }
                        break;
                    default:
                        strbWhere.Append(string.Format(" ({0}{1}'{2}') {3} ", Column, Operation, Value, Logic));
                        break;
                }
            }

            return strbWhere.ToString();
        }

    }
}
