﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// MANAGE_TYPE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MANAGE_TYPE
	{
		public MANAGE_TYPE()
		{
			
		}
		
		private int _manage_type_id;
		private string _manage_type_code;
		private string _manage_type_name;
		private string _manage_type_inout;
		private string _manage_type_group;
		private string _manage_type_class;
        private string _storage_type_class;
		private int _manage_type_order;
		private string _manage_type_flag;
        private string _manage_type_property;
		
		///<sumary>
		/// 任务类型编号
        ///</sumary>
        [DataMember]
		public int MANAGE_TYPE_ID
		{
			get{return _manage_type_id;}
			set{_manage_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CODE
		{
			get{return _manage_type_code;}
			set{_manage_type_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_NAME
		{
			get{return _manage_type_name;}
			set{_manage_type_name = value;}
		}
		///<sumary>
		/// 出入库
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_INOUT
		{
			get{return _manage_type_inout;}
			set{_manage_type_inout = value;}
		}
		///<sumary>
		/// 分组
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_GROUP
		{
			get{return _manage_type_group;}
			set{_manage_type_group = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CLASS
		{
			get{return _manage_type_class;}
			set{_manage_type_class = value;}
		}

        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string STORAGE_TYPE_CLASS
        {
            get { return _storage_type_class; }
            set { _storage_type_class = value; }
        }

        ///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int MANAGE_TYPE_ORDER
		{
			get{return _manage_type_order;}
			set{_manage_type_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_FLAG
		{
			get{return _manage_type_flag;}
			set{_manage_type_flag = value;}
		}

        [DataMember]
        public string MANAGE_TYPE_PROPERTY
        {
            get { return _manage_type_property; }
            set { _manage_type_property = value; }
        }

	}
}
