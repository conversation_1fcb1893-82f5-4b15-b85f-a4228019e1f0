﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucStockTaskInput.xaml 的交互逻辑
    /// </summary>
    public partial class ucSotckOut : UserControl
    {

        public delegate void U_QueryHandler();

        public event U_QueryHandler U_Query;

        /// <summary>
        /// 托盘类型选择事件
        /// </summary>
        public delegate void U_StockTypeValueChangedHandler();

        //托盘类型选择事件
        public event U_StockTypeValueChangedHandler U_StockTypeChanged;

        int intGoodsClassID = 0;

        /// <summary>
        /// 托盘类型
        /// </summary>
        public int U_StockType
        {

            get
            {
                if (this.cmbStockType.SelectedValue != null)

                    return Convert.ToInt32(this.cmbStockType.SelectedValue.ToString());
                else
                    return 0;
            }

        }

        /// <summary>
        /// 物料类别
        /// </summary>
        public int U_GoodsClassID
        {
            get { return intGoodsClassID; }
            set { intGoodsClassID = value; }
        }

        /// <summary>
        /// 选择是单一的托盘任务还是连续的托盘任务
        /// </summary>
        public bool U_StockOutAuto
        {
            get
            {
                if (this.rbManual.IsChecked.Value)
                    return false;

                if(this.rbAuto.IsChecked.Value)
                    return true;

                return false;
            }

        }

        public decimal U_StockOutQuantity
        {
            get
            {
                return Convert.ToDecimal(this.txtStockQuantity.Text.TrimEnd());
            }
        }



        public ucSotckOut()
        {
            InitializeComponent();
        }

        public void U_InitControl()
        {
            cmbStockType.DisplayMemberPath = "GOODS_NAME";
            cmbStockType.SelectedValuePath = "GOODS_ID";
            cmbStockType.ItemsSource = MainApp.I_SystemService.GoodsGetListGoodsClassID(intGoodsClassID);

            this.cmbStockType.SelectionChanged += new SelectionChangedEventHandler(cmbStockType_SelectionChanged);

            this.rbManual.IsChecked = true;

            if (this.cmbStockType.Items.Count == 1)
            {
                this.cmbStockType.SelectedIndex = 0;
            }
        }

        void cmbStockType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.U_StockTypeChanged != null)
                U_StockTypeChanged();

        }


        public bool U_CheckStockOut(out string Message)
        {
            Message = string.Empty;

            if (cmbStockType.SelectedValue == null)
            {
                Message = "请选择托盘类型！";
                return false;
            }


            if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(this.txtStockQuantity.Text))
            {
                Message = "请检查任务数量是否合法！";
                txtStockQuantity.Focus();
                txtStockQuantity.SelectAll();
                return false;
            }

            return true;
        }

        private void rbSingle_Checked(object sender, RoutedEventArgs e)
        {
            if (this.rbManual.IsChecked.Value)
            {
                this.panelStockQuantity.Visibility = System.Windows.Visibility.Hidden;

                if (this.U_Query != null && this.cmbStockType.SelectedValue != null)
                {
                    this.U_Query();
                }
            }

            if (this.rbAuto.IsChecked.Value)
            {
                this.panelStockQuantity.Visibility = System.Windows.Visibility.Visible;

            }

        }


    }
}
