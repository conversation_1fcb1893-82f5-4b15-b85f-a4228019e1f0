﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="LeftTable">左边ListTreeView关联的数据库表</param>
    /// <param name="strLeftNodeRelativeColumn">左边ListTreeView父子节点关联列名</param>
    /// <param name="LeftNodeTagColumn">节点标识关联列名</param>
    /// <param name="LeftNodeTextColumn">节点显示关联列名</param>
    /// <param name="LeftWhere">左边ListTreeView筛选条件</param>
    /// <param name="RightTagColumn">右边DataGridView与左边ListTreeView关联列</param>
    /// <param name="RightTable">右边DataGridView关联的数据库表</param>
    /// <param name="RightWhere">右边数据筛选条件</param>
    /// <param name="SplitGroupHeader">右边DataGridView数据中拆分列的分组显示列</param>
    /// <param name="SplitGroupColumn">右边DataGridView数据中拆分列的分组列</param>
    /// <param name="SplitPropertyColumn">右边DataGridView数据中拆分列名</param>
    /// <param name="SplitPropertyType">右边DataGridView数据中拆分列类型</param>
    public partial class MDI_TreeView_DataGrid : AvalonDock.DocumentContent
    {
        public MDI_TreeView_DataGrid(string LeftTable,string strLeftNodeRelativeColumn,string LeftNodeTagColumn,string LeftNodeTextColumn,string LeftWhere,
                                                                string RightTagColumn, string RightTable, string RightWhere, 
                                                                string SplitGroupHeader, string SplitGroupColumn, string SplitPropertyColumn, string SplitPropertyType)
        {
            InitializeComponent();

            //设置属性
            this.ucTreeGrid.U_LeftTable = LeftTable;
            this.ucTreeGrid.U_LeftNodeRelativeColumn = strLeftNodeRelativeColumn;
            this.ucTreeGrid.U_LeftNodeTagColumn = LeftNodeTagColumn;
            this.ucTreeGrid.U_LeftNodeTextColumn = LeftNodeTextColumn;
            this.ucTreeGrid.U_LeftWhere = LeftWhere;

            this.ucTreeGrid.U_AllowChecked = false;
            this.ucTreeGrid.U_AllowOperation = true;

            this.ucTreeGrid.U_RigthTagColumn = RightTagColumn;
            this.ucTreeGrid.U_RightTable = RightTable;
            this.ucTreeGrid.U_RightWhere = RightWhere;
            this.ucTreeGrid.U_SplitGroupHeader = SplitGroupHeader;
            this.ucTreeGrid.U_SplitGroupColumn = SplitGroupColumn;
            this.ucTreeGrid.U_SplitPropertyColumn = SplitPropertyColumn;
            this.ucTreeGrid.U_SplitPropertyType = SplitPropertyType;
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //设置树控件显示标题
                this.ucTreeGrid.U_LeftHeader = this.Title;
                //初始化控件
                this.ucTreeGrid.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }
    }
}
