﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// RECORD_DETAIL
	/// </summary>
	public class P_RECORD_DETAIL : P_Base_House
	{
		public P_RECORD_DETAIL ()
		{
			//
			// TODO: 此处添加RECORD_DETAIL的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<RECORD_DETAIL> GetList()
		{
			return ExecuteQueryForList<RECORD_DETAIL>("RECORD_DETAIL_SELECT",null);
		}

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(RECORD_DETAIL record_detail)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("RECORD_DETAIL");
                record_detail.RECORD_DETAIL_ID = id;
            }
            return ExecuteInsert("RECORD_DETAIL_INSERT", record_detail);
        }

		/// <summary>
		/// 修改
		/// </summary>
		public int Update(RECORD_DETAIL record_detail)
		{
			return ExecuteUpdate("RECORD_DETAIL_UPDATE",record_detail);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public RECORD_DETAIL GetModel(System.Int32 RECORD_DETAIL_ID)
		{
			return ExecuteQueryForObject<RECORD_DETAIL>("RECORD_DETAIL_SELECT_BY_ID",RECORD_DETAIL_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 RECORD_DETAIL_ID)
		{
			return ExecuteDelete("RECORD_DETAIL_DELETE",RECORD_DETAIL_ID);
		}
		

	}
}
