<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://127.0.0.1:8002/Service/Database?xsd=xsd6" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:complexType name="ObjectT">
    <xs:sequence>
      <xs:element minOccurs="0" name="RequestObject" nillable="true" type="xs:anyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ObjectT" nillable="true" type="tns:ObjectT" />
  <xs:complexType name="StorageLockParam">
    <xs:sequence>
      <xs:element minOccurs="0" name="areaCode" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="cartonMark" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="delayMark" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="goodsClassId" type="xs:int" />
      <xs:element minOccurs="0" name="lockQuantity" type="xs:decimal" />
      <xs:element minOccurs="0" name="splitPallet" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="storageId" type="xs:int" />
      <xs:element minOccurs="0" name="storageListId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="StorageLockParam" nillable="true" type="tns:StorageLockParam" />
  <xs:complexType name="LCD_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_INFO9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DISPLAY_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HANDLE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LCD_ID" type="xs:decimal" />
      <xs:element minOccurs="0" name="LCD_LIST_ID" type="xs:decimal" />
      <xs:element minOccurs="0" name="LIFE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WRITE_TIME" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LCD_LIST" nillable="true" type="tns:LCD_LIST" />
  <xs:complexType name="STORAGE_LOCK">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DETAIL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOCK_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="STORAGE_LOCK" nillable="true" type="tns:STORAGE_LOCK" />
  <xs:complexType name="PdaMessage">
    <xs:sequence>
      <xs:element minOccurs="0" name="Flag" type="xs:boolean" />
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PdaMessage" nillable="true" type="tns:PdaMessage" />
  <xs:complexType name="INTERFACE_QUEUE">
    <xs:sequence>
      <xs:element name="_backup_filed1" nillable="true" type="xs:string" />
      <xs:element name="_backup_filed2" nillable="true" type="xs:string" />
      <xs:element name="_backup_filed3" nillable="true" type="xs:string" />
      <xs:element name="_backup_filed4" nillable="true" type="xs:string" />
      <xs:element name="_backup_filed5" nillable="true" type="xs:string" />
      <xs:element name="_error_describe" nillable="true" type="xs:string" />
      <xs:element name="_handle_datetime" nillable="true" type="xs:string" />
      <xs:element name="_handle_flag" type="xs:decimal" />
      <xs:element name="_interface_name" nillable="true" type="xs:string" />
      <xs:element name="_interface_type" nillable="true" type="xs:string" />
      <xs:element name="_invoke_type" nillable="true" type="xs:string" />
      <xs:element name="_manage_id" type="xs:decimal" />
      <xs:element name="_param_in" nillable="true" type="xs:string" />
      <xs:element name="_param_out" nillable="true" type="xs:string" />
      <xs:element name="_plan_code" nillable="true" type="xs:string" />
      <xs:element name="_plan_id" type="xs:decimal" />
      <xs:element name="_queue_id" type="xs:decimal" />
      <xs:element name="_queue_remark" nillable="true" type="xs:string" />
      <xs:element name="_stock_barcode" nillable="true" type="xs:string" />
      <xs:element name="_target_system" nillable="true" type="xs:string" />
      <xs:element name="_write_datetime" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="INTERFACE_QUEUE" nillable="true" type="tns:INTERFACE_QUEUE" />
  <xs:complexType name="APPLY_TYPE">
    <xs:sequence>
      <xs:element name="_apply_id" type="xs:int" />
      <xs:element name="_apply_type_class" nillable="true" type="xs:string" />
      <xs:element name="_apply_type_code" nillable="true" type="xs:string" />
      <xs:element name="_apply_type_flag" nillable="true" type="xs:string" />
      <xs:element name="_apply_type_name" nillable="true" type="xs:string" />
      <xs:element name="_apply_type_order" type="xs:int" />
      <xs:element name="_apply_type_property" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="APPLY_TYPE" nillable="true" type="tns:APPLY_TYPE" />
  <xs:complexType name="APPLY_TYPE_PARAM">
    <xs:sequence>
      <xs:element minOccurs="0" name="U_CheckManageExist" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_CheckWeightStation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_CreateExceptionTask" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_ExceptionStation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_GoodsProperty" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_LedStation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_NextStation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_RelateProcess" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_SendLedMessage" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_StorageApplyChange" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StorageVerifyStation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_WriteHisData" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="APPLY_TYPE_PARAM" nillable="true" type="tns:APPLY_TYPE_PARAM" />
  <xs:complexType name="TB_TRAYINFO_UPLOAD">
    <xs:sequence>
      <xs:element minOccurs="0" name="BATCH_ID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_COUNT" type="xs:int" />
      <xs:element minOccurs="0" name="DATA_INDEX" type="xs:long" />
      <xs:element minOccurs="0" name="LINE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MDL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TIME_UPDATE" type="xs:dateTime" />
      <xs:element minOccurs="0" name="TRAY_NO" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TB_TRAYINFO_UPLOAD" nillable="true" type="tns:TB_TRAYINFO_UPLOAD" />
  <xs:complexType name="FIELD_DESCRIPTION">
    <xs:sequence>
      <xs:element minOccurs="0" name="AllowQuery" type="xs:int" />
      <xs:element minOccurs="0" name="Column" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ControlType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DataBind" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DbType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DefaultValue" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Header" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Order" type="xs:int" />
      <xs:element minOccurs="0" name="QueryOperation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ReadOnly" type="xs:int" />
      <xs:element minOccurs="0" name="Remark" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Validation" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FIELD_DESCRIPTION" nillable="true" type="tns:FIELD_DESCRIPTION" />
  <xs:complexType name="FLOW_ACTION">
    <xs:sequence>
      <xs:element minOccurs="0" name="FLOW_ACTION_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_DEFAULT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_EVENT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_ACTION_IMAGE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_ACTION_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_ACTION_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_NODE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FLOW_ACTION" nillable="true" type="tns:FLOW_ACTION" />
  <xs:complexType name="FLOW_NODE">
    <xs:sequence>
      <xs:element minOccurs="0" name="FLOW_NODE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_NODE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_NODE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_NODE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_NODE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_NODE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FLOW_NODE" nillable="true" type="tns:FLOW_NODE" />
  <xs:complexType name="FLOW_PARA">
    <xs:sequence>
      <xs:element minOccurs="0" name="FLOW_PARA_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_PARA_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_PARA_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_PARA_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FLOW_PARA" nillable="true" type="tns:FLOW_PARA" />
  <xs:complexType name="FLOW_TYPE">
    <xs:sequence>
      <xs:element minOccurs="0" name="FLOW_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_TYPE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLOW_TYPE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="FLOW_TYPE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="FLOW_TYPE" nillable="true" type="tns:FLOW_TYPE" />
  <xs:complexType name="GOODS_CLASS">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_CLASS_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CLASS_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_PARENT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CLASS_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_CLASS" nillable="true" type="tns:GOODS_CLASS" />
  <xs:complexType name="GOODS_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_CLASS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_COLOR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_CONST_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_LIMIT_LOWER_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_LIMIT_UPPER_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_UNITS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_MAIN" nillable="true" type="tns:GOODS_MAIN" />
  <xs:complexType name="TB_TRAYINFO_CHECK">
    <xs:sequence>
      <xs:element minOccurs="0" name="BATCH_ID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_COUNT" type="xs:int" />
      <xs:element minOccurs="0" name="CHECK_FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="CHECK_TIME" type="xs:dateTime" />
      <xs:element minOccurs="0" name="INDEX_UPLOAD" type="xs:long" />
      <xs:element minOccurs="0" name="MDL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="REVERT_FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="REVERT_TIME" type="xs:dateTime" />
      <xs:element minOccurs="0" name="TRAY_NO" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TB_TRAYINFO_CHECK" nillable="true" type="tns:TB_TRAYINFO_CHECK" />
  <xs:complexType name="GOODS_PROPERTY">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_DATASOURCE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FIELDTYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_KEYFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY_VALID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_PROPERTY" nillable="true" type="tns:GOODS_PROPERTY" />
  <xs:complexType name="GOODS_TEMPLATE">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_TEMPLATE" nillable="true" type="tns:GOODS_TEMPLATE" />
  <xs:complexType name="GOODS_TEMPLATE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CHILD_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CONVERT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MDL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MES_OPERATION_NO" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MES_RESOURCE_NO" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_TEMPLATE_LIST" nillable="true" type="tns:GOODS_TEMPLATE_LIST" />
  <xs:complexType name="GOODS_TYPE">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TYPE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TYPE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GOODS_TYPE" nillable="true" type="tns:GOODS_TYPE" />
  <xs:complexType name="IO_CONTROL_APPLY">
    <xs:sequence>
      <xs:element minOccurs="0" name="APPLY_TASK_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA01" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA02" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARAMETER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CREATE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IO_CONTROL_APPLY" nillable="true" type="tns:IO_CONTROL_APPLY" />
  <xs:complexType name="IO_CONTROL">
    <xs:sequence>
      <xs:element minOccurs="0" name="AGV_NO" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_BEGIN_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_END_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_TASK_LEVEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_TASK_TYPE" type="xs:int" />
      <xs:element minOccurs="0" name="END_DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_DEVICE_CODE2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_WAREHOUSE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PRE_CONTROL_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="RELATIVE_CONTROL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="START_DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_DEVICE_CODE2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_WAREHOUSE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IO_CONTROL" nillable="true" type="tns:IO_CONTROL" />
  <xs:complexType name="IO_CONTROL_APPLY_HIS">
    <xs:sequence>
      <xs:element minOccurs="0" name="APPLY_TASK_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA01" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARA02" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_PARAMETER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_APPLY_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CREATE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HANDLE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ERROR_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IO_CONTROL_APPLY_HIS" nillable="true" type="tns:IO_CONTROL_APPLY_HIS" />
  <xs:complexType name="IO_CONTROL_ROUTE">
    <xs:sequence>
      <xs:element minOccurs="0" name="CONTROL_ROUTE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_MANAGE" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="CONTROL_ROUTE_TYPE" type="xs:int" />
      <xs:element minOccurs="0" name="END_DEVICE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FINALL_DEVICE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_DEVICE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IO_CONTROL_ROUTE" nillable="true" type="tns:IO_CONTROL_ROUTE" />
  <xs:complexType name="LCD_MAIN">
    <xs:sequence>
      <xs:element name="_lcd_code" nillable="true" type="xs:string" />
      <xs:element name="_lcd_id" type="xs:int" />
      <xs:element name="_lcd_ip" nillable="true" type="xs:string" />
      <xs:element name="_lcd_message" nillable="true" type="xs:string" />
      <xs:element name="_lcd_read_flag" nillable="true" type="xs:string" />
      <xs:element name="_lcd_remark" nillable="true" type="xs:string" />
      <xs:element name="_lcd_station" nillable="true" type="xs:string" />
      <xs:element name="_lcd_update_time" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LCD_MAIN" nillable="true" type="tns:LCD_MAIN" />
  <xs:complexType name="LED_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="AREA_HEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_WIDTH" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_X" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_Y" type="xs:int" />
      <xs:element minOccurs="0" name="FILE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FONT_SIZE" type="xs:int" />
      <xs:element minOccurs="0" name="LED_ID" type="xs:int" />
      <xs:element minOccurs="0" name="LED_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="LED_LIST_PARA1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_LIST_PARA2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_LIST_PARA3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_LIST_PARA4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_LIST_PARA5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_LIST_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LINE_NO" type="xs:int" />
      <xs:element minOccurs="0" name="LINE_TEXT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RUN_SPEED" type="xs:int" />
      <xs:element minOccurs="0" name="SHOW_STUNT" type="xs:int" />
      <xs:element minOccurs="0" name="SHOW_TIME" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LED_LIST" nillable="true" type="tns:LED_LIST" />
  <xs:complexType name="LED_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="AUTO_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_TYPE" type="xs:int" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_ID" type="xs:int" />
      <xs:element minOccurs="0" name="LED_IP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_PARA1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_PARA2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_PARA3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_PARA4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_PARA5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_MAIN_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LED_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LINE_NUM" type="xs:int" />
      <xs:element minOccurs="0" name="SCREEN_HEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="SCREEN_WIDTH" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LED_MAIN" nillable="true" type="tns:LED_MAIN" />
  <xs:complexType name="Log">
    <xs:sequence>
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TimeStamp" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Log" nillable="true" type="tns:Log" />
  <xs:complexType name="MANAGE_ACTION_EXCUTE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="NEXT_NODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_ACTION_EXCUTE" nillable="true" type="tns:MANAGE_ACTION_EXCUTE" />
  <xs:complexType name="MANAGE_DETAIL">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_DETAIL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_DETAIL_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="MANAGE_DETAIL_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_DETAIL" nillable="true" type="tns:MANAGE_DETAIL" />
  <xs:complexType name="MANAGE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DETAIL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="MANAGE_LIST_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID_SPLIT_SOURCE" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LOCK_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_LIST" nillable="true" type="tns:MANAGE_LIST" />
  <xs:complexType name="MANAGE_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_LEVEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_OPERATOR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_RELATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_SOURCE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PICK_SEQ" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_WEIGHT" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_MAIN" nillable="true" type="tns:MANAGE_MAIN" />
  <xs:complexType name="MANAGE_TYPE">
    <xs:sequence>
      <xs:element minOccurs="0" name="MANAGE_TYPE_CLASS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_INOUT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_PROPERTY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STORAGE_TYPE_CLASS" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_TYPE" nillable="true" type="tns:MANAGE_TYPE" />
  <xs:complexType name="MANAGE_TYPE_PARAM">
    <xs:sequence>
      <xs:element minOccurs="0" name="U_AllowAutoEndPostion" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowAutoStartPostion" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowCellModel" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowEndPosition" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowOccupyPercent" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowStartPosition" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AllowShowStockBarcode" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AssembleResource" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_AutoCompleteTask" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_AutoDownloadControlTask" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_CellModelDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_CheckStockExistStorage" type="xs:boolean" />
      <xs:element minOccurs="0" name="U_EndCellInOut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndCellType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndPositionCellStatus" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_EndPositionDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartCellInOut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartCellType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartPositionCellStatus" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_StartPositionDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="U_WarehouseType" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MANAGE_TYPE_PARAM" nillable="true" type="tns:MANAGE_TYPE_PARAM" />
  <xs:complexType name="PLAN_ACTION_EXCUTE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ACTION_EVENT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_ACTION_EXCUTE" nillable="true" type="tns:PLAN_ACTION_EXCUTE" />
  <xs:complexType name="PLAN_DETAIL">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_DETAIL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_DETAIL_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_DETAIL" nillable="true" type="tns:PLAN_DETAIL" />
  <xs:complexType name="PLAN_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="OUT_POSITION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_FINISHED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_LIST_ORDERED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_PICKED_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_QUANTITY_APPEND" type="xs:decimal" />
      <xs:element minOccurs="0" name="PLAN_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_LIST" nillable="true" type="tns:PLAN_LIST" />
  <xs:complexType name="PLAN_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="BACKUP_FIELD1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BACKUP_FIELD5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_BEGIN_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_BILL_DATE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CONFIRM_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CONFIRM_USER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CREATER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CREATE_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_END_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_FROM_DEPT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_FROM_USER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_INOUT_STATION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LEVEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_RELATIVE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TO_DEPT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TO_USER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_MAIN" nillable="true" type="tns:PLAN_MAIN" />
  <xs:complexType name="PLAN_TYPE">
    <xs:sequence>
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CLASS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_TYPE_INOUT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_TYPE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_TYPE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="PLAN_TYPE" nillable="true" type="tns:PLAN_TYPE" />
  <xs:complexType name="QueryObject">
    <xs:sequence>
      <xs:element minOccurs="0" name="Column" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Header" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Logic" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Operation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Value" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QueryObject" nillable="true" type="tns:QueryObject" />
  <xs:complexType name="RECORD_DETAIL">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RECORD_DETAIL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RECORD_DETAIL_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RECORD_LIST_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RECORD_DETAIL" nillable="true" type="tns:RECORD_DETAIL" />
  <xs:complexType name="RECORD_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RECORD_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RECORD_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RECORD_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="RECORD_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RECORD_LIST" nillable="true" type="tns:RECORD_LIST" />
  <xs:complexType name="RECORD_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_POSITION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_POSITION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_BEGIN_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_CONFIRM_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_END_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MANAGE_RELATE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_SOURCE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MANAGE_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PICK_SEQ" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PLAN_TYPE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RECORD_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RECORD_OPERATOR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RECORD_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_POSITION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_POSITION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_WEIGHT" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RECORD_MAIN" nillable="true" type="tns:RECORD_MAIN" />
  <xs:complexType name="ServiceMessage">
    <xs:sequence>
      <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
      <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Params" nillable="true" type="q1:ArrayOfanyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServiceMessage" nillable="true" type="tns:ServiceMessage" />
  <xs:complexType name="ServiceResponse">
    <xs:sequence>
      <xs:element xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Params" nillable="true" type="q2:ArrayOfanyType" />
      <xs:element minOccurs="0" name="Result" nillable="true" type="xs:anyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ServiceResponse" nillable="true" type="tns:ServiceResponse" />
  <xs:complexType name="STOCK">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_LIST" nillable="true" type="tns:ArrayOfBOX" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="STOCK" nillable="true" type="tns:STOCK" />
  <xs:complexType name="ArrayOfBOX">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="BOX" nillable="true" type="tns:BOX" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfBOX" nillable="true" type="tns:ArrayOfBOX" />
  <xs:complexType name="BOX">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GOODS_BARCODE_LIST" nillable="true" type="q3:ArrayOfstring" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="BOX" nillable="true" type="tns:BOX" />
  <xs:complexType name="STORAGE_DETAIL">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STORAGE_DETAIL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_DETAIL_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="STORAGE_DETAIL" nillable="true" type="tns:STORAGE_DETAIL" />
  <xs:complexType name="STORAGE_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="BOX_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ENTRY_TIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_ID" type="xs:int" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY10" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY11" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY12" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY13" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY14" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY15" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY16" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY17" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY18" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY19" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY20" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY21" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY22" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY23" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY24" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY25" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY26" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY27" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY28" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY29" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY30" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY4" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY5" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY6" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY7" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY8" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_PROPERTY9" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PLAN_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_LIST_QUANTITY" type="xs:decimal" />
      <xs:element minOccurs="0" name="STORAGE_LIST_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UPDATE_TIME" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="STORAGE_LIST" nillable="true" type="tns:STORAGE_LIST" />
  <xs:complexType name="STORAGE_MAIN">
    <xs:sequence>
      <xs:element minOccurs="0" name="CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FULL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TEMPLATE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="IS_EXCEPTION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="KITBOX_UP_COMPLETE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SPLIT_SOURCE_CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STOCK_BARCODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="STOCK_WEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="STORAGE_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="STORAGE_MAIN" nillable="true" type="tns:STORAGE_MAIN" />
  <xs:complexType name="SYS_ITEM">
    <xs:sequence>
      <xs:element minOccurs="0" name="ITEM_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_PARENT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ITEM" nillable="true" type="tns:SYS_ITEM" />
  <xs:complexType name="SYS_ITEM_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="ITEM_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ITEM_LIST_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="ITEM_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ITEM_LIST" nillable="true" type="tns:SYS_ITEM_LIST" />
  <xs:complexType name="SYS_LOG">
    <xs:sequence>
      <xs:element minOccurs="0" name="LOG_DATE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOG_ID" type="xs:int" />
      <xs:element minOccurs="0" name="LOG_LEVEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOG_LOGGER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOG_MESSAGE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOG_THREAD" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_LOG" nillable="true" type="tns:SYS_LOG" />
  <xs:complexType name="SYS_MENU">
    <xs:sequence>
      <xs:element minOccurs="0" name="MENU_CHILDNODEFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_CLASS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_DEVELOPFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_GROUP" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_IMAGE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_IMAGE_SL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_PARAMETER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_PARAMETER_SL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_PARENT_ID" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_SELECTEDFLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MENU_SYSFLAG" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_MENU" nillable="true" type="tns:SYS_MENU" />
  <xs:complexType name="SYS_RELATION">
    <xs:sequence>
      <xs:element minOccurs="0" name="RELATION_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_ID2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATION_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATON_NAME1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RELATON_NAME2" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_RELATION" nillable="true" type="tns:SYS_RELATION" />
  <xs:complexType name="SYS_RELATION_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="RELATION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID1" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_ID2" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RELATION_LIST_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_RELATION_LIST" nillable="true" type="tns:SYS_RELATION_LIST" />
  <xs:complexType name="SYS_ROLE">
    <xs:sequence>
      <xs:element minOccurs="0" name="ROLE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ROLE_START_MENU_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ROLE" nillable="true" type="tns:SYS_ROLE" />
  <xs:complexType name="SYS_ROLE_WINDOW">
    <xs:sequence>
      <xs:element minOccurs="0" name="CONTROL_HEADER" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONTROL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FLAG" type="xs:int" />
      <xs:element minOccurs="0" name="MENU_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="ROLE_WINDOW_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_ROLE_WINDOW" nillable="true" type="tns:SYS_ROLE_WINDOW" />
  <xs:complexType name="SYS_TABLE_CONVERTER_LIST">
    <xs:sequence>
      <xs:element minOccurs="0" name="COLUMN_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CONVERT_COLUMN_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ISNULL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_LIST_ID" type="xs:int" />
      <xs:element minOccurs="0" name="TABLE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UNIQUE_FLAG" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_TABLE_CONVERTER_LIST" nillable="true" type="tns:SYS_TABLE_CONVERTER_LIST" />
  <xs:complexType name="SYS_USER">
    <xs:sequence>
      <xs:element minOccurs="0" name="USER_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="USER_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="USER_PASSWORD" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="USER_REMARK" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_USER" nillable="true" type="tns:SYS_USER" />
  <xs:complexType name="WH_AREA">
    <xs:sequence>
      <xs:element minOccurs="0" name="AREA_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AREA_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AREA_GROUP" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AREA_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="AREA_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AREA_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_AREA" nillable="true" type="tns:WH_AREA" />
  <xs:complexType name="WH_CELL">
    <xs:sequence>
      <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_FORK_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_GROUP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_HEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_LOGICAL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_PROPERTY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_STORAGE_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_WIDTH" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_X" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Y" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Y_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_Z" type="xs:int" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GET_IN_STATION" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LANE_WAY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOCK_DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
      <xs:element minOccurs="0" name="RUN_STATUS" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SHELF_NEIGHBOUR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SHELF_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UNIT_STATUS" type="xs:int" />
      <xs:element minOccurs="0" name="UPDATETIME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_CELL" nillable="true" type="tns:WH_CELL" />
  <xs:complexType name="WH_DESCRIPTION">
    <xs:sequence>
      <xs:element minOccurs="0" name="AREA_ID" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_FORK_COUNT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_FORK_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_HEIGHT" type="xs:int" />
      <xs:element minOccurs="0" name="CELL_INOUT" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_LOGICAL_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_MODEL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_STORAGE_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CELL_WIDTH" type="xs:int" />
      <xs:element minOccurs="0" name="DESCRIPTION_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DESCRIPTION_ID" type="xs:int" />
      <xs:element minOccurs="0" name="DEVICE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DEVICE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="END_X" type="xs:int" />
      <xs:element minOccurs="0" name="END_Y" type="xs:int" />
      <xs:element minOccurs="0" name="END_Z" type="xs:int" />
      <xs:element minOccurs="0" name="LANE_WAY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
      <xs:element minOccurs="0" name="SHELF_NEIGHBOUR" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SHELF_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="START_X" type="xs:int" />
      <xs:element minOccurs="0" name="START_Y" type="xs:int" />
      <xs:element minOccurs="0" name="START_Z" type="xs:int" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_DESCRIPTION" nillable="true" type="tns:WH_DESCRIPTION" />
  <xs:complexType name="WH_LOGIC">
    <xs:sequence>
      <xs:element minOccurs="0" name="LOGIC_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_GROUP" type="xs:int" />
      <xs:element minOccurs="0" name="LOGIC_ID" type="xs:int" />
      <xs:element minOccurs="0" name="LOGIC_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="LOGIC_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="LOGIC_TYPE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_LOGIC" nillable="true" type="tns:WH_LOGIC" />
  <xs:complexType name="WH_WAREHOUSE">
    <xs:sequence>
      <xs:element minOccurs="0" name="WAREHOUSE_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_FLAG" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="WAREHOUSE_NAME" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_ORDER" type="xs:int" />
      <xs:element minOccurs="0" name="WAREHOUSE_REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="WAREHOUSE_TYPE" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WH_WAREHOUSE" nillable="true" type="tns:WH_WAREHOUSE" />
  <xs:complexType name="SYS_TABLE_CONVERTER">
    <xs:sequence>
      <xs:element minOccurs="0" name="CHILD_FOREIGN_KEY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CHILD_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GOODS_TYPE_ID" type="xs:int" />
      <xs:element minOccurs="0" name="PARENT_KEY" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PARENT_TABLE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="REMARK" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_CODE" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_ID" type="xs:int" />
      <xs:element minOccurs="0" name="TABLE_CONVERTER_NAME" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SYS_TABLE_CONVERTER" nillable="true" type="tns:SYS_TABLE_CONVERTER" />
  <xs:complexType name="ObjectTList">
    <xs:sequence>
      <xs:element xmlns:q4="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="RequestObject" nillable="true" type="q4:ArrayOfanyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ObjectTList" nillable="true" type="tns:ObjectTList" />
  <xs:complexType name="ObjectT._x003C__x003E_c">
    <xs:sequence />
  </xs:complexType>
  <xs:element name="ObjectT._x003C__x003E_c" nillable="true" type="tns:ObjectT._x003C__x003E_c" />
  <xs:complexType name="ObjectTList._x003C__x003E_c">
    <xs:sequence />
  </xs:complexType>
  <xs:element name="ObjectTList._x003C__x003E_c" nillable="true" type="tns:ObjectTList._x003C__x003E_c" />
</xs:schema>