﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_TEMPLETE_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_TEMPLATE_LIST
	{
        public GOODS_TEMPLATE_LIST()
		{
			
		}

        private int _goods_template_list_id;
        private int _goods_template_id;
        private int _goods_template_convert_id;
        private string _goods_template_name;
        private string _goods_template_code;
        private string _goods_template_type;
        private string _goods_template_child_table;
        private string _goods_template_status;
        private decimal _goods_template_quantity;
        private int _goods_type_id;
        private string _manage_type_code;
        private string _goods_template_remark;
        private string _mes_resource_no;
        private string _mes_operation_no;
        private string _mdl_name;

        ///<sumary>
        /// 物料模板列表编号
        ///</sumary>
        [DataMember]
        public int GOODS_TEMPLATE_LIST_ID
        {
            get { return _goods_template_list_id; }
            set { _goods_template_list_id = value; }
        }
        ///<sumary>
        /// 物料模板编号
        ///</sumary>
        [DataMember]
        public int GOODS_TEMPLATE_ID
        {
            get { return _goods_template_id; }
            set { _goods_template_id = value; }
        }
        ///<sumary>
        /// 物料编码
        ///</sumary>
        [DataMember]
        public int GOODS_TEMPLATE_CONVERT_ID
        {
            get { return _goods_template_convert_id; }
            set { _goods_template_convert_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_NAME
        {
            get { return _goods_template_name; }
            set { _goods_template_name = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_CODE
        {
            get { return _goods_template_code; }
            set { _goods_template_code = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_TYPE
        {
            get { return _goods_template_type; }
            set { _goods_template_type = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_CHILD_TABLE
        {
            get { return _goods_template_child_table; }
            set { _goods_template_child_table = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_STATUS
        {
            get { return _goods_template_status; }
            set { _goods_template_status = value; }
        }
        ///<sumary>
        /// 数量
        ///</sumary>
        [DataMember]
        public decimal GOODS_TEMPLATE_QUANTITY
        {
            get { return _goods_template_quantity; }
            set { _goods_template_quantity = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int GOODS_TYPE_ID
        {
            get { return _goods_type_id; }
            set { _goods_type_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MANAGE_TYPE_CODE
        {
            get { return _manage_type_code; }
            set { _manage_type_code = value; }
        }
        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_REMARK
        {
            get { return _goods_template_remark; }
            set { _goods_template_remark = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MES_RESOURCE_NO
        {
            get { return _mes_resource_no; }
            set { _mes_resource_no = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MES_OPERATION_NO
        {
            get { return _mes_operation_no; }
            set { _mes_operation_no = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string MDL_NAME
        {
            get { return _mdl_name; }
            set { _mdl_name = value; }
        }
	}
}
