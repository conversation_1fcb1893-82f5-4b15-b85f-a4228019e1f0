﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     siasun
 *       日期：     2013/5/21
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// MANAGE_TYPE_PARAM
    /// </summary>
    public class P_MANAGE_TYPE_PARAM : P_Base_House
    {
        public P_MANAGE_TYPE_PARAM()
        {
            //
            // TODO: 此处添加MANAGE_TYPE_PARAM的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_TYPE_PARAM> GetList(int MANAGE_TYPE_ID)
        {
            return ExecuteQueryForList<MANAGE_TYPE_PARAM>("MANAGE_TYPE_PARAM_SELECT_BY_MANAGE_TYPE_ID", MANAGE_TYPE_ID);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(MANAGE_TYPE_PARAM manage_type_param)
        {
            return ExecuteInsert("MANAGE_TYPE_PARAM_INSERT", manage_type_param);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public int Update(MANAGE_TYPE_PARAM manage_type_param)
        {
            return ExecuteUpdate("MANAGE_TYPE_PARAM_UPDATE", manage_type_param);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public MANAGE_TYPE_PARAM GetModel(System.Int32 PARAM_ID)
        {
            return ExecuteQueryForObject<MANAGE_TYPE_PARAM>("MANAGE_TYPE_PARAM_SELECT_BY_ID", PARAM_ID);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 PARAM_ID)
        {
            return ExecuteDelete("MANAGE_TYPE_PARAM_DELETE", PARAM_ID);
        }


    }
}
