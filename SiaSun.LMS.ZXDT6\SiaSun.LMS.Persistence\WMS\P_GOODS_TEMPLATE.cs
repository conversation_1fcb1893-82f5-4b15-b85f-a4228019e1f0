﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_TEMPLETE
	/// </summary>
	public class P_GOODS_TEMPLATE : P_Base_House
	{
		public P_GOODS_TEMPLATE ()
		{
			//
			// TODO: 此处添加GOODS_TEMPLETE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_TEMPLATE> GetList()
		{
			return ExecuteQueryForList<GOODS_TEMPLATE>("GOODS_TEMPLATE_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<GOODS_TEMPLATE> GetList(int GOODS_ID)
        {
            return ExecuteQueryForList<GOODS_TEMPLATE>("GOODS_TEMPLATE_SELECT_BY_GOODS_ID", GOODS_ID);
        }


        /// <summary>
        /// 新建
        /// </summary>
        public int Add(GOODS_TEMPLATE goods_template)
        {
            if (_isOracleProvider)
            {
                int id = this.GetPrimaryID("GOODS_TEMPLETE");
                goods_template.GOODS_TEMPLATE_ID = id;
            }

            return ExecuteInsert("GOODS_TEMPLATE_INSERT", goods_template);
        }
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_TEMPLATE goods_templete)
		{
			return ExecuteUpdate("GOODS_TEMPLATE_UPDATE",goods_templete);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_TEMPLATE GetModel(System.Int32 GOODS_TEMPLETE_ID)
		{
			return ExecuteQueryForObject<GOODS_TEMPLATE>("GOODS_TEMPLATE_SELECT_BY_ID",GOODS_TEMPLETE_ID);
		}


        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public GOODS_TEMPLATE GetModel(string GOODS_TEMPLATE_CODE)
        {
            return ExecuteQueryForObject<GOODS_TEMPLATE>("GOODS_TEMPLATE_SELECT_BY_TEMPLATE_CODE", GOODS_TEMPLATE_CODE);
        }


		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_TEMPLETE_ID)
		{
			return ExecuteDelete("GOODS_TEMPLATE_DELETE",GOODS_TEMPLETE_ID);
		}
		

	}
}
