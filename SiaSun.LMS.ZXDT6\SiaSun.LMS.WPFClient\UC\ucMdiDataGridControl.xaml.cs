﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucMdiDataGridControl.xaml 的交互逻辑
    /// </summary>
    public partial class ucMdiDataGridControl : UserControl
    {
        private bool boolAllowEdit = true;
        private string strWindowName = string.Empty;
        private string strParentTableName = string.Empty;
        private string strParentHeader = string.Empty;
        private string strParentTag = string.Empty;
        private string strParentWhere = string.Empty;
        private Dictionary<string, string> dicDefaultValues = new Dictionary<string, string>();
        private string strChildTableName = string.Empty;
        private string strChildHeader = string.Empty;
        private string strChildWhere = string.Empty;
        private string strChildTag = string.Empty;

        private string strParentOrder = string.Empty;
        private string strChildOrder = string.Empty;

        /// <summary>
        /// 是否允许编辑
        /// </summary>
        public bool U_AllowEdit
        {
            get { return boolAllowEdit; }
            set { boolAllowEdit = value; }
        }

        /// <summary>
        /// 窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return strWindowName; }
            set { strWindowName = value; }
        }

        /// <summary>
        /// 上级标题
        /// </summary>
        public string U_ParentHeader
        {
            get { return strParentHeader; }
            set { strParentHeader = value; }
        }

        /// <summary>
        /// 上级表名
        /// </summary>
        public string U_ParentTableName
        {
            get { return strParentTableName; }
            set { strParentTableName = value; }
        }

        /// <summary>
        /// 上级节点标识
        /// </summary>
        public string U_ParentTag
        {
            get { return strParentTag; }
            set { strParentTag = value; }
        }

        /// <summary>
        /// 上级绑定条件
        /// </summary>
        public string U_ParentWhere
        {
            get { return strParentWhere; }
            set { strParentWhere = value; }
        }

        /// <summary>
        /// 父控件默认值
        /// </summary>
        public Dictionary<string, string> U_ParentDefaultValues
        {
            get { return this.dicDefaultValues; }
            set { this.dicDefaultValues = value; }
        }

        /// <summary>
        /// 下级标题
        /// </summary>
        public string U_ChildHeader
        {
            get { return strChildHeader; }
            set { strChildHeader = value;  }
        }

        /// <summary>
        /// 下级表名
        /// </summary>
        public string U_ChildTableName
        {
            get { return strChildTableName; }
            set { strChildTableName = value; }
        }

        /// <summary>
        /// 子表查询条件
        /// </summary>
        public string U_ChildWhere
        {
            get { return strChildWhere; }
            set { strChildWhere = value; }
        }

        /// <summary>
        /// 下级标识
        /// </summary>
        public string U_ChildTag
        {
            get { return strChildTag; }
            set { strChildTag = value; }
        }

        /// <summary>
        /// 父窗体排序
        /// </summary>
        public string U_ParentOrder
        {
            get { return strParentOrder; }
            set { strParentOrder = value; }
        }

        /// <summary>
        /// 子窗体排序
        /// </summary>
        public string U_ChildOrder
        {
            get { return strChildOrder; }
            set { strChildOrder = value; }
        }


        public ucMdiDataGridControl()
        {
            InitializeComponent();

            //this.gridParent.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
            this.gridParent.gridApp.GotFocus += new RoutedEventHandler(gridApp_GotFocus);

            

            this.gridParent.gridApp.LostFocus += new RoutedEventHandler(gridApp_LostFocus);

            this.ucQueryQuick.U_Query += new ucQuickQuery.U_QueryEventHandler(ucQueryQuick_U_Query);
        }

        void gridApp_LostFocus(object sender, RoutedEventArgs e)
        {
            this.gridParent.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        void gridApp_GotFocus(object sender, RoutedEventArgs e)
        {
            this.gridParent.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
            this.gridParent.gridApp.SelectionChanged+=new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //显示信息
            this.grpboxParent.Header = string.Format(this.grpboxParent.Header.ToString(),strParentHeader);
            this.grpboxChild.Header = string.Format(this.grpboxChild.Header .ToString(),strChildHeader);

            //初始化查询控件
            InitQuery();

            //如果不允许编辑
            this.gridParent.U_AllowOperatData = boolAllowEdit;
            this.gridParent.U_AllowChecked = boolAllowEdit;
            this.gridParent.U_AllowPage = false;

            this.gridChild.U_AllowOperatData = boolAllowEdit;
            this.gridChild.U_AllowChecked = boolAllowEdit;
            this.gridChild.U_AllowPage = false;

            //加载上级列表数据
            this.LoadParentData(string.Empty);
        }

        /// <summary>
        /// 加载查询控件
        /// </summary>
        private void InitQuery()
        {
            //初始化查询控件
            this.ucQueryQuick.U_WindowName = this.strWindowName;
            this.ucQueryQuick.U_XmlTableName = strParentTableName;
            this.ucQueryQuick.U_InitControl();
         }

        /// <summary>
        /// 查询
        /// </summary>
        void ucQueryQuick_U_Query(string QueryWhere)
        {
            this.LoadParentData(QueryWhere);
        }

        /// <summary>
        /// 加载父控件数据
        /// </summary>
        private void LoadParentData(string QueryWhere)
        {
            try
            {
                gridParent.U_WindowName = this.strWindowName;
                gridParent.U_TableName = strParentTableName;
                gridParent.U_XmlTableName = strParentTableName;
                gridParent.U_Where = strParentWhere.Length > 0 ? (QueryWhere.Length == 0 ? string.Format("{0}", strParentWhere) : QueryWhere + string.Format(" AND {0}", strParentWhere)) : QueryWhere;

                gridParent.U_DefaultRowValues = this.dicDefaultValues;

                gridParent.U_OrderField = this.strParentOrder;


                gridParent.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
        
        /// <summary>
        /// 选择行
        /// </summary>
        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.gridParent.gridApp.SelectedItem == null)
                return;

            DataRowView dataRowView = this.gridParent.gridApp.SelectedItem as DataRowView;
            if (dataRowView != null)
            {
                //加载数据
                if (dataRowView[strParentTag].ToString().Length > 0)
                {
                    //绑定下级数据
                    LoadChildData(dataRowView[strParentTag].ToString());
                }
            }

            this.gridParent.gridApp.SelectionChanged -= new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        /// <summary>
        /// 加载下级数据
        /// </summary>
        private void LoadChildData(string ParentTag)
        {
            try
            {
                gridChild.U_Clear();
                gridChild.U_WindowName = this.strWindowName;
                gridChild.U_TableName = strChildTableName;
                gridChild.U_XmlTableName = strChildTableName;
                gridChild.U_Where = string.Format("{0}='{1}'", strChildTag, ParentTag) + (strChildWhere.Length == 0 ? string.Empty : " AND " + strChildWhere);
                gridChild.U_DefaultRowValues.Add(strChildTag, ParentTag);

                gridChild.U_OrderField = this.strChildOrder;

                gridChild.U_InitControl();
               

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
