<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!--FATAL-毁灭级别,ERROR-错误级别,WARN-警告级别,INFO-消息级别,DEBUG-调试级别-->
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  </configSections>
  <log4net>

    <logger name="WinServiceLog">
      <level value="INFO"/>
      <appender-ref ref="WinServiceLogFileAppender" />
    </logger>

    <logger name="ImplementLog">
      <level value="INFO"/>
      <appender-ref ref="ImplementLogFileAppender" />
    </logger>

	<logger name="ClientLog">
		<level value="INFO"/>
		<appender-ref ref="ClientLogFileAppender" />
	</logger>

	<!--<logger name="PdaLog">
    <level value="INFO"/>
      <appender-ref ref="PdaLogFileAppender" />
    </logger>-->

    <!--输出到SqlServer数据库-->
    <appender name="SqlServerAppender" type="log4net.Appender.AdoNetAppender">
      <bufferSize value="1" />
      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      <connectionString value="data source=192.168.10.103;initial catalog=JXFST_WMS;integrated security=false;persist security info=True;User ID=sa;Password=`123qwe" />
      <commandText value="insert into SYS_LOG1 (LOG_DATE,LOG_THREAD,LOG_LEVEL,LOG_CLASS,LOG_METHOD,LOG_LOGGER,LOG_MESSAGE,LOG_EXCEPTION,LOG_TYPE,LOG_OPERATOR) values (@log_date, @thread, @log_level, @log_class, @log_method, @logger, @message, @exception, @log_type, @log_operator)" />

      <parameter>
        <parameterName value="@log_date" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout" >
          <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.ffff}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@thread" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%thread" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_level" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_class" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%class" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_method" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%method" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@logger" />
        <dbType value="String" />
        <size value="255" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%logger" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="4000" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@exception" />
        <dbType value="String" />
        <size value="2000" />
        <layout type="log4net.Layout.ExceptionLayout" />
      </parameter>
      <parameter>
        <parameterName value="@log_type" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{LogType}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_operator" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{LogOperator}" />
        </layout>
      </parameter>
    </appender>

    <!--输出到Oracle数据库-->
    <appender name="OracleAppender" type="log4net.Appender.ADONetAppender">

      <bufferSize value="0" />
      <connectionType value="Oracle.ManagedDataAccess.Client.OracleConnection, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342"/>
      <connectionString value="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=127.0.0.1)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=orcl)));User Id=c##***;Password=***;" />

      <commandText value="insert into SYS_LOG (LOG_ID,LOG_THREAD,LOG_LEVEL,LOG_LOGGER,LOG_DATE,LOG_MESSAGE) values (SYS_LOG_SEQ.nextval, :customThread, :customLevel, :customLogger, :log_date, :message)" />

      <parameter>
        <parameterName value=":customThread" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{CustomThread}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value=":customLevel" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{CustomLevel}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value=":customLogger" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{CustomLogger}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value=":log_date" />
        <dbType value="String" />
        <size value="50" />
        <layout type="log4net.Layout.PatternLayout" >
          <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.ffff}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value=":message" />
        <dbType value="String" />
        <size value="1000" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
    </appender>

    
    <!--输出到文件WinService-->
    <appender name="WinServiceLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="log\WinServiceLog_" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyy-MM-dd&quot;.html&quot;" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='100%' border='1' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='1' cellspacing='0' &gt;
              &lt;tr&gt;
            &lt;td&gt;记录时间&lt;/td&gt;
            &lt;td&gt;线程&lt;/td&gt;
            &lt;td&gt;等级&lt;/td&gt;
            &lt;td&gt;方法&lt;/td&gt;
            &lt;td&gt;信息&lt;/td&gt;
            &lt;td&gt;异常&lt;/td&gt;
            &lt;/tr&gt;" />
        <conversionPattern value="&lt;tr&gt;
             &lt;td&gt;%date{yyyy-MM-dd HH:mm:ss.ffff}&lt;/td&gt;
            &lt;td&gt;%thread&lt;/td&gt;
            &lt;td&gt;%level&lt;/td&gt;
            &lt;td&gt;%class.%method()&lt;/td&gt;
            &lt;td&gt;%message&lt;/td&gt;
            &lt;td&gt;%exception&lt;/td&gt;
            &lt;/tr&gt;" />
        <footer value="&lt;/table&gt;" />
      </layout>
    </appender>

    <!--输出到文件Implement-->
    <appender name="ImplementLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="log\ImplementLog_" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyy-MM-dd&quot;.html&quot;" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='100%' border='1' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='1' cellspacing='0' &gt;
              &lt;tr&gt;
            &lt;td&gt;记录时间&lt;/td&gt;
            &lt;td&gt;线程&lt;/td&gt;
            &lt;td&gt;等级&lt;/td&gt;
            &lt;td&gt;方法&lt;/td&gt;
            &lt;td&gt;信息&lt;/td&gt;
            &lt;td&gt;异常&lt;/td&gt;
            &lt;/tr&gt;" />
        <conversionPattern value="&lt;tr&gt;
             &lt;td&gt;%date{yyyy-MM-dd HH:mm:ss.ffff}&lt;/td&gt;
            &lt;td&gt;%thread&lt;/td&gt;
            &lt;td&gt;%level&lt;/td&gt;
            &lt;td&gt;%class.%method()&lt;/td&gt;
            &lt;td&gt;%message&lt;/td&gt;
            &lt;td&gt;%exception&lt;/td&gt;
            &lt;/tr&gt;" />
        <footer value="&lt;/table&gt;" />
      </layout>
    </appender>

	<!--输出到文件Client-->
	<appender name="ClientLogFileAppender" type="log4net.Appender.RollingFileAppender">
		<file value="log\ClientLog_" />
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<datePattern value="yyyy-MM-dd&quot;.html&quot;" />
		<staticLogFileName value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='100%' border='1' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='1' cellspacing='0' &gt;
            &lt;tr&gt;
        &lt;td&gt;记录时间&lt;/td&gt;
        &lt;td&gt;线程&lt;/td&gt;
        &lt;td&gt;等级&lt;/td&gt;
        &lt;td&gt;方法&lt;/td&gt;
        &lt;td&gt;信息&lt;/td&gt;
        &lt;td&gt;异常&lt;/td&gt;
        &lt;/tr&gt;" />
			<conversionPattern value="&lt;tr&gt;
            &lt;td&gt;%date{yyyy-MM-dd HH:mm:ss.ffff}&lt;/td&gt;
        &lt;td&gt;%thread&lt;/td&gt;
        &lt;td&gt;%level&lt;/td&gt;
        &lt;td&gt;%class.%method()&lt;/td&gt;
        &lt;td&gt;%message&lt;/td&gt;
        &lt;td&gt;%exception&lt;/td&gt;
        &lt;/tr&gt;" />
			<footer value="&lt;/table&gt;" />
		</layout>
	</appender>

    <!--输出到文件Pda-->
    <appender name="PdaLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="log\PdaLog_" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />
      <datePattern value="yyyy-MM-dd&quot;.html&quot;" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='100%' border='1' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='1' cellspacing='0' &gt;
              &lt;tr&gt;
            &lt;td&gt;记录时间&lt;/td&gt;
            &lt;td&gt;线程&lt;/td&gt;
            &lt;td&gt;等级&lt;/td&gt;
            &lt;td&gt;方法&lt;/td&gt;
            &lt;td&gt;信息&lt;/td&gt;
            &lt;td&gt;异常&lt;/td&gt;
            &lt;/tr&gt;" />
        <conversionPattern value="&lt;tr&gt;
             &lt;td&gt;%date{yyyy-MM-dd HH:mm:ss.ffff}&lt;/td&gt;
            &lt;td&gt;%thread&lt;/td&gt;
            &lt;td&gt;%level&lt;/td&gt;
            &lt;td&gt;%class.%method()&lt;/td&gt;
            &lt;td&gt;%message&lt;/td&gt;
            &lt;td&gt;%exception&lt;/td&gt;
            &lt;/tr&gt;" />
        <footer value="&lt;/table&gt;" />
      </layout>
    </appender>


  </log4net>
</configuration>